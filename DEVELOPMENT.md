# Development Guide

This document outlines the development setup and guidelines for this project.

## Code Formatting and Linting

This project uses **Prettier** for code formatting and **ESLint** for linting, automatically enforced on every commit.

### Prettier Configuration

Prettier is configured with the following settings (`.prettierrc`):

- Semi-colons: enabled
- Single quotes: enabled
- Print width: 80 characters
- Tab width: 2 spaces
- Trailing commas: ES5 compatible
- Arrow function parentheses: avoid when possible

### Automatic Formatting on Commit

When you commit code, the following happens automatically:

1. **Pre-commit hook** runs `lint-staged` which:

   - Formats all staged files with Prettier
   - Runs ESLint with auto-fix on TypeScript/JavaScript files
   - Only processes files that are staged for commit

2. **Commit message validation** ensures every commit contains a ticket number

### Manual Formatting

You can also format code manually:

```bash
# Format all files
pnpm run format

# Check if files are properly formatted (without changing them)
pnpm run format:check
```

## Commit Message Requirements

Every commit message **must** contain a ticket number in the format `CHIL-XX` where XX is the ticket number.

### Valid Commit Messages ✅

```bash
git commit -m "CHIL-69 Add user authentication feature"
git commit -m "Fix login bug CHIL-123"
git commit -m "CHIL-456: Update documentation for API endpoints"
git commit -m "Refactor database queries (CHIL-789)"
```

### Invalid Commit Messages ❌

```bash
git commit -m "Add user authentication feature"  # Missing ticket number
git commit -m "Fix login bug"                    # Missing ticket number
git commit -m "Update documentation"             # Missing ticket number
```

### Testing Commit Messages

You can test if a commit message would be accepted:

```bash
# This will fail - no ticket number
echo "test commit without ticket" | npx commitlint

# This will pass - contains ticket number
echo "CHIL-69 test commit with ticket" | npx commitlint
```

## Development Workflow

1. **Make your changes** to the codebase
2. **Stage your files** with `git add`
3. **Commit with a proper message** including ticket number:
   ```bash
   git commit -m "CHIL-123 Your descriptive commit message"
   ```
4. The pre-commit hook will automatically:
   - Format your staged files with Prettier
   - Run ESLint and fix auto-fixable issues
   - Validate your commit message contains a ticket number

## Bypassing Hooks (Not Recommended)

In rare cases where you need to bypass the hooks:

```bash
# Skip pre-commit hook (formatting/linting)
git commit --no-verify -m "CHIL-123 Emergency fix"

# Note: This will still validate the commit message
```

## IDE Integration

### VS Code

Install these extensions for the best development experience:

- **Prettier - Code formatter**: Auto-format on save
- **ESLint**: Real-time linting feedback

Add to your VS Code settings (`.vscode/settings.json`):

```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  }
}
```

### Other IDEs

Most modern IDEs support Prettier and ESLint. Configure them to:

- Format files on save using Prettier
- Show ESLint warnings/errors inline
- Use the project's `.prettierrc` and ESLint configurations

## Troubleshooting

### Commit Rejected Due to Formatting

If your commit is rejected due to formatting issues:

1. The pre-commit hook should have automatically formatted your files
2. Review the changes and stage them: `git add .`
3. Commit again with the same message

### Commit Rejected Due to Missing Ticket Number

If your commit is rejected due to missing ticket number:

1. Use `git commit --amend` to edit your commit message
2. Add the ticket number in format `CHIL-XX`
3. Save and exit the editor

### Prettier Conflicts with ESLint

The configurations are designed to work together. If you encounter conflicts:

1. Check that you're using the latest versions of both tools
2. Prettier rules take precedence over ESLint formatting rules
3. ESLint focuses on code quality, Prettier on formatting

## Package Scripts

- `pnpm run format` - Format all files with Prettier
- `pnpm run format:check` - Check if files are formatted correctly
- `pnpm run lint` - Run ESLint on the codebase
- `pnpm run prepare` - Set up Husky hooks (runs automatically after install)

## Configuration Files

- `.prettierrc` - Prettier configuration
- `.prettierignore` - Files to ignore during formatting
- `commitlint.config.js` - Commit message validation rules
- `.husky/pre-commit` - Pre-commit hook script
- `.husky/commit-msg` - Commit message validation hook
- `package.json` - Contains lint-staged configuration
