import * as dotenv from 'dotenv';
dotenv.config({ path: '.env' });

import { referenceAuth } from '@aws-amplify/backend';
import { postSignUp } from '../functions/postSignUpTrigger/resource';
import { addUserToGroup } from '../functions/addUserToGroup/resource';
import { checkEmail } from '../functions/checkEmail/resource';
import { trackLoginAttempt } from '../functions/trackLoginAttempt/resource';
import { generateVerificationToken } from '../functions/generateVerificationToken/resource';
import { verifyEmailToken } from '../functions/verifyEmailToken/resource';
import { activateDeactivateUser } from '../functions/activateDeactivateUser/resource';
import { addUserToGroupV2 } from '../functions/addUserToGroupV2/resource';
import { signOutDevice } from '../functions/signOutDevice/resource';
import { inviteUser } from '../functions/inviteUser/inviteUser/resource';
import { acceptInvite } from '../functions/inviteUser/acceptInvite/resource';
import { scheduler<PERSON>ramework } from '../functions/scheduler-framework/resource';
import { deleteUserComplete } from '../functions/deleteUserComplete/resource';
import { initiatePhoneVerification } from '../functions/initiatePhoneVerification/resource';
import { verifyPhoneCode } from '../functions/verifyPhoneCode/resource';

const userPoolId = process.env.CFLegacy_UserPoolID;
const identityPoolId = process.env.CFLegacy_IdentityPoolID;
const authRoleArn = process.env.CFLegacy_authRoleARN;
const unauthRoleArn = process.env.CFLegacy_unauthRoleARN;
const userPoolClientId = process.env.CFLegacy_userPoolClientID;

// This is here to throw an error during deployment to know which one is undefined or missing
if (
  !userPoolId ||
  !identityPoolId ||
  !authRoleArn ||
  !unauthRoleArn ||
  !userPoolClientId
) {
  throw new Error(
    'Missing required Cognito environment variables. Current values:\n\tuserPoolId: ' +
      userPoolId +
      '\n\tidentityPoolId: ' +
      identityPoolId +
      '\n\tauthRoleArn: ' +
      authRoleArn +
      '\n\tunauthRoleArn: ' +
      unauthRoleArn +
      '\n\tuserPoolClientId: ' +
      userPoolClientId
  );
}

export const auth = referenceAuth({
  userPoolId,
  identityPoolId,
  authRoleArn,
  unauthRoleArn,
  userPoolClientId,
  groups: {
    ADMINS: process.env.Admins_Role || 'not found',
    WELONTRUST: process.env.WelonTrust_Role || 'not found',
  },
  access: allow => [
    allow.resource(activateDeactivateUser).to(['manageUsers', 'listUsers']),
    allow.resource(inviteUser).to(['manageUsers', 'listUsers']),
    allow.resource(acceptInvite).to(['manageUsers', 'listUsers']),
    allow
      .resource(addUserToGroup)
      .to(['addUserToGroup', 'removeUserFromGroup', 'listGroupsForUser']),
    allow.resource(postSignUp).to(['manageUsers', 'listUsers']),
    allow
      .resource(addUserToGroupV2)
      .to(['addUserToGroup', 'removeUserFromGroup', 'listGroupsForUser']),
    allow.resource(checkEmail).to(['manageUsers', 'listUsers']),
    allow.resource(trackLoginAttempt).to(['manageUsers', 'listUsers']),
    allow.resource(generateVerificationToken).to(['manageUsers', 'listUsers']),
    allow
      .resource(verifyEmailToken)
      .to(['manageUsers', 'listUsers', 'managePasswordRecovery']),
    allow.resource(initiatePhoneVerification).to(['manageUsers', 'listUsers']),
    allow.resource(verifyPhoneCode).to(['manageUsers', 'listUsers']),
    allow.resource(signOutDevice).to(['manageUserDevices', 'listUsers']),
    allow.resource(schedulerFramework).to(['manageUsers', 'listUsers']),
    allow.resource(deleteUserComplete).to(['manageUsers', 'listUsers']),
  ],
});
