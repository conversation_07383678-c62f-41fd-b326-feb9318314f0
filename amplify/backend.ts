import * as dotenv from 'dotenv';
dotenv.config({ path: '.env' });

import { defineBackend } from '@aws-amplify/backend';
import { PolicyStatement, Effect } from 'aws-cdk-lib/aws-iam';
import { auth } from './auth/resource';
import { data } from './data/resource';
import { storage } from './storage/resource';
import { postSignUp } from './functions/postSignUpTrigger/resource';
import { addUserToGroup } from './functions/addUserToGroup/resource';
import { checkEmail } from './functions/checkEmail/resource';
import { trackLoginAttempt } from './functions/trackLoginAttempt/resource';
import { sendEmail } from './functions/sendEmail/resource';
import { generateVerificationToken } from './functions/generateVerificationToken/resource';
import { verifyEmailToken } from './functions/verifyEmailToken/resource';
import { initiatePhoneVerification } from './functions/initiatePhoneVerification/resource';
import { verifyPhoneCode } from './functions/verifyPhoneCode/resource';
import { activateDeactivateUser } from './functions/activateDeactivateUser/resource';
import { addUserToGroupV2 } from './functions/addUserToGroupV2/resource';
import { exportDocumentUpdateLogs } from './functions/exportDocumentUpdateLogs/resource';
import { signOutDevice } from './functions/signOutDevice/resource';
import { getSecrets } from './functions/get-secrets/resource';
import { schedulerFramework } from './functions/scheduler-framework/resource';
import { inviteUser } from './functions/inviteUser/inviteUser/resource';
import { acceptInvite } from './functions/inviteUser/acceptInvite/resource';
import { dmsCheck } from './functions/scheduler-functions/dms-check/resource';
import { verifyAndFetchDocuments } from './functions/verifyAndFetchDocuments/resource';
import { getEmergencyFileUrl } from './functions/getEmergencyFileUrl/resource';
import { deleteUserComplete } from './functions/deleteUserComplete/resource';
import { generatePdfDoc } from './functions/generatePdfDoc/resource';
import { preTokenGeneration } from './functions/preTokenGeneration/resource';

/**
 * @see https://docs.amplify.aws/react/build-a-backend/ to add storage, functions, and more
 */
const backend = defineBackend({
  auth,
  data,
  addUserToGroup,
  addUserToGroupV2,
  checkEmail,
  storage,
  postSignUp,
  preTokenGeneration,
  trackLoginAttempt,
  generatePdfDoc,
  sendEmail,
  generateVerificationToken,
  verifyEmailToken,
  initiatePhoneVerification,
  verifyPhoneCode,
  activateDeactivateUser,
  exportDocumentUpdateLogs,
  signOutDevice,
  getSecrets,
  schedulerFramework,
  inviteUser,
  acceptInvite,
  dmsCheck,
  verifyAndFetchDocuments,
  getEmergencyFileUrl,
  deleteUserComplete,
});

// Add environment variables to the postSignUp function
backend.postSignUp.addEnvironment(
  'USER_POOL_ID',
  process.env.CFLegacy_UserPoolID || 'not found'
);

// Add environment variables to the addUserToGroup function
backend.addUserToGroup.addEnvironment(
  'USER_POOL_ID',
  process.env.CFLegacy_UserPoolID || 'not found'
);

// Add environment variables to the addUserToGroup function
backend.addUserToGroupV2.addEnvironment(
  'USER_POOL_ID',
  process.env.CFLegacy_UserPoolID || 'not found'
);

// Add environment variables to the addUserToGroup function
backend.inviteUser.addEnvironment(
  'USER_POOL_ID',
  process.env.CFLegacy_UserPoolID || 'not found'
);

// Add environment variables to the acceptInvite function
backend.acceptInvite.addEnvironment(
  'USER_POOL_ID',
  process.env.CFLegacy_UserPoolID || 'not found'
);

backend.schedulerFramework.addEnvironment(
  'USER_POOL_ID',
  process.env.CFLegacy_UserPoolID || 'not found'
);

// Add environment variables to the activateDeactivateUser function
backend.activateDeactivateUser.addEnvironment(
  'USER_POOL_ID',
  process.env.CFLegacy_UserPoolID || 'not found'
);

// Add environment variables to the checkEmail function
backend.checkEmail.addEnvironment(
  'USER_POOL_ID',
  process.env.CFLegacy_UserPoolID || 'not found'
);

backend.verifyEmailToken.addEnvironment(
  'USER_POOL_ID',
  process.env.CFLegacy_UserPoolID || 'not found'
);

backend.verifyPhoneCode.addEnvironment(
  'USER_POOL_ID',
  backend.auth.resources.userPool.userPoolId
);

backend.verifyAndFetchDocuments.addEnvironment(
  'USER_POOL_ID',
  process.env.CFLegacy_UserPoolID || 'not found'
);

// Add environment variables to the deleteUserComplete function
backend.deleteUserComplete.addEnvironment(
  'USER_POOL_ID',
  process.env.CFLegacy_UserPoolID || 'not found'
);

// Add SES permissions to the sendEmail function
backend.sendEmail.resources.lambda.addToRolePolicy(
  new PolicyStatement({
    effect: Effect.ALLOW,
    actions: [
      'ses:SendEmail',
      'ses:SendRawEmail',
      'ses:GetSendQuota',
      'ses:GetSendStatistics',
    ],
    resources: ['*'],
  })
);

// Grant the postConfirmation function access to the data resources
// backend.data.resources.graphqlApi.grantMutation(
//   backend.postConfirmation.resources.lambda,
//   'User'
// );
// backend.data.resources.graphqlApi.grantMutation(
//   backend.postConfirmation.resources.lambda,
//   'UserOnboarding'
// );

// // extract L1 CfnUserPool resources
// const { cfnUserPool, cfnUserPoolClient } = backend.auth.resources.cfnResources;
// // modify cfnUserPool policies directly
// cfnUserPool.policies = {
//   passwordPolicy: {
//     minimumLength: 12,
//     requireLowercase: true,
//     requireNumbers: true,
//     requireSymbols: true,
//     requireUppercase: true,
//     temporaryPasswordValidityDays: 3,
//   },
// };
//
// // Enable device tracking
// cfnUserPool.deviceConfiguration = {
//   challengeRequiredOnNewDevice: false,
//   deviceOnlyRememberedOnUserPrompt: false,
// };
//
// if (cfnUserPoolClient) {
//   cfnUserPoolClient.accessTokenValidity = 1;
//   cfnUserPoolClient.idTokenValidity = 1;
//   cfnUserPoolClient.refreshTokenValidity = 30;
//   cfnUserPoolClient.tokenValidityUnits = {
//     accessToken: 'hours',
//     idToken: 'hours',
//     refreshToken: 'days',
//   };
// }

// Add S3 permissions to the getEmergencyFileUrl function
backend.getEmergencyFileUrl.resources.lambda.addToRolePolicy(
  new PolicyStatement({
    effect: Effect.ALLOW,
    actions: ['s3:GetObject', 's3:ListBucket'],
    resources: [
      // reference to the storage bucket
      backend.storage.resources.bucket.bucketArn,
      `${backend.storage.resources.bucket.bucketArn}/*`,
    ],
  })
);

// Add S3 permissions to the generatePdfDoc function
backend.generatePdfDoc.resources.lambda.addToRolePolicy(
  new PolicyStatement({
    effect: Effect.ALLOW,
    actions: [
      's3:PutObject',
      's3:PutObjectAcl',
      's3:GetObject',
      's3:ListBucket',
      's3:ListBucketVersions',
    ],
    resources: [
      // reference to the storage bucket
      backend.storage.resources.bucket.bucketArn,
      `${backend.storage.resources.bucket.bucketArn}/*`,
    ],
  })
);

// Add the bucket name as an environment variable to the getEmergencyFileUrl function
backend.getEmergencyFileUrl.addEnvironment(
  'STORAGE_BUCKET',
  backend.storage.resources.bucket.bucketName
);

// Add the bucket name as an environment variable to the generatePdfDoc function
backend.generatePdfDoc.addEnvironment(
  'STORAGE_BUCKET',
  backend.storage.resources.bucket.bucketName
);

// Add SNS permissions to the initiatePhoneVerification function
backend.initiatePhoneVerification.resources.lambda.addToRolePolicy(
  new PolicyStatement({
    effect: Effect.ALLOW,
    actions: ['sns:Publish'],
    resources: ['*'], // Allow publishing to any phone number
  })
);

export default backend;
