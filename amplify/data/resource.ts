import {
  type ClientSchema,
  a,
  defineData,
  defineFunction,
} from '@aws-amplify/backend';

// Import all model definitions from schemas
import {
  Todo,
  UserOnboarding,
  AdminResource,
  LoginAttempt,
  LoginHistory,
  VerificationTokens,
  InterviewSet,
  InterviewQuestion,
  InterviewSetVersion,
  UserInterviewProgress,
  Template,
  TemplateVersion,
  EmergencyContact,
  CareDocument,
  Document,
  DocumentUpdateLog,
  VaultAccess,
  User,
  WelonTrustAssignment,
  UserCareDocument,
  UserCareDocumentChangeLog,
  LinkedAccount,
  LinkedAccountWithSharedFields,
  Attorney,
  EvidenceSubmission,
  EducationalContent,
  DeadMansSwitch,
  CommunicationMethod,
  EscalationProtocol,
  DMSStatus,
  CheckInFrequency,
  CheckInHistory,
  EscalationEvent,
  DMSTestEvent,
  Interview,
  InterviewVersion,
  Question,
  ConditionalLogic,
  UserInterviewProgressNew,
  UserCareDocumentAnswer,
  UserAnswer,
  UserInvite,
  UserSubscription,
  Notification,
  CareDocumentTemplate,
  CareDocumentQuestion,
  CareDocumentQuestionOption,
  UserAddress,
  ShippingLabel,
  MemberNote,
  TemplateSection,
  SectionVersion,
  InterviewProgressV2,
} from './schemas';
import { postSignUp } from '../functions/postSignUpTrigger/resource';
import { addUserToGroup } from '../functions/addUserToGroup/resource';
import { checkEmail } from '../functions/checkEmail/resource';
import { trackLoginAttempt } from '../functions/trackLoginAttempt/resource';
import { generatePdfDoc } from '../functions/generatePdfDoc/resource';
import { generateVerificationToken } from '../functions/generateVerificationToken/resource';
import { verifyEmailToken } from '../functions/verifyEmailToken/resource';
import { sendEmail } from '../functions/sendEmail/resource';
import { initiatePhoneVerification } from '../functions/initiatePhoneVerification/resource';
import { verifyPhoneCode } from '../functions/verifyPhoneCode/resource';
import { activateDeactivateUser } from '../functions/activateDeactivateUser/resource';
import { exportDocumentUpdateLogs } from '../functions/exportDocumentUpdateLogs/resource';
import { signOutDevice } from '../functions/signOutDevice/resource';
import { getSecrets } from '../functions/get-secrets/resource';
import { schedulerFramework } from '../functions/scheduler-framework/resource';
import { inviteUser } from '../functions/inviteUser/inviteUser/resource';
import { readInvite } from '../functions/inviteUser/readInvite/resource';
import { acceptInvite } from '../functions/inviteUser/acceptInvite/resource';
import { verifyAndFetchDocuments } from '../functions/verifyAndFetchDocuments/resource';
import { deleteUserComplete } from '../functions/deleteUserComplete/resource';
import { dmsCheck } from '../functions/scheduler-functions/dms-check/resource';
import { getEmergencyFileUrl } from '../functions/getEmergencyFileUrl/resource';

/**
 * You can use the secret SQL_CONNECTION_STRING to get access to the
 * DB for any code push. If you want to use a differencet user or non-default DB
 * then let me know. The default DB is call cfl_dev_db and the credentials will
 * be sent outside of the code.
 */

const schema = a
  .schema({
    // Demo/Example models
    Todo,

    // Authentication and user onboarding models
    UserOnboarding,
    AdminResource,
    LoginAttempt,
    LoginHistory,
    VerificationTokens,

    // Interview and questionnaire models
    InterviewSet,
    InterviewQuestion,
    UserInterviewProgress,
    InterviewSetVersion,

    // Template management models
    Template,
    TemplateVersion,
    TemplateSection,
    SectionVersion,

    // Emergency contact models
    EmergencyContact,

    // Care document models
    CareDocument,
    DocumentUpdateLog,
    Document,

    // Vault access models
    VaultAccess,

    // User management models
    User,
    UserAddress,
    ShippingLabel,
    WelonTrustAssignment,
    MemberNote,
    LinkedAccount,
    LinkedAccountWithSharedFields,
    UserInterviewProgressNew,
    UserCareDocumentAnswer,
    UserAnswer,
    UserCareDocument,
    UserCareDocumentChangeLog,
    InterviewProgressV2,
    // TABLES AND CUSTOM RESOURCES FOR CREATION AND WORK WITH USER INVITES
    UserInvite,
    inviteUser: a
      .mutation()
      .arguments({
        firstName: a.string().required(),
        lastName: a.string().required(),
        email: a.string().required(),
        role: a.string().required(),
        subrole: a.string().required(),
        sharedFields: a.string(),
        userId: a.id(),
        baseUrl: a.string(),
      })
      .authorization(allow => [allow.group('ADMINS'), allow.authenticated()])
      .handler(a.handler.function(inviteUser))
      .returns(a.json()),
    readInvite: a
      .query()
      .arguments({
        token: a.string().required(),
      })
      .authorization(allow => [allow.guest()])
      .handler(a.handler.function(readInvite))
      .returns(a.ref('UserInvite')),
    acceptInvite: a
      .mutation()
      .arguments({
        inviteToken: a.string().required(),
        email: a.string().required(),
      })
      .authorization(allow => [allow.guest()])
      .handler(a.handler.function(acceptInvite))
      .returns(a.json()),
    deleteUserComplete: a
      .mutation()
      .arguments({
        userId: a.string().required(),
      })
      .authorization(allow => [allow.group('ADMINS')])
      .handler(a.handler.function(deleteUserComplete))
      .returns(a.json()),

    // Attorney management models
    Attorney,

    // Evidence submission models
    EvidenceSubmission,

    // Educational content models
    EducationalContent,

    // Notification models
    Notification,

    Interview,
    InterviewVersion,
    Question,
    ConditionalLogic,

    // Subscription models
    UserSubscription,

    CareDocumentTemplate,
    CareDocumentQuestion,
    CareDocumentQuestionOption,

    addUserToGroup: a
      .mutation()
      .arguments({
        userId: a.string().required(),
        groupName: a.string().required(),
      })
      .authorization(allow => [allow.group('ADMINS')])
      .handler(a.handler.function(addUserToGroup))
      .returns(a.json()),

    addUserToGroupV2: a
      .mutation()
      .arguments({
        userId: a.string().required(),
        groupName: a.string().required(),
      })
      .authorization(allow => [allow.group('ADMINS')])
      .handler(a.handler.function(addUserToGroup))
      .returns(a.json()),

    checkEmail: a
      .query()
      .arguments({
        email: a.string().required(),
      })
      .authorization(allow => [allow.guest()])
      .handler(a.handler.function(checkEmail))
      .returns(a.json()),

    trackLoginAttempt: a
      .mutation()
      .arguments({
        email: a.string().required(),
        success: a.boolean().required(),
        action: a.string().required(),
        ipAddress: a.string(),
        userAgent: a.string(),
      })
      .authorization(allow => [allow.guest(), allow.authenticated()])
      .handler(a.handler.function(trackLoginAttempt))
      .returns(a.json()),

    checkAccountLockout: a
      .query()
      .arguments({
        email: a.string().required(),
        action: a.string().required(),
      })
      .authorization(allow => [allow.guest()])
      .handler(a.handler.function(trackLoginAttempt))
      .returns(a.json()),

    generatePdfDoc: a
      .mutation()
      .arguments({
        documentId: a.string().required(),
        html: a.string().required(),
        isSignedVersion: a.boolean(),
        signatureData: a.string(),
        userId: a.string(), // Optional user ID for S3 path
        signatureType: a.string(), // Add signature type parameter
        memberId: a.string(), // Add member ID for metadata
        documentVersion: a.string(), // Add document version for metadata
      })
      .authorization(allow => [allow.authenticated()])
      .handler(a.handler.function(generatePdfDoc))
      .returns(a.json()),

    sendEmail: a
      .mutation()
      .arguments({
        to: a.string().required(),
        subject: a.string().required(),
        message: a.string().required(),
        verificationLink: a.string(),
        emailType: a.string(),
        isNewAccount: a.boolean(),
        baseUrl: a.string(),
      })
      .authorization(allow => [allow.guest(), allow.authenticated()])
      .handler(a.handler.function(sendEmail))
      .returns(a.json()),

    generateVerificationToken: a
      .mutation()
      .arguments({
        email: a.string().required(),
        verificationType: a.string().required(), // 'accountConfirmation' or 'passwordReset'
        baseUrl: a.string(),
      })
      .authorization(allow => [allow.guest(), allow.authenticated()])
      .handler(a.handler.function(generateVerificationToken))
      .returns(a.json()),

    verifyEmailToken: a
      .mutation()
      .arguments({
        email: a.string().required(),
        token: a.string().required(),
        verificationType: a.string().required(), // 'accountConfirmation' or 'passwordReset'
        newPassword: a.string(), // Required for passwordReset
      })
      .authorization(allow => [allow.guest(), allow.authenticated()])
      .handler(a.handler.function(verifyEmailToken))
      .returns(a.json()),

    initiatePhoneVerification: a
      .mutation()
      .arguments({
        phoneNumber: a.string().required(),
        email: a.string().required(),
      })
      .authorization(allow => [allow.authenticated()])
      .handler(a.handler.function(initiatePhoneVerification))
      .returns(a.json()),

    verifyPhoneCode: a
      .mutation()
      .arguments({
        phoneNumber: a.string().required(),
        verificationCode: a.string().required(),
        email: a.string().required(),
        userId: a.string().required(),
      })
      .authorization(allow => [allow.authenticated()])
      .handler(a.handler.function(verifyPhoneCode))
      .returns(a.json()),

    verifyAndFetchDocuments: a
      .mutation()
      .arguments({
        email: a.string().required(),
        token: a.string().required(),
      })
      .authorization(allow => [allow.guest(), allow.authenticated()])
      .handler(a.handler.function(verifyAndFetchDocuments))
      .returns(a.json()),

    getEmergencyFileUrl: a
      .mutation()
      .arguments({
        email: a.string().required(),
        token: a.string().required(),
        fileKey: a.string().required(),
      })
      .authorization(allow => [allow.guest(), allow.authenticated()])
      .handler(a.handler.function(getEmergencyFileUrl))
      .returns(a.json()),

    activateDeactivateUser: a
      .mutation()
      .arguments({
        userId: a.string().required(),
        activate: a.boolean().required(),
      })
      .authorization(allow => [allow.group('ADMINS')])
      .handler(a.handler.function(activateDeactivateUser))
      .returns(a.json()),

    exportDocumentUpdateLogs: a
      .query()
      .arguments({
        startDate: a.datetime(),
        endDate: a.datetime(),
        userId: a.string(),
        changeType: a.string(),
      })
      .authorization(allow => [allow.group('ADMINS')])
      .handler(a.handler.function(exportDocumentUpdateLogs))
      .returns(a.json()),

    signOutDevice: a
      .mutation()
      .arguments({
        deviceId: a.string().required(),
        username: a.string().required(),
      })
      .authorization(allow => [allow.authenticated()])
      .handler(a.handler.function(signOutDevice))
      .returns(a.json()),

    getSecrets: a
      .query()
      .arguments({
        secretName: a.string().required(),
      })
      .authorization(allow => [allow.publicApiKey()])
      .handler(a.handler.function(getSecrets))
      .returns(a.json()),
    DeadMansSwitch,
    CheckInFrequency,
    CommunicationMethod,
    EscalationProtocol,
    DMSStatus,
    CheckInHistory,
    EscalationEvent,
    DMSTestEvent,
  })
  .authorization(allow => [
    allow.resource(dmsCheck).to(['query', 'mutate']),
    allow.resource(inviteUser).to(['query', 'mutate']),
    allow.resource(readInvite).to(['query']),
    allow.resource(acceptInvite).to(['query', 'mutate']),
    allow.resource(postSignUp).to(['query', 'mutate']),
    allow.resource(exportDocumentUpdateLogs).to(['query']),
    allow.resource(addUserToGroup).to(['query', 'mutate']),
    allow.resource(checkEmail).to(['query']),
    allow.resource(trackLoginAttempt).to(['query', 'mutate']),
    allow.resource(generatePdfDoc).to(['query', 'mutate']),
    allow.resource(sendEmail).to(['mutate']),
    allow.resource(generateVerificationToken).to(['query', 'mutate']),
    allow.resource(verifyEmailToken).to(['query', 'mutate']),
    allow.resource(initiatePhoneVerification).to(['mutate']),
    allow.resource(verifyPhoneCode).to(['query', 'mutate']),
    allow.resource(activateDeactivateUser).to(['query', 'mutate']),
    allow.resource(getSecrets).to(['query']),
    allow.group('ADMINS').to(['list', 'get', 'create', 'update', 'delete']),
    allow.guest().to(['list', 'get', 'create', 'update', 'delete']),
    allow.resource(schedulerFramework).to(['query', 'mutate']),
    allow.resource(verifyAndFetchDocuments).to(['query', 'mutate']),
    allow.resource(getEmergencyFileUrl).to(['query', 'mutate']),
    allow.resource(deleteUserComplete).to(['query', 'mutate']),
  ]);

export type Schema = ClientSchema<typeof schema>;

export const data = defineData({
  schema,
  authorizationModes: {
    defaultAuthorizationMode: 'userPool',
    lambdaAuthorizationMode: {
      function: defineFunction({
        entry: './custom-authorizer.ts',
      }),
    },
    apiKeyAuthorizationMode: {
      expiresInDays: 30,
    },
  },
});
