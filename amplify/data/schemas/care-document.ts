import { a } from '@aws-amplify/backend';

const CareDocumentsStatus = a.enum(['Draft', 'Active', 'Archived', 'Deleted']);

// Question types for care document templates
const CareDocumentQuestionType = a.enum([
  'text',
  'select',
  'radio',
  'file',
  'date',
  'phone',
]);

// Review frequency types
const ReviewFrequencyType = a.enum([
  'monthly',
  'quarterly',
  'semiAnnually',
  'annually',
]);

// Custom type for question options
export const CareDocumentQuestionOption = a.customType({
  id: a.string().required(),
  label: a.string().required(),
  value: a.string().required(),
});

// Custom type for care document questions
export const CareDocumentQuestion = a.customType({
  id: a.string().required(),
  text: a.string().required(),
  type: CareDocumentQuestionType,
  required: a.boolean(),
  placeholder: a.string(),
  helpText: a.string(),
  options: a.ref('CareDocumentQuestionOption').array(),
  dateConfig: a.datetime(),
  allowMultiple: a.boolean(),
  order: a.integer().required(),
});

export const CareDocumentTemplate = a
  .model({
    createdByEmail: a.string().required(),
    documentType: a.string().required(),
    title: a.string().required(),
    description: a.string(),
    content: a.string(),
    questions: a.ref('CareDocumentQuestion').array(), // Structured questions
    version: a.integer().default(1),
    status: CareDocumentsStatus,
    icon: a.string(),
    allowMultiple: a.boolean(),
    reviewFrequency: ReviewFrequencyType,
    createdAt: a.datetime(),
    updatedAt: a.datetime(),
  })
  .authorization(allow => [
    allow.group('ADMINS'),
    allow.authenticated().to(['read']),
  ]);
