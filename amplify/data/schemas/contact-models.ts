import { a } from '@aws-amplify/backend';

/**
 * Emergency contact related models
 */

const DocumentType = a.enum(['DeadDocument', 'CareDocument', 'All']);

export const EmergencyContact = a
  .model({
    id: a.id(),
    userId: a.string().required(),
    fullName: a.string().required(),
    relationship: a.string().required(),
    phoneNumber: a.string().required(),
    emailAddress: a.string().required(),
    contactType: a.enum(['NotificationOnly', 'NotificationWithDocumentAccess']),
    documentType: DocumentType,
    isPrimaryForType: a.boolean().default(false),
    isVerified: a.boolean().default(false),
    verificationToken: a.string(),
    tokenExpiry: a.datetime(),
  })
  .identifier(['id'])
  .authorization(allow => [
    allow.owner(),
    allow.guest(),
    allow.group('ADMINS'),
  ]);
