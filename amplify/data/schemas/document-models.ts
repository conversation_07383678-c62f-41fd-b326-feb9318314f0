import { a } from '@aws-amplify/backend';

const DocumentStatus = a.enum([
  'draft',
  'sent',
  'approved',
  'rejected',
  'signed',
  'shipped',
  'received',
  'archived',
]);

export const CareDocument = a
  .model({
    id: a.id(),
    userId: a.string().required(),
    documentType: a.enum([
      'EmergencyContacts',
      'PetCare',
      'DigitalAssets',
      'EndOfLifeWishes',
      'MedicalDirectives',
      'Other',
    ]),
    title: a.string().required(),
    content: a.string().required(), // JSON string containing document data
    version: a.integer().default(1),
    status: a.enum(['Draft', 'Active', 'Archived']),
    lastReviewDate: a.datetime(),
    nextReviewDate: a.datetime(),
    reminderFrequency: a.enum([
      'Monthly',
      'Quarterly',
      'SemiAnnually',
      'Annually',
    ]),
    isTemplate: a.boolean().default(false),
    templateId: a.string(),
    createdAt: a.datetime(),
    updatedAt: a.datetime(),
  })
  .identifier(['id'])
  .authorization(allow => [allow.owner()]);

export const Document = a
  .model({
    id: a.id(),
    title: a.string().required(),
    type: a.enum([
      'Will',
      'Trust',
      'Financial_POA',
      'Healthcare_POA',
      'Advance_Directive',
      'Other',
    ]),
    status: DocumentStatus,
    dateCreated: a.datetime().required(),
    lastModified: a.datetime(),
    version: a.string().required(),
    content: a.string().required(),
    userId: a.string().required(),
    fileUrl: a.string(),
    trackingNumber: a.string(),
    signatureType: a.enum(['manual', 'electronic', 'notarized']),
    executionDate: a.datetime(),
    createdAt: a.datetime(),
    updatedAt: a.datetime(),
    // Template information
    templateId: a.string(),
    templateVersion: a.integer(), // Version of template used when document was created
    documentState: a.string(),
    // Signed document storage
    signedDocumentUrl: a.string(),
    signedAt: a.datetime(),
    documentHash: a.string(),
    // Relationships
    user: a.belongsTo('User', 'userId'),
    assignedWelonTrustId: a.string(),
    rejectionReason: a.string(),
  })
  .identifier(['id'])
  .authorization(allow => [
    allow.owner(),
    allow.ownerDefinedIn('assignedWelonTrustId').to(['read', 'update']),
    allow.group('ADMINS'),
  ]);

export const DocumentUpdateLog = a
  .model({
    id: a.id(),
    documentId: a.string().required(),
    userId: a.string().required(),
    changeType: a.enum([
      'Created',
      'Updated',
      'Reviewed',
      'Archived',
      'Deleted',
    ]),
    changeDescription: a.string(),
    previousVersion: a.integer(),
    newVersion: a.integer(),
    timestamp: a.datetime().required(),
  })
  .identifier(['id'])
  .authorization(allow => [allow.owner()]);
