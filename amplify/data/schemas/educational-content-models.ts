import { a } from '@aws-amplify/backend';

export const EducationalContent = a
  .model({
    title: a.string().required(),
    type: a.enum(['video', 'article', 'infographic', 'avatar', 'tooltip']),
    status: a.enum(['draft', 'published', 'archived']),
    description: a.string(),
    tags: a.string().array(),
    contentUrl: a.string(), // URL for video, article content, infographic image, etc.
    thumbnailUrl: a.string(),
    duration: a.integer(), // For videos - duration in seconds
    readingTime: a.integer(), // For articles - reading time in minutes
    metadata: a.json(), // Additional metadata specific to content type
    createdAt: a.datetime(),
    updatedAt: a.datetime(),
    version: a.integer().default(1),
  })
  .authorization(allow => [
    allow.authenticated().to(['read']),
    allow.group('ADMINS').to(['create', 'read', 'update', 'delete']),
    allow.group('WELONTRUST').to(['create', 'read', 'update']),

  ]);
