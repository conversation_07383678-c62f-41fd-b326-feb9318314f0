import { a } from '@aws-amplify/backend';

export const EvidenceSubmission = a
  .model({
    id: a.id(),
    submitterName: a.string().required(),
    submitterEmail: a.string().required(),
    memberName: a.string().required(),
    memberEmail: a.string().required(),
    relationship: a.string().required(),
    evidenceType: a.enum(['Death', 'Incapacitation']),
    status: a.enum(['pending', 'under_review', 'approved', 'rejected']),
    additionalInfo: a.string(),
    // File fields
    fileKey: a.string(),
    fileName: a.string(),
    fileType: a.string(),
    fileSize: a.integer(),
    // Upload tracking
    uploadedBy: a.string(), // Cognito ID of the user who uploaded the file
    // Review information
    reviewedBy: a.string(),
    reviewedAt: a.datetime(),
    reviewNotes: a.string(),
    // Timestamps
    createdAt: a.datetime(),
    updatedAt: a.datetime(),
  })
  .authorization(allow => [
    allow.ownerDefinedIn('uploadedBy'), // Only the uploader can access
    allow.group('ADMINS'), // <PERSON><PERSON> can access all submissions
  ]);
