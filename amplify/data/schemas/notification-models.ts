import { a } from '@aws-amplify/backend';

export const Notification = a
  .model({
    id: a.id(),
    message: a.string().required(),
    recipient: a.string().required(),
    author: a.string().required(),
    createdAt: a.datetime().required(),
    isRead: a.boolean().default(false),
  })
  .authorization(allow => [
    allow.ownerDefinedIn('recipient').to(['read', 'update']),
    allow.authenticated().to(['create']),
    allow.groups(['ADMINS', 'WELONTRUST']).to(['create', 'read', 'update', 'delete'])
  ]);
