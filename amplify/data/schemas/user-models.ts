import { a } from '@aws-amplify/backend';

/**
 * User management related models
 */

export const UserAnswer = a.customType({
  questionId: a.string().required(),
  answer: a.string(),
  answeredAt: a.datetime().required(),
});

export const UserInterviewProgressNew = a.customType({
  interviewVersionId: a.id().required(),
  isCompleted: a.boolean().required(),
  startedAt: a.datetime().required(),
  completedAt: a.datetime(),
  currentQuestionId: a.string(),
  answers: a.ref('UserAnswer').array(),
});

// User status enum
export const UserStatus = a.enum([
  'active',
  'inactive',
  'pending',
  'deceased',
  'invited',
]);

// Master role enum
export const MasterRole = a.enum([
  'Member',
  'Administrator',
  'WelonTrust',
  'Professional',
]);

// Invite status enum
export const InviteStatus = a.enum([
  'pending',
  'accepted',
  'expired',
  'cancelled',
]);

// User Invite model
export const UserInvite = a
  .model({
    email: a.string().required(),
    firstName: a.string().required(),
    lastName: a.string().required(),
    role: MasterRole,
    subrole: a.string(),
    invitedBy: a.string().required(), // Admin user ID who sent the invite
    invitedByEmail: a.string().required(),
    token: a.string().required(),
    status: InviteStatus,
    expiresAt: a.datetime().required(),
    acceptedAt: a.datetime(),
    createdAt: a.datetime().required(),
    sharedFields: a.string(),
    userId: a.id(), // id of the user that sends invite
  })
  .authorization(allow => [
    allow.group('ADMINS'),
    allow.authenticated(),
    allow.guest().to(['read']),
  ]);

// Link type enum
export const LinkType = a.enum([
  'primary',
  'secondary',
  'delegate',
  'emergency',
]);

// Link status enum
export const LinkStatus = a.enum(['pending', 'active', 'revoked']);

// WelonTrustAssignment status enum
export const WelonTrustAssignmentStatus = a.enum([
  'active',
  'pending',
  'revoked',
]);

export const InterviewProgressV2 = a.customType({
  status: a.string(),
  currentStep: a.string(),
  stepsData: a.json(),
  createdAt: a.datetime(),
  updatedAt: a.datetime(),
});

// User's answer to a care document question
export const UserCareDocumentAnswer = a.customType({
  questionId: a.string().required(),
  questionText: a.string().required(),
  answer: a.string(),
  fileUrl: a.string(), // For file type questions (single file - legacy support)
  fileKeys: a.string().array(), // For multiple file uploads - stores S3 keys
  fileNames: a.string().array(), // Original file names
  fileTypes: a.string().array(), // MIME types of files
  fileSizes: a.integer().array(), // File sizes in bytes
  answeredAt: a.datetime().required(),
});

// User Address model for shipping
export const UserAddress = a
  .model({
    userId: a.id().required(),
    addressLine1: a.string().required(),
    addressLine2: a.string(),
    city: a.string().required(),
    stateProvinceCode: a.string().required(),
    postalCode: a.string().required(),
    countryCode: a.string().required().default('US'),
    isDefault: a.boolean().default(false),
    createdAt: a.datetime().required(),
    updatedAt: a.datetime().required(),
    // Relationship
    user: a.belongsTo('User', 'userId'),
  })
  .authorization(allow => [allow.authenticated(), allow.group('ADMINS')]);

// Shipping Label model for tracking UPS labels
export const ShippingLabel = a
  .model({
    userId: a.id().required(),
    assignedWelonTrustId: a.id(), // ID of the WelonTrust user receiving the package
    documentIds: a.string().array(), // Array of document IDs included in this shipment
    trackingNumber: a.string().required(),
    labelUrl: a.string().required(),
    fromAddress: a.string().required(), // JSON string of address
    toAddress: a.string().required(), // JSON string of address
    serviceCode: a.string().required(),
    cost: a.string().required(), // JSON string with amount and currency
    estimatedDelivery: a.string(),
    status: a.string().default('created'),
    createdAt: a.datetime().required(),
    // Additional fields for multiple documents
    packageDescription: a.string(), // Description of all documents in the package
    totalDocuments: a.integer(), // Number of documents in the package
    // Relationships
    user: a.belongsTo('User', 'userId'),
    assignedWelonTrust: a.belongsTo('User', 'assignedWelonTrustId'),
  })
  .authorization(allow => [
    allow.authenticated(),
    allow.ownerDefinedIn('assignedWelonTrustId'), // Allow WelonTrust to see packages sent to them
    allow.group('ADMINS'),
  ]);

// Main User model
export const User = a
  .model({
    email: a.string().required(),
    firstName: a.string().required(),
    lastName: a.string().required(),
    phoneNumber: a.string(),
    birthdate: a.string(),
    state: a.string(),
    cognitoId: a.string(), // Optional - will be null for invited users until they register
    role: MasterRole,
    subrole: a.string(),
    status: UserStatus,
    journeyStatus: a.string(),
    createdAt: a.datetime().required(),
    // Dead Man's Switch fields. TRUE - if all ok, FALSE - if user didn't check in
    isDmsCheckSuccessful: a.boolean().default(true),
    // Referral source field - optional
    howDidYouHearAboutUs: a.string(),
    // Relationships
    linkedAccounts: a.hasMany('LinkedAccount', 'userId'),
    linkedAccountsWithSharedFields: a.hasMany(
      'LinkedAccountWithSharedFields',
      'userId'
    ),
    assignedWelonTrust: a.hasOne('WelonTrustAssignment', 'userId'),
    assignedWelonTrustId: a.string(),
    documents: a.hasMany('Document', 'userId'),
    subscriptions: a.hasMany('UserSubscription', 'userId'),
    interviewProgress: a.ref('UserInterviewProgressNew').array(),
    careDocuments: a.hasMany('UserCareDocument', 'userId'), // New relationship to user care documents
    addresses: a.hasMany('UserAddress', 'userId'),
    shippingLabels: a.hasMany('ShippingLabel', 'userId'),
    assignedShippingLabels: a.hasMany('ShippingLabel', 'assignedWelonTrustId'),
    memberNotes: a.hasMany('MemberNote', 'userId'), // Notes about this member
    interviewProgressV2: a.ref('InterviewProgressV2'),
  })
  .authorization(allow => [
    allow.ownerDefinedIn('cognitoId'),
    allow.ownerDefinedIn('assignedWelonTrustId'),
    allow.authenticated().to(['read']),
    allow.group('ADMINS'),
  ]);

// User Care Document model
export const UserCareDocument = a
  .model({
    userId: a.id().required(), // The ID of the user who owns this document
    templateId: a.string().required(), // Reference to the template this document was created from
    title: a.string().required(),
    documentType: a.string().required(),
    version: a.integer().required(),
    answers: a.ref('UserCareDocumentAnswer').array(), // User's answers to the template questions
    createdAt: a.datetime().required(),
    updatedAt: a.datetime().required(),
    // Relationship
    user: a.belongsTo('User', 'userId'),
    // Add relationship to change logs
    changeLogs: a.hasMany('UserCareDocumentChangeLog', 'documentId'),
    expirationDate: a.datetime(),
  })
  .authorization(allow => [
    allow.ownerDefinedIn('userId'),
    allow.group('ADMINS'),
  ]);

// Add a new model for tracking changes specific to UserCareDocument
export const UserCareDocumentChangeLog = a
  .model({
    id: a.id(),
    documentId: a.id().required(), // Reference to the UserCareDocument
    userId: a.string().required(), // User who made the change
    changeType: a.enum(['Created', 'Updated', 'Reviewed', 'Archived']),
    changeDescription: a.string(), // Description of what changed
    previousVersion: a.integer(),
    newVersion: a.integer(),
    changedFields: a.string(), // JSON string containing changed fields
    previousValues: a.string(), // JSON string of previous values
    newValues: a.string(), // JSON string of new values
    timestamp: a.datetime().required(),
    // Relationship
    document: a.belongsTo('UserCareDocument', 'documentId'),
  })
  .authorization(allow => [
    allow.ownerDefinedIn('userId'),
    allow.group('ADMINS'),
  ]);

// Welon Trust assignment model
export const WelonTrustAssignment = a
  .model({
    userId: a.id().required(),
    welonTrustUserId: a.string().required(),
    welonTrustName: a.string().required(),
    welonTrustEmail: a.string().required(),
    welonTrustCognitoId: a.string().required().default(''),
    assignedAt: a.datetime().required(),
    assignedBy: a.string().required(), // Admin user ID who made the assignment
    status: WelonTrustAssignmentStatus,
    // Relationship
    user: a.belongsTo('User', 'userId'),
  })
  .authorization(allow => [
    allow.ownerDefinedIn('welonTrustCognitoId'),
    allow.group('ADMINS'),
  ]);

// Member Note model for internal notes by Welon Trust users
export const MemberNote = a
  .model({
    userId: a.id().required(), // ID of the member this note is about
    authorId: a.string().required(), // Cognito ID of the Welon Trust user who created the note
    authorName: a.string().required(), // Name of the author for display purposes
    content: a.string().required(), // The note content
    createdAt: a.datetime().required(),
    updatedAt: a.datetime().required(),
    // Relationship
    user: a.belongsTo('User', 'userId'),
  })
  .authorization(allow => [
    // Only Welon Trust users can create, read, update, and delete notes
    allow.groups(['WELONTRUST']).to(['create', 'read', 'update', 'delete']),
    // Admins can also manage notes
    allow.group('ADMINS').to(['create', 'read', 'update', 'delete']),
  ]);

// Linked account model
export const LinkedAccount = a
  .model({
    userId: a.id().required(), // The ID of the user who owns this link
    linkedUserId: a.string().required(), // The ID of the user who is linked
    linkType: LinkType, // The type of link
    status: LinkStatus, // The status of the link
    permissions: a.string().array().required(), // Permissions granted to the linked user
    createdAt: a.datetime().required(), // When the link was created
    updatedAt: a.datetime().required(), // When the link was last updated
    expiresAt: a.datetime(), // Optional expiration date
    // Relationship
    user: a.belongsTo('User', 'userId'),
  })
  .authorization(allow => [allow.owner()]);

export const LinkedAccountWithSharedFields = a
  .model({
    userId: a.id().required(), // The ID of the user who owns this link
    linkedUserId: a.string().required(), // The ID of the user who is linked
    sharedFields: a.string().required(),
    isAccepted: a.boolean().required().default(false),
    createdAt: a.datetime().required(), // When the link was created
    updatedAt: a.datetime().required(), // When the link was last updated
    // Relationship
    user: a.belongsTo('User', 'userId'),
  })
  .authorization(allow => [
    allow.owner(),
    allow.ownerDefinedIn('linkedUserId'),
    allow.authenticated(),
  ]);
