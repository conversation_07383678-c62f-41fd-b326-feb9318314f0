import { a } from '@aws-amplify/backend';

/**
 * Vault access related models
 */

export const VaultAccess = a
  .model({
    id: a.id(),
    documentId: a.string().required(),
    userId: a.string().required(),
    accessorId: a.string().required(),
    accessType: a.enum(['Emergency', 'Authorized', 'Temporary']),
    grantedAt: a.datetime().required(),
    revokedAt: a.datetime(),
    reason: a.string(),
    isActive: a.boolean().default(true),
  })
  .identifier(['id'])
  .authorization(allow => [allow.owner()]);
