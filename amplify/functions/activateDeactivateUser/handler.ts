// @ts-nocheck

import type { Schema } from '../../data/resource';
import { Amplify } from 'aws-amplify';
import { getAmplifyDataClientConfig } from '@aws-amplify/backend/function/runtime';
import { generateClient } from 'aws-amplify/data';
import { env } from '$amplify/env/activateDeactivateUser';
import {
  CognitoIdentityProviderClient,
  AdminEnableUserCommand,
  AdminDisableUserCommand,
} from '@aws-sdk/client-cognito-identity-provider';

const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);

Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>();
const cognitoClient = new CognitoIdentityProviderClient({
  region: process.env.AWS_REGION || 'us-east-1',
});

export const handler: Schema['activateDeactivateUser']['functionHandler'] =
  async event => {
    const { userId, activate } = event.arguments;

    console.log('===> START ACTIVATE DEACTIVATE USER LOGIC:', {
      userId,
      activate,
    });

    try {
      // Find the user in the database
      const { data: users } = await client.models.User.list({
        filter: { id: { eq: userId } },
      });

      if (!users || users.length === 0) {
        throw new Error(`User not found with id: ${userId}`);
      }

      const user = users[0];
      const userEmail = user.email;

      console.log('===> Found user:', {
        id: user.id,
        email: userEmail,
        cognitoId: user.cognitoId,
        currentStatus: user.status,
      });

      // Update user status in Cognito
      if (activate) {
        console.log('===> Enabling user in Cognito...');
        const enableCommand = new AdminEnableUserCommand({
          UserPoolId: env.USER_POOL_ID,
          Username: userEmail,
        });
        await cognitoClient.send(enableCommand);
        console.log('===> User enabled in Cognito successfully');
      } else {
        console.log('===> Disabling user in Cognito...');
        const disableCommand = new AdminDisableUserCommand({
          UserPoolId: env.USER_POOL_ID,
          Username: userEmail,
        });
        await cognitoClient.send(disableCommand);
        console.log('===> User disabled in Cognito successfully');
      }

      // Update user status in database
      const newStatus = activate ? 'active' : 'inactive';
      const { data: updatedUser, errors } = await client.models.User.update({
        id: user.id,
        status: newStatus,
      });

      if (errors) {
        console.error('===> Database update errors:', errors);
        throw new Error(
          `Failed to update user status in database: ${errors.map(e => e.message).join(', ')}`
        );
      }

      console.log('===> User status updated successfully:', {
        userId: user.id,
        newStatus,
        cognitoAction: activate ? 'enabled' : 'disabled',
      });

      return {
        success: true,
        message: `User successfully ${activate ? 'activated' : 'deactivated'}`,
        data: {
          userId: user.id,
          email: userEmail,
          status: newStatus,
          cognitoAction: activate ? 'enabled' : 'disabled',
        },
      };
    } catch (error) {
      console.error('===> Error activating/deactivating user:', error);

      return {
        success: false,
        message:
          error instanceof Error ? error.message : 'Unknown error occurred',
        error: error instanceof Error ? error.message : String(error),
      };
    }
  };
