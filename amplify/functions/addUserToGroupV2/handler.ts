import type { Schema } from '../../data/resource';
import { Amplify } from 'aws-amplify';
import { getAmplifyDataClientConfig } from '@aws-amplify/backend/function/runtime';
import { generateClient } from 'aws-amplify/data';
// @ts-ignore
import { env } from '$amplify/env/addUserToGroupV2';

import {
  CognitoIdentityProviderClient,
  AdminAddUserToGroupCommand,
  AdminRemoveUserFromGroupCommand,
  AdminListGroupsForUserCommand,
  ListUsersCommand,
} from '@aws-sdk/client-cognito-identity-provider';

const { resourceConfig, libraryOptions } =
  // @ts-ignore
  await getAmplifyDataClientConfig(env);

Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>();
const cognitoClient = new CognitoIdentityProviderClient({
  region: process.env.AWS_REGION || 'us-east-1',
});

const GROUP_MAPPING = {
  member: {
    cognitoGroup: null,
    databaseRole: 'Member',
  },
  welontrust: {
    cognitoGroup: 'WELONTRUST',
    databaseRole: 'WelonTrust',
  },
  administrator: {
    cognitoGroup: 'ADMINS',
    databaseRole: 'Administrator',
  },
} as const;

type GroupName = keyof typeof GROUP_MAPPING;

async function findUserByEmail(email: string): Promise<string> {
  const command = new ListUsersCommand({
    UserPoolId: env.USER_POOL_ID,
    Filter: `email = "${email}"`,
    Limit: 1,
  });

  const response = await cognitoClient.send(command);

  if (!response.Users || response.Users.length === 0) {
    throw new Error(`User with email ${email} not found in Cognito`);
  }

  const cognitoUser = response.Users[0];
  if (!cognitoUser.Username) {
    throw new Error(`User with email ${email} has no Username in Cognito`);
  }

  return cognitoUser.Username; // Return the Cognito Username (user ID)
}

export const handler: Schema['addUserToGroupV2']['functionHandler'] =
  async event => {
    try {
      const { userId, groupName } = event.arguments;

      console.log('===> Processing user group update:', { userId, groupName });

      // Validate group name
      if (!Object.keys(GROUP_MAPPING).includes(groupName.toLowerCase())) {
        return JSON.stringify({
          success: false,
        });
      }

      const normalizedGroupName = groupName.toLowerCase() as GroupName;
      const mapping = GROUP_MAPPING[normalizedGroupName];

      const { data: users } = await client.models.User.list({
        filter: { id: { eq: userId } },
      });

      if (!users || users.length === 0) {
        return JSON.stringify({
          success: false,
        });
      }

      const user = users[0];
      const userEmail = user.email;

      console.log('===> Found user:', {
        id: user.id,
        email: userEmail,
        currentRole: user.role,
      });

      // Get Cognito user ID from email
      let cognitoUserId: string;
      try {
        cognitoUserId = await findUserByEmail(user.email);
        console.log('===> Found Cognito user ID:', cognitoUserId);
      } catch (error) {
        console.log(
          'User not found in Cognito (invited user), skipping group operations'
        );
        // Update database role and return for invited users
        const { errors } = await client.models.User.update({
          id: user.id,
          role: mapping.databaseRole,
        });

        if (errors) {
          console.error('===> Database update errors:', errors);
        }

        return {
          statusCode: 200,
          body: JSON.stringify({
            message:
              'User role updated in database. Group operations skipped for invited user.',
            userId: user.id,
            newRole: mapping.databaseRole,
          }),
        };
      }

      const listGroupsCommand = new AdminListGroupsForUserCommand({
        UserPoolId: env.USER_POOL_ID,
        Username: userEmail,
      });

      let currentGroups: string[] = [];
      try {
        const groupsResponse = await cognitoClient.send(listGroupsCommand);
        currentGroups =
          groupsResponse.Groups?.map(group => group.GroupName || '') || [];
        console.log('===> Current Cognito groups:', currentGroups);
      } catch (error) {
        console.error('===> Error fetching current groups:', error);
        // Continue with empty groups array if we can't fetch current groups
      }

      const allCognitoGroups = ['ADMINS', 'WELONTRUST'];

      for (const group of currentGroups) {
        if (allCognitoGroups.includes(group)) {
          try {
            const removeCommand = new AdminRemoveUserFromGroupCommand({
              UserPoolId: env.USER_POOL_ID,
              Username: userEmail,
              GroupName: group,
            });
            await cognitoClient.send(removeCommand);
            console.log(`===> Removed user from Cognito group: ${group}`);
          } catch (error) {
            console.error(
              `===> Error removing user from group ${group}:`,
              error
            );
          }
        }
      }

      if (mapping.cognitoGroup) {
        try {
          const addCommand = new AdminAddUserToGroupCommand({
            UserPoolId: env.USER_POOL_ID,
            Username: userEmail,
            GroupName: mapping.cognitoGroup,
          });
          await cognitoClient.send(addCommand);
          console.log(
            `===> Added user to Cognito group: ${mapping.cognitoGroup}`
          );
        } catch (error) {
          console.error(
            `===> Error adding user to group ${mapping.cognitoGroup}:`,
            error
          );
          return JSON.stringify({
            success: false,
          });
        }
      }

      const { errors } = await client.models.User.update({
        id: user.id,
        role: mapping.databaseRole,
      });

      if (errors) {
        console.error('===> Database update errors:', errors);
      }
      return JSON.stringify({
        success: true,
        message: `User added to group`,
      });
    } catch (error) {
      console.error('===> ERROR', error);
      return JSON.stringify({
        success: false,
      });
    }
  };
