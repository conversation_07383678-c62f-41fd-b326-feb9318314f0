import type { <PERSON><PERSON> } from 'aws-lambda';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '../../data/resource';
import { Amplify } from 'aws-amplify';
import { getAmplifyDataClientConfig } from '@aws-amplify/backend/function/runtime';
// @ts-ignore
import { env } from '$amplify/env/generatePdfDoc';

// Initialize S3 client
const s3Client = new S3Client({
  region: process.env.AWS_REGION || 'us-east-1',
});

// Initialize Amplify client for database operations
const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);
Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>();

interface PDFGenerationRequest {
  documentId: string;
  html: string;
  isSignedVersion?: boolean;
  signatureData?: string;
  userId?: string;
  signatureType?: 'electronic' | 'manual' | 'e-notarization';
  memberId?: string;
  documentVersion?: string;
}

interface PDFGenerationResponse {
  success: boolean;
  fileUrl?: string;
  error?: string;
}

// Helper function to clean HTML for PDF generation
function cleanHTMLForPDF(htmlContent: string): string {
  if (!htmlContent) return '';

  let cleaned = htmlContent;

  // Remove problematic CSS functions
  cleaned = cleaned.replace(/oklch\s*\([^)]+\)/gi, '#333333');
  cleaned = cleaned.replace(/hsl\s*\([^)]+\)/gi, '#333333');
  cleaned = cleaned.replace(/var\s*\([^)]+\)/gi, '#333333');
  cleaned = cleaned.replace(/calc\s*\([^)]+\)/gi, '1rem');
  cleaned = cleaned.replace(/clamp\s*\([^)]+\)/gi, '1rem');

  // Remove CSS variables
  cleaned = cleaned.replace(/--[a-zA-Z0-9-_]+\s*:\s*[^;]+;/gi, '');

  // Clean up malformed CSS
  cleaned = cleaned.replace(/:\s*;/g, '');
  cleaned = cleaned.replace(/;\s*;/g, ';');
  cleaned = cleaned.replace(/color:\s*;/gi, '');
  cleaned = cleaned.replace(/background:\s*;/gi, '');

  // Remove empty attributes
  cleaned = cleaned.replace(/\s+style=""\s*/gi, ' ');
  cleaned = cleaned.replace(/\s+class=""\s*/gi, ' ');
  cleaned = cleaned.replace(/\s+/g, ' ');
  cleaned = cleaned.replace(/>\s+</g, '><');

  return cleaned;
}

// Helper function to build signed document HTML
function buildSignedDocumentHTML(
  baseHtml: string,
  signatureData?: string
): string {
  if (!signatureData) {
    return baseHtml;
  }

  const signatureSection = `
    <div style="margin-top: 3rem; padding-top: 2rem; border-top: 2px solid #cccccc;">
      <h2 style="color: #1f2937; margin-bottom: 1rem;">Electronic Signature</h2>
      <div style="background-color: #f9f9f9; padding: 1.5rem; border: 1px solid #cccccc;">
        <p><strong>Signed on:</strong> ${new Date().toLocaleString()}</p>
        <p><strong>Signature Type:</strong> Electronic</p>
        <p><strong>Status:</strong> Legally Binding</p>
        <div style="margin-top: 1rem; padding: 1rem; background-color: #ffffff; border: 1px solid #cccccc;">
          <p style="margin: 0; font-style: italic; color: #666666;">
            This document has been electronically signed and is legally binding under applicable electronic signature laws.
          </p>
        </div>
      </div>
    </div>
  `;

  return baseHtml.replace('</body>', `${signatureSection}</body>`);
}

export const handler: Handler = async (
  event,
  _context
): Promise<PDFGenerationResponse> => {
  try {
    console.log(
      'PDF Handler - Event received:',
      JSON.stringify(event, null, 2)
    );

    // Handle both direct arguments (from Amplify GraphQL) and HTTP body format
    let body: PDFGenerationRequest;

    if (event.arguments) {
      body = event.arguments as PDFGenerationRequest;
    } else if (event.body) {
      body =
        typeof event.body === 'string' ? JSON.parse(event.body) : event.body;
    } else {
      body = event as PDFGenerationRequest;
    }

    const {
      documentId,
      html,
      isSignedVersion = false,
      signatureData,
      userId,
      signatureType = 'electronic',
      memberId,
      documentVersion,
    } = body;

    // Only treat as signed version when explicitly requested
    // This ensures preview/download documents still show DRAFT watermark
    const actuallySignedVersion = isSignedVersion;

    console.log('PDF Handler - Processing:', {
      documentId,
      htmlLength: html?.length,
      isSignedVersion,
      actuallySignedVersion,
      signatureType,
      hasSignatureData: !!signatureData,
    });

    // Validate required fields
    if (!documentId || !html) {
      return {
        success: false,
        error: 'Document ID and HTML content are required',
      };
    }

    // HTML is already processed on the client side with Handlebars
    // Just prepare final HTML content
    let finalHtml = html;

    // Only add signature HTML section for electronic signatures
    if (
      actuallySignedVersion &&
      signatureData &&
      signatureType === 'electronic'
    ) {
      finalHtml = buildSignedDocumentHTML(html, signatureData);
    }

    // Clean HTML for PDF generation
    const cleanedHtml = cleanHTMLForPDF(finalHtml);

    // Generate PDF using improved jsPDF with better HTML parsing
    const pdfBuffer = await generatePDFWithImprovedJsPDF(cleanedHtml, {
      isSignedVersion: actuallySignedVersion, // Use actuallySignedVersion for ALL signature types
      signatureType,
      memberId,
      documentVersion,
      documentId,
    });

    // Generate S3 path using Amplify Storage conventions with document title and user identity
    let fileName = '';
    let cognitoUserId = userId;

    try {
      const { data: documentData } = await client.models.Document.get({
        id: documentId,
      });
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const suffix = actuallySignedVersion ? 'signed' : 'unsigned';

      // Get the Cognito User ID from the User record associated with this document
      if (documentData && documentData.userId) {
        const { data: userData } = await client.models.User.get({
          id: documentData.userId,
        });
        if (userData && userData.cognitoId) {
          cognitoUserId = userData.cognitoId;
          console.log('Using Cognito User ID for S3 path:', cognitoUserId);
        }
      }

      if (documentData && documentData.title) {
        const cleanTitle = documentData.title.replace(/[^a-zA-Z0-9]/g, '_');
        fileName = `${cleanTitle}_${suffix}_${timestamp}.pdf`;
      } else {
        fileName = `document_${suffix}_${timestamp}.pdf`;
      }
    } catch (error) {
      console.error('Error getting document data for filename:', error);
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const suffix = actuallySignedVersion ? 'signed' : 'unsigned';
      fileName = `document_${suffix}_${timestamp}.pdf`;
    }

    // Use Cognito User ID in S3 key for proper access control
    const s3Key = `public/documents/${cognitoUserId}/${documentId}/${fileName}`;

    // Upload to S3 using AWS SDK with Amplify auto-generated bucket name
    const bucketName =
      process.env.STORAGE_EVIDENCESTORAGE_BUCKETNAME ||
      process.env.STORAGE_BUCKET ||
      'evidenceStorage';
    console.log('Using S3 bucket:', bucketName);

    console.log('Uploading PDF to S3:', { bucket: bucketName, key: s3Key });

    try {
      const uploadCommand = new PutObjectCommand({
        Bucket: bucketName,
        Key: s3Key,
        Body: pdfBuffer,
        ContentType: 'application/pdf',
        Metadata: {
          documentId,
          isSignedVersion: actuallySignedVersion.toString(),
          generatedAt: new Date().toISOString(),
          signatureType: signatureType || 'electronic',
          ...(actuallySignedVersion &&
            signatureType === 'electronic' && {
              memberId: memberId || '',
              documentVersion: documentVersion || '1.0',
              signedAt: new Date().toISOString(),
              hash: Buffer.from(pdfBuffer).toString('base64').substring(0, 32), // Simple hash
              signatureProvider: 'internal',
            }),
        },
      });

      await s3Client.send(uploadCommand);
      console.log('PDF uploaded successfully to S3');
    } catch (uploadError) {
      console.error('Error uploading PDF to S3:', uploadError);
      throw new Error(
        `Failed to upload PDF to S3: ${
          uploadError instanceof Error ? uploadError.message : 'Unknown error'
        }`
      );
    }

    // Update document record with file URL (using the key for Amplify Storage)
    function removePublicPrefix(path: string): string {
      return path.replace(/^\/?public\/?/, '');
    }
    const fileUrl = removePublicPrefix(s3Key);

    try {
      await client.models.Document.update({
        id: documentId,
        fileUrl: fileUrl,
        lastModified: new Date().toISOString(),
      });

      console.log(
        `Successfully updated document ${documentId} with fileUrl: ${fileUrl}`
      );
    } catch (updateError) {
      console.error('Error updating document with fileUrl:', updateError);
      // Don't fail the entire operation if document update fails
    }

    return {
      success: true,
      fileUrl: fileUrl,
    };
  } catch (error) {
    console.error('PDF generation error:', error);

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
};

// Enhanced HTML parsing with section awareness for legal documents
function parseHTMLContentWithSections(htmlContent: string): Array<{
  type: 'paragraph' | 'heading' | 'section-break' | 'page-break';
  text: string;
  section?: 'start-page' | 'main-content' | 'end-page';
  style?: {
    bold?: boolean;
    underline?: boolean;
    center?: boolean;
    fontSize?: number;
    italic?: boolean;
    uppercase?: boolean;
  };
  classes?: string[];
}> {
  const elements: Array<any> = [];

  // Parse different sections
  const startPageMatch = htmlContent.match(
    /<!-- START_PAGE_SECTION -->([\s\S]*?)<!-- END_START_PAGE_SECTION -->/i
  );
  const mainContentMatch = htmlContent.match(
    /<!-- MAIN_CONTENT_SECTION -->([\s\S]*?)<!-- END_MAIN_CONTENT_SECTION -->/i
  );
  const endPageMatch = htmlContent.match(
    /<!-- END_PAGE_SECTION -->([\s\S]*?)<!-- END_END_PAGE_SECTION -->/i
  );

  // Process start page section
  if (startPageMatch) {
    elements.push({
      type: 'section-break',
      text: '',
      section: 'start-page',
    });
    parseContentSection(startPageMatch[1], elements, 'start-page');
  }

  // Process main content section
  if (mainContentMatch) {
    if (startPageMatch) {
      elements.push({
        type: 'section-break',
        text: '',
        section: 'main-content',
      });
    }
    parseContentSection(mainContentMatch[1], elements, 'main-content');
  }

  // Process end page section
  if (endPageMatch) {
    elements.push({
      type: 'section-break',
      text: '',
      section: 'end-page',
    });
    parseContentSection(endPageMatch[1], elements, 'end-page');
  }

  // If no sections found, parse as single content
  if (!startPageMatch && !mainContentMatch && !endPageMatch) {
    parseContentSection(htmlContent, elements, 'main-content');
  }

  return elements;
}

// Helper function to parse content within a section
function parseContentSection(
  content: string,
  elements: Array<any>,
  section: string
) {
  // Extract title (h1 in document header) - treat as heading
  const titleMatch = content.match(/<h1[^>]*>([\s\S]*?)<\/h1>/i);
  if (titleMatch) {
    const titleText = cleanTextContent(titleMatch[1]);
    elements.push({
      type: 'heading',
      text: titleText,
      section: section,
      style: { bold: true, center: true, fontSize: 12, uppercase: true },
    });
  }

  // Split by paragraphs, headings, and page breaks
  const parts = content
    .split(
      /(<p[^>]*>[\s\S]*?<\/p>|<h[1-6][^>]*>[\s\S]*?<\/h[1-6]>|<div[^>]*class="[^"]*signature[^"]*"[^>]*>[\s\S]*?<\/div>|<div[^>]*class="[^"]*witness[^"]*"[^>]*>[\s\S]*?<\/div>)/i
    )
    .filter(part => part.trim());

  parts.forEach(part => {
    if (!part.trim()) return;

    // Handle page breaks - look for "Inserted Page Break" text
    const text = cleanTextContent(part);
    if (text.trim() === 'Inserted Page Break') {
      elements.push({
        type: 'page-break',
        text: '',
        section: section,
      });
      return;
    }

    // Handle signature blocks - treat as regular paragraphs
    if (part.includes('signature')) {
      const text = cleanTextContent(part);
      if (text.trim()) {
        elements.push({
          type: 'paragraph',
          text: text,
          section: section,
          style: { center: true },
        });
      }
      return;
    }

    // Handle witness blocks - treat as regular paragraphs
    if (part.includes('witness') || part.includes('notary')) {
      const text = cleanTextContent(part);
      if (text.trim()) {
        elements.push({
          type: 'paragraph',
          text: text,
          section: section,
          style: { center: false },
        });
      }
      return;
    }

    // Handle headings
    const headingMatch = part.match(/<h([1-6])[^>]*>([\s\S]*?)<\/h[1-6]>/i);
    if (headingMatch) {
      const level = parseInt(headingMatch[1]);
      const text = cleanTextContent(headingMatch[2]);
      if (text.trim()) {
        elements.push({
          type: 'heading',
          text: text,
          section: section,
          style: {
            bold: true,
            center: true,
            fontSize: 12,
          },
        });
      }
      return;
    }

    // Handle paragraphs
    const paragraphMatch = part.match(/<p[^>]*>([\s\S]*?)<\/p>/i);
    if (paragraphMatch) {
      const text = cleanTextContent(paragraphMatch[1]);
      if (text.trim()) {
        // Check for center alignment
        const isCenter =
          part.includes('text-align: center') ||
          part.includes('text-align:center');

        // Check for special classes
        const classes = extractClasses(part);

        elements.push({
          type: 'paragraph',
          text: text,
          section: section,
          classes: classes,
          style: { center: isCenter },
        });
      }
    }
  });
}

// Helper function to extract CSS classes
function extractClasses(htmlPart: string): string[] {
  const classMatch = htmlPart.match(/class="([^"]*)"/i);
  return classMatch ? classMatch[1].split(/\s+/).filter(c => c.trim()) : [];
}

function cleanTextContent(html: string): string {
  return html
    .replace(
      /<strong[^>]*>(.*?)<\/strong>/gi,
      (match, content) => `**${content}**`
    )
    .replace(/<em[^>]*>(.*?)<\/em>/gi, (match, content) => `*${content}*`)
    .replace(/<u[^>]*>(.*?)<\/u>/gi, (match, content) => `__${content}__`)
    .replace(/<[^>]*>/g, ' ')
    .replace(/\s+/g, ' ')
    .replace(/&nbsp;/g, ' ')
    .trim();
}

// Calculate estimated height for an element
function calculateElementHeight(element: any): number {
  switch (element.type) {
    case 'heading':
      return 0.4;
    case 'section-break':
    case 'page-break':
      return 0;
    default:
      return 0.3; // Default paragraph height
  }
}

// Get font configuration for legal documents
function getLegalDocumentFontConfig(element: any): {
  style: string;
  size: number;
  lineHeight: number;
} {
  const baseConfig = {
    style: 'normal' as string,
    size: 12,
    lineHeight: 0.2, // Consistent line spacing
  };

  if (element.style?.bold || element.type === 'heading') {
    baseConfig.style = 'bold';
  }

  if (element.style?.italic) {
    baseConfig.style = baseConfig.style === 'bold' ? 'bolditalic' : 'italic';
  }

  // Adjust line height based on element type
  switch (element.type) {
    case 'heading':
      baseConfig.lineHeight = 0.25;
      break;
    default:
      baseConfig.lineHeight = 0.2; // Standard paragraph spacing
  }

  return baseConfig;
}

// Process text with legal document formatting
function processLegalDocumentText(
  text: string,
  doc: any,
  maxWidth: number,
  element: any,
  firstLineIndent: number
): Array<{
  text: string;
  bold?: boolean;
  underline?: boolean;
  italic?: boolean;
}> {
  const lines: Array<{
    text: string;
    bold?: boolean;
    underline?: boolean;
    italic?: boolean;
  }> = [];

  // Handle formatting markers
  let processedText = text;
  const segments: Array<{
    text: string;
    bold?: boolean;
    underline?: boolean;
    italic?: boolean;
  }> = [];

  // Process bold markers
  const boldRegex = /\*\*(.*?)\*\*/g;
  let lastIndex = 0;
  let match;

  while ((match = boldRegex.exec(processedText)) !== null) {
    // Add text before bold
    if (match.index > lastIndex) {
      const beforeText = processedText.substring(lastIndex, match.index);
      if (beforeText.trim()) {
        segments.push({ text: beforeText });
      }
    }
    // Add bold text
    segments.push({ text: match[1], bold: true });
    lastIndex = match.index + match[0].length;
  }

  // Add remaining text
  if (lastIndex < processedText.length) {
    const remainingText = processedText.substring(lastIndex);
    if (remainingText.trim()) {
      segments.push({ text: remainingText });
    }
  }

  // If no segments, add the whole text
  if (segments.length === 0) {
    segments.push({ text: processedText });
  }

  // Split segments into lines that fit the page width
  let currentLine = '';
  let currentFormatting: {
    bold?: boolean;
    underline?: boolean;
    italic?: boolean;
  } = {};

  segments.forEach(segment => {
    const words = segment.text.split(' ');

    words.forEach(word => {
      const testLine = currentLine + (currentLine ? ' ' : '') + word;
      const textWidth = doc.getTextWidth(testLine);

      if (textWidth > maxWidth && currentLine) {
        // Add current line and start new one
        lines.push({ text: currentLine, ...currentFormatting });
        currentLine = word;
        currentFormatting = {
          bold: segment.bold,
          underline: segment.underline,
          italic: segment.italic,
        };
      } else {
        currentLine = testLine;
        currentFormatting = {
          bold: segment.bold,
          underline: segment.underline,
          italic: segment.italic,
        };
      }
    });
  });

  // Add final line
  if (currentLine) {
    lines.push({ text: currentLine, ...currentFormatting });
  }

  return lines;
}

// Get X position for text based on alignment and indentation
function getXPosition(
  element: any,
  line: any,
  pageWidth: number,
  margin: number,
  firstLineIndent: number,
  lineIndex: number
): number {
  if (element.style?.center || element.type === 'heading') {
    return pageWidth / 2;
  }

  // First line indent for paragraphs
  if (element.type === 'paragraph' && lineIndex === 0) {
    return margin + firstLineIndent;
  }

  return margin;
}

// Get text alignment
function getTextAlignment(
  element: any,
  line: any
): 'left' | 'center' | 'right' | 'justify' {
  if (element.style?.center || element.type === 'heading') {
    return 'center';
  }
  return 'left'; // Legal documents use justified, but jsPDF doesn't support it well
}

// Get spacing after elements
function getLegalDocumentSpacing(element: any): number {
  switch (element.type) {
    case 'heading':
      return 0.24; // Double line spacing
    default:
      return 0.12; // Single line spacing between paragraphs
  }
}

// Generate PDF using improved jsPDF with legal document formatting
async function generatePDFWithImprovedJsPDF(
  htmlContent: string,
  options: any = {}
): Promise<Buffer> {
  try {
    console.log('Generating PDF with legal document formatting...');

    // Dynamic import of jsPDF
    const { jsPDF } = await import('jspdf');

    // Create new PDF document with legal document specifications
    const doc = new jsPDF({
      orientation: 'portrait',
      unit: 'in', // Use inches for legal document formatting
      format: 'letter', // 8.5" x 11" letter format
    });

    // Legal document page settings
    const pageWidth = doc.internal.pageSize.getWidth(); // 8.5 inches
    const pageHeight = doc.internal.pageSize.getHeight(); // 11 inches
    const margin = 1.0; // 1 inch margins on all sides
    const maxWidth = pageWidth - margin * 2; // 6.5 inches
    const firstLineIndent = 0.5; // 0.5 inch first line indent
    let yPosition = margin;
    let pageNumber = 1;
    let isFirstPage = true;

    // Parse HTML content into structured elements with section awareness
    const elements = parseHTMLContentWithSections(htmlContent);

    // Add DRAFT watermark ONLY for truly unsigned documents
    // ALL signed documents (electronic, manual, e-notarization) should NOT show DRAFT watermark
    if (!options.isSignedVersion) {
      // Save current state
      doc.setFont('times', 'bold');
      doc.setFontSize(72);
      doc.setTextColor(200, 200, 200); // Light gray

      // Calculate center position and rotate
      const centerX = pageWidth / 2;
      const centerY = pageHeight / 2;

      // Add rotated DRAFT text
      doc.text('DRAFT', centerX, centerY, {
        align: 'center',
        angle: 45,
      });

      // Restore original color
      doc.setTextColor(0, 0, 0); // Black
    }

    // Helper function to add page numbers (skip first page)
    const addPageNumber = () => {
      if (!isFirstPage) {
        doc.setFont('times', 'normal');
        doc.setFontSize(12);
        doc.text(pageNumber.toString(), pageWidth / 2, pageHeight - 0.5, {
          align: 'center',
        });
      }
    };

    // Helper function to add DRAFT watermark
    const addDraftWatermark = () => {
      // Only show DRAFT for truly unsigned documents
      // ALL signed documents (electronic, manual, e-notarization) should NOT show DRAFT watermark
      if (!options.isSignedVersion) {
        // Set watermark properties
        doc.setFont('times', 'bold');
        doc.setFontSize(72);
        doc.setTextColor(200, 200, 200); // Light gray

        // Calculate center position and rotate
        const centerX = pageWidth / 2;
        const centerY = pageHeight / 2;

        // Add rotated DRAFT text
        doc.text('DRAFT', centerX, centerY, {
          align: 'center',
          angle: 45,
        });

        // Restore original color
        doc.setTextColor(0, 0, 0); // Black
      }
    };

    elements.forEach((element, index) => {
      // Handle explicit page breaks for sections and manual page breaks
      if (element.type === 'section-break' || element.type === 'page-break') {
        // Only add page break if this is not the first element
        if (index > 0) {
          doc.addPage();
          pageNumber++;
          yPosition = margin;
          isFirstPage = false;
          addDraftWatermark();
          addPageNumber();
        }
        return; // Skip rendering break elements
      }

      // Force page break when transitioning between sections
      const prevElement = index > 0 ? elements[index - 1] : null;
      if (
        prevElement &&
        prevElement.section !== element.section &&
        element.section &&
        prevElement.section
      ) {
        doc.addPage();
        pageNumber++;
        yPosition = margin;
        isFirstPage = false;
        addDraftWatermark();
        addPageNumber();
      }

      // Check if we need a new page for content
      const estimatedHeight = calculateElementHeight(element);
      if (yPosition + estimatedHeight > pageHeight - margin - 0.5) {
        // Leave space for page number
        doc.addPage();
        pageNumber++;
        yPosition = margin;
        isFirstPage = false;
        addDraftWatermark();
        addPageNumber();
      }

      // Set font and formatting based on legal document standards
      const fontConfig = getLegalDocumentFontConfig(element);
      doc.setFont('times', fontConfig.style);
      doc.setFontSize(fontConfig.size);

      // Process text with legal document formatting
      const processedText = processLegalDocumentText(
        element.text,
        doc,
        maxWidth,
        element,
        firstLineIndent
      );

      // Add text to PDF with proper spacing
      processedText.forEach((line, lineIndex) => {
        if (yPosition + fontConfig.lineHeight > pageHeight - margin - 0.5) {
          doc.addPage();
          pageNumber++;
          yPosition = margin;
          isFirstPage = false;
          addDraftWatermark();
          addPageNumber();
        }

        const xPosition = getXPosition(
          element,
          line,
          pageWidth,
          margin,
          firstLineIndent,
          lineIndex
        );
        const align = getTextAlignment(element, line);

        doc.text(line.text, xPosition, yPosition, { align: align });
        yPosition += fontConfig.lineHeight;
      });

      // Add spacing after elements according to legal document standards
      yPosition += getLegalDocumentSpacing(element);
    });

    // Add final page number if not first page
    if (!isFirstPage) {
      addPageNumber();
    }

    // Add signature section only for electronic signatures - ALWAYS on new page
    if (options.isSignedVersion && options.signatureType === 'electronic') {
      // Force new page for Electronic Signature section
      doc.addPage();
      pageNumber++;
      yPosition = margin;
      isFirstPage = false;
      addDraftWatermark();
      addPageNumber();

      const signatureLines = [
        'Electronic Signature',
        '',
        'This document has been electronically signed and is legally binding.',
        `Signed on: ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}`,
        'Signature Type: Electronic',
        'Status: Legally Binding',
      ];

      signatureLines.forEach((line, index) => {
        if (index === 0) {
          // Title line
          doc.setFont('times', 'bold');
          doc.setFontSize(14);
          doc.text(line, margin, yPosition);
          yPosition += 0.3;
        } else if (line === '') {
          // Empty line for spacing
          yPosition += 0.1;
        } else {
          // Regular signature lines
          doc.setFont('times', 'normal');
          doc.setFontSize(12);
          doc.text(line, margin, yPosition);
          yPosition += 0.2;
        }
      });
    }

    // Convert to buffer
    const pdfBuffer = Buffer.from(doc.output('arraybuffer'));

    console.log(
      'Improved PDF generated successfully, size:',
      pdfBuffer.length,
      'bytes'
    );
    return pdfBuffer;
  } catch (error) {
    console.error('Error generating PDF with improved jsPDF:', error);
    throw error;
  }
}

function processTextFormatting(
  text: string,
  doc: any,
  maxWidth: number,
  center: boolean = false
): Array<{ text: string; bold?: boolean; underline?: boolean }> {
  const lines: Array<{ text: string; bold?: boolean; underline?: boolean }> =
    [];

  // Handle bold and underline formatting
  let processedText = text;
  const segments: Array<{ text: string; bold?: boolean; underline?: boolean }> =
    [];

  // Split by formatting markers
  const parts = processedText.split(/(\*\*.*?\*\*|__.*?__)/g);

  parts.forEach(part => {
    if (part.startsWith('**') && part.endsWith('**')) {
      // Bold text
      const boldText = part.slice(2, -2);
      segments.push({ text: boldText, bold: true });
    } else if (part.startsWith('__') && part.endsWith('__')) {
      // Underlined text
      const underlinedText = part.slice(2, -2);
      segments.push({ text: underlinedText, underline: true });
    } else if (part.trim()) {
      segments.push({ text: part });
    }
  });

  // Combine segments and split to fit page width
  let currentLine = '';
  let currentFormatting: { bold?: boolean; underline?: boolean } = {};

  segments.forEach(segment => {
    const words = segment.text.split(' ');

    words.forEach(word => {
      const testLine = currentLine + (currentLine ? ' ' : '') + word;

      // Check if line fits
      const textWidth = doc.getTextWidth(testLine);

      if (textWidth > maxWidth && currentLine) {
        // Add current line and start new one
        lines.push({ text: currentLine, ...currentFormatting });
        currentLine = word;
        currentFormatting = {
          bold: segment.bold,
          underline: segment.underline,
        };
      } else {
        currentLine = testLine;
        currentFormatting = {
          bold: segment.bold,
          underline: segment.underline,
        };
      }
    });
  });

  // Add remaining text
  if (currentLine) {
    lines.push({ text: currentLine, ...currentFormatting });
  }

  return lines;
}
