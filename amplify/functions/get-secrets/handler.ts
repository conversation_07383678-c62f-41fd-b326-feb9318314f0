import type { Schema } from '../../data/resource';

export const handler: Schema['getSecrets']['functionHandler'] = async event => {
  try {
    const { secretName } = event.arguments;

    if (!secretName) {
      throw new Error('secretName is required');
    }

    // Get secret from environment (Amplify will inject from secret store)
    let secretValue: string | undefined;

    switch (secretName) {
      case 'STRIPE_SECRET_KEY':
        secretValue = process.env.STRIPE_SECRET_KEY;
        break;
      case 'STRIPE_WEBHOOK_SECRET':
        secretValue = process.env.STRIPE_WEBHOOK_SECRET;
        break;
      case 'UPS_CLIENT_SECRET':
        secretValue = process.env.UPS_CLIENT_SECRET;
        break;
      default:
        throw new Error('Invalid secret name');
    }

    if (!secretValue) {
      throw new Error('Secret not found');
    }

    return {
      value: secretValue,
    };
  } catch (error) {
    console.error('Error getting secret:', error);
    throw error;
  }
};
