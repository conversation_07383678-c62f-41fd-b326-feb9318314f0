// @ts-nocheck

import type { Schema } from '../../data/resource';
import { getAmplifyDataClientConfig } from '@aws-amplify/backend/function/runtime';
import { Amplify } from 'aws-amplify';
import { generateClient } from 'aws-amplify/data';
import { env } from '$amplify/env/getEmergencyFileUrl';
import { S3Client, GetObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);

Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>();

// Initialize S3 client
const s3Client = new S3Client({
  region: process.env.AWS_REGION || 'us-east-1',
});

export const handler = async event => {
  console.log(
    'Get emergency file URL request:',
    JSON.stringify(event, null, 2)
  );

  try {
    // Extract data from event - handle both direct Lambda calls and GraphQL/AppSync calls
    const { email, token, fileKey } = event.arguments || event;

    // Validate required fields
    if (!email || !token || !fileKey) {
      throw new Error('Missing required fields: email, token, fileKey');
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new Error('Invalid email format');
    }

    // Step 1: Verify the token
    const verifyResult = await client.mutations.verifyEmailToken({
      email: decodeURIComponent(email),
      token,
      verificationType: 'emergencyNotification',
    });

    const verifyResultData = verifyResult.data;

    // Try to parse if it's a string
    let parsedVerifyResultData = verifyResultData;
    if (typeof verifyResultData === 'string') {
      try {
        parsedVerifyResultData = JSON.parse(verifyResultData);
      } catch (e) {
        console.log('Failed to parse verify result data as JSON:', e);
      }
    }

    if (!parsedVerifyResultData?.success) {
      throw new Error(
        parsedVerifyResultData?.error || 'Failed to verify emergency access'
      );
    }

    // Step 2: Find the emergency contact by email
    const contactsResult = await client.models.EmergencyContact.list({
      filter: {
        emailAddress: { eq: decodeURIComponent(email) },
      },
    });

    if (!contactsResult.data || contactsResult.data.length === 0) {
      throw new Error('Emergency contact not found');
    }

    const contact = contactsResult.data[0];
    const userId = contact.userId;

    // Step 3: Verify the file belongs to the user associated with this emergency contact
    // Extract the user ID from the file key (assuming format like "living-documents/{userId}/...")
    const fileKeyParts = fileKey.split('/');
    let fileOwnerId;
    let correctedFileKey = fileKey;
    // If the path doesn't start with 'public/' but permissions require it
    if (!correctedFileKey.startsWith('public/')) {
        correctedFileKey = 'public/' + correctedFileKey;
        console.log('Added public/ prefix:', correctedFileKey);
    }

    // Check if it's a living document and add public/ prefix if needed
    if (correctedFileKey.includes('living-documents/')) {
        fileOwnerId = fileKeyParts[1];
    } else if (correctedFileKey.includes('documents/')) {
      fileOwnerId = fileKeyParts[1];
    } else {
      throw new Error('Invalid file path format');
    }

    // Step 4: Generate a pre-signed URL for the file
    // Extract bucket name from environment variables - use Amplify's auto-generated env var
    // Amplify sets STORAGE_{STORAGE_NAME}_BUCKETNAME env var for each storage resource
    const bucketName = process.env.STORAGE_EVIDENCESTORAGE_BUCKETNAME || process.env.STORAGE_BUCKET || 'evidenceStorage'
    
    console.log('Using S3 bucket:', bucketName);

    // Use the corrected file key for the S3 command
    const command = new GetObjectCommand({
      Bucket: bucketName,
      Key: correctedFileKey,
    });

    // Generate a pre-signed URL that expires in 5 minutes (300 seconds)
    const signedUrl = await getSignedUrl(s3Client, command, {
      expiresIn: 300,
    });

    return {
      success: true,
      url: signedUrl,
      expiresIn: 300, // 5 minutes in seconds
    };
  } catch (error) {
    console.error('Error generating pre-signed URL:', error);

    return {
      success: false,
      error: error.message || 'Failed to generate pre-signed URL',
    };
  }
};