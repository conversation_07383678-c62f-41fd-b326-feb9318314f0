import { Amplify } from 'aws-amplify';
import { getAmplifyDataClientConfig } from '@aws-amplify/backend/function/runtime';
import { generateClient } from 'aws-amplify/data';
// @ts-ignore
import { env } from '$amplify/env/inviteUser';
import {
  CognitoIdentityProviderClient,
  AdminUpdateUserAttributesCommand,
  AdminConfirmSignUpCommand,
} from '@aws-sdk/client-cognito-identity-provider';
import { Schema } from '../../../data/resource';

const { resourceConfig, libraryOptions } =
  // @ts-ignore
  await getAmplifyDataClientConfig(env);

Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>();
const cognitoClient = new CognitoIdentityProviderClient({
  region: process.env.AWS_REGION || 'us-east-1',
});

// CUSTOM LAMBDA FOR SUBMIT INVITE (AUTO CONFIRM USER AND ASSIGN REQUIRED ROLES)
export const handler: Schema['acceptInvite']['functionHandler'] =
  async event => {
    console.log('===> ACCEPT INVITE REQUEST:', JSON.stringify(event, null, 2));

    const { inviteToken, email } = event.arguments;

    // 1. Validate and retrieve the invite
    const { data: invites, errors: inviteErrors } =
      await client.models.UserInvite.list({
        filter: { token: { eq: inviteToken } },
      });

    if (inviteErrors) {
      throw new Error(`Error retrieving invite: ${inviteErrors[0].message}`);
    }

    if (!invites || invites.length === 0) {
      throw new Error('Invalid invitation token');
    }

    const invite = invites[0];

    console.log('===> Found invite:', JSON.stringify(invite, null, 2));

    // 2. Check invite expiration
    if (new Date(invite.expiresAt) < new Date()) {
      throw new Error('Invitation has expired');
    }

    // 3. Check invite status
    if (invite.status !== 'pending') {
      throw new Error('Invitation has already been used or is no longer valid');
    }

    // 4. Verify email matches invite
    if (invite.email !== email) {
      throw new Error('Email does not match the invitation');
    }

    // 11. Verify user email and confirm account in Cognito
    try {
      const verifyEmailCommand = new AdminUpdateUserAttributesCommand({
        UserPoolId: env.USER_POOL_ID,
        Username: email,
        UserAttributes: [
          {
            Name: 'email_verified',
            Value: 'true',
          },
        ],
      });

      await cognitoClient.send(verifyEmailCommand);
      console.log(`Successfully verified email for user: ${email}`);

      // Then, confirm the user account
      const confirmSignUpCommand = new AdminConfirmSignUpCommand({
        UserPoolId: env.USER_POOL_ID,
        Username: email,
      });

      await cognitoClient.send(confirmSignUpCommand);
      console.log(`Successfully confirmed account for user: ${email}`);
    } catch (verifyError) {
      console.error(
        'Error verifying user email or confirming account:',
        verifyError
      );
      // Don't fail the entire process for email verification/confirmation errors
      console.warn(
        'User created but email verification or account confirmation failed'
      );
    }

    const { data: existingUsers, errors: userCheckErrors } =
      await client.models.User.list({
        filter: { email: { eq: email } },
      });

    console.log('===> Existing users:', existingUsers);

    if (userCheckErrors) {
      throw new Error(
        `Error checking existing users: ${userCheckErrors[0].message}`
      );
    }

    const userId = existingUsers?.[0]?.id;

    if (!userId) {
      throw new Error(
        "Can't find user in database after creating Cognito user"
      );
    }

    // 8. Map role to group name format expected by addUserToGroupV2
    const roleToGroupMapping: Record<string, string> = {
      Member: 'member',
      Administrator: 'administrator',
      WelonTrust: 'welontrust',
      Professional: 'member', // Professional users are treated as members in Cognito
    };

    const groupName = roleToGroupMapping[invite.role as string] || 'member';

    try {
      const groupAssignmentResult = await client.mutations.addUserToGroupV2({
        userId: userId,
        groupName: groupName,
      });

      const groupResult = JSON.parse(groupAssignmentResult.data as string);
      if (groupResult.success) {
        console.log(`Successfully assigned user to group: ${groupName}`);
      } else {
        console.warn(
          `Group assignment may have failed: ${groupResult.message || 'Unknown error'}`
        );
      }
    } catch (groupError) {
      console.error('Error calling addUserToGroupV2:', groupError);
      // Don't fail the entire process for group assignment errors
      console.warn('User created but group assignment failed');
    }

    // 12. Update invite status to accepted
    const { errors: updateInviteErrors } =
      await client.models.UserInvite.update({
        id: invite.id,
        status: 'accepted',
        acceptedAt: new Date().toISOString(),
      });

    if (updateInviteErrors) {
      console.error('Error updating invite status:', updateInviteErrors);
      // Don't fail the process, user is already created
      console.warn('User created but invite status update failed');
    }

    console.log(`Successfully processed invite acceptance for: ${email}`);

    return {
      success: true,
      message: 'User account created successfully',
    };
  };
