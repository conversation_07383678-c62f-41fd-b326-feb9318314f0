import type { Schema } from '../../../data/resource';
import { Amplify } from 'aws-amplify';
import { getAmplifyDataClientConfig } from '@aws-amplify/backend/function/runtime';
import { generateClient } from 'aws-amplify/data';
// @ts-ignore
import { env } from '$amplify/env/inviteUser';
import crypto from 'crypto';
import {
  CognitoIdentityProviderClient,
  AdminGetUserCommand,
} from '@aws-sdk/client-cognito-identity-provider';

const { resourceConfig, libraryOptions } =
  // @ts-ignore
  await getAmplifyDataClientConfig(env);

Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>();
const cognitoClient = new CognitoIdentityProviderClient({
  region: process.env.AWS_REGION || 'us-east-1',
});

export const handler: Schema['inviteUser']['functionHandler'] = async event => {
  console.log('===> INVITE USER REQUEST:', JSON.stringify(event, null, 2));

  try {
    const {
      firstName,
      lastName,
      email,
      role,
      subrole,
      sharedFields,
      userId,
      baseUrl,
    } = event.arguments;

    // CHECK IF USER ALREADY EXISTS IN DATABASE
    const { data: existingUsers, errors: userCheckErrors } =
      await client.models.User.list({
        filter: { email: { eq: email } },
      });

    if (userCheckErrors) {
      throw new Error(
        `Error checking existing users: ${userCheckErrors[0].message}`
      );
    }

    if (existingUsers && existingUsers.length > 0) {
      if (!sharedFields) {
        throw new Error(
          'This email is already registered in the system. The user can sign in directly.'
        );
      } else {
        if (userId) {
          await client.models.LinkedAccountWithSharedFields.create({
            userId: userId,
            linkedUserId: existingUsers[0].id,
            sharedFields: sharedFields,
            isAccepted: false,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          });
        }
      }
    }
    // CHECK IF USER ALREADY EXISTS IN COGNITO
    try {
      const getUserCommand = new AdminGetUserCommand({
        UserPoolId: env.USER_POOL_ID,
        Username: email,
      });
      await cognitoClient.send(getUserCommand);

      if (!sharedFields) {
        throw new Error(
          'This email is already registered in Cognito. The user can sign in directly.'
        );
      }
    } catch (cognitoError: any) {
      // UserNotFoundException is expected and means we can proceed
      if (cognitoError.name !== 'UserNotFoundException') {
        console.error('Error checking Cognito user:', cognitoError);
        throw new Error('Error checking existing Cognito user');
      }
      console.log(
        `No existing Cognito user found for ${email}, proceeding with invite`
      );
    }

    // 2. CHECK IF THERE'S ALREADY A PENDING INVITE FOR THIS EMAIL
    const { data: existingInvites, errors: inviteCheckErrors } =
      await client.models.UserInvite.list({
        filter: {
          and: [{ email: { eq: email } }, { status: { eq: 'pending' } }],
        },
      });

    if (inviteCheckErrors) {
      throw new Error(
        `Error checking existing invites: ${inviteCheckErrors[0].message}`
      );
    }

    if (existingInvites && existingInvites.length > 0) {
      const existingInvite = existingInvites[0];
      const expiresAt = new Date(existingInvite.expiresAt);
      const isExpired = expiresAt < new Date();

      if (!isExpired) {
        throw new Error(
          `There is already a pending invitation for ${
            email
          }. The invitation expires on ${expiresAt.toLocaleDateString()}.`
        );
      } else {
        // AUTO CANCEL EXPIRED INVITE
        await client.models.UserInvite.update({
          id: existingInvite.id,
          status: 'expired',
        });
        console.log(`Automatically cancelled expired invite for ${email}`);
      }
    }

    // GENERATE UNIQUE TOKEN FOR THE INVITE
    const token = crypto.randomUUID();

    // SET EXPIRATION TIME (7 DAYS FROM NOW)
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7);

    // CREATE THE INVITE RECORD
    const { errors } = await client.models.UserInvite.create({
      firstName,
      lastName,
      email,
      role: role as any, // Cast to handle enum type
      subrole,
      invitedBy: 'admin', // TODO: Get actual admin user ID from context
      invitedByEmail: '<EMAIL>', // TODO: Get actual admin email from context
      token,
      status: 'pending',
      expiresAt: expiresAt.toISOString(),
      createdAt: new Date().toISOString(),
      ...(sharedFields && { sharedFields }),
      ...(userId && { userId }),
    });

    if (errors) {
      throw new Error(errors[0].message);
    }

    // SEND INVITATION EMAIL
    const frontendUrl = baseUrl || 'http://localhost:3000';

    // Prepare email content based on whether Cognito user was created
    const roleDisplayName = role === 'WelonTrust' ? 'Welon Trust' : role;
    const emailMessage =
      existingUsers && existingUsers.length > 0
        ? 'You have been shared some fields for documents in Childfree'
        : 'You have been invited to join Childfree';

    const emailResult = await client.mutations.sendEmail({
      to: email,
      subject:
        existingUsers && existingUsers.length > 0
          ? 'Notification about shared fields for documents'
          : `Invitation to join Childfree as ${roleDisplayName}`,
      message: emailMessage,
      emailType: 'invitation',
      verificationLink:
        existingUsers && existingUsers.length > 0
          ? `${baseUrl}/`
          : `${baseUrl}/auth/accept-invite?token=${token}`,
      baseUrl: frontendUrl,
    });

    if (emailResult.errors) {
      console.error('Email sending errors:', emailResult.errors);
      // Don't throw error here, invite was created successfully
      console.warn('Invite created but email may not have been sent');
    }

    console.log(`Successfully created invitation for ${email}`);

    return {
      success: true,
      message: `Invitation sent to ${email}`,
      data: {
        email,
        token,
        expiresAt: expiresAt.toISOString(),
      },
    };
  } catch (error) {
    console.error('===> INVITE USER ERROR:', error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : 'An unexpected error occurred',
    };
  }
};
