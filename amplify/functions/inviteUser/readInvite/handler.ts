import { Amplify } from 'aws-amplify';
import { getAmplifyDataClientConfig } from '@aws-amplify/backend/function/runtime';
import { generateClient } from 'aws-amplify/data';
// @ts-ignore
import { env } from '$amplify/env/inviteUser';
import crypto from 'crypto';
import {
  CognitoIdentityProviderClient,
  AdminCreateUserCommand,
  AdminGetUserCommand,
} from '@aws-sdk/client-cognito-identity-provider';
import { Schema } from '../../../data/resource';

const { resourceConfig, libraryOptions } =
  // @ts-ignore
  await getAmplifyDataClientConfig(env);

Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>();

// CUSTOM LAMBDA FOR READ INVITE DATA. CREATED FOR BETTER SECURITY AND DISABLE READ ALL USERS TO READ INVITES
export const handler: Schema['readInvite']['functionHandler'] = async event => {
  const { token } = event.arguments;
  const { data: invites, errors } = await client.models.UserInvite.list({
    filter: { token: { eq: token } },
  });
  if (errors) {
    throw new Error(errors[0].message);
  }

  // Check if invite exists
  if (!invites || invites.length === 0) {
    throw new Error('Invite not found');
  }

  const invite = invites[0];

  if (new Date(invite.expiresAt) < new Date()) {
    throw new Error('Invite token expired');
  }
  if (invite.status !== 'pending') {
    throw new Error('Invite already used or invalid');
  }
  return invite;
};
