// @ts-nocheck
import * as dotenv from 'dotenv';
dotenv.config({ path: '.env' });

import type { PostConfirmationTriggerHandler } from 'aws-lambda';
import type { Schema } from '../../data/resource';
import { Amplify } from 'aws-amplify';
import { getAmplifyDataClientConfig } from '@aws-amplify/backend/function/runtime';
import { generateClient } from 'aws-amplify/data';
import { env } from '$amplify/env/postSignUp';
import {
  CognitoIdentityProviderClient,
  AdminUpdateUserAttributesCommand,
} from '@aws-sdk/client-cognito-identity-provider';

const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);

Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>();
const cognitoClient = new CognitoIdentityProviderClient({
  region: process.env.AWS_REGION || 'no region',
});

function getRandomItem<T>(array: T[]): T | null {
  if (!array || array.length === 0) {
    return null;
  }

  const randomIndex = Math.floor(Math.random() * array.length);
  return array[randomIndex];
}

async function fetchWelonTrustUsersByState(state: string) {
  try {
    // First, try to find Welon Trust users in the same state
    const { data: stateUsers, errors: stateErrors } =
      await client.models.User.list({
        filter: {
          and: [{ role: { eq: 'WelonTrust' } }, { state: { eq: state } }],
        },
      });

    if (stateErrors) {
      console.error('Errors fetching Welon Trust users by state:', stateErrors);
    }

    // If we found users in the same state, return them
    if (stateUsers && stateUsers.length > 0) {
      console.log(
        `Found ${stateUsers.length} Welon Trust users in state: ${state}`
      );
      return stateUsers;
    }

    // Fallback: fetch any available active Welon Trust users
    console.log(
      `No Welon Trust users found in state ${state}, fetching any available`
    );
    const { data: allUsers, errors: allErrors } = await client.models.User.list(
      {
        filter: {
          and: [{ role: { eq: 'WelonTrust' } }],
        },
      }
    );

    if (allErrors) {
      console.error('Errors fetching all Welon Trust users:', allErrors);
      return [];
    }

    if (!allUsers || allUsers.length === 0) {
      console.warn('No active Welon Trust users found in the system');
      return [];
    }

    console.log(
      `Found ${allUsers.length} active Welon Trust users as fallback`
    );
    return allUsers;
  } catch (error) {
    console.error('Error fetching Welon Trust users by state:', error);
    return [];
  }
}

/**
 * Create a Welon Trust assignment for a user
 */
async function createWelonTrustAssignment(userId: string, welonTrustUser: any) {
  try {
    const { errors } = await client.models.WelonTrustAssignment.create({
      userId,
      welonTrustUserId: welonTrustUser.id,
      welonTrustName: `${welonTrustUser.firstName} ${welonTrustUser.lastName}`,
      welonTrustEmail: welonTrustUser.email,
      welonTrustCognitoId: welonTrustUser.cognitoId,
      assignedAt: new Date().toISOString(),
      assignedBy: 'system-auto-assignment',
      status: 'active',
    });

    if (errors) {
      console.error('Errors creating Welon Trust assignment:', errors);
      throw new Error('Failed to create Welon Trust assignment');
    }

    console.log(
      `Successfully assigned Welon Trust ${welonTrustUser.firstName} ${welonTrustUser.lastName} to user ${userId}`
    );
  } catch (error) {
    console.error('Error creating Welon Trust assignment:', error);
    throw error;
  }
}

export const handler: PostConfirmationTriggerHandler = async event => {
  console.log('===> FULL EVENT:', JSON.stringify(event, null, 2));

  const userAttributes = event.request.userAttributes;

  const cognitoUserName = event.userName; // EQUAL TO USER EMAIL

  const userId = userAttributes.sub;
  const email = userAttributes.email;
  const givenName = userAttributes.given_name;
  const familyName = userAttributes.family_name;
  const phoneNumber = userAttributes.phone_number;
  const birthdate = userAttributes.birthdate;
  const address = userAttributes.address;
  const referralSource = userAttributes['custom:referral_source'];

  console.log('===> REAL USER ATTRIBUTES', {
    userId,
    email,
    givenName,
    familyName,
    phoneNumber,
    birthdate,
    address,
    referralSource,
  });

  try {
    // Default role for regular users
    let userRole = 'Member';
    let userSubrole = 'Basic';
    let inviteData = null;

    // Check if user has pending invite by token
    const inviteToken =
      userAttributes['custom:invite_token'] || userAttributes.inviteToken;
    const address_data = address;

    // Parse address data if available
    let parsedAddress: any = null;
    if (address_data) {
      try {
        parsedAddress = JSON.parse(address_data);
        console.log('Parsed address data:', parsedAddress);
      } catch (parseError) {
        console.error('Error parsing address data:', parseError);
      }
    }

    if (inviteToken && inviteToken !== '') {
      console.log(`Checking invite by token: ${inviteToken}`);

      const { data: invites, errors } = await client.models.UserInvite.list({
        filter: {
          and: [{ token: { eq: inviteToken } }, { status: { eq: 'pending' } }],
        },
      });

      if (errors) {
        console.error('Error checking for invites by token:', errors);
      } else if (invites && invites.length > 0) {
        inviteData = invites[0];

        // Verify email matches
        if (inviteData.email !== email) {
          console.error(
            `Email mismatch: invite email ${inviteData.email} vs registration email ${email}`
          );
          throw new Error('Email does not match the invitation');
        }

        userRole = inviteData.role;
        userSubrole = inviteData.subrole || 'Basic';
        console.log(
          `User ${email} was invited with token ${inviteToken} and role: ${userRole}`
        );
      } else {
        console.log(
          `No valid invite found for token: ${inviteToken}, registering as regular user`
        );
      }
    } else {
      console.log(
        `No invite token provided, registering user ${email} as regular member`
      );
    }

    // Check if user already exists (from invite)
    const { data: existingUsers, errors: existingUserErrors } =
      await client.models.User.list({
        filter: { email: { eq: email } },
      });

    if (existingUserErrors) {
      console.error('Error checking for existing users:', existingUserErrors);
      throw new Error('Failed to check for existing users');
    }

    let newUser;

    if (existingUsers && existingUsers.length > 0) {
      // Update existing user record (from invite)
      const existingUser = existingUsers[0];
      console.log(`Updating existing user record for ${email}`);

      const { data: updatedUser, errors: updateErrors } =
        await client.models.User.update({
          id: existingUser.id,
          phoneNumber: phoneNumber,
          birthdate: birthdate,
          state: address,
          cognitoId: userId,
          status: 'active',
          howDidYouHearAboutUs: referralSource || 'No referral',
        });

      if (updateErrors) {
        console.error('Errors updating user:', updateErrors);
        throw new Error('Failed to update user');
      }

      newUser = updatedUser;
    } else {
      // Create new user (regular registration)
      console.log(`Creating new user record for ${email}`);

      const { data: createdUser, errors: userErrors } =
        await client.models.User.create({
          firstName: givenName,
          lastName: familyName,
          phoneNumber: phoneNumber,
          birthdate: birthdate,
          state: address,
          cognitoId: userId,
          email,
          role: userRole,
          subrole: userSubrole,
          status: 'active',
          createdAt: new Date().toISOString(),
          howDidYouHearAboutUs: referralSource || 'No referral',
        });

      if (userErrors) {
        console.error('Errors creating user:', userErrors);
        throw new Error('Failed to create user');
      }

      newUser = createdUser;
    }

    if (!newUser) {
      throw new Error('User creation/update returned null');
    }

    console.log('USER CREATE RESULT: ', newUser);

    // Update Cognito user with externalId custom attribute
    try {
      console.log('===> UPDATING COGNITO USER EXTERNAL ID');
      const updateAttributesCommand = new AdminUpdateUserAttributesCommand({
        UserPoolId: env.USER_POOL_ID,
        Username: cognitoUserName,
        UserAttributes: [
          {
            Name: 'custom:externalId',
            Value: newUser.id, // Database user ID
          },
        ],
      });

      await cognitoClient.send(updateAttributesCommand);
      console.log(
        `Successfully updated externalId attribute for user ${email} with database ID: ${newUser.id}`
      );
    } catch (attributeError) {
      console.error(
        'Error updating user externalId attribute:',
        attributeError
      );
      // Don't fail the registration if attribute update fails
    }

    // Create user address if address data is available
    if (parsedAddress && newUser) {
      try {
        const { data: userAddress, errors: addressErrors } =
          await client.models.UserAddress.create({
            userId: newUser.id,
            addressLine1: parsedAddress.addressLine1,
            addressLine2: parsedAddress.addressLine2 || '',
            city: parsedAddress.city,
            stateProvinceCode: parsedAddress.stateProvinceCode,
            postalCode: parsedAddress.postalCode,
            countryCode: parsedAddress.countryCode || 'US',
            isDefault: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          });

        if (addressErrors) {
          console.error('Errors creating user address:', addressErrors);
        } else {
          console.log('Successfully created user address:', userAddress);
        }
      } catch (addressError) {
        // Don't fail the entire registration if address creation fails
        console.error(
          'Error creating user address (user registration will continue):',
          addressError
        );
      }
    }

    // Add user to appropriate Cognito group based on role
    console.log('===> USER ROLE', userRole);
    if (userRole === 'WelonTrust') {
      try {
        const result = await client.functions.addUserToGroupV2({
          userId: newUser.id,
          groupName: 'welontrust',
        });

        console.log(`User ${email} added to Cognito group: WELONTRUST`);
      } catch (groupError) {
        console.error('Error adding user to Cognito group:', groupError);
      }
    }

    // If this was an invite signup, mark the invite as accepted
    if (inviteData) {
      try {
        await client.models.UserInvite.update({
          id: inviteData.id,
          status: 'accepted',
          acceptedAt: new Date().toISOString(),
        });

        if (inviteData.sharedFields && inviteData.userId) {
          await client.models.LinkedAccountWithSharedFields.create({
            userId: inviteData.userId,
            linkedUserId: newUser.id,
            sharedFields: inviteData.sharedFields,
          });
        }

        console.log(
          `Marked invite ${inviteData.id} as accepted for user ${email}`
        );
      } catch (inviteUpdateError) {
        console.error('Error updating invite status:', inviteUpdateError);
        // Don't fail user creation if invite update fails
      }
    }

    // Only assign Welon Trust if the user is a Member (not if they are WelonTrust themselves)
    if (userRole === 'Member') {
      // Automatically assign a Welon Trust from the same state
      try {
        console.log(
          `Attempting to assign Welon Trust for user in state: ${address}`
        );

        const availableWelonTrusts = await fetchWelonTrustUsersByState(address);

        if (availableWelonTrusts.length > 0) {
          const selectedWelonTrust = getRandomItem(availableWelonTrusts);

          if (selectedWelonTrust) {
            await createWelonTrustAssignment(newUser.id, selectedWelonTrust);

            // Update the user with the assigned Welon Trust ID
            await client.models.User.update({
              id: newUser.id,
              assignedWelonTrustId: selectedWelonTrust.cognitoId,
            });

            console.log(
              `Successfully assigned Welon Trust ${selectedWelonTrust.firstName} ${selectedWelonTrust.lastName} to new user ${newUser.email}`
            );
          } else {
            console.warn(
              'Random selection returned null despite available Welon Trusts'
            );
          }
        } else {
          console.warn('No Welon Trust users available for assignment');
        }
      } catch (welonTrustError) {
        // Don't fail the entire registration if Welon Trust assignment fails
        console.error(
          'Error assigning Welon Trust (user registration will continue):',
          welonTrustError
        );
      }
    } else {
      console.log(
        `User ${email} is WelonTrust, skipping Welon Trust assignment`
      );
    }
  } catch (error) {
    console.error('ERROR CREATING USER', error);
    // Re-throw to fail the registration if user creation fails
    throw error;
  }

  return event;
};
