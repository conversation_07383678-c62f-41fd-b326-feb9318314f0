import {
  PreTokenGenerationTriggerEvent,
  PreTokenGenerationTriggerHandler,
} from 'aws-lambda';

export const handler: PreTokenGenerationTriggerHandler = async (
  event: PreTokenGenerationTriggerEvent
) => {
  console.log('Pre Token Generation event:', JSON.stringify(event, null, 2));

  const userAttrs = event.request.userAttributes;

  if (userAttrs['custom:externalId']) {
    console.log('EXTERNAL ID', userAttrs['custom:externalId']);
    event.response = {
      claimsOverrideDetails: {
        claimsToAddOrOverride: {
          externalId: userAttrs['custom:externalId'],
          fullName:
            `${userAttrs['given_name'] || ''} ${userAttrs['family_name'] || ''}`.trim(),
        },
      },
    };
  }

  return event;
};
