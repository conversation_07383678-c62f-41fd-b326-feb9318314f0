import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'aws-lambda';
import type { Schema } from '../../data/resource';
import { Amplify } from 'aws-amplify';
import { getAmplifyDataClientConfig } from '@aws-amplify/backend/function/runtime';
import { generateClient } from 'aws-amplify/data';
// @ts-ignore
import { env } from '$amplify/env/scheduler-framework';

// Initialize Amplify client for database operations
const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);

Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>();

// Define task types
type ScheduledTask = {
  name: string;
  frequency: 'hourly' | 'daily' | 'weekly' | 'monthly';
  handler: () => Promise<void>;
};

// Register all scheduled tasks here
const scheduledTasks: ScheduledTask[] = [
  {
    name: 'checkExpiredDocuments',
    frequency: 'daily',
    handler: async () => {
      // Get current date at midnight UTC
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      console.log(
        '[checkExpiredDocuments] Checking for documents expired before:',
        today.toISOString()
      );

      // Query for expired documents
      const expiredDocumentsResponse =
        await client.models.UserCareDocument.list({
          filter: {
            expirationDate: {
              lt: today.toISOString(),
            },
          },
        });

      const expiredDocuments = expiredDocumentsResponse.data;
      console.log(
        `[checkExpiredDocuments] Found ${expiredDocuments.length} expired documents`
      );

      // Process each expired document
      for (const document of expiredDocuments) {
        console.log(
          '[checkExpiredDocuments] Checking the documents data:',
          document
        );
        // Get the user who owns this document
        const userResponse = await client.models.User.list({
          filter: { cognitoId: { eq: document.userId } },
        });

        const user = userResponse.data?.[0];
        if (!user) {
          console.log(
            `[checkExpiredDocuments] User not found for document ${document.id}`
          );
          continue;
        }

        console.log(
          '[checkExpiredDocuments] Document owner information:',
          JSON.stringify(
            {
              userId: user.id,
              email: user.email,
              name: `${user.firstName} ${user.lastName}`,
              documentTitle: document.title,
              documentType: document.documentType,
              expirationDate: document.expirationDate,
            },
            null,
            2
          )
        );

        // TODO: Send notification to the user
        // 1. Email notification
        // 2. In-app notification
      }
    },
  },
  // Add more tasks here with different frequencies
  {
    name: 'cleanupOldData',
    frequency: 'monthly',
    handler: async () => {
      console.log('[cleanupOldData] Running monthly cleanup task');
      // Implement cleanup logic here
    },
  },
  {
    name: 'generateReports',
    frequency: 'weekly',
    handler: async () => {
      console.log('[generateReports] Running weekly report generation');
      // Implement report generation logic here
    },
  },
];

// Determine which frequency this execution belongs to
function determineFrequency(
  event: any
): 'hourly' | 'daily' | 'weekly' | 'monthly' {
  // Log the entire event to understand its structure
  console.log('Event structure for debugging:', JSON.stringify(event, null, 2));

  // Check if this is a scheduled event
  if (event['detail-type'] === 'Scheduled Event') {
    // Extract the schedule expression from the resources array if available
    const resources = event.resources || [];
    const resourceStr = resources[0] || '';

    // Log the resource string for debugging
    console.log('Resource string:', resourceStr);

    // Check for schedule patterns in the resource string
    // if (resourceStr.includes('every-1m') || resourceStr.includes('every-minute')) {
    //   return 'minutely';
    // } else if (resourceStr.includes('every-1h') || resourceStr.includes('every-hour')) {
    //   return 'hourly';
    // } else if (resourceStr.includes('every-day')) {
    //   return 'daily';
    // } else if (resourceStr.includes('every-week')) {
    //   return 'weekly';
    // } else if (resourceStr.includes('every-month')) {
    //   return 'monthly';
    // }
  }

  // Fallback to the original time-based logic
  const eventTime = new Date(event.time);
  const hour = eventTime.getUTCHours();
  const minute = eventTime.getUTCMinutes();
  const dayOfWeek = eventTime.getUTCDay();
  const dayOfMonth = eventTime.getUTCDate();
  console.log(
    '[determineFrequency]Event time - hour:',
    hour,
    'minute:',
    minute,
    'dayOfWeek:',
    dayOfWeek,
    'dayOfMonth:',
    dayOfMonth
  );

  if (hour === 0 && minute === 0) {
    if (dayOfMonth === 1) return 'monthly';
    if (dayOfWeek === 0) return 'weekly';
    return 'daily';
  }

  if (minute === 0) return 'hourly';

  return 'daily';
}

export const handler: EventBridgeHandler<string, any, void> = async (
  event,
  context
) => {
  console.log('Scheduler framework triggered at:', new Date().toISOString());
  console.log('Event details:', JSON.stringify(event, null, 2));

  try {
    // Determine which frequency this execution belongs to
    const frequency = determineFrequency(event);
    console.log(`Determined frequency: ${frequency}`);

    // Execute all tasks that match this frequency
    const tasksToRun = scheduledTasks.filter(
      task => task.frequency === frequency
    );
    console.log(
      `Running ${tasksToRun.length} tasks for frequency ${frequency}`
    );

    for (const task of tasksToRun) {
      console.log(`Executing task: ${task.name}`);
      try {
        await task.handler();
        console.log(`Task ${task.name} completed successfully`);
      } catch (error) {
        console.error(`Error executing task ${task.name}:`, error);
        // Continue with other tasks even if one fails
      }
    }

    console.log('Scheduler framework execution completed');
    return;
  } catch (error) {
    console.error('Error in scheduler framework:', error);
    throw error;
  }
};
