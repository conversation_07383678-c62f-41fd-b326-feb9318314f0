import { defineFunction } from '@aws-amplify/backend';

export const schedulerFramework = defineFunction({
  name: 'scheduler-framework',
  entry: './handler.ts',
   resourceGroupName: 'auth',
  // This is a multi-schedule function that can run at different intervals
  schedule: [
    'every day',       // Daily tasks
    'every 1h',        // Hourly tasks
    'every week',      // Weekly tasks
    'every month',     // Monthly tasks
    // 'cron(0 12 * * *)'  // Custom schedule (noon every day)
  ],
});