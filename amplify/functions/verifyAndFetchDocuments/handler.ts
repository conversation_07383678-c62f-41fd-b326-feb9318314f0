// @ts-nocheck

import type { Schema } from '../../data/resource';
import { getAmplifyDataClientConfig } from '@aws-amplify/backend/function/runtime';
import { Amplify } from 'aws-amplify';
import { generateClient } from 'aws-amplify/data';
import { env } from '$amplify/env/verifyAndFetchDocuments';

const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);

Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>();

export const handler = async event => {
  console.log(
    'Verify and fetch documents request:',
    JSON.stringify(event, null, 2)
  );

  try {
    // Extract data from event - handle both direct Lambda calls and GraphQL/AppSync calls
    const { email, token } = event.arguments || event;

    // Validate required fields
    if (!email || !token) {
      throw new Error('Missing required fields: email, token');
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new Error('Invalid email format');
    }

    // Step 1: Verify the token
    const verifyResult = await client.mutations.verifyEmailToken({
      email: decodeURIComponent(email),
      token,
      verificationType: 'emergencyNotification',
    });

    const verifyResultData = verifyResult.data;

    // Try to parse if it's a string
    let parsedVerifyResultData = verifyResultData;
    if (typeof verifyResultData === 'string') {
      try {
        parsedVerifyResultData = JSON.parse(verifyResultData);
        console.log('Parsed verify result data:', parsedVerifyResultData);
      } catch (e) {
        console.log('Failed to parse verify result data as JSON:', e);
      }
    }

    if (!parsedVerifyResultData?.success) {
      throw new Error(
        parsedVerifyResultData?.error || 'Failed to verify emergency access'
      );
    }

    // Step 2: Find the emergency contact by email
    const contactsResult = await client.models.EmergencyContact.list({
      filter: {
        emailAddress: { eq: decodeURIComponent(email) },
      },
    });

    if (!contactsResult.data || contactsResult.data.length === 0) {
      throw new Error('Emergency contact not found');
    }

    const contact = contactsResult.data[0];

    // Step 3: Fetch documents based on documentType
    const userId = contact.userId;
    const documentType = contact.documentType || 'All';

    let documents = [];

    // Fetch documents based on documentType
    if (documentType === 'DeadDocument' || documentType === 'All') {
      const deadDocumentsResult = await client.models.Document.list({
        filter: {
          owner: { beginsWith: userId },
        },
      });

      if (deadDocumentsResult.data) {
        documents = [...documents, ...deadDocumentsResult.data];
      }
    }

    if (documentType === 'CareDocument' || documentType === 'All') {
      const careDocumentsResult = await client.models.UserCareDocument.list({
        filter: {
          userId: { eq: userId },
        },
      });

      if (careDocumentsResult.data) {
        documents = [...documents, ...careDocumentsResult.data];
      }
    }

    // Return the combined results
    return {
      success: true,
      contact,
      documents,
    };
  } catch (error) {
    console.error('Error verifying and fetching documents:', error);

    return {
      success: false,
      error: error.message || 'Failed to verify and fetch documents',
    };
  }
};
