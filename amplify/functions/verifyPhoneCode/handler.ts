// @ts-nocheck

import type { Schema } from '../../data/resource';
import { getAmplifyDataClientConfig } from '@aws-amplify/backend/function/runtime';
import { Amplify } from 'aws-amplify';
import { generateClient } from 'aws-amplify/data';
import { env } from '$amplify/env/verifyPhoneCode';
import {
  CognitoIdentityProviderClient,
  AdminUpdateUserAttributesCommand,
  AdminGetUserCommand,
} from '@aws-sdk/client-cognito-identity-provider';

const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);

Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>({
  authMode: 'iam',
});

const cognitoClient = new CognitoIdentityProviderClient({
  region: process.env.AWS_REGION || 'us-east-1',
});

export const handler: Schema['verifyPhoneCode']['functionHandler'] =
  async event => {
    console.log(
      '===> Verify phone code event:',
      JSON.stringify(event, null, 2)
    );

    const { phoneNumber, verificationCode, email, userId } = event.arguments;
    console.log('===> Extracted parameters:', {
      phoneNumber,
      verificationCode,
      email,
      userId,
    });

    if (!phoneNumber || !verificationCode || !email || !userId) {
      console.error('===> Missing required parameters:', {
        phoneNumber: !!phoneNumber,
        verificationCode: !!verificationCode,
        email: !!email,
        userId: !!userId,
      });
      return {
        success: false,
        error:
          'Phone number, verification code, email, and user ID are required',
      };
    }

    try {
      console.log('===> Starting phone code verification process...');

      // Add a small delay to account for eventual consistency
      await new Promise(resolve => setTimeout(resolve, 1000));

      // First, let's try to get ALL tokens without any filters
      const allTokensNoFilter = await client.models.VerificationTokens.list();
      console.log(
        '===> All verification tokens:',
        JSON.stringify(allTokensNoFilter.data, null, 2)
      );

      if (allTokensNoFilter.data && allTokensNoFilter.data.length > 0) {
        // Filter manually for our email and verification type
        const tokensForEmail = allTokensNoFilter.data.filter(
          t => t.email === email && t.verificationType === 'phoneVerification'
        );

        console.log(
          '===> Tokens for email:',
          email,
          'Count:',
          tokensForEmail.length
        );

        // Filter by unused tokens
        const unusedTokens = tokensForEmail.filter(t => !t.isUsed);
        console.log('===> Unused tokens count:', unusedTokens.length);

        // Find matching token
        const matchingToken = unusedTokens.find(
          t => t.token === verificationCode
        );

        if (matchingToken) {
          console.log(
            '===> Found matching token:',
            JSON.stringify(matchingToken, null, 2)
          );

          // Additional safety check
          if (!matchingToken || !matchingToken.id) {
            console.error('===> Invalid verification token data');
            return {
              success: false,
              error: 'Invalid verification token data',
            };
          }

          // Check if token has expired
          const now = new Date();
          const expiresAt = new Date(matchingToken.expiresAt);
          console.log('===> Token expiry check:', {
            now: now.toISOString(),
            expiresAt: expiresAt.toISOString(),
            isExpired: now > expiresAt,
          });

          if (now > expiresAt) {
            console.log('===> Token has expired');
            return {
              success: false,
              error: 'Verification code has expired',
            };
          }

          // Mark token as used
          console.log('===> Marking token as used:', matchingToken.id);
          const updateResult = await client.models.VerificationTokens.update({
            id: matchingToken.id,
            isUsed: true,
            usedAt: now.toISOString(),
          });
          console.log('===> Token update result:', updateResult);

          // Use this token for Cognito update
          const token = matchingToken;

          // Update user's phone_number_verified attribute in Cognito
          try {
            const cognitoCommand = new AdminUpdateUserAttributesCommand({
              UserPoolId: env.USER_POOL_ID,
              Username: userId,
              UserAttributes: [
                {
                  Name: 'phone_number',
                  Value: phoneNumber,
                },
                {
                  Name: 'phone_number_verified',
                  Value: 'true',
                },
              ],
            });

            const cognitoResult = await cognitoClient.send(cognitoCommand);
          } catch (cognitoError) {
            console.error(
              '===> Error updating Cognito user attributes:',
              cognitoError
            );
            console.error('===> Cognito error details:', {
              name: cognitoError.name,
              message: cognitoError.message,
              stack: cognitoError.stack,
            });
            // Don't fail the entire operation if Cognito update fails
            // The token is already marked as used
          }

          console.log('===> Phone code verification completed successfully');
          return {
            success: true,
            message: 'Phone number verified successfully',
          };
        } else {
          console.log('===> No matching token found');
          return {
            success: false,
            error: 'Invalid or expired verification code',
          };
        }
      } else {
        console.log('===> No tokens found in database');
        return {
          success: false,
          error: 'Invalid or expired verification code',
        };
      }
    } catch (error) {
      console.error('===> Error verifying phone code:', error);
      console.error('===> Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack,
      });
      return {
        success: false,
        error: 'Failed to verify phone number. Please try again.',
      };
    }
  };
