import { defineStorage } from '@aws-amplify/backend';

export const storage = defineStorage({
  name: 'evidenceStorage',
  access: allow => ({
    // Owner-based evidence access - only uploader and admins can access
    'public/evidence/*': [
      allow.groups(['ADMINS']).to(['read', 'write', 'delete']), // <PERSON><PERSON> can access all
      allow.groups(['WELONTRUST']).to(['read', 'write', 'delete']),
    ],
    // Document storage for Welon Trust uploads - only admins, document owners, and assigned Welon Trust users
    'public/documents/*': [
      allow.groups(['ADMINS']).to(['read', 'write', 'delete']), // <PERSON><PERSON> can access all documents
      allow.groups(['WELONTRUST']).to(['read', 'write', 'delete']), // Welon Trust can upload documents
      allow.entity('identity').to(['read', 'write', 'delete']), // Document owner can access their documents
    ],
    // Care documents storage - for user care document file uploads
    'public/care-documents/{identity}/*': [
      allow.groups(['ADMINS']).to(['read', 'write', 'delete']), // <PERSON><PERSON> can access all
      allow.entity('identity').to(['read', 'write', 'delete']), // Document owner can access their documents
    ],
    // Template sections storage - encrypted storage for template sections content
    'public/template-sections/*': [
      allow.groups(['ADMINS']).to(['read', 'write', 'delete']), // Admins can manage all template sections
    ],
  }),
});
