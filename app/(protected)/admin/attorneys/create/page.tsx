'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { AttorneyForm } from '@/components/dashboard/admin/attorney-form';
import { createAttorney } from '@/lib/data/attorneys';
import { Attorney } from '@/types/attorney-reviews';
import { toast } from 'sonner';

export default function CreateAttorneyPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const handleSave = async (attorneyData: any) => {
    setIsLoading(true);

    try {
      // Create the attorney with Amplify backend
      const newAttorneyData: Omit<Attorney, 'id'> = {
        name: attorneyData.name,
        firm: attorneyData.firm || '',
        phone: attorneyData.phone,
        email: attorneyData.email,
        address: attorneyData.address || '',
        city: attorneyData.city || '',
        state: attorneyData.state,
        zipCode: attorneyData.zipCode || '',
        specialties: attorneyData.specialties || [],
        barNumber: attorneyData.barNumber || '',
        yearsExperience: attorneyData.yearsExperience || 0,
        rating: 0, // Default rating for new attorneys
        isPreferred: attorneyData.isPreferred || false,
        isActive: true, // New attorneys start as active
      };

      await createAttorney(newAttorneyData);

      toast.success('Attorney created successfully!');

      // Navigate back to attorneys list
      router.push('/admin/attorneys');
    } catch (error) {
      console.error('Error creating attorney:', error);
      toast.error('Failed to create attorney. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AttorneyForm mode='create' onSave={handleSave} isLoading={isLoading} />
  );
}
