'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { AttorneyForm } from '@/components/dashboard/admin/attorney-form';
import { AttorneyFormSkeleton } from '@/components/skeletons/attorney-form-skeleton';
import {
  fetchAttorney,
  updateAttorney,
  deleteAttorney,
} from '@/lib/data/attorneys';
import { Attorney } from '@/types/attorney-reviews';
import { toast } from 'sonner';

export default function EditAttorneyPage() {
  const router = useRouter();
  const params = useParams();
  const attorneyId = params.id as string;

  const [attorney, setAttorney] = useState<Attorney | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingAttorney, setIsLoadingAttorney] = useState(true);

  // Load attorney data on component mount
  useEffect(() => {
    const loadAttorney = async () => {
      try {
        setIsLoadingAttorney(true);
        const attorneyData = await fetchAttorney(attorneyId);

        if (!attorneyData) {
          toast.error('Attorney not found');
          router.push('/admin/attorneys');
          return;
        }

        setAttorney(attorneyData);
      } catch (error) {
        console.error('Error loading attorney:', error);
        toast.error('Failed to load attorney data');
        router.push('/admin/attorneys');
      } finally {
        setIsLoadingAttorney(false);
      }
    };

    if (attorneyId) {
      loadAttorney();
    }
  }, [attorneyId, router]);

  const handleSave = async (attorneyData: any) => {
    if (!attorney) return;

    setIsLoading(true);

    try {
      // Update the attorney with Amplify backend
      const updateData: Partial<Omit<Attorney, 'id'>> = {
        name: attorneyData.name,
        firm: attorneyData.firm || '',
        phone: attorneyData.phone,
        email: attorneyData.email,
        address: attorneyData.address || '',
        city: attorneyData.city || '',
        state: attorneyData.state,
        zipCode: attorneyData.zipCode || '',
        specialties: attorneyData.specialties || [],
        barNumber: attorneyData.barNumber || '',
        yearsExperience: attorneyData.yearsExperience || 0,
        isPreferred: attorneyData.isPreferred || false,
        isActive: attorneyData.isActive !== false,
      };

      await updateAttorney(attorney.id, updateData);

      toast.success('Attorney updated successfully!');

      // Navigate back to attorneys list
      router.push('/admin/attorneys');
    } catch (error) {
      console.error('Error updating attorney:', error);
      toast.error('Failed to update attorney. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!attorney) return;

    try {
      await deleteAttorney(attorney.id);
      toast.success(`Attorney ${attorney.name} has been deleted`);

      // Navigate back to attorneys list
      router.push('/admin/attorneys');
    } catch (error) {
      console.error('Error deleting attorney:', error);
      toast.error('Failed to delete attorney. Please try again.');
    }
  };

  if (isLoadingAttorney) {
    return <AttorneyFormSkeleton />;
  }

  if (!attorney) {
    return null;
  }

  return (
    <AttorneyForm
      attorney={attorney}
      mode='edit'
      onSave={handleSave}
      onDelete={handleDelete}
      isLoading={isLoading}
    />
  );
}
