'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { AttorneyManagementTable } from '@/components/dashboard/admin/attorney-management-table';
import { Attorney } from '@/types/attorney-reviews';

export default function AttorneyManagementPage() {
  const router = useRouter();

  const handleEditAttorney = (attorney: Attorney) => {
    router.push(`/admin/attorneys/edit/${attorney.id}`);
  };

  const handleCreateAttorney = () => {
    router.push('/admin/attorneys/create');
  };

  return (
    <div className='h-full'>
      <AttorneyManagementTable
        onEdit={handleEditAttorney}
        onCreateNew={handleCreateAttorney}
      />
    </div>
  );
}
