'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import {
  ArrowLeft,
  BarChart3,
  DollarSign,
  Search,
  Users,
  AlertTriangle,
} from 'lucide-react';
import { SubscriptionStatus } from '@/components/billing/types';
import { fetchUsers } from '@/lib/data/users';
import { User } from '@/types/account';

// Temporary interfaces for admin page
interface AdminBillingStats {
  totalRevenue: number;
  monthlyRevenue: number;
  activeSubscriptions: number;
  totalUsers: number;
}

interface Subscription {
  id: string;
  userId: string;
  tier: string;
  status: SubscriptionStatus;
  amount: number;
  startDate: string;
  nextBillingDate: string;
}

interface Transaction {
  id: string;
  userId: string;
  userName: string;
  userEmail: string;
  amount: number;
  status: string;
  date: string;
  description: string;
}

export default function AdminBillingPage() {
  const router = useRouter();
  const [stats, setStats] = useState<AdminBillingStats | null>(null);
  const [subscriptions, setSubscriptions] = useState<
    (Subscription & { userName: string; userEmail: string })[]
  >([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  // Pagination state
  const [subscriptionsPage, setSubscriptionsPage] = useState(1);
  const [transactionsPage, setTransactionsPage] = useState(1);
  const subscriptionsPerPage = 30;
  const transactionsPerPage = 30;

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Fetch users first
        const usersData = await fetchUsers();
        setUsers(usersData);

        // Fetch subscriptions and stats
        const subscriptionsResponse = await fetch('/api/admin/subscriptions');
        if (subscriptionsResponse.ok) {
          const subscriptionsData = await subscriptionsResponse.json();
          setStats(subscriptionsData.stats);

          // Enrich subscriptions with user data
          const enrichedSubscriptions = subscriptionsData.subscriptions.map(
            (sub: any) => {
              const user = usersData.find(
                u => u.cognitoId === sub.cognitoId || u.id === sub.userId
              );
              return {
                ...sub,
                userName: user ? user.name : 'Unknown User',
                userEmail: user ? user.email : 'Unknown Email',
              };
            }
          );
          setSubscriptions(enrichedSubscriptions);
        } else {
          console.error('Failed to fetch subscriptions');
        }

        // Fetch transactions
        const transactionsResponse = await fetch('/api/admin/transactions');
        if (transactionsResponse.ok) {
          const transactionsData = await transactionsResponse.json();

          // Set transactions without user enrichment
          setTransactions(transactionsData.transactions);
        } else {
          console.error('Failed to fetch transactions');
        }
      } catch (error) {
        console.error('Error fetching admin data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    setSubscriptionsPage(1); // Reset to first page when searching
  };

  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
    setSubscriptionsPage(1); // Reset to first page when filtering
  };

  const filteredSubscriptions = subscriptions.filter(sub => {
    // Apply search filter
    const matchesSearch =
      searchQuery === '' ||
      sub.userName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      sub.userEmail.toLowerCase().includes(searchQuery.toLowerCase()) ||
      sub.id.toLowerCase().includes(searchQuery.toLowerCase());

    // Apply status filter
    const matchesStatus = statusFilter === 'all' || sub.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Pagination logic for subscriptions
  const totalSubscriptionsPages = Math.ceil(
    filteredSubscriptions.length / subscriptionsPerPage
  );
  const paginatedSubscriptions = filteredSubscriptions.slice(
    (subscriptionsPage - 1) * subscriptionsPerPage,
    subscriptionsPage * subscriptionsPerPage
  );

  // Pagination logic for transactions
  const totalTransactionsPages = Math.ceil(
    transactions.length / transactionsPerPage
  );
  const paginatedTransactions = transactions.slice(
    (transactionsPage - 1) * transactionsPerPage,
    transactionsPage * transactionsPerPage
  );

  // Helper function to get visible page numbers for pagination
  const getVisiblePages = (currentPage: number, totalPages: number) => {
    const pages: (number | string)[] = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      if (currentPage <= 3) {
        for (let i = 1; i <= 4; i++) {
          pages.push(i);
        }
        pages.push('...');
        pages.push(totalPages);
      } else if (currentPage >= totalPages - 2) {
        pages.push(1);
        pages.push('...');
        for (let i = totalPages - 3; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        pages.push(1);
        pages.push('...');
        for (let i = currentPage - 1; i <= currentPage + 1; i++) {
          pages.push(i);
        }
        pages.push('...');
        pages.push(totalPages);
      }
    }

    return pages;
  };

  // Pagination handlers
  const handleSubscriptionsPageChange = (page: number) => {
    setSubscriptionsPage(page);
  };

  const handleTransactionsPageChange = (page: number) => {
    setTransactionsPage(page);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getStatusBadge = (status: SubscriptionStatus) => {
    switch (status) {
      case 'Active':
        return <Badge className='bg-green-100 text-green-800'>Active</Badge>;
      case 'Canceled':
        return <Badge variant='secondary'>Canceled</Badge>;
      case 'Past Due':
        return <Badge variant='destructive'>Past Due</Badge>;
      case 'Inactive':
        return <Badge variant='outline'>Inactive</Badge>;
      case 'Incomplete':
        return <Badge variant='destructive'>Incomplete</Badge>;
      case 'Trialing':
        return <Badge variant='outline'>Trialing</Badge>;
      default:
        return <Badge variant='outline'>Unknown</Badge>;
    }
  };

  if (isLoading) {
    return (
      <div className='container mx-auto py-8 px-4'>
        <div className='flex items-center justify-center h-64'>
          <p className='text-muted-foreground'>
            Loading billing information...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className='flex justify-between items-center mb-6'>
        <h1 className='text-3xl font-geologica font-semibold'>
          Billing Management
        </h1>
        <Button variant='outline' onClick={() => router.push('/admin')}>
          <ArrowLeft className='mr-2 h-4 w-4' />
          Back to Admin
        </Button>
      </div>

      <div className='grid  md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8'>
        <Card>
          <CardHeader className='pb-2'>
            <CardTitle className='text-sm font-medium text-muted-foreground'>
              Total Active Subscriptions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='flex items-center'>
              <Users className='h-5 w-5 text-blue-2157c mr-2' />
              <div className='text-2xl font-bold'>
                {stats?.activeSubscriptions || 0}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='pb-2'>
            <CardTitle className='text-sm font-medium text-muted-foreground'>
              Total Revenue
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='flex items-center'>
              <DollarSign className='h-5 w-5 text-green-2010c mr-2' />
              <div className='text-2xl font-bold'>
                {formatCurrency(stats?.totalRevenue || 0)}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='pb-2'>
            <CardTitle className='text-sm font-medium text-muted-foreground'>
              Revenue This Month
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='flex items-center'>
              <BarChart3 className='h-5 w-5 text-blue-500 mr-2' />
              <div className='text-2xl font-bold'>
                {formatCurrency(stats?.monthlyRevenue || 0)}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='pb-2'>
            <CardTitle className='text-sm font-medium text-muted-foreground'>
              Delinquent Accounts
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='flex items-center'>
              <AlertTriangle className='h-5 w-5 text-destructive mr-2' />
              <div className='text-2xl font-bold'>{stats?.totalUsers || 0}</div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue='subscriptions' className='mb-8'>
        <TabsList className='mb-4'>
          <TabsTrigger value='subscriptions'>Subscriptions</TabsTrigger>
          <TabsTrigger value='transactions'>Transactions</TabsTrigger>
        </TabsList>

        <TabsContent value='subscriptions'>
          <Card>
            <CardHeader>
              <CardTitle>Subscription Management</CardTitle>
              <CardDescription>
                View and manage user subscriptions.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='flex flex-col md:flex-row gap-4 mb-6'>
                <div className='relative flex-1'>
                  <Search className='absolute left-2 top-2.5 h-4 w-4 text-muted-foreground' />
                  <Input
                    placeholder='Search by name, email, or ID...'
                    className='pl-8'
                    value={searchQuery}
                    onChange={handleSearchChange}
                  />
                </div>
                <div className='w-full md:w-64'>
                  <Select
                    value={statusFilter}
                    onValueChange={handleStatusFilterChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Filter by status' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='all'>All Statuses</SelectItem>
                      <SelectItem value='Active'>Active</SelectItem>
                      <SelectItem value='Canceled'>Canceled</SelectItem>
                      <SelectItem value='Suspended'>Suspended</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className='rounded-md border'>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>User</TableHead>
                      <TableHead>Plan</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Next Billing</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {paginatedSubscriptions.length === 0 ? (
                      <TableRow>
                        <TableCell
                          colSpan={5}
                          className='text-center py-6 text-muted-foreground'
                        >
                          No subscriptions found
                        </TableCell>
                      </TableRow>
                    ) : (
                      paginatedSubscriptions.map(subscription => (
                        <TableRow key={subscription.id}>
                          <TableCell>
                            <div>
                              <div className='font-medium'>
                                {subscription.userName}
                              </div>
                              <div className='text-sm text-muted-foreground'>
                                {subscription.userEmail}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>{subscription.tier}</TableCell>
                          <TableCell>
                            {formatCurrency(subscription.amount)}
                          </TableCell>
                          <TableCell>
                            {subscription.nextBillingDate
                              ? new Date(
                                  subscription.nextBillingDate
                                ).toLocaleDateString()
                              : 'N/A'}
                          </TableCell>
                          <TableCell>
                            {getStatusBadge(subscription.status)}
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination for subscriptions */}
              {totalSubscriptionsPages > 1 && (
                <div className='mt-4 flex justify-center'>
                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious
                          onClick={() =>
                            handleSubscriptionsPageChange(
                              Math.max(1, subscriptionsPage - 1)
                            )
                          }
                          className={`w-auto px-3 ${
                            subscriptionsPage === 1
                              ? 'pointer-events-none opacity-50'
                              : 'cursor-pointer'
                          }`}
                        />
                      </PaginationItem>

                      {getVisiblePages(
                        subscriptionsPage,
                        totalSubscriptionsPages
                      ).map((page, index) => (
                        <PaginationItem key={index}>
                          {page === '...' ? (
                            <PaginationEllipsis />
                          ) : (
                            <PaginationLink
                              onClick={() =>
                                handleSubscriptionsPageChange(page as number)
                              }
                              isActive={subscriptionsPage === page}
                              className='cursor-pointer'
                            >
                              {page}
                            </PaginationLink>
                          )}
                        </PaginationItem>
                      ))}

                      <PaginationItem>
                        <PaginationNext
                          onClick={() =>
                            handleSubscriptionsPageChange(
                              Math.min(
                                totalSubscriptionsPages,
                                subscriptionsPage + 1
                              )
                            )
                          }
                          className={`w-auto px-3 ${
                            subscriptionsPage === totalSubscriptionsPages
                              ? 'pointer-events-none opacity-50'
                              : 'cursor-pointer'
                          }`}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='transactions'>
          <Card>
            <CardHeader>
              <CardTitle>Transaction History</CardTitle>
              <CardDescription>View payment transactions.</CardDescription>
            </CardHeader>
            <CardContent>
              <div className='rounded-md border'>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {paginatedTransactions.length === 0 ? (
                      <TableRow>
                        <TableCell
                          colSpan={4}
                          className='text-center py-6 text-muted-foreground'
                        >
                          No transactions found
                        </TableCell>
                      </TableRow>
                    ) : (
                      paginatedTransactions.map(transaction => (
                        <TableRow key={transaction.id}>
                          <TableCell>
                            {transaction.date
                              ? new Date(transaction.date).toLocaleDateString(
                                  'en-US',
                                  {
                                    year: 'numeric',
                                    month: '2-digit',
                                    day: '2-digit',
                                  }
                                )
                              : 'N/A'}
                          </TableCell>
                          <TableCell>{transaction.description}</TableCell>
                          <TableCell>
                            {formatCurrency(transaction.amount)}
                          </TableCell>
                          <TableCell>
                            <Badge
                              className={
                                transaction.status === 'Successful'
                                  ? 'bg-green-500 text-white'
                                  : transaction.status === 'Failed'
                                    ? 'bg-red-500 text-white'
                                    : transaction.status === 'Refunded'
                                      ? 'bg-gray-500 text-white'
                                      : transaction.status === 'Processing'
                                        ? 'bg-blue-500 text-white'
                                        : 'bg-gray-400 text-white'
                              }
                            >
                              {transaction.status}
                            </Badge>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination for transactions */}
              {totalTransactionsPages > 1 && (
                <div className='mt-4 flex justify-center'>
                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious
                          onClick={() =>
                            handleTransactionsPageChange(
                              Math.max(1, transactionsPage - 1)
                            )
                          }
                          className={`w-auto px-3 ${
                            transactionsPage === 1
                              ? 'pointer-events-none opacity-50'
                              : 'cursor-pointer'
                          }`}
                        />
                      </PaginationItem>

                      {getVisiblePages(
                        transactionsPage,
                        totalTransactionsPages
                      ).map((page, index) => (
                        <PaginationItem key={index}>
                          {page === '...' ? (
                            <PaginationEllipsis />
                          ) : (
                            <PaginationLink
                              onClick={() =>
                                handleTransactionsPageChange(page as number)
                              }
                              isActive={transactionsPage === page}
                              className='cursor-pointer'
                            >
                              {page}
                            </PaginationLink>
                          )}
                        </PaginationItem>
                      ))}

                      <PaginationItem>
                        <PaginationNext
                          onClick={() =>
                            handleTransactionsPageChange(
                              Math.min(
                                totalTransactionsPages,
                                transactionsPage + 1
                              )
                            )
                          }
                          className={`w-auto px-3 ${
                            transactionsPage === totalTransactionsPages
                              ? 'pointer-events-none opacity-50'
                              : 'cursor-pointer'
                          }`}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
