/**
 * Admin Content Management Page
 *
 * Page for administrators to manage educational content.
 */

'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useEducationalContent } from '@/hooks/use-educational-content';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

import { ContentType, ContentStatus } from '@/types/education';
import { CreateContentModal } from '@/components/admin/create-content-modal';
import { EditContentModal } from '@/components/admin/edit-content-modal';

export default function AdminContentPage() {
  const { content, updateContent, deleteContent, refreshContent } =
    useEducationalContent();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<ContentStatus | 'all'>(
    'all'
  );
  const [typeFilter, setTypeFilter] = useState<ContentType | 'all'>('all');
  const [editingContent, setEditingContent] = useState<any>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  // Filter content based on search term and filters
  const filteredContent = content.filter((item: any) => {
    // Filter by search term
    const matchesSearch =
      searchTerm === '' ||
      item.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.description?.toLowerCase().includes(searchTerm.toLowerCase());

    // Filter by status
    const matchesStatus =
      statusFilter === 'all' || item.status === statusFilter;

    // Filter by type
    const matchesType = typeFilter === 'all' || item.type === typeFilter;

    return matchesSearch && matchesStatus && matchesType;
  });

  // Format date
  const formatDate = (dateString?: string | null) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Handle status change
  const handleStatusChange = async (contentId: string, newStatus: string) => {
    try {
      await updateContent(contentId, { status: newStatus as any });
      // Content will be automatically updated in the state by the hook
    } catch (error) {
      console.error('Error updating content status:', error);
    }
  };

  // Handle content created
  const handleContentCreated = () => {
    refreshContent(); // Refresh the content list after creation
  };

  // Handle content updated
  const handleContentUpdated = () => {
    // Content is automatically updated in the state by the hook
  };

  // Handle edit content
  const handleEditContent = (content: any) => {
    setEditingContent(content);
    setIsEditModalOpen(true);
  };

  // Handle delete content
  const handleDeleteContent = async (id: string) => {
    if (confirm('Are you sure you want to delete this content?')) {
      try {
        await deleteContent(id);
      } catch (error) {
        console.error('Error deleting content:', error);
      }
    }
  };

  return (
    <div className='w-full max-w-none space-y-8'>
      <div className='flex flex-col space-y-2 mr-12'>
        <h1 className='text-3xl font-bold text-[var(--foreground)]'>
          Content Management
        </h1>
        <p className='text-[var(--custom-gray-dark)]'>
          Upload, organize, and manage educational videos, articles, and
          resources for members. Control content visibility, categorization, and
          delivery throughout the user journey.
        </p>
      </div>

      <div className='flex justify-between items-center'>
        <div className='flex items-center space-x-4'>
          <Input
            placeholder='Search content...'
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            className='w-64'
          />
          <Select
            value={statusFilter}
            onValueChange={value =>
              setStatusFilter(value as ContentStatus | 'all')
            }
          >
            <SelectTrigger className='w-32'>
              <SelectValue placeholder='Status' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>All Status</SelectItem>
              <SelectItem value={ContentStatus.PUBLISHED}>Published</SelectItem>
              <SelectItem value={ContentStatus.DRAFT}>Draft</SelectItem>
              <SelectItem value={ContentStatus.ARCHIVED}>Archived</SelectItem>
            </SelectContent>
          </Select>
          <TooltipProvider>
            <Select
              value={typeFilter}
              onValueChange={value =>
                setTypeFilter(value as ContentType | 'all')
              }
            >
              <SelectTrigger className='w-32'>
                <SelectValue placeholder='Type' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>All Types</SelectItem>
                <SelectItem value={ContentType.VIDEO}>Video</SelectItem>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <SelectItem
                      value={ContentType.ARTICLE}
                      disabled
                      className='opacity-50'
                    >
                      Article (In development)
                    </SelectItem>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>In development</p>
                  </TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <SelectItem
                      value={ContentType.INFOGRAPHIC}
                      disabled
                      className='opacity-50'
                    >
                      Infographic (In development)
                    </SelectItem>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>In development</p>
                  </TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <SelectItem
                      value={ContentType.AVATAR}
                      disabled
                      className='opacity-50'
                    >
                      Avatar (In development)
                    </SelectItem>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>In development</p>
                  </TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <SelectItem
                      value={ContentType.TOOLTIP}
                      disabled
                      className='opacity-50'
                    >
                      Tooltip (In development)
                    </SelectItem>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>In development</p>
                  </TooltipContent>
                </Tooltip>
              </SelectContent>
            </Select>
          </TooltipProvider>
        </div>

        <CreateContentModal onContentCreated={handleContentCreated} />
      </div>

      <Tabs defaultValue='all'>
        <TabsList>
          <TabsTrigger value='all'>All Content</TabsTrigger>
          <TabsTrigger value='published'>Published</TabsTrigger>
          <TabsTrigger value='draft'>Drafts</TabsTrigger>
          <TabsTrigger value='archived'>Archived</TabsTrigger>
        </TabsList>

        <TabsContent value='all' className='mt-6'>
          <div className='w-full overflow-x-auto'>
            <Table className='w-full min-w-full'>
              <TableHeader>
                <TableRow>
                  <TableHead className='min-w-[200px]'>Title</TableHead>
                  <TableHead className='min-w-[120px]'>Type</TableHead>
                  <TableHead className='min-w-[150px]'>Status</TableHead>
                  <TableHead className='min-w-[140px]'>Last Updated</TableHead>
                  <TableHead className='min-w-[200px]'>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredContent.map(content => (
                  <TableRow key={content.id}>
                    <TableCell className='font-medium'>
                      {content.title}
                    </TableCell>
                    <TableCell>{content.type}</TableCell>
                    <TableCell>
                      <Select
                        value={content.status || ContentStatus.DRAFT}
                        onValueChange={value =>
                          handleStatusChange(content.id, value)
                        }
                      >
                        <SelectTrigger className='w-32'>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value={ContentStatus.DRAFT}>
                            Draft
                          </SelectItem>
                          <SelectItem value={ContentStatus.PUBLISHED}>
                            Published
                          </SelectItem>
                          <SelectItem value={ContentStatus.ARCHIVED}>
                            Archived
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </TableCell>
                    <TableCell>{formatDate(content.updatedAt)}</TableCell>
                    <TableCell>
                      <div className='flex items-center space-x-2'>
                        <Button
                          variant='outline'
                          size='sm'
                          onClick={() => handleEditContent(content)}
                        >
                          Edit
                        </Button>
                        <Button
                          variant='outline'
                          size='sm'
                          onClick={() => handleDeleteContent(content.id)}
                        >
                          Delete
                        </Button>
                        {/* <Link
                          href={`/admin/content/analytics?id=${content.id}`}
                          passHref
                        >
                          <Button variant='outline' size='sm'>
                            Analytics
                          </Button>
                        </Link> */}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}

                {filteredContent.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={5} className='text-center py-8'>
                      No content found. Try adjusting your filters.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </TabsContent>

        <TabsContent value='published' className='mt-6'>
          <div className='w-full overflow-x-auto'>
            <Table className='w-full min-w-full'>
              <TableHeader>
                <TableRow>
                  <TableHead className='min-w-[200px]'>Title</TableHead>
                  <TableHead className='min-w-[120px]'>Type</TableHead>
                  <TableHead className='min-w-[150px]'>Status</TableHead>
                  <TableHead className='min-w-[140px]'>Last Updated</TableHead>
                  <TableHead className='min-w-[200px]'>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredContent
                  .filter(content => content.status === ContentStatus.PUBLISHED)
                  .map(content => (
                    <TableRow key={content.id}>
                      <TableCell className='font-medium'>
                        {content.title}
                      </TableCell>
                      <TableCell>{content.type}</TableCell>
                      <TableCell>
                        <Select
                          value={content.status || ContentStatus.DRAFT}
                          onValueChange={value =>
                            handleStatusChange(content.id, value)
                          }
                        >
                          <SelectTrigger className='w-32'>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value={ContentStatus.DRAFT}>
                              Draft
                            </SelectItem>
                            <SelectItem value={ContentStatus.PUBLISHED}>
                              Published
                            </SelectItem>
                            <SelectItem value={ContentStatus.ARCHIVED}>
                              Archived
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </TableCell>
                      <TableCell>{formatDate(content.updatedAt)}</TableCell>
                      <TableCell>
                        <div className='flex items-center space-x-2'>
                          <Button
                            variant='outline'
                            size='sm'
                            onClick={() => handleEditContent(content)}
                          >
                            Edit
                          </Button>
                          <Button
                            variant='outline'
                            size='sm'
                            onClick={() =>
                              handleStatusChange(
                                content.id,
                                ContentStatus.ARCHIVED
                              )
                            }
                          >
                            Archive
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </div>
        </TabsContent>

        <TabsContent value='draft' className='mt-6'>
          <div className='w-full overflow-x-auto'>
            <Table className='w-full min-w-full'>
              <TableHeader>
                <TableRow>
                  <TableHead className='min-w-[200px]'>Title</TableHead>
                  <TableHead className='min-w-[120px]'>Type</TableHead>
                  <TableHead className='min-w-[150px]'>Status</TableHead>
                  <TableHead className='min-w-[140px]'>Last Updated</TableHead>
                  <TableHead className='min-w-[200px]'>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredContent
                  .filter(content => content.status === ContentStatus.DRAFT)
                  .map(content => (
                    <TableRow key={content.id}>
                      <TableCell className='font-medium'>
                        {content.title}
                      </TableCell>
                      <TableCell>{content.type}</TableCell>
                      <TableCell>
                        <Select
                          value={content.status || ContentStatus.DRAFT}
                          onValueChange={value =>
                            handleStatusChange(content.id, value)
                          }
                        >
                          <SelectTrigger className='w-32'>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value={ContentStatus.DRAFT}>
                              Draft
                            </SelectItem>
                            <SelectItem value={ContentStatus.PUBLISHED}>
                              Published
                            </SelectItem>
                            <SelectItem value={ContentStatus.ARCHIVED}>
                              Archived
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </TableCell>
                      <TableCell>{formatDate(content.updatedAt)}</TableCell>
                      <TableCell>
                        <div className='flex items-center space-x-2'>
                          <Button
                            variant='outline'
                            size='sm'
                            onClick={() => handleEditContent(content)}
                          >
                            Edit
                          </Button>
                          <Button
                            variant='outline'
                            size='sm'
                            onClick={() =>
                              handleStatusChange(
                                content.id,
                                ContentStatus.PUBLISHED
                              )
                            }
                          >
                            Publish
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </div>
        </TabsContent>

        <TabsContent value='archived' className='mt-6'>
          <div className='w-full overflow-x-auto'>
            <Table className='w-full min-w-full'>
              <TableHeader>
                <TableRow>
                  <TableHead className='min-w-[200px]'>Title</TableHead>
                  <TableHead className='min-w-[120px]'>Type</TableHead>
                  <TableHead className='min-w-[150px]'>Status</TableHead>
                  <TableHead className='min-w-[140px]'>Last Updated</TableHead>
                  <TableHead className='min-w-[200px]'>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredContent
                  .filter(content => content.status === ContentStatus.ARCHIVED)
                  .map(content => (
                    <TableRow key={content.id}>
                      <TableCell className='font-medium'>
                        {content.title}
                      </TableCell>
                      <TableCell>{content.type}</TableCell>
                      <TableCell>
                        <Select
                          value={content.status || ContentStatus.DRAFT}
                          onValueChange={value =>
                            handleStatusChange(content.id, value)
                          }
                        >
                          <SelectTrigger className='w-32'>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value={ContentStatus.DRAFT}>
                              Draft
                            </SelectItem>
                            <SelectItem value={ContentStatus.PUBLISHED}>
                              Published
                            </SelectItem>
                            <SelectItem value={ContentStatus.ARCHIVED}>
                              Archived
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </TableCell>
                      <TableCell>{formatDate(content.updatedAt)}</TableCell>
                      <TableCell>
                        <div className='flex items-center space-x-2'>
                          <Button
                            variant='outline'
                            size='sm'
                            onClick={() =>
                              handleStatusChange(
                                content.id,
                                ContentStatus.DRAFT
                              )
                            }
                          >
                            Restore
                          </Button>
                          <Button
                            variant='destructive'
                            size='sm'
                            onClick={() => handleDeleteContent(content.id)}
                          >
                            Delete
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </div>
        </TabsContent>
      </Tabs>

      {/* Edit Content Modal */}
      <EditContentModal
        contentItem={editingContent}
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        onContentUpdated={handleContentUpdated}
      />
    </div>
  );
}
