'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';

import {
  Users,
  FileText,
  AlertTriangle,
  BarChart3,
  CreditCard,
  Settings,
  PieChart,
  Scale,
  MessageSquare,
  CheckSquare,
} from 'lucide-react';

export default function AdminDashboardPage() {
  const router = useRouter();

  const adminModules = [
    {
      title: 'User Management',
      description: 'Create, edit, and manage user accounts and permissions',
      icon: <Users className='h-8 w-8 text-[var(--eggplant)]' />,
      path: '/admin/users',
      color: 'bg-[var(--eggplant)]/10',
    },
    // {
    //   title: 'Role Management',
    //   description: 'Manage roles, subroles, and permission assignments',
    //   icon: <Settings className='h-8 w-8 text-purple-600' />,
    //   path: '/admin/roles',
    //   color: 'bg-purple-600/10',
    // },
    {
      title: 'Template Management',
      description: 'Manage document templates and legal updates',
      icon: <FileText className='h-8 w-8 text-[var(--tangerine)]' />,
      path: '/admin/templates',
      color: 'bg-[var(--tangerine)]/10',
    },
    {
      title: 'Interview Builder',
      description: 'Create and manage member interview questions',
      icon: <MessageSquare className='h-8 w-8 text-[var(--berry)]' />,
      path: '/admin/interview-builder',
      color: 'bg-[var(--berry)]/10',
    },
    // {
    //   title: 'Attorney Management',
    //   description: 'Manage state-licensed attorneys for document review',
    //   icon: <Scale className='h-8 w-8 text-amber-600' />,
    //   path: '/admin/attorneys',
    //   color: 'bg-amber-600/10',
    // },
    // {
    //   title: 'Legal Updates',
    //   description: 'Track and manage legal changes affecting templates',
    //   icon: <AlertTriangle className='h-8 w-8 text-amber-500' />,
    //   path: '/admin/legal-updates',
    //   color: 'bg-amber-500/10',
    // },
    // {
    //   title: 'Quarterly Review',
    //   description: 'Manage quarterly template review process',
    //   icon: <BarChart3 className='h-8 w-8 text-purple-500' />,
    //   path: '/admin/quarterly-review',
    //   color: 'bg-purple-500/10',
    // },
    // {
    //   title: 'Document Reports',
    //   description: 'View and analyze document activity reports',
    //   icon: <PieChart className='h-8 w-8 text-indigo-500' />,
    //   path: '/admin/reports/documents',
    //   color: 'bg-indigo-500/10',
    // },
    // {
    //   title: 'Billing Management',
    //   description: 'Manage subscriptions and payment processing',
    //   icon: <CreditCard className='h-8 w-8 text-blue-500' />,
    //   path: '/admin/billing',
    //   color: 'bg-blue-500/10',
    // },
    {
      title: 'System Settings',
      description: 'Configure system-wide settings and preferences',
      icon: <Settings className='h-8 w-8 text-[var(--custom-gray-dark)]' />,
      path: '/admin/settings',
      color: 'bg-[var(--custom-gray-dark)]/10',
    },
  ];

  return (
    <>
      <div className='flex flex-col space-y-2 mr-12 mb-8'>
        <h1 className='text-3xl font-bold text-[var(--foreground)]'>
          Admin Dashboard
        </h1>
        <p className='text-[var(--custom-gray-dark)]'>
          Manage users, templates, and system settings
        </p>
      </div>

      <div className='grid md:grid-cols-2 gap-6 w-full max-w-none'>
        {adminModules.map(module => (
          <Card key={module.title} className='overflow-hidden'>
            <CardHeader className={`${module.color} p-4`}>
              <div className='flex items-center'>
                {module.icon}
                <CardTitle className='ml-3'>{module.title}</CardTitle>
              </div>
            </CardHeader>
            <CardContent className='p-4'>
              <CardDescription className='text-sm'>
                {module.description}
              </CardDescription>
            </CardContent>
            <CardFooter className='p-4 pt-0'>
              <Button
                onClick={() => router.push(module.path)}
                className='w-full'
              >
                Access
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>
    </>
  );
}
