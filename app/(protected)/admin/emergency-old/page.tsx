'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import {
  AlertCircle,
  CheckCircle2,
  FileText,
  Eye,
  Download,
  Clock,
  CheckCircle,
  XCircle,
  Calendar,
  User,
  Mail,
  FileCheck,
  Filter,
  ImageIcon,
} from 'lucide-react';
import { Headline, Subhead } from '@/components/ui/brand/typography';
import { AccessTrigger, AccessStatus } from '@/components/emergency/types';
import { useSearchParams } from 'next/navigation';
import { EvidenceManagementTable } from '@/components/dashboard/admin/evidence-management-table';
import Image from 'next/image';
import { useEvidenceSubmissions } from '@/hooks/useEvidenceSubmissions';
import { toast } from 'sonner';

// Loading fallback component
function AdminEmergencyPageLoading() {
  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-6xl mx-auto'>
        <div className='mb-8'>
          <div className='h-8 w-64 bg-gray-200 rounded animate-pulse mb-2'></div>
          <div className='h-4 w-96 bg-gray-200 rounded animate-pulse'></div>
        </div>
        <div className='h-96 bg-gray-100 rounded-lg animate-pulse'></div>
      </div>
    </div>
  );
}

// Define the EvidenceSubmission type
interface EvidenceSubmission {
  id: string;
  submitterName: string;
  submitterEmail: string;
  memberName: string;
  memberEmail: string;
  relationship: string;
  evidenceType: 'Death' | 'Incapacitation';
  status: 'pending' | 'under_review' | 'approved' | 'rejected';
  additionalInfo?: string;
  reviewedBy?: string;
  reviewedAt?: string;
  reviewNotes?: string;
  createdAt: string;
  updatedAt: string;
  fileKey?: string;
  fileName?: string;
  fileType?: string;
  fileSize?: number;
}

// Main component that uses useSearchParams
function AdminEmergencyContent() {
  const searchParams = useSearchParams();
  const typeParam = searchParams.get('type') as AccessTrigger | null;

  const [selectedSubmission, setSelectedSubmission] =
    useState<EvidenceSubmission | null>(null);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isApproveDialogOpen, setIsApproveDialogOpen] = useState(false);
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false);
  const [rejectionReason, setRejectionReason] = useState('');
  const [alert, setAlert] = useState<{
    type: 'success' | 'error';
    message: string;
  } | null>(null);
  const [fileUrl, setFileUrl] = useState<string | null>(null);
  const [fullImageDialogOpen, setFullImageDialogOpen] = useState(false);
  const [fullImageUrl, setFullImageUrl] = useState<string | null>(null);
  const { getFileUrl } = useEvidenceSubmissions();

  const { updateEvidenceStatus } = useEvidenceSubmissions();

  const handleViewSubmission = (submission: EvidenceSubmission) => {
    setSelectedSubmission(submission);
    setIsViewDialogOpen(true);
  };

  const handleApproveSubmission = (submission: EvidenceSubmission) => {
    setSelectedSubmission(submission);
    setIsApproveDialogOpen(true);
  };

  const handleRejectClick = (submission: EvidenceSubmission) => {
    setSelectedSubmission(submission);
    setIsRejectDialogOpen(true);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const isImageFile = (type: string | undefined) => {
    return type?.startsWith('image/');
  };

  const formatFileSize = (size: number | undefined) => {
    if (size! < 1024) return `${size!} bytes`;
    const i = Math.floor(Math.log(size! / 1024) / Math.log(1024));
    return `${(size! / 1024 ** i).toFixed(2)} ${['B', 'KB', 'MB', 'GB', 'TB'][i]}`;
  };

  const handleViewFullImage = async (submission: EvidenceSubmission) => {
    if (!submission.fileKey) return;

    try {
      const url = await getFileUrl(submission.fileKey);
      setFullImageUrl(url);
      setFullImageDialogOpen(true);
    } catch (err) {
      console.error('Failed to get image URL:', err);
      toast.error('Failed to load full image');
    }
  };

  const handleDownloadFile = async (submission: EvidenceSubmission) => {
    if (!submission.fileKey) return;

    try {
      const url = await getFileUrl(submission.fileKey);
      console.log('Download URL:', url);

      // Create a temporary link element to trigger download
      const a = document.createElement('a');
      a.href = url;
      a.download = submission.fileName || 'evidence-file';
      a.target = '_blank'; // Open in new tab to handle potential redirects
      document.body.appendChild(a);
      a.click();

      // Small delay before removing the element
      setTimeout(() => {
        document.body.removeChild(a);
      }, 100);

      toast.success('File download initiated');
    } catch (err) {
      console.error('Failed to download file:', err);
      toast.error(
        `Failed to download file: ${err instanceof Error ? err.message : 'Unknown error'}`
      );
    }
  };

  useEffect(() => {
    let isMounted = true;

    if (selectedSubmission?.fileKey) {
      setFileUrl(null); // Reset while loading

      getFileUrl(selectedSubmission.fileKey)
        .then(url => {
          if (isMounted) {
            setFileUrl(url);
          }
        })
        .catch(err => {
          if (isMounted) {
            console.error('Failed to get file URL:', err);
            toast.error('Failed to load file preview');
          }
        });
    } else {
      setFileUrl(null);
    }

    // Cleanup function to prevent state updates if component unmounts
    return () => {
      isMounted = false;
    };
  }, [selectedSubmission?.fileKey]); // Only depend on the fileKey, not the entire selectedSubmission object

  return (
    <div>
      <div className='max-w-6xl mx-auto'>
        <div className='mb-8'>
          <div className='flex justify-between items-start'>
            <div>
              <h1 className='text-3xl font-bold text-[var(--foreground)]'>
                Emergency Management
              </h1>
              <p className='text-[var(--custom-gray-medium)] mt-1'>
                Review emergency evidence submissions, and manage emergency
                access requests. Oversee critical member safety protocols and
                incapacitation procedures.
              </p>
            </div>
            {typeParam && (
              <div className='flex items-center'>
                <Badge
                  className={
                    typeParam === 'Death' ? 'bg-destructive' : 'bg-blue-2157c'
                  }
                >
                  <Filter className='mr-1 h-3 w-3' />
                  {typeParam === 'Death'
                    ? 'Death Certificates Only'
                    : 'Incapacitation Evidence Only'}
                </Badge>
                <Button
                  variant='ghost'
                  size='sm'
                  className='ml-2 h-8 w-8 p-0'
                  onClick={() => (window.location.href = '/admin/emergency')}
                >
                  <XCircle className='h-4 w-4' />
                </Button>
              </div>
            )}
          </div>
        </div>

        {alert && (
          <Alert
            className={`mb-6 ${
              alert.type === 'success'
                ? 'bg-green-50 text-green-800 border-green-200'
                : 'bg-destructive/10 text-destructive border-destructive/20'
            }`}
          >
            {alert.type === 'success' ? (
              <CheckCircle2 className='h-4 w-4' />
            ) : (
              <AlertCircle className='h-4 w-4' />
            )}
            <AlertTitle>
              {alert.type === 'success' ? 'Success' : 'Error'}
            </AlertTitle>
            <AlertDescription>{alert.message}</AlertDescription>
          </Alert>
        )}

        {/* Use the EvidenceManagementTable component with correct props */}
        <EvidenceManagementTable
          onView={handleViewSubmission}
          onApprove={handleApproveSubmission}
          onReject={handleRejectClick}
          typeParam={typeParam}
        />

        {/* View Submission Dialog */}
        {selectedSubmission && (
          <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
            <DialogContent className='max-w-3xl'>
              <DialogHeader>
                <DialogTitle>Evidence Submission Details</DialogTitle>
                <DialogDescription>
                  Review the submitted evidence and related information.
                </DialogDescription>
              </DialogHeader>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-6 py-4'>
                <div className='space-y-4'>
                  <div>
                    <h3 className='text-lg font-semibold mb-2'>
                      Member Information
                    </h3>
                    <div className='space-y-2'>
                      <div className='flex items-center'>
                        <User className='h-4 w-4 mr-2 text-muted-foreground' />
                        <span className='text-sm'>
                          {selectedSubmission.memberName}
                        </span>
                      </div>
                      <div className='flex items-center'>
                        <Mail className='h-4 w-4 mr-2 text-muted-foreground' />
                        <span className='text-sm'>
                          {selectedSubmission.memberEmail}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className='text-lg font-semibold mb-2'>
                      Submitter Information
                    </h3>
                    <div className='space-y-2'>
                      <div className='flex items-center'>
                        <User className='h-4 w-4 mr-2 text-muted-foreground' />
                        <span className='text-sm'>
                          {selectedSubmission.submitterName}
                        </span>
                      </div>
                      <div className='flex items-center'>
                        <Mail className='h-4 w-4 mr-2 text-muted-foreground' />
                        <span className='text-sm'>
                          {selectedSubmission.submitterEmail}
                        </span>
                      </div>
                      <div className='flex items-center'>
                        <User className='h-4 w-4 mr-2 text-muted-foreground' />
                        <span className='text-sm'>
                          Relationship: {selectedSubmission.relationship}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className='text-lg font-semibold mb-2'>
                      Submission Details
                    </h3>
                    <div className='space-y-2'>
                      <div className='flex items-center'>
                        <FileCheck className='h-4 w-4 mr-2 text-muted-foreground' />
                        <span className='text-sm'>
                          Type: {selectedSubmission.evidenceType}
                        </span>
                      </div>
                      <div className='flex items-center'>
                        <Calendar className='h-4 w-4 mr-2 text-muted-foreground' />
                        <span className='text-sm'>
                          Submitted: {formatDate(selectedSubmission.createdAt)}
                        </span>
                      </div>
                      {selectedSubmission.additionalInfo && (
                        <div className='mt-2'>
                          <h4 className='text-sm font-medium mb-1'>
                            Additional Information:
                          </h4>
                          <p className='text-sm bg-muted p-2 rounded'>
                            {selectedSubmission.additionalInfo}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className='space-y-4'>
                  <div>
                    <h3 className='text-lg font-semibold mb-2'>
                      Status Information
                    </h3>
                    <div className='space-y-2'>
                      <div className='flex items-center'>
                        <Clock className='h-4 w-4 mr-2 text-muted-foreground' />
                        <span className='text-sm'>
                          Status:{' '}
                          <Badge
                            variant={
                              selectedSubmission.status === 'approved'
                                ? 'default'
                                : selectedSubmission.status === 'rejected'
                                  ? 'destructive'
                                  : 'outline'
                            }
                            className='ml-1'
                          >
                            {selectedSubmission.status.replace('_', ' ')}
                          </Badge>
                        </span>
                      </div>
                      {selectedSubmission.reviewedBy && (
                        <>
                          <div className='flex items-center'>
                            <User className='h-4 w-4 mr-2 text-muted-foreground' />
                            <span className='text-sm'>
                              Reviewed by: {selectedSubmission.reviewedBy}
                            </span>
                          </div>
                          <div className='flex items-center'>
                            <Calendar className='h-4 w-4 mr-2 text-muted-foreground' />
                            <span className='text-sm'>
                              Reviewed on:{' '}
                              {formatDate(selectedSubmission.reviewedAt || '')}
                            </span>
                          </div>
                        </>
                      )}
                      {selectedSubmission.reviewNotes && (
                        <div className='mt-2'>
                          <h4 className='text-sm font-medium mb-1'>
                            Review Notes:
                          </h4>
                          <p className='text-sm bg-muted p-2 rounded'>
                            {selectedSubmission.reviewNotes}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>

                  <div>
                    <h3 className='text-lg font-semibold mb-2'>
                      Evidence Files
                    </h3>
                    {selectedSubmission.fileKey ? (
                      <div className='border rounded-md p-4'>
                        {isImageFile(selectedSubmission.fileType) ? (
                          <div className='space-y-3'>
                            <div className='relative w-full h-48 overflow-hidden rounded-md bg-gray-100'>
                              {fileUrl ? (
                                <div className='relative w-full h-48 flex items-center justify-center'>
                                  <img
                                    src={fileUrl}
                                    alt={
                                      selectedSubmission.fileName ||
                                      'Evidence image'
                                    }
                                    className='max-h-full max-w-full object-contain rounded-md'
                                  />
                                </div>
                              ) : (
                                <div className='flex items-center justify-center h-full'>
                                  <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600'></div>
                                </div>
                              )}
                            </div>
                            <div className='flex items-center justify-between'>
                              <div className='flex items-center space-x-2'>
                                <ImageIcon className='h-4 w-4 text-blue-500' />
                                <span className='text-sm font-medium'>
                                  {selectedSubmission.fileName}
                                </span>
                                <span className='text-xs text-muted-foreground'>
                                  ({formatFileSize(selectedSubmission.fileSize)}
                                  )
                                </span>
                              </div>
                              <Button
                                variant='outline'
                                size='sm'
                                onClick={() =>
                                  handleViewFullImage(selectedSubmission)
                                }
                              >
                                <Eye className='h-4 w-4 mr-1' />
                                View Full
                              </Button>
                            </div>
                          </div>
                        ) : (
                          <div className='flex flex-col items-center space-y-3'>
                            <FileText className='h-12 w-12 text-blue-500' />
                            <div className='text-center'>
                              <p className='text-sm font-medium'>
                                {selectedSubmission.fileName}
                              </p>
                              <p className='text-xs text-muted-foreground'>
                                {formatFileSize(selectedSubmission.fileSize)}
                              </p>
                            </div>
                            <Button
                              variant='outline'
                              size='sm'
                              onClick={() =>
                                handleDownloadFile(selectedSubmission)
                              }
                            >
                              <Download className='h-4 w-4 mr-1' />
                              Download
                            </Button>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className='border rounded-md p-4 text-center'>
                        <FileText className='h-8 w-8 mx-auto mb-2 text-muted-foreground' />
                        <p className='text-sm text-muted-foreground'>
                          No files attached to this submission.
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <DialogFooter className='mt-6'>
                {selectedSubmission.status === 'pending' && (
                  <>
                    <Button
                      variant='destructive'
                      onClick={() => {
                        setIsViewDialogOpen(false);
                        handleRejectClick(selectedSubmission);
                      }}
                    >
                      <XCircle className='mr-2 h-4 w-4' />
                      Reject
                    </Button>
                    <Button
                      onClick={() => {
                        setIsViewDialogOpen(false);
                        handleApproveSubmission(selectedSubmission);
                      }}
                    >
                      <CheckCircle className='mr-2 h-4 w-4' />
                      Approve
                    </Button>
                  </>
                )}
                {selectedSubmission.status !== 'pending' && (
                  <Button onClick={() => setIsViewDialogOpen(false)}>
                    Close
                  </Button>
                )}
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}

        {/* Approve Confirmation Dialog */}
        {selectedSubmission && (
          <Dialog
            open={isApproveDialogOpen}
            onOpenChange={setIsApproveDialogOpen}
          >
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Approve Evidence</DialogTitle>
                <DialogDescription>
                  Are you sure you want to approve this evidence submission?
                  {selectedSubmission.evidenceType === 'Death' ? (
                    <>
                      <br />
                      <br />
                      <strong>Death Certificate Verification:</strong>
                      <br />
                      • Member status will be updated to "Deceased"
                      <br />
                      • Member's login will be permanently disabled
                      <br />• This action cannot be undone
                    </>
                  ) : (
                    <>
                      <br />
                      This will grant temporary access to the member's
                      documents.
                    </>
                  )}
                </DialogDescription>
              </DialogHeader>
              <DialogFooter className='mt-6'>
                <Button
                  variant='outline'
                  onClick={() => setIsApproveDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  className=''
                  onClick={async () => {
                    try {
                      await updateEvidenceStatus(
                        selectedSubmission.id,
                        'approved'
                      );
                      if (selectedSubmission.evidenceType !== 'Death') {
                        toast.success(
                          `Evidence for ${selectedSubmission.memberName} has been approved.`
                        );
                      }
                      setIsApproveDialogOpen(false);
                      // Refresh data if needed
                    } catch (err) {
                      console.error('Failed to approve evidence:', err);
                      toast.error('Failed to approve evidence');
                    }
                  }}
                >
                  {selectedSubmission.evidenceType === 'Death'
                    ? 'Verify Death Certificate'
                    : 'Confirm Approval'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}

        {/* Reject Confirmation Dialog */}
        {selectedSubmission && (
          <Dialog
            open={isRejectDialogOpen}
            onOpenChange={setIsRejectDialogOpen}
          >
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Reject Evidence</DialogTitle>
                <DialogDescription>
                  Please provide a reason for rejecting this evidence
                  submission.
                </DialogDescription>
              </DialogHeader>
              <div className='py-4'>
                <Label htmlFor='rejectionReason'>Reason for Rejection</Label>
                <Textarea
                  id='rejectionReason'
                  value={rejectionReason}
                  onChange={e => setRejectionReason(e.target.value)}
                  placeholder='Enter the reason for rejection...'
                  className='mt-2'
                  rows={4}
                />
              </div>
              <DialogFooter>
                <Button
                  variant='outline'
                  onClick={() => setIsRejectDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  variant='destructive'
                  onClick={async () => {
                    if (!rejectionReason.trim()) {
                      toast.error('Please provide a reason for rejection.');
                      return;
                    }

                    try {
                      await updateEvidenceStatus(
                        selectedSubmission.id,
                        'rejected',
                        rejectionReason
                      );
                      toast.success(
                        `Evidence for ${selectedSubmission.memberName} has been rejected.`
                      );
                      setIsRejectDialogOpen(false);
                      setRejectionReason('');
                      // Refresh data if needed
                    } catch (err) {
                      console.error('Failed to reject evidence:', err);
                      toast.error('Failed to reject evidence');
                    }
                  }}
                  disabled={!rejectionReason.trim()}
                >
                  Confirm Rejection
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}

        {/* Full Image Dialog */}
        {selectedSubmission && (
          <Dialog
            open={fullImageDialogOpen}
            onOpenChange={setFullImageDialogOpen}
          >
            <DialogContent className='max-w-4xl'>
              <DialogHeader>
                <DialogTitle>
                  {selectedSubmission?.fileName || 'Evidence Image'}
                </DialogTitle>
              </DialogHeader>
              <div className='relative w-full h-[70vh] overflow-hidden rounded-md flex items-center justify-center'>
                {fullImageUrl && (
                  <img
                    src={fullImageUrl}
                    alt={selectedSubmission?.fileName || 'Evidence image'}
                    className='max-h-full max-w-full object-contain rounded-md'
                  />
                )}
              </div>
              <DialogFooter>
                <Button onClick={() => setFullImageDialogOpen(false)}>
                  Close
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}
      </div>
    </div>
  );
}

// Export the main component wrapped in Suspense
export default function AdminEmergencyPage() {
  return (
    <Suspense fallback={<AdminEmergencyPageLoading />}>
      <AdminEmergencyContent />
    </Suspense>
  );
}
