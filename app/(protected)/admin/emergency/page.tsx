'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  CheckCircle,
  Activity,
  FileText,
  UserX,
  UserCircle,
  PhoneCall,
} from 'lucide-react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminUsersAPI } from '@/lib/api/admin-users';
import { toast } from 'sonner';
import { EmergencyContactsTable } from '@/components/dashboard/admin/emergency-contacts-table';
import { AdminDMSUserList } from '@/app/components/dashboard/admin/admin-dms-user-list';

export type DMSFailedUser = {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  status: string;
  isDmsCheckSuccessful: boolean;
  createdAt: string;
  role: string;
  lastCheckIn?: string | null;
  nextCheckIn?: string | null;
  cognitoId?: string | null;
};

export default function AdminEmergencyPage() {
  const [activeTab, setActiveTab] = useState('failed-users');
  const [resettingUserId, setResettingUserId] = useState<string | null>(null);
  const queryClient = useQueryClient();

  const {
    data: dmsFailedUsers = [],
    isLoading: loadingFailedUsers,
    isRefetching: refetchingFailedUsers,
    refetch,
  } = useQuery({
    queryKey: ['dms-failed-users'],
    queryFn: adminUsersAPI.fetchDMSFailedUsers,
  });

  const resetDMSMutation = useMutation({
    mutationFn: ({
      userId,
      cognitoId,
    }: {
      userId: string;
      cognitoId: string;
    }) => adminUsersAPI.resetDMSCheckIn(userId, cognitoId),
    onSuccess: () => {
      toast.success('DMS check-in timer reset successfully');
      queryClient.invalidateQueries({ queryKey: ['dms-failed-users'] });
      setResettingUserId(null);
    },
    onError: error => {
      toast.error(`Failed to reset DMS timer: ${error.message}`);
      setResettingUserId(null);
    },
  });

  const handleResetDMS = async (user: DMSFailedUser) => {
    if (!user.cognitoId) {
      toast.error('Cannot reset DMS: User has no Cognito ID');
      return;
    }

    setResettingUserId(user.id);
    resetDMSMutation.mutate({
      userId: user.id,
      cognitoId: user.cognitoId,
    });
  };

  console.log('===> DMS FAILED USERS', dmsFailedUsers);

  return (
    <div className='max-w-7xl mx-auto'>
      <div className='flex flex-col space-y-2 mr-12 mb-8'>
        <h1 className='text-3xl font-bold text-[var(--foreground)]'>
          Emergency Management
        </h1>
        <p className='text-[var(--custom-gray-dark)]'>
          Monitor Wellness Checks status, review evidence submissions, and
          manage emergency access
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className='w-full'>
        <TabsList className='grid w-full grid-cols-3 mb-8'>
          <TabsTrigger value='failed-users' className='flex items-center gap-2'>
            <UserX className='h-4 w-4' />
            Failed Wellness Checks Users
          </TabsTrigger>
          <TabsTrigger
            disabled
            value='evidence'
            className='flex items-center gap-2'
          >
            <FileText className='h-4 w-4' />
            Evidence Review
          </TabsTrigger>
          <TabsTrigger value='contacts' className='flex items-center'>
            <UserCircle className='mr-2 h-4 w-4' />
            Emergency Contacts
          </TabsTrigger>
        </TabsList>

        {/* Failed DMS Users Tab */}
        <TabsContent value='failed-users'>
          <Card>
            <CardHeader className='flex justify-between items-center'>
              <div className={'flex flex-col gap-1.5'}>
                <CardTitle className='flex items-center'>
                  <UserX className='mr-2 h-5 w-5 text-primary' />
                  Users with Failed Wellness Checks
                </CardTitle>
                <CardDescription>
                  Users who have failed their Wellness Checks check-ins
                </CardDescription>
              </div>
              <Button
                variant='outline'
                size='sm'
                onClick={() => refetch()}
                disabled={refetchingFailedUsers}
              >
                <Activity className='h-4 w-4 mr-2' />
                {refetchingFailedUsers ? 'Refreshing...' : 'Refresh'}
              </Button>
            </CardHeader>
            <CardContent>
              {dmsFailedUsers.length === 0 && !loadingFailedUsers ? (
                <div className='flex justify-center items-center py-8'>
                  <div className='text-center'>
                    <CheckCircle className='h-12 w-12 text-green-600 mx-auto mb-4' />
                    <h3 className='text-lg font-semibold mb-2'>
                      All Users Passing Wellness Checks
                    </h3>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      No users currently have failed Wellness Checks.
                    </p>
                  </div>
                </div>
              ) : (
                <AdminDMSUserList
                  users={dmsFailedUsers}
                  loading={loadingFailedUsers}
                  onResetDMS={handleResetDMS}
                  resettingUserId={resettingUserId}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Evidence Review Tab */}
        {/*<TabsContent value='evidence'>*/}
        {/*  <Card>*/}
        {/*    <CardHeader>*/}
        {/*      <CardTitle>Evidence Submissions</CardTitle>*/}
        {/*      <CardDescription>*/}
        {/*        Review and approve evidence submissions for emergency access*/}
        {/*      </CardDescription>*/}
        {/*    </CardHeader>*/}
        {/*    <CardContent>*/}
        {/*      <div className='space-y-4'>*/}
        {/*        {[].map(evidence => (*/}
        {/*          <div key={evidence.id} className='p-4 border rounded-lg'>*/}
        {/*            <div className='flex justify-between items-start'>*/}
        {/*              <div className='flex-1'>*/}
        {/*                <div className='flex items-center gap-3 mb-2'>*/}
        {/*                  <h3 className='font-semibold'>*/}
        {/*                    {evidence.submitterName}*/}
        {/*                  </h3>*/}
        {/*                  {getStatusBadge(evidence.status, 'evidence')}*/}
        {/*                  <Badge variant='outline' className='text-xs'>*/}
        {/*                    {evidence.evidenceType}*/}
        {/*                  </Badge>*/}
        {/*                </div>*/}

        {/*                <div className='grid grid-cols-2 md:grid-cols-3 gap-4 text-sm mb-3'>*/}
        {/*                  <div>*/}
        {/*                    <p className='text-[var(--custom-gray-medium)]'>*/}
        {/*                      Member*/}
        {/*                    </p>*/}
        {/*                    <p className='font-medium'>{evidence.memberName}</p>*/}
        {/*                  </div>*/}
        {/*                  <div>*/}
        {/*                    <p className='text-[var(--custom-gray-medium)]'>*/}
        {/*                      Submitted*/}
        {/*                    </p>*/}
        {/*                    <p className='font-medium'>*/}
        {/*                      {formatDate(evidence.submittedAt)}*/}
        {/*                    </p>*/}
        {/*                  </div>*/}
        {/*                  <div>*/}
        {/*                    <p className='text-[var(--custom-gray-medium)]'>*/}
        {/*                      Files*/}
        {/*                    </p>*/}
        {/*                    <p className='font-medium'>*/}
        {/*                      {evidence.filesCount} files*/}
        {/*                    </p>*/}
        {/*                  </div>*/}
        {/*                </div>*/}

        {/*                {evidence.reviewedBy && (*/}
        {/*                  <div className='text-sm text-[var(--custom-gray-medium)]'>*/}
        {/*                    Reviewed by {evidence.reviewedBy} on{' '}*/}
        {/*                    {formatDate(evidence.reviewedAt!)}*/}
        {/*                  </div>*/}
        {/*                )}*/}
        {/*              </div>*/}

        {/*              <div className='flex gap-2 ml-4'>*/}
        {/*                <Button variant='outline' size='sm'>*/}
        {/*                  <Eye className='h-4 w-4 mr-1' />*/}
        {/*                  View*/}
        {/*                </Button>*/}
        {/*                {evidence.status === 'pending' && (*/}
        {/*                  <>*/}
        {/*                    <Button*/}
        {/*                      variant='default'*/}
        {/*                      size='sm'*/}
        {/*                      onClick={() => handleApproveEvidence(evidence.id)}*/}
        {/*                    >*/}
        {/*                      <CheckCircle className='h-4 w-4 mr-1' />*/}
        {/*                      Approve*/}
        {/*                    </Button>*/}
        {/*                    <Button*/}
        {/*                      variant='destructive'*/}
        {/*                      size='sm'*/}
        {/*                      onClick={() => handleRejectEvidence(evidence.id)}*/}
        {/*                    >*/}
        {/*                      <XCircle className='h-4 w-4 mr-1' />*/}
        {/*                      Reject*/}
        {/*                    </Button>*/}
        {/*                  </>*/}
        {/*                )}*/}
        {/*              </div>*/}
        {/*            </div>*/}
        {/*          </div>*/}
        {/*        ))}*/}
        {/*      </div>*/}
        {/*    </CardContent>*/}
        {/*  </Card>*/}
        {/*</TabsContent>*/}
        <TabsContent value='contacts' className='space-y-4'>
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center'>
                <PhoneCall className='mr-2 h-5 w-5 text-primary' />
                Member Emergency Contacts
              </CardTitle>
              <CardDescription>
                View and manage emergency contacts for all members. These
                contacts will be notified in case of emergencies.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <EmergencyContactsTable />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
