'use client';

import React, { useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useQuery } from '@tanstack/react-query';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
  ArrowLeft,
  MessageSquare,
  AlertTriangle,
  Settings,
  Eye,
} from 'lucide-react';
import { getInterviewWithVersion } from '@/lib/api/interview-builder-new';
import { QuestionBuilder } from '@/components/dashboard/admin/interview-builder-new/question-builder';
import { InterviewPreview } from '@/components/dashboard/admin/interview-builder-new/interview-preview';
import routes from '@/utils/routes';

export default function InterviewBuildPage() {
  const params = useParams();
  const router = useRouter();
  const interviewId = params.id as string;
  
  const [activeTab, setActiveTab] = useState('questions');
  const [error, setError] = useState<string | null>(null);

  // Fetch interview and version data
  const { 
    data: interviewData, 
    isLoading, 
    refetch 
  } = useQuery({
    queryKey: ['interview-with-version', interviewId],
    queryFn: () => getInterviewWithVersion(interviewId),
    enabled: !!interviewId,
  });



  const interview = interviewData?.interview;
  const version = interviewData?.version;

  const handleBack = () => {
    router.push(routes.admin.interviewBuilder);
  };

  const handleRefetch = () => {
    refetch();
  };

  if (isLoading) {
    return (
      <div className='max-w-7xl mx-auto'>
        <div className='space-y-6'>
          <div className='h-8 bg-gray-200 rounded animate-pulse' />
          <div className='h-64 bg-gray-200 rounded animate-pulse' />
        </div>
      </div>
    );
  }

  if (!interview) {
    return (
      <div className='max-w-7xl mx-auto'>
        <Alert variant='destructive'>
          <AlertTriangle className='h-4 w-4' />
          <AlertDescription>
            Interview not found. Please check the URL and try again.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <DndProvider backend={HTML5Backend}>
      <div className='max-w-7xl mx-auto'>
        {/* Header */}
        <div className='mb-6'>
          <div className='flex justify-between items-center space-x-4'>
            <Button variant='ghost' size='sm' onClick={handleBack}>
              <ArrowLeft className='mr-2 h-4 w-4' />
              Back to Interviews
            </Button>
            <div>
              <h1 className='text-3xl font-bold text-[var(--foreground)] flex items-center'>
                <MessageSquare className='mr-3 h-8 w-8 text-blue-600' />
                {interview.name}
              </h1>
              <div className='flex items-center space-x-4 mt-1'>
                <p className='text-[var(--custom-gray-dark)]'>
                  {interview.description || 'No description provided'}
                </p>
                {version && (
                  <Badge variant='outline'>
                    Version {version.versionNumber}
                  </Badge>
                )}
                <Badge variant={interview.isActive ? 'default' : 'secondary'}>
                  {interview.isActive ? 'Active' : 'Inactive'}
                </Badge>
              </div>
            </div>
          </div>
        </div>

        {/* Error Alert */}
        {error && (
          <Alert variant='destructive' className='mb-6'>
            <AlertTriangle className='h-4 w-4' />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Main Content */}
        <Card>
          <CardHeader>
            <CardTitle>Interview Builder</CardTitle>
            <CardDescription>
              Build and edit your interview questions using the tools below
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab} className='w-full'>
              <TabsList className='grid w-full grid-cols-2'>
                <TabsTrigger value='questions'>Question Builder</TabsTrigger>
                <TabsTrigger value='preview'>Live Preview</TabsTrigger>
              </TabsList>

              <TabsContent value='questions' className='mt-6'>
                <QuestionBuilder
                  interviewId={interviewId}
                  interview={interview}
                  version={version || null}
                  onQuestionsUpdated={handleRefetch}
                  refetch={handleRefetch}
                />
              </TabsContent>

              <TabsContent value='preview' className='mt-6'>
                <InterviewPreview
                  interview={interview}
                  version={version || null}
                />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </DndProvider>
  );
}
