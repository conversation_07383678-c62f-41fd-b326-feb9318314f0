'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { ArrowLeft, BarChart3, Download, FileText } from 'lucide-react';
import { DocumentActivityTable } from '@/components/reports/document-activity-table';
import { DocumentActivityChart } from '@/components/reports/document-activity-chart';
import { mockDocumentActivities } from '@/components/reports/types';

export default function DocumentReportsPage() {
  const router = useRouter();
  const [timePeriod, setTimePeriod] = useState<string>('month');

  // Filter activities based on time period
  const getFilteredActivities = () => {
    const now = new Date();
    let cutoffDate = new Date();

    if (timePeriod === 'week') {
      cutoffDate.setDate(now.getDate() - 7);
    } else if (timePeriod === 'month') {
      cutoffDate.setMonth(now.getMonth() - 1);
    } else if (timePeriod === 'year') {
      cutoffDate.setFullYear(now.getFullYear() - 1);
    } else {
      // All time
      return mockDocumentActivities;
    }

    return mockDocumentActivities.filter(activity => {
      const activityDate = new Date(activity.timestamp);
      return activityDate >= cutoffDate;
    });
  };

  const filteredActivities = getFilteredActivities();

  // Calculate summary statistics
  const totalDocuments = new Set(filteredActivities.map(a => a.documentId))
    .size;
  const totalUsers = new Set(filteredActivities.map(a => a.userId)).size;
  const createdCount = filteredActivities.filter(
    a => a.activityType === 'created'
  ).length;
  const updatedCount = filteredActivities.filter(
    a => a.activityType === 'updated'
  ).length;

  // Handle export report
  const handleExportReport = () => {
    alert('Exporting document activity report...');
    // In a real implementation, this would generate and download a CSV or PDF report
  };

  return (
    <div>
      <div className='flex justify-between items-center mb-6'>
        <div className='flex items-center'>
          <Button
            variant='outline'
            onClick={() => router.push('/admin')}
            className='mr-4'
          >
            <ArrowLeft className='mr-2 h-4 w-4' />
            Back to Admin
          </Button>
          <h1 className='text-3xl font-geologica font-semibold'>
            Document Activity Reports
          </h1>
        </div>

        <div className='flex items-center gap-2'>
          <Select value={timePeriod} onValueChange={setTimePeriod}>
            <SelectTrigger className='w-[180px]'>
              <SelectValue placeholder='Time Period' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='week'>Last 7 Days</SelectItem>
              <SelectItem value='month'>Last 30 Days</SelectItem>
              <SelectItem value='year'>Last Year</SelectItem>
              <SelectItem value='all'>All Time</SelectItem>
            </SelectContent>
          </Select>

          <Button variant='outline' onClick={handleExportReport}>
            <Download className='mr-2 h-4 w-4' />
            Export Report
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className='grid  sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8'>
        <Card>
          <CardHeader className='pb-2'>
            <CardTitle className='text-lg'>Total Activities</CardTitle>
            <CardDescription>All document activities</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='text-3xl font-bold'>
              {filteredActivities.length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='pb-2'>
            <CardTitle className='text-lg'>Documents</CardTitle>
            <CardDescription>Unique documents</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='text-3xl font-bold'>{totalDocuments}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='pb-2'>
            <CardTitle className='text-lg'>Created</CardTitle>
            <CardDescription>New documents</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='text-3xl font-bold'>{createdCount}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='pb-2'>
            <CardTitle className='text-lg'>Updated</CardTitle>
            <CardDescription>Document updates</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='text-3xl font-bold'>{updatedCount}</div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs for different views */}
      <Tabs defaultValue='activity' className='mb-8'>
        <TabsList className='mb-4'>
          <TabsTrigger value='activity'>
            <FileText className='mr-2 h-4 w-4' />
            Activity Log
          </TabsTrigger>
          <TabsTrigger value='analytics'>
            <BarChart3 className='mr-2 h-4 w-4' />
            Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value='activity'>
          <DocumentActivityTable
            activities={filteredActivities}
            onExport={handleExportReport}
          />
        </TabsContent>

        <TabsContent value='analytics'>
          <DocumentActivityChart
            activities={filteredActivities}
            period={timePeriod as 'week' | 'month' | 'year'}
          />
        </TabsContent>
      </Tabs>

      {/* Information Card */}
      <Card className='bg-blue-50 border-blue-100'>
        <CardHeader>
          <CardTitle className='text-blue-800'>
            About Document Activity Reports
          </CardTitle>
        </CardHeader>
        <CardContent className='text-blue-700'>
          <p className='mb-4'>
            This dashboard provides insights into document activities across the
            platform, including:
          </p>
          <ul className='list-disc list-inside space-y-2'>
            <li>Document creation and updates</li>
            <li>User interactions with documents (views, downloads, shares)</li>
            <li>Activity trends over time</li>
            <li>Distribution by document type and activity type</li>
          </ul>
          <p className='mt-4'>
            Use the filters above to adjust the time period and export reports
            for record-keeping or analysis.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
