'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  PlusCircle,
  FileEdit,
  History,
  Archive,
  AlertTriangle,
  Send,
  Filter,
  Search,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Trash2,
  RefreshCw,
  Download,
  RotateCcw,
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationN<PERSON>t,
  Pa<PERSON>ationPrevious,
  PaginationEllipsis,
} from '@/components/ui/pagination';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import type { Schema } from '@/amplify/data/resource';
import Link from 'next/link';
import routes from '@/utils/routes';
import { adminTemplatesAPI } from '@/lib/api/admin-templates';
import {
  TemplateManagementGuard,
  useCanEditTemplates,
} from '@/lib/auth/template-management-guard';

type Template = Schema['Template']['type'];
type TemplateVersion = Schema['TemplateVersion']['type'];

interface TemplateWithVersion extends Template {
  latestVersion?: TemplateVersion;
}
// Helper function to transform real template data to display format
const transformTemplateData = (template: TemplateWithVersion) => {
  // Handle templateStates array - show all states or "General" if empty
  const getStateDisplay = () => {
    if (!template.templateStates || template.templateStates.length === 0) {
      return 'General';
    }
    // Filter out null values and join with commas
    const validStates = template.templateStates.filter(
      (state): state is string => state !== null
    );
    return validStates.length > 0 ? validStates.join(', ') : 'General';
  };

  return {
    id: template.id,
    state: getStateDisplay(),
    type: template.type,
    version: template.latestVersion?.versionNumber?.toString() || '1.0',
    startDate: template.createdAt
      ? new Date(template.createdAt).toISOString().split('T')[0]
      : '',
    status: template.isActive ? 'Active' : 'Archived',
    lastUpdated: template.updatedAt
      ? new Date(template.updatedAt).toISOString().split('T')[0]
      : '',
    isAdditional: template.isAdditional ?? false,
    updatedBy: template.createdByEmail || 'Unknown',
    isDraft: template.isDraft ?? true,
    affectedUsers: 0, // Mock data since real data doesn't have this
    templateName: template.templateName,
    endDate: template.updatedAt
      ? new Date(template.updatedAt).toISOString().split('T')[0]
      : '',
  };
};

// Mock data for states and document types
const states = [
  'All States',
  'Alabama',
  'Alaska',
  'Arizona',
  'Arkansas',
  'California',
  'Colorado',
  'Connecticut',
  'Delaware',
  'Florida',
  'Georgia',
  'Hawaii',
  'Idaho',
  'Illinois',
  'Indiana',
  'Iowa',
  'Kansas',
  'Kentucky',
  'Louisiana',
  'Maine',
  'Maryland',
  'Massachusetts',
  'Michigan',
  'Minnesota',
  'Mississippi',
  'Missouri',
  'Montana',
  'Nebraska',
  'Nevada',
  'New Hampshire',
  'New Jersey',
  'New Mexico',
  'New York',
  'North Carolina',
  'North Dakota',
  'Ohio',
  'Oklahoma',
  'Oregon',
  'Pennsylvania',
  'Rhode Island',
  'South Carolina',
  'South Dakota',
  'Tennessee',
  'Texas',
  'Utah',
  'Vermont',
  'Virginia',
  'Washington',
  'West Virginia',
  'Wisconsin',
  'Wyoming',
];

const documentTypes = [
  'All Types',
  'Will',
  'Trust',
  'Healthcare POA',
  'Financial POA',
  'Advance Directive',
];

type SortField =
  | 'state'
  | 'type'
  | 'version'
  | 'lastUpdated'
  | 'propagationStatus'
  | 'affectedUsers';
type SortDirection = 'asc' | 'desc' | null;

export default function TemplatesPage() {
  const router = useRouter();
  const canEditTemplates = useCanEditTemplates();
  const queryClient = useQueryClient();
  const [selectedState, setSelectedState] = useState<string>('All States');
  const [selectedType, setSelectedType] = useState<string>('All Types');
  const [activeTab, setActiveTab] = useState<string>('active');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [sortField, setSortField] = useState<SortField | null>(null);
  const [sortDirection, setSortDirection] = useState<SortDirection>(null);

  const { data: templates = [], isLoading } = useQuery({
    queryKey: ['admin-templates'],
    queryFn: adminTemplatesAPI.listTemplatesV2,
  });

  console.log('===> TEMPLATES', templates);

  // Mutations for bulk operations
  const archiveMutation = useMutation({
    mutationFn: adminTemplatesAPI.archiveTemplates,
    onSuccess: () => {
      toast.success('Templates archived successfully');
      queryClient.invalidateQueries({ queryKey: ['admin-templates'] });
      setSelectedItems(new Set());
      setIsAllSelected(false);
      setIsAllAcrossPagesSelected(false);
    },
    onError: error => {
      console.error('Archive error:', error);
      toast.error('Failed to archive templates');
    },
  });

  const deleteMutation = useMutation({
    mutationFn: adminTemplatesAPI.deleteTemplates,
    onSuccess: () => {
      toast.success('Templates deleted successfully');
      queryClient.invalidateQueries({ queryKey: ['admin-templates'] });
      setSelectedItems(new Set());
      setIsAllSelected(false);
      setIsAllAcrossPagesSelected(false);
    },
    onError: error => {
      console.error('Delete error:', error);
      toast.error('Failed to delete templates');
    },
  });

  const exportMutation = useMutation({
    mutationFn: adminTemplatesAPI.exportTemplates,
    onSuccess: () => {
      toast.success('Templates exported successfully');
    },
    onError: error => {
      console.error('Export error:', error);
      toast.error('Failed to export templates');
    },
  });

  const restoreMutation = useMutation({
    mutationFn: adminTemplatesAPI.restoreTemplates,
    onSuccess: () => {
      toast.success('Templates restored successfully');
      queryClient.invalidateQueries({ queryKey: ['admin-templates'] });
      setSelectedItems(new Set());
      setIsAllSelected(false);
      setIsAllAcrossPagesSelected(false);
    },
    onError: error => {
      console.error('Restore error:', error);
      toast.error('Failed to restore templates');
    },
  });

  // Transform real data to display format
  const transformedTemplates = templates.map(transformTemplateData);

  // Separate active, archived, and additional templates
  const activeTemplates = transformedTemplates.filter(
    template => template.status === 'Active' && !template.isAdditional
  );
  const archivedTemplates = transformedTemplates.filter(
    template => template.status === 'Archived'
  );
  const additionalTemplates = transformedTemplates.filter(
    template => template.isAdditional && template.status === 'Active'
  );

  // Pagination state
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);

  // Bulk actions state
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [isAllSelected, setIsAllSelected] = useState<boolean>(false);
  const [isAllAcrossPagesSelected, setIsAllAcrossPagesSelected] =
    useState<boolean>(false);

  // Handle sorting
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      // Toggle direction or reset
      if (sortDirection === 'asc') {
        setSortDirection('desc');
      } else if (sortDirection === 'desc') {
        setSortField(null);
        setSortDirection(null);
      } else {
        setSortDirection('asc');
      }
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Sort function
  const sortTemplates = (templates: any[]) => {
    if (!sortField || !sortDirection) return templates;

    return [...templates].sort((a, b) => {
      let aValue = a[sortField];
      let bValue = b[sortField];

      // Handle different data types
      if (sortField === 'lastUpdated') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      } else if (sortField === 'affectedUsers') {
        aValue = Number(aValue);
        bValue = Number(bValue);
      } else if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (sortDirection === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
  };

  // Filter and search templates
  const filterAndSearchTemplates = (templates: any[]) => {
    return templates.filter(template => {
      // Handle state filtering - check if selected state is included in the template's states
      const matchesState =
        selectedState === 'All States' ||
        template.state === 'General' ||
        template.state.includes(selectedState);

      const matchesType =
        selectedType === 'All Types' || template.type === selectedType;
      const matchesSearch =
        searchQuery === '' ||
        template.state?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.type?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.updatedBy?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.templateName
          ?.toLowerCase()
          .includes(searchQuery.toLowerCase());

      return matchesState && matchesType && matchesSearch;
    });
  };

  // Apply filtering, searching, and sorting
  const allFilteredActiveTemplates = sortTemplates(
    filterAndSearchTemplates(activeTemplates)
  );
  const allFilteredArchivedTemplates = sortTemplates(
    filterAndSearchTemplates(archivedTemplates)
  );
  const allFilteredAdditionalTemplates = sortTemplates(
    filterAndSearchTemplates(additionalTemplates)
  );

  // Pagination logic
  const getTotalPages = (totalItems: number) =>
    Math.ceil(totalItems / itemsPerPage);

  const getPaginatedData = (data: any[], page: number) => {
    const startIndex = (page - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return data.slice(startIndex, endIndex);
  };

  // Get paginated data for current page
  const filteredActiveTemplates = getPaginatedData(
    allFilteredActiveTemplates,
    currentPage
  );
  const filteredArchivedTemplates = getPaginatedData(
    allFilteredArchivedTemplates,
    currentPage
  );
  const filteredAdditionalTemplates = getPaginatedData(
    allFilteredAdditionalTemplates,
    currentPage
  );

  // Get total pages for current tab
  const totalActivePages = getTotalPages(allFilteredActiveTemplates.length);
  const totalArchivedPages = getTotalPages(allFilteredArchivedTemplates.length);
  const totalAdditionalPages = getTotalPages(
    allFilteredAdditionalTemplates.length
  );

  // Reset to page 1 when filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [
    searchQuery,
    selectedState,
    selectedType,
    sortField,
    sortDirection,
    itemsPerPage,
  ]);

  // Reset to page 1 when switching tabs
  React.useEffect(() => {
    setCurrentPage(1);
  }, [activeTab]);

  // Clear selections when switching tabs or changing filters
  React.useEffect(() => {
    setSelectedItems(new Set());
    setIsAllSelected(false);
    setIsAllAcrossPagesSelected(false);
  }, [activeTab, searchQuery, selectedState, selectedType, currentPage]);

  // Handle individual item selection
  const handleItemSelect = (itemId: string, checked: boolean) => {
    const newSelected = new Set(selectedItems);
    if (checked) {
      newSelected.add(itemId);
    } else {
      newSelected.delete(itemId);
    }
    setSelectedItems(newSelected);

    // Update "select all" state
    const currentPageItems =
      activeTab === 'active'
        ? filteredActiveTemplates
        : activeTab === 'archived'
          ? filteredArchivedTemplates
          : filteredAdditionalTemplates;
    const allCurrentPageSelected = currentPageItems.every(item =>
      newSelected.has(item.id)
    );
    setIsAllSelected(allCurrentPageSelected && currentPageItems.length > 0);
  };

  // Handle select all for current page
  const handleSelectAll = (checked: boolean) => {
    const currentPageItems =
      activeTab === 'active'
        ? filteredActiveTemplates
        : activeTab === 'archived'
          ? filteredArchivedTemplates
          : filteredAdditionalTemplates;
    const newSelected = new Set(selectedItems);

    if (checked) {
      currentPageItems.forEach(item => newSelected.add(item.id));
    } else {
      currentPageItems.forEach(item => newSelected.delete(item.id));
      setIsAllAcrossPagesSelected(false);
    }

    setSelectedItems(newSelected);
    setIsAllSelected(checked);
  };

  // Handle select all across all pages
  const handleSelectAllAcrossPages = () => {
    const allItems =
      activeTab === 'active'
        ? allFilteredActiveTemplates
        : activeTab === 'archived'
          ? allFilteredArchivedTemplates
          : allFilteredAdditionalTemplates;
    const newSelected = new Set<string>();
    allItems.forEach(item => newSelected.add(item.id));

    setSelectedItems(newSelected);
    setIsAllSelected(true);
    setIsAllAcrossPagesSelected(true);
  };

  // Bulk action handlers
  const handleBulkDelete = async () => {
    if (selectedItems.size === 0) return;

    const templateIds = Array.from(selectedItems);
    const confirmMessage = `Are you sure you want to permanently delete ${templateIds.length} template(s)? This action cannot be undone.`;

    if (confirm(confirmMessage)) {
      deleteMutation.mutate(templateIds);
    }
  };

  const handleBulkArchive = async () => {
    if (selectedItems.size === 0) return;

    const templateIds = Array.from(selectedItems);
    const confirmMessage = `Are you sure you want to archive ${templateIds.length} template(s)? They will be moved to the archived templates tab.`;

    if (confirm(confirmMessage)) {
      archiveMutation.mutate(templateIds);
    }
  };

  const handleBulkExport = async () => {
    if (selectedItems.size === 0) return;

    const templateIds = Array.from(selectedItems);
    exportMutation.mutate(templateIds);
  };

  const handleBulkRestore = async () => {
    if (selectedItems.size === 0) return;

    const templateIds = Array.from(selectedItems);
    const confirmMessage = `Are you sure you want to restore ${templateIds.length} template(s)? They will be moved back to active templates.`;

    if (confirm(confirmMessage)) {
      restoreMutation.mutate(templateIds);
    }
  };

  // const handleCreateTemplate = () => {
  //   router.push('/admin/templates/edit/new');
  // };

  const handleEditTemplate = (id: string) => {
    router.push(`/admin/templates/edit/${id}`);
  };

  const handleViewHistory = (id: string) => {
    router.push(`/admin/templates/history/${id}`);
  };

  // const handlePropagateUpdate = (id: string) => {
  //   router.push(`/admin/templates/propagate?templateId=${id}`);
  // };

  const getStatusBadge = (status: string) => {
    const statusColors = {
      completed: 'bg-green-600 text-white border border-green-700',
      pending: 'bg-amber-500 text-white border border-amber-600',
      'in-progress': 'bg-blue-600 text-white border border-blue-700',
      failed: 'bg-red-600 text-white border border-red-700',
    };

    const statusIcons = {
      completed: '✓',
      pending: '⏳',
      'in-progress': '⟳',
      failed: '✗',
    };

    return (
      <span
        className={`inline-flex items-center gap-1 px-2.5 py-1 rounded-full text-xs font-semibold ${statusColors[status as keyof typeof statusColors] || 'bg-gray-600 text-white border border-gray-700'}`}
      >
        <span className='text-xs'>
          {statusIcons[status as keyof typeof statusIcons] || '•'}
        </span>
        {status}
      </span>
    );
  };

  // Get sort icon for column headers
  const getSortIcon = (field: SortField) => {
    if (sortField !== field) {
      return (
        <ArrowUpDown className='h-3 w-3 text-[var(--custom-gray-medium)]' />
      );
    }
    if (sortDirection === 'asc') {
      return <ArrowUp className='h-3 w-3 text-blue-600' />;
    }
    if (sortDirection === 'desc') {
      return <ArrowDown className='h-3 w-3 text-blue-600' />;
    }
    return <ArrowUpDown className='h-3 w-3 text-[var(--custom-gray-medium)]' />;
  };

  // Icon action button component with tooltip
  const IconActionButton = ({
    onClick,
    icon: Icon,
    label,
    variant = 'outline',
    className = '',
  }: {
    onClick: () => void;
    icon: React.ComponentType<{ className?: string }>;
    label: string;
    variant?: 'outline' | 'default' | 'destructive';
    className?: string;
  }) => (
    <Button
      variant={variant}
      size='sm'
      onClick={onClick}
      className={`h-6 w-6 p-0 min-w-0 flex-shrink-0 hover:scale-110 hover:shadow-md transition-all duration-200 ${className}`}
      title={label}
    >
      <Icon className='h-3 w-3' />
      <span className='sr-only'>{label}</span>
    </Button>
  );

  // Sortable table header component
  const SortableTableHead = ({
    field,
    children,
    className = '',
  }: {
    field: SortField;
    children: React.ReactNode;
    className?: string;
  }) => (
    <TableHead
      className={`cursor-pointer hover:bg-muted/50 select-none font-semibold py-4 ${className}`}
      onClick={() => handleSort(field)}
    >
      <div className='flex items-center gap-1'>
        {children}
        {getSortIcon(field)}
      </div>
    </TableHead>
  );

  // Bulk actions bar component
  const BulkActionsBar = () => {
    if (selectedItems.size === 0 || !canEditTemplates) return null;

    const currentPageItems =
      activeTab === 'active'
        ? filteredActiveTemplates
        : activeTab === 'archived'
          ? filteredArchivedTemplates
          : filteredAdditionalTemplates;
    const allItems =
      activeTab === 'active'
        ? allFilteredActiveTemplates
        : activeTab === 'archived'
          ? allFilteredArchivedTemplates
          : allFilteredAdditionalTemplates;
    const allCurrentPageSelected = currentPageItems.every(item =>
      selectedItems.has(item.id)
    );
    const someCurrentPageSelected = currentPageItems.some(item =>
      selectedItems.has(item.id)
    );

    return (
      <div className='bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-4 shadow-sm'>
        <div className='flex flex-col gap-3'>
          <div className='flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4'>
            <div className='flex items-center gap-4'>
              <div className='flex items-center gap-2'>
                <span className='text-sm font-semibold text-blue-900 dark:text-blue-100'>
                  {selectedItems.size} template
                  {selectedItems.size !== 1 ? 's' : ''} selected
                  {isAllAcrossPagesSelected &&
                    ` (all ${allItems.length} templates)`}
                </span>
                {someCurrentPageSelected &&
                  !allCurrentPageSelected &&
                  !isAllAcrossPagesSelected && (
                    <span className='text-xs text-white bg-blue-600 border border-blue-700 px-2 py-1 rounded-full font-medium'>
                      ⚪ Partial page selection
                    </span>
                  )}
                {allCurrentPageSelected &&
                  currentPageItems.length > 0 &&
                  !isAllAcrossPagesSelected && (
                    <span className='text-xs text-white bg-blue-600 border border-blue-700 px-2 py-1 rounded-full font-medium'>
                      ✓ All on page selected
                    </span>
                  )}
                {isAllAcrossPagesSelected && (
                  <span className='text-xs text-white bg-green-600 border border-green-700 px-2 py-1 rounded-full font-medium'>
                    ✓✓ All pages selected
                  </span>
                )}
              </div>
              <Button
                variant='ghost'
                size='sm'
                onClick={() => {
                  setSelectedItems(new Set());
                  setIsAllSelected(false);
                  setIsAllAcrossPagesSelected(false);
                }}
                className='h-8 text-xs text-blue-700 hover:text-blue-900 hover:bg-blue-100 dark:text-blue-300 dark:hover:text-blue-100 dark:hover:bg-blue-900/50'
              >
                ✕ Clear selection
              </Button>
            </div>
          </div>

          {/* Select all across pages option */}
          {allCurrentPageSelected &&
            currentPageItems.length > 0 &&
            !isAllAcrossPagesSelected &&
            allItems.length > currentPageItems.length && (
              <div className='flex items-center gap-2 text-sm'>
                <span className='text-blue-700 dark:text-blue-300'>
                  All {currentPageItems.length} items on this page are selected.
                </span>
                <Button
                  variant='link'
                  size='sm'
                  onClick={handleSelectAllAcrossPages}
                  className='h-auto p-0 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200 underline'
                >
                  Select all {allItems.length} items across all pages
                </Button>
              </div>
            )}
        </div>

        <div className='mt-3 pt-3 border-t border-border'>
          <p className='text-xs text-muted-foreground mb-3'>
            Choose an action to perform on the selected templates:
          </p>
          <div className='flex items-center gap-3 flex-wrap'>
            {(activeTab === 'active' || activeTab === 'additional') &&
              canEditTemplates && (
                <Button
                  variant='outline'
                  size='sm'
                  onClick={handleBulkArchive}
                  disabled={
                    archiveMutation.isPending || selectedItems.size === 0
                  }
                  className='h-9 text-sm font-medium border-amber-300 text-amber-700 hover:bg-amber-50 hover:border-amber-400 disabled:opacity-50 dark:border-amber-600 dark:text-amber-400 dark:hover:bg-amber-950 dark:hover:border-amber-500'
                  title='Archive all selected templates'
                >
                  {archiveMutation.isPending ? (
                    <RefreshCw className='mr-2 h-4 w-4 animate-spin' />
                  ) : (
                    <Archive className='mr-2 h-4 w-4' />
                  )}
                  {archiveMutation.isPending ? 'Archiving...' : 'Archive'}
                </Button>
              )}
            {activeTab === 'archived' && canEditTemplates && (
              <Button
                variant='outline'
                size='sm'
                onClick={handleBulkRestore}
                disabled={restoreMutation.isPending || selectedItems.size === 0}
                className='h-9 text-sm font-medium border-blue-300 text-blue-700 hover:bg-blue-50 hover:border-blue-400 disabled:opacity-50 dark:border-blue-600 dark:text-blue-400 dark:hover:bg-blue-950 dark:hover:border-blue-500'
                title='Restore selected templates to active'
              >
                {restoreMutation.isPending ? (
                  <RefreshCw className='mr-2 h-4 w-4 animate-spin' />
                ) : (
                  <RotateCcw className='mr-2 h-4 w-4' />
                )}
                {restoreMutation.isPending ? 'Restoring...' : 'Restore'}
              </Button>
            )}
            <Button
              variant='outline'
              size='sm'
              onClick={handleBulkExport}
              disabled={exportMutation.isPending || selectedItems.size === 0}
              className='h-9 text-sm font-medium border-green-300 text-green-700 hover:bg-green-50 hover:border-green-400 disabled:opacity-50 dark:border-green-600 dark:text-green-400 dark:hover:bg-green-950 dark:hover:border-green-500'
              title='Export selected templates to file'
            >
              {exportMutation.isPending ? (
                <RefreshCw className='mr-2 h-4 w-4 animate-spin' />
              ) : (
                <Download className='mr-2 h-4 w-4' />
              )}
              {exportMutation.isPending ? 'Exporting...' : 'Export'}
            </Button>
            {canEditTemplates && (
              <Button
                variant='destructive'
                size='sm'
                onClick={handleBulkDelete}
                disabled={deleteMutation.isPending || selectedItems.size === 0}
                className='h-9 text-sm font-medium disabled:opacity-50'
                title='Permanently delete selected templates'
              >
                {deleteMutation.isPending ? (
                  <RefreshCw className='mr-2 h-4 w-4 animate-spin' />
                ) : (
                  <Trash2 className='mr-2 h-4 w-4' />
                )}
                {deleteMutation.isPending ? 'Deleting...' : 'Delete'}
              </Button>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Pagination component
  const PaginationComponent = ({
    currentPage,
    totalPages,
    onPageChange,
  }: {
    currentPage: number;
    totalPages: number;
    onPageChange: (page: number) => void;
  }) => {
    const totalItems =
      activeTab === 'active'
        ? allFilteredActiveTemplates.length
        : activeTab === 'archived'
          ? allFilteredArchivedTemplates.length
          : allFilteredAdditionalTemplates.length;

    if (totalItems === 0) return null;

    const getVisiblePages = () => {
      const delta = 2;
      const range = [];
      const rangeWithDots = [];

      for (
        let i = Math.max(2, currentPage - delta);
        i <= Math.min(totalPages - 1, currentPage + delta);
        i++
      ) {
        range.push(i);
      }

      if (currentPage - delta > 2) {
        rangeWithDots.push(1, '...');
      } else {
        rangeWithDots.push(1);
      }

      rangeWithDots.push(...range);

      if (currentPage + delta < totalPages - 1) {
        rangeWithDots.push('...', totalPages);
      } else {
        rangeWithDots.push(totalPages);
      }

      return rangeWithDots;
    };

    return (
      <div className='flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mt-6'>
        <div className='flex items-center gap-4'>
          <div className='text-sm text-[var(--custom-gray-medium)]'>
            Showing {(currentPage - 1) * itemsPerPage + 1} to{' '}
            {Math.min(currentPage * itemsPerPage, totalItems)} of {totalItems}{' '}
            results
          </div>

          <div className='flex items-center gap-2'>
            <span className='text-sm text-[var(--custom-gray-medium)]'>
              Show:
            </span>
            <Select
              value={itemsPerPage.toString()}
              onValueChange={value => setItemsPerPage(Number(value))}
            >
              <SelectTrigger className='w-20 h-8'>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='5'>5</SelectItem>
                <SelectItem value='10'>10</SelectItem>
                <SelectItem value='20'>20</SelectItem>
                <SelectItem value='50'>50</SelectItem>
              </SelectContent>
            </Select>
            <span className='text-sm text-[var(--custom-gray-medium)]'>
              per page
            </span>
          </div>
        </div>

        {totalPages > 1 && (
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() => onPageChange(Math.max(1, currentPage - 1))}
                  className={
                    currentPage === 1
                      ? 'pointer-events-none opacity-50'
                      : 'cursor-pointer'
                  }
                />
              </PaginationItem>

              {getVisiblePages().map((page, index) => (
                <PaginationItem key={index}>
                  {page === '...' ? (
                    <PaginationEllipsis />
                  ) : (
                    <PaginationLink
                      onClick={() => onPageChange(page as number)}
                      isActive={currentPage === page}
                      className='cursor-pointer'
                    >
                      {page}
                    </PaginationLink>
                  )}
                </PaginationItem>
              ))}

              <PaginationItem>
                <PaginationNext
                  onClick={() =>
                    onPageChange(Math.min(totalPages, currentPage + 1))
                  }
                  className={
                    currentPage === totalPages
                      ? 'pointer-events-none opacity-50'
                      : 'cursor-pointer'
                  }
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        )}
      </div>
    );
  };

  return (
    <TemplateManagementGuard>
      <div className='max-w-7xl mx-auto'>
        <div className='flex justify-between items-center mb-6'>
          <div className='flex flex-col space-y-2 mr-12'>
            <h1 className='text-3xl font-bold text-[var(--foreground)]'>
              Template Management
            </h1>
            <p className='text-[var(--custom-gray-medium)]'>
              {canEditTemplates
                ? 'Manage legal document templates, versions, and state-specific variations. Upload new templates, configure variables, and maintain legal compliance.'
                : 'View legal document templates, versions, and state-specific variations for different U.S. states.'}
            </p>
          </div>
          {canEditTemplates && (
            <Button asChild variant='default' size='sm'>
              <Link href={routes.admin.newTemplate}>
                <PlusCircle className='mr-2 h-4 w-4' />
                Create New Template
              </Link>
            </Button>
          )}
        </div>

        <Card className='mb-8'>
          <CardHeader>
            <CardTitle>Search & Filter Templates</CardTitle>
            <CardDescription>
              Search by state, document type, or updated by user. Use filters to
              narrow down results.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className='grid  md:grid-cols-3 gap-4'>
              <div>
                <label className='text-sm font-medium mb-2 block'>Search</label>
                <div className='relative'>
                  <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[var(--custom-gray-medium)]' />
                  <Input
                    placeholder='Search by state, type, or user...'
                    value={searchQuery}
                    onChange={e => setSearchQuery(e.target.value)}
                    className='pl-10'
                  />
                </div>
              </div>
              <div>
                <label className='text-sm font-medium mb-2 block'>State</label>
                <Select value={selectedState} onValueChange={setSelectedState}>
                  <SelectTrigger>
                    <SelectValue placeholder='Select state' />
                  </SelectTrigger>
                  <SelectContent>
                    {states.map(state => (
                      <SelectItem key={state} value={state}>
                        {state}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className='text-sm font-medium mb-2 block'>
                  Document Type
                </label>
                <Select value={selectedType} onValueChange={setSelectedType}>
                  <SelectTrigger>
                    <SelectValue placeholder='Select document type' />
                  </SelectTrigger>
                  <SelectContent>
                    {documentTypes.map(type => (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            {(searchQuery ||
              selectedState !== 'All States' ||
              selectedType !== 'All Types') && (
              <div className='mt-4 flex items-center gap-2'>
                <span className='text-sm font-medium text-[var(--custom-gray-dark)]'>
                  Active filters:
                </span>
                {searchQuery && (
                  <span className='inline-flex items-center px-2.5 py-1 bg-blue-600 text-white rounded-full text-xs font-medium border border-blue-700'>
                    🔍 Search: "{searchQuery}"
                  </span>
                )}
                {selectedState !== 'All States' && (
                  <span className='inline-flex items-center px-2.5 py-1 bg-green-600 text-white rounded-full text-xs font-medium border border-green-700'>
                    📍 State: {selectedState}
                  </span>
                )}
                {selectedType !== 'All Types' && (
                  <span className='inline-flex items-center px-2.5 py-1 bg-purple-600 text-white rounded-full text-xs font-medium border border-purple-700'>
                    📄 Type: {selectedType}
                  </span>
                )}
                <Button
                  variant='outline'
                  size='sm'
                  onClick={() => {
                    setSearchQuery('');
                    setSelectedState('All States');
                    setSelectedType('All Types');
                  }}
                  className='h-6 text-xs'
                >
                  Clear all
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        <Tabs value={activeTab} onValueChange={setActiveTab} className='w-full'>
          <TabsList className='grid w-full grid-cols-3 mb-6'>
            <TabsTrigger value='active'>Active Templates</TabsTrigger>
            <TabsTrigger value='archived'>Archived Templates</TabsTrigger>
            <TabsTrigger value='additional'>Additional Templates</TabsTrigger>
          </TabsList>

          <TabsContent value='active'>
            <Card>
              <CardHeader>
                <CardTitle>Active Templates</CardTitle>
                <CardDescription>
                  Currently active document templates
                  {!isLoading &&
                    allFilteredActiveTemplates.length !==
                      activeTemplates.length &&
                    ` (${allFilteredActiveTemplates.length} of ${activeTemplates.length} matching filters)`}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <BulkActionsBar />

                {isLoading ? (
                  <div className='text-center py-8 text-[var(--custom-gray-medium)]'>
                    <RefreshCw className='h-6 w-6 animate-spin mx-auto mb-2' />
                    Loading templates...
                  </div>
                ) : filteredActiveTemplates.length === 0 ? (
                  <div className='text-center py-8 text-[var(--custom-gray-medium)]'>
                    {templates.length === 0
                      ? 'No templates found. Create your first template to get started.'
                      : 'No active templates found for the selected filters.'}
                  </div>
                ) : (
                  <div className='overflow-x-auto'>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          {canEditTemplates && (
                            <TableHead className='w-12 font-semibold py-4'>
                              <Checkbox
                                checked={isAllSelected}
                                onCheckedChange={handleSelectAll}
                                aria-label='Select all templates'
                              />
                            </TableHead>
                          )}
                          <SortableTableHead
                            field='state'
                            className='w-20 max-w-20'
                          >
                            State
                          </SortableTableHead>
                          <SortableTableHead field='type'>
                            Document Type
                          </SortableTableHead>
                          <SortableTableHead field='version'>
                            Version
                          </SortableTableHead>
                          <SortableTableHead field='lastUpdated'>
                            Last Updated
                          </SortableTableHead>
                          <TableHead className='font-semibold py-4'>
                            Created By
                          </TableHead>
                          {/*<SortableTableHead field='affectedUsers'>*/}
                          {/*  Affected Users*/}
                          {/*</SortableTableHead>*/}
                          <SortableTableHead field='affectedUsers'>
                            Status
                          </SortableTableHead>
                          <TableHead className='font-semibold py-4 w-20'>
                            Actions
                          </TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredActiveTemplates.map(template => (
                          <TableRow
                            key={template.id}
                            className='border-b border-gray-100 hover:bg-gray-50/50'
                          >
                            {canEditTemplates && (
                              <TableCell className='py-4'>
                                <Checkbox
                                  checked={selectedItems.has(template.id)}
                                  onCheckedChange={checked =>
                                    handleItemSelect(
                                      template.id,
                                      checked as boolean
                                    )
                                  }
                                  aria-label={`Select ${template.state} ${template.type} template`}
                                />
                              </TableCell>
                            )}
                            <TableCell className='py-4 w-24 max-w-24'>
                              <div className='truncate' title={template.state}>
                                {template.state}
                              </div>
                            </TableCell>
                            <TableCell className='py-4'>
                              {template.type}
                            </TableCell>
                            <TableCell className='py-4'>
                              v{template.version}
                            </TableCell>
                            <TableCell className='py-4'>
                              {template.lastUpdated}
                            </TableCell>
                            <TableCell className='py-4 text-sm text-[var(--custom-gray-dark)]'>
                              {template.updatedBy}
                            </TableCell>
                            <TableCell className='py-4 text-sm'>
                              {template.isDraft ? 'Draft' : 'Published'}
                            </TableCell>
                            <TableCell className='py-4'>
                              <div className='flex gap-1 w-20'>
                                {canEditTemplates && (
                                  <IconActionButton
                                    onClick={() =>
                                      handleEditTemplate(template.id)
                                    }
                                    icon={FileEdit}
                                    label='Edit template'
                                    variant='outline'
                                    className='hover:bg-blue-50 hover:border-blue-300'
                                  />
                                )}
                                <IconActionButton
                                  onClick={() => handleViewHistory(template.id)}
                                  icon={History}
                                  label='View history'
                                  variant='outline'
                                  className='hover:bg-gray-50 hover:border-gray-300'
                                />
                                {/* {canEditTemplates && (
                                  <IconActionButton
                                    onClick={() =>
                                      handlePropagateUpdate(template.id)
                                    }
                                    icon={Send}
                                    label='Propagate updates'
                                    variant='default'
                                    className='bg-green-600 hover:bg-green-700 border-green-600 hover:border-green-700'
                                  />
                                )} */}
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}

                {/* Pagination for Active Templates */}
                <PaginationComponent
                  currentPage={currentPage}
                  totalPages={totalActivePages}
                  onPageChange={setCurrentPage}
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value='archived'>
            <Card>
              <CardHeader>
                <CardTitle>Archived Templates</CardTitle>
                <CardDescription>
                  Previously active document templates
                  {!isLoading &&
                    allFilteredArchivedTemplates.length !==
                      archivedTemplates.length &&
                    ` (${allFilteredArchivedTemplates.length} of ${archivedTemplates.length} matching filters)`}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <BulkActionsBar />

                {isLoading ? (
                  <div className='text-center py-8 text-[var(--custom-gray-medium)]'>
                    <RefreshCw className='h-6 w-6 animate-spin mx-auto mb-2' />
                    Loading templates...
                  </div>
                ) : filteredArchivedTemplates.length === 0 ? (
                  <div className='text-center py-8 text-[var(--custom-gray-medium)]'>
                    {templates.length === 0
                      ? 'No templates found.'
                      : 'No archived templates found for the selected filters.'}
                  </div>
                ) : (
                  <div className='overflow-x-auto'>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          {canEditTemplates && (
                            <TableHead className='w-12 font-semibold py-4'>
                              <Checkbox
                                checked={isAllSelected}
                                onCheckedChange={handleSelectAll}
                                aria-label='Select all templates'
                              />
                            </TableHead>
                          )}
                          <SortableTableHead
                            field='state'
                            className='w-20 max-w-20'
                          >
                            State
                          </SortableTableHead>
                          <SortableTableHead field='type'>
                            Document Type
                          </SortableTableHead>
                          <SortableTableHead field='version'>
                            Version
                          </SortableTableHead>
                          <TableHead className='font-semibold py-4'>
                            Start Date
                          </TableHead>
                          <TableHead className='font-semibold py-4'>
                            End Date
                          </TableHead>
                          <TableHead className='font-semibold py-4'>
                            Status
                          </TableHead>
                          <TableHead className='font-semibold py-4 w-20'>
                            Actions
                          </TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredArchivedTemplates.map(template => (
                          <TableRow
                            key={template.id}
                            className='border-b border-gray-100 hover:bg-gray-50/50'
                          >
                            {canEditTemplates && (
                              <TableCell className='py-4'>
                                <Checkbox
                                  checked={selectedItems.has(template.id)}
                                  onCheckedChange={checked =>
                                    handleItemSelect(
                                      template.id,
                                      checked as boolean
                                    )
                                  }
                                  aria-label={`Select ${template.state} ${template.type} template`}
                                />
                              </TableCell>
                            )}
                            <TableCell className='py-4 w-20 max-w-20'>
                              <div className='truncate' title={template.state}>
                                {template.state}
                              </div>
                            </TableCell>
                            <TableCell className='py-4'>
                              {template.type}
                            </TableCell>
                            <TableCell className='py-4'>
                              v{template.version}
                            </TableCell>
                            <TableCell className='py-4'>
                              {template.startDate}
                            </TableCell>
                            <TableCell className='py-4'>
                              {template.endDate}
                            </TableCell>
                            <TableCell className='py-4'>
                              {template.status}
                            </TableCell>
                            <TableCell className='py-4'>
                              <div className='flex gap-1 w-20'>
                                <IconActionButton
                                  onClick={() => handleViewHistory(template.id)}
                                  icon={History}
                                  label='View history'
                                  variant='outline'
                                />
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}

                {/* Pagination for Archived Templates */}
                <PaginationComponent
                  currentPage={currentPage}
                  totalPages={totalArchivedPages}
                  onPageChange={setCurrentPage}
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value='additional'>
            <Card>
              <CardHeader>
                <CardTitle>Additional Templates</CardTitle>
                <CardDescription>
                  Supplementary document templates marked as additional
                  {!isLoading &&
                    allFilteredAdditionalTemplates.length !==
                      additionalTemplates.length &&
                    ` (${allFilteredAdditionalTemplates.length} of ${additionalTemplates.length} matching filters)`}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <BulkActionsBar />

                {isLoading ? (
                  <div className='text-center py-8 text-[var(--custom-gray-medium)]'>
                    <RefreshCw className='h-6 w-6 animate-spin mx-auto mb-2' />
                    Loading templates...
                  </div>
                ) : filteredAdditionalTemplates.length === 0 ? (
                  <div className='text-center py-8 text-[var(--custom-gray-medium)]'>
                    {templates.length === 0
                      ? 'No templates found.'
                      : 'No additional templates found for the selected filters.'}
                  </div>
                ) : (
                  <div className='overflow-x-auto'>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className='w-12 font-semibold py-4'>
                            <Checkbox
                              checked={isAllSelected}
                              onCheckedChange={handleSelectAll}
                              aria-label='Select all templates'
                            />
                          </TableHead>
                          <SortableTableHead
                            field='state'
                            className='w-20 max-w-20'
                          >
                            State
                          </SortableTableHead>
                          <SortableTableHead field='type'>
                            Document Type
                          </SortableTableHead>
                          <SortableTableHead field='version'>
                            Version
                          </SortableTableHead>
                          <SortableTableHead field='lastUpdated'>
                            Last Updated
                          </SortableTableHead>
                          <TableHead className='font-semibold py-4'>
                            Created By
                          </TableHead>
                          <SortableTableHead field='affectedUsers'>
                            Status
                          </SortableTableHead>
                          <TableHead className='font-semibold py-4 w-20'>
                            Actions
                          </TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredAdditionalTemplates.map(template => (
                          <TableRow
                            key={template.id}
                            className='border-b border-gray-100 hover:bg-gray-50/50'
                          >
                            <TableCell className='py-4'>
                              <Checkbox
                                checked={selectedItems.has(template.id)}
                                onCheckedChange={checked =>
                                  handleItemSelect(
                                    template.id,
                                    checked as boolean
                                  )
                                }
                                aria-label={`Select ${template.state} ${template.type} template`}
                              />
                            </TableCell>
                            <TableCell className='py-4 w-20 max-w-20'>
                              <div className='truncate' title={template.state}>
                                {template.state}
                              </div>
                            </TableCell>
                            <TableCell className='py-4'>
                              {template.type}
                            </TableCell>
                            <TableCell className='py-4'>
                              v{template.version}
                            </TableCell>
                            <TableCell className='py-4'>
                              {template.lastUpdated}
                            </TableCell>
                            <TableCell className='py-4 text-sm text-[var(--custom-gray-medium)]'>
                              {template.updatedBy}
                            </TableCell>
                            <TableCell className='py-4 text-sm'>
                              {template.isDraft ? 'Draft' : 'Published'}
                            </TableCell>
                            <TableCell className='py-4'>
                              <div className='flex gap-1 w-20'>
                                <IconActionButton
                                  onClick={() =>
                                    handleEditTemplate(template.id)
                                  }
                                  icon={FileEdit}
                                  label='Edit template'
                                  variant='outline'
                                  className='hover:bg-blue-50 hover:border-blue-300'
                                />
                                <IconActionButton
                                  onClick={() => handleViewHistory(template.id)}
                                  icon={History}
                                  label='View history'
                                  variant='outline'
                                  className='hover:bg-gray-50 hover:border-gray-300'
                                />
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}

                {/* Pagination for Additional Templates */}
                <PaginationComponent
                  currentPage={currentPage}
                  totalPages={totalAdditionalPages}
                  onPageChange={setCurrentPage}
                />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </TemplateManagementGuard>
  );
}
