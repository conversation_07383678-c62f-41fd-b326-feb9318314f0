'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  cleanTipTapHTML,
  htmlToCleanText,
  stripHTML,
  getTextContentLength,
} from '@/lib/utils/html-cleaner';
// @ts-ignore
import html2pdf from 'html2pdf.js';

export default function TestHTMLCleanerPage() {
  const [inputHTML, setInputHTML] =
    useState(`<p style="text-align: justify"><strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <PERSON>, <PERSON>ma User2 , a resident of ___________ County, Alaska , being of sound mind and disposing memory, do hereby make, publish and declar this instrument as and for my Last Will and Testament, hereby revoking any and all other wills and other testamentary instruments heretofore made by me.</strong></p>

<p style="text-align: center"><strong>LAST WILL AND TESTAMENT</strong></p>

<p style="text-align: justify">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; (A)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; My spouse is _________________, and all references in this Will to â€œmy spouseâ€ are to such named individual.&nbsp; I have no children.&nbsp;</p>

<p style="text-align: justify">Â Â Â Â Â Â References to my â€œheirsâ€ mean those persons, other than creditors, who would take my property under the laws of the state in which I die.</p>`);

  const [results, setResults] = useState({
    cleaned: '',
    plainText: '',
    stripped: '',
    textLength: 0,
  });

  const handleClean = () => {
    const cleaned = cleanTipTapHTML(inputHTML);
    const plainText = htmlToCleanText(inputHTML);
    const stripped = stripHTML(inputHTML);
    const textLength = getTextContentLength(inputHTML);

    setResults({
      cleaned,
      plainText,
      stripped,
      textLength,
    });
  };

  return (
    <div className='container mx-auto p-6 space-y-6'>
      <div className='flex items-center justify-between'>
        <h1 className='text-3xl font-bold'>HTML Cleaner Test</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Input HTML</CardTitle>
        </CardHeader>
        <CardContent>
          <Textarea
            value={inputHTML}
            onChange={e => setInputHTML(e.target.value)}
            rows={8}
            className='font-mono text-sm'
            placeholder='Paste your HTML here...'
          />
          <Button onClick={handleClean} className='mt-4'>
            Clean HTML
          </Button>
        </CardContent>
      </Card>

      {results.cleaned && (
        <>
          <Card>
            <CardHeader>
              <CardTitle>Cleaned HTML</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='bg-gray-50 p-4 rounded border'>
                <pre className='text-sm whitespace-pre-wrap font-mono'>
                  {results.cleaned}
                </pre>
              </div>
              <div className='mt-4 text-sm text-gray-600'>
                Original length: {inputHTML.length} characters
                <br />
                Cleaned length: {results.cleaned.length} characters
                <br />
                Reduction:{' '}
                {(
                  ((inputHTML.length - results.cleaned.length) /
                    inputHTML.length) *
                  100
                ).toFixed(1)}
                %
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Plain Text (Structured)</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='bg-gray-50 p-4 rounded border'>
                <pre className='text-sm whitespace-pre-wrap'>
                  {results.plainText}
                </pre>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Stripped HTML (Text Only)</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='bg-gray-50 p-4 rounded border'>
                <p className='text-sm'>{results.stripped}</p>
              </div>
              <div className='mt-4 text-sm text-gray-600'>
                Text content length: {results.textLength} characters
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Visual Comparison</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div>
                  <h4 className='font-semibold mb-2'>
                    Original HTML Rendered:
                  </h4>
                  <div
                    className='border p-4 bg-white'
                    dangerouslySetInnerHTML={{ __html: inputHTML }}
                  />
                </div>
                <div>
                  <h4 className='font-semibold mb-2'>Cleaned HTML Rendered:</h4>
                  <div
                    className='border p-4 bg-white'
                    dangerouslySetInnerHTML={{ __html: results.cleaned }}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}
