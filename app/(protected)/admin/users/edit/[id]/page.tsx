'use client';

import React, { useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { UserForm } from '@/components/dashboard/admin/user-form';
import { UserFormSkeleton } from '@/components/skeletons/user-form-skeleton';
import {
  updateUser,
  handleChangeUserRole,
  getUserByIdWithWelonTrustUsers,
} from '@/lib/data/users';
import { toast } from 'sonner';
import { useQuery } from '@tanstack/react-query';
import { assignWelonToUserDocuments } from '@/lib/api/documents';
import {
  createWelonTrustAssignment,
  deleteWelonTrustAssignment,
  updateWelonTrustAssignment,
} from '@/lib/data/assignments';

export default function EditUserPage() {
  const params = useParams();
  const router = useRouter();
  const userId = params.id as string;
  const [isLoading, setIsLoading] = useState(false);

  const {
    data: { user, welonTrustUsers } = { user: null, welonTrustUsers: [] },
    isLoading: loading,
  } = useQuery({
    queryKey: ['user-with-welon-trust', userId],
    queryFn: () => getUserByIdWithWelonTrustUsers(userId),
  });

  // Helper function to determine the assignment action type
  const getAssignmentActionType = (userData: any, currentAssignment: any) => {
    if (userData.role !== 'Member') {
      return 'REMOVE_FOR_NON_MEMBER';
    }

    if (
      userData.assignedWelonTrust === 'invite_new' &&
      userData.newWelonTrustEmail
    ) {
      return 'INVITE_NEW';
    }

    if (
      userData.assignedWelonTrust &&
      userData.assignedWelonTrust !== 'invite_new'
    ) {
      return 'ASSIGN_EXISTING';
    }

    if (!userData.assignedWelonTrust && currentAssignment) {
      return 'REMOVE_ASSIGNMENT';
    }

    return 'NO_CHANGE';
  };

  // Helper function to handle inviting new Welon Trust
  const handleInviteNewWelonTrust = async (
    userData: any,
    currentAssignment: any
  ) => {
    if (currentAssignment) {
      await deleteWelonTrustAssignment(userId, currentAssignment.id);
    }

    // TODO ADD HERE LOGIC FOR INVITE WELONS
    // await createWelonTrustAssignment(userId, {
    //   welonTrustUserId: 'pending-invitation',
    //   welonTrustName: 'Pending Invitation',
    //   welonTrustEmail: userData.newWelonTrustEmail,
    //   assignedBy: 'current-admin-user', // TODO: Get actual admin user ID
    // });
    //
    // toast.success(
    //   `User updated! Invitation sent to ${userData.newWelonTrustEmail}`
    // );
  };

  // Helper function to handle assigning existing Welon Trust
  const handleAssignExistingWelonTrust = async (
    userData: any,
    currentAssignment: any
  ) => {
    const selectedWelonTrust = welonTrustUsers.find(
      wt => wt.id === userData.assignedWelonTrust
    );

    console.log('===> SELECTED WELON', selectedWelonTrust);

    if (!selectedWelonTrust) {
      toast.success('User updated successfully!');
      return;
    }

    if (currentAssignment) {
      await updateWelonTrustAssignment(currentAssignment.id, {
        welonTrustUserId: selectedWelonTrust.id,
        welonTrustName: selectedWelonTrust.name,
        welonTrustEmail: selectedWelonTrust.email,
        welonTrustCognitoId: selectedWelonTrust.cognitoId,
        status: 'pending',
      });
    } else {
      await createWelonTrustAssignment(userId, {
        welonTrustUserId: selectedWelonTrust.id,
        welonTrustName: selectedWelonTrust.name,
        welonTrustEmail: selectedWelonTrust.email,
        welonTrustCognitoId: selectedWelonTrust.cognitoId!,
        assignedBy: 'current-admin-user',
      });
    }

    await updateUser(userId, {
      assignedWelonTrustId: selectedWelonTrust.cognitoId,
    });

    await assignWelonToUserDocuments(userId, selectedWelonTrust.cognitoId!);

    toast.success(
      `User updated! ${selectedWelonTrust.name} assigned as Welon Trust.`
    );
  };

  // Helper function to remove Welon Trust assignment
  const handleRemoveWelonTrustAssignment = async (
    userId: string,
    assignmentId: string
  ) => {
    await deleteWelonTrustAssignment(userId, assignmentId);
    toast.success('User updated! Welon Trust assignment removed.');
  };

  const handleSave = async (userData: any) => {
    if (!user) return;

    setIsLoading(true);
    console.log('===> USER DATA', userData);

    try {
      // Update the user basic information
      // Note: role and subrole are handled by handleChangeUserRole
      const updatedUserData = {
        name: userData.name,
        email: userData.email,
        // Keep existing status and journeyStatus unless specifically changed
        status: user.status,
        journeyStatus: user.journeyStatus,
      };

      await handleChangeUserRole(userData.role, userId, userData.subrole);
      console.log('===> SUCCESSFULLY CHANGED ROLE');
      await updateUser(userId, updatedUserData);
      console.log('===> SUCCESSFULLY UPDATED USER');

      // Handle Welon Trust assignment changes using switch case
      const currentAssignment = user.assignedWelonTrust;
      const actionType = getAssignmentActionType(userData, currentAssignment);

      switch (actionType) {
        case 'INVITE_NEW':
          await handleInviteNewWelonTrust(userData, currentAssignment);
          break;

        case 'ASSIGN_EXISTING':
          await handleAssignExistingWelonTrust(userData, currentAssignment);
          break;

        case 'REMOVE_ASSIGNMENT':
          if (currentAssignment) {
            await handleRemoveWelonTrustAssignment(
              userId,
              currentAssignment.id
            );
          }
          break;

        case 'REMOVE_FOR_NON_MEMBER':
          if (user.assignedWelonTrust) {
            await deleteWelonTrustAssignment(
              userId,
              user.assignedWelonTrust.id
            );
          }
          toast.success('User updated successfully!');
          break;

        case 'NO_CHANGE':
        default:
          toast.success('User updated successfully!');
          break;
      }

      // Handle subscription activation if requested
      if (userData.activateSubscription && userData.subscriptionPlan) {
        try {
          const response = await fetch('/api/admin/subscription/activate', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'x-cognito-id': 'admin-user', // TODO: Get actual admin cognito ID from auth context
            },
            body: JSON.stringify({
              userId: userId,
              cognitoId: user.cognitoId,
              plan: userData.subscriptionPlan,
              amount:
                userData.subscriptionType === 'trial'
                  ? 0
                  : userData.subscriptionPlan === 'BASIC'
                    ? 1000
                    : 2000,
              currency: 'usd',
              trialDays: userData.subscriptionTrialDays || 31,
            }),
          });

          if (response.ok) {
            const result = await response.json();
            const subscriptionTypeText =
              userData.subscriptionType === 'trial' ? 'trial' : 'paid';
            toast.success(
              `User updated! ${userData.subscriptionPlan} ${subscriptionTypeText} subscription activated successfully.`
            );
          } else {
            const error = await response.json();
            toast.error(
              `User updated, but subscription activation failed: ${error.error}`
            );
          }
        } catch (error) {
          console.error('Error activating subscription:', error);
          toast.error(
            'User updated, but subscription activation failed. Please try again.'
          );
        }
      }

      // Navigate back to users list
      router.push('/admin/users');
    } catch (error) {
      console.error('Error updating user:', error);
      toast.error('Failed to update user. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (loading) {
    return <UserFormSkeleton />;
  }

  if (!user) {
    return (
      <div className='flex items-center justify-center h-64'>
        <div className='text-lg text-red-600'>User not found</div>
      </div>
    );
  }

  return (
    <UserForm
      mode='edit'
      user={user}
      onSave={handleSave}
      isLoading={isLoading}
    />
  );
}
