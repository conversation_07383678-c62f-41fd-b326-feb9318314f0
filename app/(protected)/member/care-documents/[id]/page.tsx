'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useQuery } from '@tanstack/react-query';
import { careDocumentTemplatesAPI } from '@/lib/api/care-documents';
import { userCareDocumentsAPI } from '@/lib/api/user-care-documents';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  FileText,
  X,
  CheckCircle,
  AlertTriangle,
  Plus,
  Trash2,
  Edit,
  Clock,
  ChevronDown as ChevronDownIcon,
  ExternalLink,
  ArrowLeft,
  Phone,
} from 'lucide-react';
import { ConfirmationDialog } from '@/components/ui/confirmation-dialog';
import { DatePicker } from '@/components/ui/date-picker';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';

// Types for the care document structure
import { useCareDocumentFiles } from '@/hooks/useCareDocumentFiles';
import routes from '@/utils/routes';
import { validatePhone } from '@/utils/questionValidation';

interface Question {
  id: string;
  text: string;
  type: 'text' | 'file' | 'textarea' | 'select' | 'radio' | 'date' | 'phone';
  required: boolean;
  placeholder?: string;
  helpText?: string;
  options?: any[];
  allowMultiple?: boolean;
  order: number;
}

interface CareDocument {
  id: string;
  title: string;
  icon: string;
  questions: Question[];
  description: string;
  allowMultiple: boolean;
  reviewFrequency: 'monthly' | 'quarterly' | 'semiAnnually' | 'annually';
}

interface FormAnswer {
  questionId: string;
  values: (string | File | StoredFile)[];
}

interface FormAnswers {
  [questionId: string]: FormAnswer;
}

interface FormErrors {
  [questionId: string]: string;
}

interface FormattedAnswer {
  questionId: string;
  questionText: string;
  answer: string;
  fileUrl: string;
  answeredAt: string;
}

interface StoredFileInformation {
  questionId: string;
  key: string;
  name: string;
  type: string;
  size: number;
}

interface StoredFile {
  url: string;
  name: string;
  type: string;
  size: number;
  isStoredFile: boolean;
  key: string;
}

interface AnswerPreview {
  questionText: string;
  answerText: string;
}

const CareDocumentPage = () => {
  const params = useParams();
  const router = useRouter();
  const templateId = params.id as string;

  // Form state
  const [answers, setAnswers] = useState<FormAnswers>({});
  const [errors, setErrors] = useState<FormErrors>({});
  const [showForm, setShowForm] = useState(false);
  const [editingDocumentId, setEditingDocumentId] = useState<string | null>(
    null
  );
  const { getFileUrl } = useCareDocumentFiles();

  // Fetch the template
  const {
    data: careDocumentTemplate,
    isLoading: isLoadingCareDocumentTemplate,
    error: templateError,
  } = useQuery({
    queryKey: ['member-care-document-template', templateId],
    queryFn: () => careDocumentTemplatesAPI.getCareDocumentTemplate(templateId),
  });

  // Fetch saved documents for this template
  const {
    data: savedDocuments = [],
    isLoading: isLoadingSavedDocuments,
    error: savedDocumentsError,
    refetch: refetchSavedDocuments,
  } = useQuery({
    queryKey: ['user-care-documents', templateId],
    queryFn: () =>
      userCareDocumentsAPI.getUserCareDocumentsByTemplateId(templateId),
  });

  useEffect(() => {
    if (!savedDocuments.length && !isLoadingSavedDocuments) {
      setShowForm(true);
    }
  }, [savedDocuments, isLoadingSavedDocuments]);

  const handleBack = () => {
    router.push(routes.member.careDocuments);
  };

  // Get answer values for a question
  const getAnswerForQuestion = useCallback(
    (questionId: string): (string | File | StoredFile)[] => {
      const answer = answers[questionId];
      return answer?.values || [''];
    },
    [answers]
  );

  // Update answer values for a question
  const updateAnswer = useCallback(
    (questionId: string, values: (string | File | StoredFile)[]) => {
      setAnswers(prev => ({
        ...prev,
        [questionId]: {
          questionId,
          values: values, // Don't filter out empty strings here to preserve form state
        },
      }));

      // Clear error when user starts typing/selecting
      if (errors[questionId]) {
        setErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors[questionId];
          return newErrors;
        });
      }
    },
    [errors]
  );

  // Add multiple answer
  const addMultipleAnswer = useCallback(
    (questionId: string) => {
      const currentValues = getAnswerForQuestion(questionId);
      updateAnswer(questionId, [...currentValues, '']);
    },
    [getAnswerForQuestion, updateAnswer]
  );

  // Remove multiple answer
  const removeMultipleAnswer = useCallback(
    (questionId: string, index: number) => {
      const currentValues = getAnswerForQuestion(questionId);
      const newValues = currentValues.filter((_, i) => i !== index);
      // Ensure we always have at least one empty field
      // Check if the file being removed is a stored file (has a key)
      const fileToRemove = currentValues[index];
      if (
        typeof fileToRemove === 'object' &&
        fileToRemove !== null &&
        'isStoredFile' in fileToRemove &&
        fileToRemove.key
      ) {
        // Add the file key to the list of deleted files
        setDeletedFiles(prev => [...prev, fileToRemove.key as string]);
      }
      updateAnswer(questionId, newValues.length > 0 ? newValues : ['']);
    },
    [getAnswerForQuestion, updateAnswer]
  );

  // State to track files that have been deleted by the user
  const [deletedFiles, setDeletedFiles] = useState<string[]>([]);

  // Validate required fields and return errors if any
  const validateRequiredFields = useCallback(() => {
    const newErrors: FormErrors = {};
    let hasErrors = false;

    if (careDocumentTemplate?.questions) {
      careDocumentTemplate.questions.forEach(question => {
        if (question && question.required) {
          const answerValues = getAnswerForQuestion(question.id);
          const hasValidAnswer = answerValues.some(
            value => value !== '' && value !== undefined && value !== null
          );

          if (!hasValidAnswer) {
            newErrors[question.id] = 'This field is required';
            hasErrors = true;
          }
        }

        // Additional validation for phone numbers
        if (question && question.type === 'phone') {
          const answerValues = getAnswerForQuestion(question.id);
          answerValues.forEach(value => {
            if (value && typeof value === 'string' && value !== '+1') {
              const phoneValidation = validatePhone(value, {
                required: question.required ?? false,
              });
              if (!phoneValidation.isValid) {
                newErrors[question.id] =
                  phoneValidation.errorMessage || 'Invalid phone number';
                hasErrors = true;
              }
            }
          });
        }
      });
    }

    return { hasErrors, newErrors };
  }, [careDocumentTemplate, getAnswerForQuestion]);

  // Format answers for the UserCareDocument model
  const formatAnswers = useCallback((): (FormattedAnswer | undefined)[] => {
    return Object.values(answers)
      .map(answer => {
        if (careDocumentTemplate && careDocumentTemplate.questions) {
          const question = careDocumentTemplate.questions.find(
            q => q && q.id && q.id === answer.questionId
          );

          return {
            questionId: answer.questionId,
            questionText: question?.text || '',
            answer: Array.isArray(answer.values)
              ? answer.values.filter(v => typeof v === 'string').join('|||') // Use a special separator that's unlikely to appear in answers
              : '',
            fileUrl: '',
            answeredAt: new Date().toISOString(),
          };
        }

        return;
      })
      .filter(Boolean);
  }, [answers, careDocumentTemplate]);

  // Collect files that need to be uploaded and stored file information
  const collectFileData = useCallback(() => {
    const filesToUpload: { questionId: string; file: File }[] = [];

    // Collect stored file information to preserve in answers
    const storedFiles: StoredFileInformation[] = [];

    Object.entries(answers).forEach(([questionId, answer]) => {
      if (answer.values) {
        answer.values.forEach(value => {
          if (value instanceof File) {
            filesToUpload.push({
              questionId,
              file: value,
            });
          } else if (
            typeof value === 'object' &&
            value !== null &&
            'isStoredFile' in value
          ) {
            // Collect stored file information
            storedFiles.push({
              questionId,
              key: value.key,
              name: value.name,
              type: value.type,
              size: value.size,
            });
          }
        });
      }
    });

    return { filesToUpload, storedFiles };
  }, [answers]);

  // Add file information to formatted answers
  const addFileInfoToAnswers = useCallback(
    (
      formattedAnswers: (FormattedAnswer | undefined)[],
      storedFiles: StoredFileInformation[]
    ) => {
      return formattedAnswers.map(answer => {
        const filesForThisQuestion = storedFiles.filter(
          file => answer && file.questionId === answer.questionId
        );

        if (filesForThisQuestion.length > 0) {
          return {
            ...answer,
            fileKeys: filesForThisQuestion.map(file => file.key),
            fileNames: filesForThisQuestion.map(file => file.name),
            fileTypes: filesForThisQuestion.map(file => file.type),
            fileSizes: filesForThisQuestion.map(file => file.size),
            // For backward compatibility
            fileUrl: filesForThisQuestion[0].key,
          };
        }

        return answer;
      });
    },
    []
  );

  // Reset form state after successful save
  const resetFormState = useCallback(() => {
    setAnswers({});
    setEditingDocumentId(null);
    setShowForm(false);
    setDeletedFiles([]); // Reset deleted files
    refetchSavedDocuments();
  }, [refetchSavedDocuments]);

  // Handle save function
  const handleSave = useCallback(async () => {
    console.log('=== SAVING LIVING DOCUMENT ANSWERS ===');
    console.log('Template ID:', templateId);
    console.log('Document Title:', careDocumentTemplate?.title);
    console.log('All Answers:', answers);
    console.log('Deleted Files:', deletedFiles);

    // Validate required fields
    const { hasErrors, newErrors } = validateRequiredFields();
    if (hasErrors) {
      setErrors(newErrors);
      return;
    }

    try {
      // Format answers for the UserCareDocument model
      const formattedAnswers = formatAnswers();

      // Collect files that need to be uploaded and stored file information
      const { filesToUpload, storedFiles } = collectFileData();

      // Add file information to formatted answers
      const formattedAnswersWithFiles = addFileInfoToAnswers(
        formattedAnswers,
        storedFiles
      );

      let expirationDate;
      if (careDocumentTemplate?.reviewFrequency) {
        const expirationDateObj = new Date();
        const reviewFrequencyMap = {
          monthly: 30,
          quarterly: 90,
          semiAnnually: 180,
          annually: 365,
        };
        expirationDateObj.setDate(
          expirationDateObj.getDate() +
            reviewFrequencyMap[careDocumentTemplate.reviewFrequency]
        );
        expirationDate = expirationDateObj.toISOString();
      } else {
        const expirationDateObj = new Date();
        expirationDate = expirationDateObj.toISOString();
      }

      if (editingDocumentId) {
        // Update existing document
        await userCareDocumentsAPI.updateUserCareDocument(editingDocumentId, {
          answers: formattedAnswersWithFiles,
          files: filesToUpload,
          deletedFileKeys: deletedFiles, // Pass the deleted file keys
          expirationDate: expirationDate,
        });
      } else {
        // Create new document
        await userCareDocumentsAPI.createUserCareDocument({
          templateId: templateId,
          title: careDocumentTemplate?.title || 'Untitled Document',
          documentType: careDocumentTemplate?.documentType || 'Other',
          answers: formattedAnswers,
          files: filesToUpload,
          expirationDate: expirationDate,
        });
      }

      // Reset form state after successful save
      resetFormState();
    } catch (error) {
      console.error('Error saving care document:', error);
      // Handle error (show error message to user)
    }
  }, [
    answers,
    templateId,
    careDocumentTemplate,
    validateRequiredFields,
    formatAnswers,
    collectFileData,
    addFileInfoToAnswers,
    editingDocumentId,
    deletedFiles,
    resetFormState,
  ]);

  // Update specific answer in multiple answers
  const updateMultipleAnswer = useCallback(
    (questionId: string, index: number, value: string | File | StoredFile) => {
      const currentValues = getAnswerForQuestion(questionId);
      const newValues = [...currentValues];
      newValues[index] = value;
      updateAnswer(questionId, newValues);
    },
    [getAnswerForQuestion, updateAnswer]
  );

  // File upload handlers
  const handleFileSelect = useCallback(
    (
      questionId: string,
      index: number,
      event: React.ChangeEvent<HTMLInputElement>
    ) => {
      const file = event.target.files?.[0];
      if (file) {
        // Validate file type
        const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png'];
        if (!allowedTypes.includes(file.type)) {
          setErrors(prev => ({
            ...prev,
            [questionId]: 'Please select a PDF, JPEG, or PNG file.',
          }));
          return;
        }

        // Validate file size (max 10MB)
        if (file.size > 10 * 1024 * 1024) {
          setErrors(prev => ({
            ...prev,
            [questionId]: 'File size must be less than 10MB.',
          }));
          return;
        }

        updateMultipleAnswer(questionId, index, file);
      }
    },
    [updateMultipleAnswer]
  );

  const handleFileDrop = useCallback(
    (
      questionId: string,
      index: number,
      event: React.DragEvent<HTMLDivElement>
    ) => {
      event.preventDefault();
      const file = event.dataTransfer.files[0];
      if (file) {
        // Validate file type
        const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png'];
        if (!allowedTypes.includes(file.type)) {
          setErrors(prev => ({
            ...prev,
            [questionId]: 'Please select a PDF, JPEG, or PNG file.',
          }));
          return;
        }

        // Validate file size (max 10MB)
        if (file.size > 10 * 1024 * 1024) {
          setErrors(prev => ({
            ...prev,
            [questionId]: 'File size must be less than 10MB.',
          }));
          return;
        }

        updateMultipleAnswer(questionId, index, file);
      }
    },
    [updateMultipleAnswer]
  );

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  const removeFile = useCallback(
    (questionId: string, index: number) => {
      const currentValues = getAnswerForQuestion(questionId);
      const newValues = [...currentValues];

      // Check if the file being removed is a stored file (has a key)
      const fileToRemove = currentValues[index];
      if (
        typeof fileToRemove === 'object' &&
        fileToRemove !== null &&
        'isStoredFile' in fileToRemove &&
        fileToRemove.key
      ) {
        // Add the file key to the list of deleted files
        setDeletedFiles(prev => [...prev, fileToRemove.key as string]);
      }

      newValues[index] = ''; // Clear the file but keep the slot
      updateAnswer(questionId, newValues);
    },
    [getAnswerForQuestion, updateAnswer]
  );

  // Handle edit document
  const handleEditDocument = useCallback(
    async (documentId: string) => {
      try {
        const document =
          await userCareDocumentsAPI.getUserCareDocumentById(documentId);

        // Convert the saved answers to the form format
        const formAnswers: FormAnswers = {};

        if (document.answers) {
          for (const answer of document.answers.filter(answer => !!answer)) {
            // Find the question to check if it allows multiple answers
            const question = careDocumentTemplate?.questions
              ? careDocumentTemplate?.questions.find(
                  q => q && q.id === answer.questionId
                )
              : undefined;

            // If the question allows multiple answers and there's a separator in the answer,
            // split it into an array of values
            let values: (string | File | StoredFile)[] = [answer.answer || ''];
            if (
              question?.allowMultiple &&
              answer.answer &&
              answer.answer.includes('|||')
            ) {
              values = answer.answer.split('|||');
            }

            // For file type questions, add file metadata to the form state
            if (
              question?.type === 'file' &&
              answer.fileKeys &&
              answer.fileKeys.length > 0
            ) {
              // Create an array of promises for getting file URLs
              const filePromises = answer.fileKeys.map(async (key, index) => {
                const url = await getFileUrl(key as string);

                return {
                  url: url,
                  name: answer.fileNames?.[index] || 'Unknown file',
                  type: answer.fileTypes?.[index] || '',
                  size: answer.fileSizes?.[index] || 0,
                  isStoredFile: true, // Flag to identify this is a stored file, not a File object
                  key: key || '',
                };
              });

              // Resolve all promises before setting the values
              values = await Promise.all(filePromises);
            }

            formAnswers[answer.questionId] = {
              questionId: answer.questionId,
              values: values,
            };
          }
        }

        setAnswers(formAnswers);
        setEditingDocumentId(documentId);
        setShowForm(true);
      } catch (error) {
        console.error('Error loading document for editing:', error);
      }
    },
    [careDocumentTemplate, getFileUrl]
  );

  // Handle create new document
  const handleCreateNew = useCallback(() => {
    setAnswers({});
    setEditingDocumentId(null);
    setShowForm(true);
  }, []);

  const [documentToDelete, setDocumentToDelete] = useState<string | null>(null);

  // Handle delete document
  const handleDeleteDocument = useCallback(() => {
    if (!documentToDelete) return;

    userCareDocumentsAPI
      .deleteUserCareDocument(documentToDelete)
      .then(() => {
        setDocumentToDelete(null);
        refetchSavedDocuments();
      })
      .catch(error => {
        console.error('Error deleting document:', error);
        // Handle error (show error message to user)
      });
  }, [documentToDelete, refetchSavedDocuments]);

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Check if document is expired
  const isDocumentExpired = (expirationDate: string) => {
    if (!expirationDate) return false;
    return new Date(expirationDate) < new Date();
  };

  // Get preview of first few answers for display in CardDescription
  const getAnswersPreview = (
    doc: any,
    maxAnswers: number = 2
  ): AnswerPreview[] => {
    if (!doc.answers || doc.answers.length === 0) return [];

    return doc.answers
      .filter((answer: any) => {
        // Only include answers that have content
        return (
          answer &&
          ((answer.answer && answer.answer.trim().length > 0) ||
            (answer.fileKeys && answer.fileKeys.length > 0))
        );
      })
      .slice(0, maxAnswers)
      .map((answer: any) => {
        // Find the question for this answer
        const question = careDocumentTemplate?.questions?.find(
          (q: any) => q && answer && q.id === answer.questionId
        );

        if (!question) return null;

        // Format the answer text
        let answerText = '';
        if (answer.answer) {
          // Handle multiple answers (split by separator)
          const answerValues = answer.answer.includes('|||')
            ? answer.answer.split('|||')
            : [answer.answer];
          answerText = answerValues[0]; // Take first answer for preview

          // Truncate if too long for compact preview
          if (answerText.length > 30) {
            answerText = answerText.substring(0, 30) + '...';
          }
        } else if (answer.fileKeys && answer.fileKeys.length > 0) {
          const fileCount = answer.fileKeys.length;
          answerText =
            fileCount === 1
              ? `📎 ${answer.fileNames?.[0] || 'File attached'}`
              : `📎 ${fileCount} files attached`;
        }

        return {
          questionText: question.text,
          answerText: answerText,
        };
      })
      .filter(
        (item: AnswerPreview | null): item is AnswerPreview => item !== null
      );
  };

  const getReviewStatus = (
    expirationDate: string
  ): 'current' | 'due-soon' | 'overdue' => {
    if (!expirationDate) return 'current';

    const now = new Date();
    const reviewDate = new Date(expirationDate);
    const daysUntilReview = Math.ceil(
      (reviewDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
    );

    if (daysUntilReview < 0) return 'overdue';
    if (daysUntilReview <= 15) return 'due-soon';
    return 'current';
  };

  if (isLoadingCareDocumentTemplate || isLoadingSavedDocuments) {
    return (
      <div className='flex items-center justify-center min-h-[400px]'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4'></div>
          <p className='text-muted-foreground'>Loading your documents...</p>
        </div>
      </div>
    );
  }

  if (templateError || savedDocumentsError) {
    return (
      <div className='p-8 text-center'>
        Error: {(templateError || savedDocumentsError)?.message}
      </div>
    );
  }

  const document = {
    ...careDocumentTemplate,
    questions: (careDocumentTemplate?.questions || [])
      .filter((q): q is NonNullable<typeof q> => q !== null)
      .map(q => ({
        ...q,
        type: q.type as Question['type'],
        required: q.required ?? false,
        allowMultiple: q.allowMultiple ?? false,
        placeholder: q.placeholder ?? undefined,
        helpText: q.helpText ?? undefined,
        options: q.options ?? undefined,
      })),
  } as CareDocument;

  if (!document || !document.questions) {
    return <div className='p-8 text-center'>Document template not found</div>;
  }

  return (
    <div className='max-w-4xl mx-auto p-6'>
      <div className='flex gap-2 items-start justify-start'>
        <div className='flex items-center space-x-4'>
          <Button
            variant='outline'
            size='sm'
            onClick={handleBack}
            className='flex items-center space-x-2'
          >
            <ArrowLeft className='h-4 w-4' />
            {/* <span>Back to Templates</span> */}
          </Button>
        </div>
        <div className='mb-6'>
          <h1 className='text-2xl font-bold mb-2'>{document.title}</h1>
          <p className='text-gray-600'>{document.description}</p>
        </div>
      </div>

      {/* Saved Documents Section */}
      {!showForm && (
        <div className='mb-8'>
          <div className='flex justify-between items-center mb-4'>
            <h2 className='text-xl font-semibold'>Your Saved Documents</h2>
            {document.allowMultiple && (
              <Button onClick={handleCreateNew}>
                <Plus className='h-4 w-4 mr-2' /> Create New
              </Button>
            )}
          </div>

          {savedDocuments.length > 0 ? (
            <div className='flex flex-col gap-2'>
              {savedDocuments.map(doc => {
                // Calculate completion percentage
                const totalQuestions =
                  careDocumentTemplate?.questions?.length || 0;
                const answeredQuestions =
                  doc.answers?.filter(
                    answer =>
                      (answer?.answer && answer.answer.length > 0) ||
                      (answer?.fileKeys && answer?.fileKeys.length > 0)
                  ).length || 0;
                const completionPercentage =
                  totalQuestions > 0
                    ? Math.round((answeredQuestions / totalQuestions) * 100)
                    : 0;

                return (
                  <Card key={doc.id} className='w-full'>
                    <CardHeader>
                      <div className='flex items-center justify-between'>
                        <div className='flex items-center gap-2'>
                          <FileText className='h-5 w-5 text-primary' />
                          <CardTitle>{doc.title}</CardTitle>
                        </div>

                        {/* Status Badge with Tooltip */}
                        {doc.expirationDate && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger>
                                <Badge
                                  variant='default'
                                  className={
                                    getReviewStatus(doc.expirationDate) ===
                                    'overdue'
                                      ? 'bg-red-500'
                                      : getReviewStatus(doc.expirationDate) ===
                                          'due-soon'
                                        ? 'bg-yellow-500'
                                        : 'bg-green-500'
                                  }
                                >
                                  {getReviewStatus(doc.expirationDate) ===
                                  'overdue'
                                    ? 'Overdue'
                                    : getReviewStatus(doc.expirationDate) ===
                                        'due-soon'
                                      ? 'Due Soon'
                                      : 'Current'}
                                </Badge>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>
                                  {getReviewStatus(doc.expirationDate) ===
                                  'overdue'
                                    ? 'Last Review'
                                    : 'Next Review:'}{' '}
                                  {formatDate(doc.expirationDate)}
                                </p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                      </div>
                      <CardDescription>
                        <div className='grid grid-cols-2 gap-x-4 gap-y-1 text-xs'>
                          {/* Preview Row */}
                          <div>
                            {(() => {
                              const previewAnswers = getAnswersPreview(doc, 2);
                              const totalAnswers =
                                doc.answers?.filter(
                                  (a: any) =>
                                    a &&
                                    ((a.answer && a.answer.trim()) ||
                                      (a.fileKeys && a.fileKeys.length > 0))
                                ).length || 0;

                              return previewAnswers.length > 0 ? (
                                <div className='col-span-2'>
                                  <span className='font-medium'>Preview:</span>{' '}
                                  {previewAnswers
                                    .map(
                                      p => `${p.questionText}: ${p.answerText}`
                                    )
                                    .join(', ')}
                                  {totalAnswers > 2 && (
                                    <span className='text-gray-400'>
                                      , +{totalAnswers - 2} more
                                    </span>
                                  )}
                                </div>
                              ) : null;
                            })()}
                          </div>

                          {/* Document Metadata */}
                          <div>
                            <span className='font-medium'>Version:</span>{' '}
                            {doc.version}
                          </div>
                          <div>
                            <span className='font-medium'>Created:</span>{' '}
                            {formatDate(doc.createdAt)}
                          </div>
                          <div>
                            <span className='font-medium'>Completion:</span>{' '}
                            {completionPercentage}%
                          </div>
                          <div>
                            <span className='font-medium'>Document Type:</span>{' '}
                            {doc.documentType || 'Standard'}
                          </div>
                          <div>
                            <span className='font-medium'>Last Updated:</span>{' '}
                            {formatDate(doc.updatedAt)}
                          </div>
                        </div>
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <Accordion type='single' collapsible className='w-full'>
                        <AccordionItem value='answers'>
                          <AccordionTrigger className='text-sm font-medium cursor-pointer'>
                            Document Answers
                          </AccordionTrigger>
                          <AccordionContent>
                            <div className='space-y-4'>
                              {doc.answers?.map(answer => {
                                // Find the question for this answer
                                const question =
                                  careDocumentTemplate?.questions?.find(
                                    q =>
                                      q && answer && q.id === answer.questionId
                                  );

                                // Skip if no question found or no answer value
                                if (!question) return null;
                                if (!answer) return null;
                                if (!answer.answer && !answer.fileKeys)
                                  return null;

                                // Handle multiple answers (split by separator)
                                const answerValues =
                                  answer && answer.answer
                                    ? answer.answer.includes('|||')
                                      ? answer.answer.split('|||')
                                      : [answer.answer]
                                    : [];

                                return (
                                  <div
                                    key={answer.questionId}
                                    className='border-b pb-3 last:border-0 last:pb-0'
                                  >
                                    <h4 className='font-medium text-sm'>
                                      {question.text}
                                    </h4>
                                    <div className='mt-1 text-sm'>
                                      {answerValues.map((value, idx) => (
                                        <p key={idx}>{value}</p>
                                      ))}
                                    </div>

                                    {/* Display files if any */}
                                    {answer.fileKeys &&
                                      answer.fileKeys.length > 0 && (
                                        <div className='mt-2 flex flex-wrap gap-2'>
                                          {answer.fileKeys.map(
                                            (fileKey, idx) => {
                                              if (!fileKey) return null;
                                              const fileName =
                                                answer.fileNames?.[idx] ||
                                                'File';
                                              return (
                                                <div
                                                  key={fileKey}
                                                  className='flex items-center gap-1 rounded-md bg-muted px-2 py-1 text-xs cursor-pointer hover:bg-muted/80'
                                                  onClick={async () => {
                                                    try {
                                                      const url =
                                                        await getFileUrl(
                                                          fileKey
                                                        );
                                                      window.open(
                                                        url,
                                                        '_blank'
                                                      );
                                                    } catch (error) {
                                                      console.error(
                                                        'Error opening file:',
                                                        error
                                                      );
                                                    }
                                                  }}
                                                >
                                                  <FileText className='h-3 w-3' />
                                                  <span>{fileName}</span>
                                                </div>
                                              );
                                            }
                                          )}
                                        </div>
                                      )}
                                  </div>
                                );
                              })}
                            </div>
                          </AccordionContent>
                        </AccordionItem>
                      </Accordion>
                    </CardContent>
                    <CardFooter className='flex justify-end gap-2'>
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() => handleEditDocument(doc.id)}
                      >
                        <Edit className='h-4 w-4 mr-2' /> Edit Document
                      </Button>
                      <Button
                        variant='destructive'
                        size='sm'
                        onClick={() => setDocumentToDelete(doc.id)}
                      >
                        <Trash2 className='h-4 w-4 mr-2' /> Delete
                      </Button>
                    </CardFooter>
                  </Card>
                );
              })}
            </div>
          ) : (
            <Card className='bg-gray-50'>
              <CardContent className='p-8 text-center'>
                <Clock className='h-12 w-12 text-gray-400 mx-auto mb-4' />
                <h3 className='font-semibold text-gray-900 mb-2'>
                  No Documents Yet
                </h3>
                <p className='text-sm text-gray-600 mb-4'>
                  You haven't created any {document.title} documents yet.
                </p>
                <Button onClick={handleCreateNew}>
                  <Plus className='h-4 w-4 mr-2' /> Create Your First Document
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Form Section */}
      {showForm && (
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-3'>
              <FileText className='h-6 w-6' />
              {editingDocumentId ? 'Edit' : 'Create'} {document.title}
            </CardTitle>
            <CardDescription>{document.description}</CardDescription>
          </CardHeader>
          <CardContent>
            <form className='space-y-8'>
              {document.questions
                .sort((a, b) => a.order - b.order)
                .map(question => (
                  <QuestionRenderer
                    key={question.id}
                    question={question}
                    values={getAnswerForQuestion(question.id)}
                    error={errors[question.id]}
                    onUpdateAnswer={(index, value) =>
                      updateMultipleAnswer(question.id, index, value)
                    }
                    onAddAnswer={() => addMultipleAnswer(question.id)}
                    onRemoveAnswer={index =>
                      removeMultipleAnswer(question.id, index)
                    }
                    onFileSelect={(index, event) =>
                      handleFileSelect(question.id, index, event)
                    }
                    onFileDrop={(index, event) =>
                      handleFileDrop(question.id, index, event)
                    }
                    onFileRemove={index => removeFile(question.id, index)}
                    onDragOver={handleDragOver}
                  />
                ))}

              {/* Save Button */}
              <div className='flex justify-between pt-6 border-t'>
                <Button
                  type='button'
                  variant='outline'
                  onClick={() => {
                    setShowForm(false);
                    setEditingDocumentId(null);
                    setAnswers({});
                  }}
                >
                  Cancel
                </Button>
                <Button type='button' onClick={handleSave} className='px-8'>
                  {editingDocumentId ? 'Update' : 'Save'} Document
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}
      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={!!documentToDelete}
        onOpenChange={open => !open && setDocumentToDelete(null)}
        title='Are you sure?'
        description='This action cannot be undone. This will permanently delete the document and all of its data.'
        confirmLabel='Delete'
        confirmVariant='destructive'
        onConfirm={handleDeleteDocument}
      />
    </div>
  );
};

// Question renderer component
interface QuestionRendererProps {
  question: Question;
  values: (string | File | StoredFile)[];
  error?: string;
  onUpdateAnswer: (index: number, value: string | File | StoredFile) => void;
  onAddAnswer: () => void;
  onRemoveAnswer: (index: number) => void;
  onFileSelect: (
    index: number,
    event: React.ChangeEvent<HTMLInputElement>
  ) => void;
  onFileDrop: (index: number, event: React.DragEvent<HTMLDivElement>) => void;
  onFileRemove: (index: number) => void;
  onDragOver: (event: React.DragEvent<HTMLDivElement>) => void;
}

const QuestionRenderer: React.FC<QuestionRendererProps> = ({
  question,
  values,
  error,
  onUpdateAnswer,
  onAddAnswer,
  onRemoveAnswer,
  onFileSelect,
  onFileDrop,
  onFileRemove,
  onDragOver,
}) => {
  // Function to format phone number with +1 prefix
  const formatPhoneNumber = (value: string): string => {
    // If user tries to clear the field or remove +1, reset to +1
    if (value.length < 2 || !value.startsWith('+1')) {
      return '+1';
    }

    // Remove all non-digit characters except the + at the beginning
    const digits = value.slice(1).replace(/\D/g, '');

    // If no digits after +, return +1
    if (digits.length === 0) return '+1';

    // If starts with 1, keep it as is
    if (digits.startsWith('1')) {
      // Limit to 11 digits total (1 + 10 digits)
      const limitedDigits = digits.slice(0, 11);
      return `+${limitedDigits}`;
    } else {
      // If doesn't start with 1, add 1 prefix and limit to 10 additional digits
      const limitedDigits = digits.slice(0, 10);
      return `+1${limitedDigits}`;
    }
  };
  const renderInput = () => {
    switch (question.type) {
      case 'text':
        return (
          <div className='space-y-3'>
            {values.map((value, index) => (
              <div key={index} className='flex items-center space-x-2'>
                <div className='flex-1'>
                  <Input
                    id={`${question.id}-${index}`}
                    value={(value as string) || ''}
                    onChange={e => onUpdateAnswer(index, e.target.value)}
                    placeholder={question.placeholder || 'Enter your answer...'}
                    className={error ? 'border-red-500' : ''}
                  />
                </div>
                {question.allowMultiple && values.length > 1 && (
                  <Button
                    type='button'
                    variant='ghost'
                    size='sm'
                    onClick={() => onRemoveAnswer(index)}
                  >
                    <Trash2 className='h-4 w-4' />
                  </Button>
                )}
              </div>
            ))}
            {question.allowMultiple && (
              <Button
                type='button'
                variant='outline'
                size='sm'
                onClick={onAddAnswer}
                className='w-full'
              >
                <Plus className='h-4 w-4 mr-2' />
                Add Another Answer
              </Button>
            )}
          </div>
        );

      case 'file':
        return (
          <div className='space-y-4'>
            {values.map((value, index) => {
              // Handle both File objects and stored file objects
              const isFile = value instanceof File;
              const isStoredFile =
                !isFile &&
                typeof value === 'object' &&
                value !== null &&
                'isStoredFile' in value;
              const hasFile = isFile || isStoredFile;

              // Get file name based on type
              const fileName = isFile
                ? (value as File).name
                : isStoredFile
                  ? (value as StoredFile).name
                  : '';

              return (
                <div key={index} className='flex items-start space-x-2'>
                  <div className='flex-1'>
                    <div
                      className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                        hasFile
                          ? 'border-green-300 bg-green-50'
                          : error
                            ? 'border-red-300 bg-red-50'
                            : 'border-gray-300 hover:border-gray-400'
                      }`}
                      onDragOver={onDragOver}
                      onDrop={e => onFileDrop(index, e)}
                    >
                      {hasFile ? (
                        <div className='flex items-center justify-center gap-2'>
                          <CheckCircle className='h-5 w-5 text-green-600' />
                          <span className='text-green-800 font-medium'>
                            {fileName}
                          </span>
                          <Button
                            variant='ghost'
                            size='sm'
                            onClick={() => {
                              if (isFile) {
                                const blobUrl = URL.createObjectURL(
                                  value as File
                                );
                                window.open(blobUrl, '_blank');
                                setTimeout(
                                  () => URL.revokeObjectURL(blobUrl),
                                  1000
                                );
                              } else if (isStoredFile) {
                                window.open(value.url, '_blank');
                              }
                            }}
                            type='button'
                          >
                            <ExternalLink className='h-4 w-4' />
                          </Button>
                          <Button
                            variant='ghost'
                            size='sm'
                            onClick={() => onFileRemove(index)}
                            type='button'
                          >
                            <X className='h-4 w-4' />
                          </Button>
                        </div>
                      ) : (
                        <div>
                          <FileText className='h-8 w-8 text-gray-400 mx-auto mb-2' />
                          <p className='text-gray-600 mb-2'>
                            Drag and drop a file here, or click to select
                          </p>
                          <p className='text-sm text-gray-500'>
                            Supports PDF, JPEG, PNG (max 10MB)
                          </p>
                        </div>
                      )}
                      <input
                        id={`file-${question.id}-${index}`}
                        type='file'
                        accept='.pdf,.jpg,.jpeg,.png'
                        onChange={e => onFileSelect(index, e)}
                        className='hidden'
                      />
                      {!hasFile && (
                        <Button
                          variant='outline'
                          className='mt-2'
                          onClick={() =>
                            document
                              .getElementById(`file-${question.id}-${index}`)
                              ?.click()
                          }
                          type='button'
                        >
                          Select File
                        </Button>
                      )}
                    </div>
                  </div>
                  {question.allowMultiple && values.length > 1 && (
                    <Button
                      type='button'
                      variant='ghost'
                      size='sm'
                      onClick={() => onRemoveAnswer(index)}
                    >
                      <Trash2 className='h-4 w-4' />
                    </Button>
                  )}
                </div>
              );
            })}
            {question.allowMultiple && (
              <Button
                type='button'
                variant='outline'
                size='sm'
                onClick={onAddAnswer}
                className='w-full'
              >
                <Plus className='h-4 w-4 mr-2' />
                Add Another File
              </Button>
            )}
          </div>
        );

      case 'radio':
        return (
          <div className='space-y-3'>
            {values.map((value, index) => (
              <div key={index} className='flex items-start space-x-2'>
                {question.options && question.options.length > 0 ? (
                  <div className='flex flex-col w-full gap-2'>
                    {question.options.map(option => (
                      <div
                        key={option.id}
                        className={`flex items-center space-x-2 border p-3 rounded-md cursor-pointer transition-all ${
                          values[index] === option.value
                            ? 'border-primary bg-primary/5'
                            : 'hover:border-gray-300'
                        }`}
                        onClick={() => onUpdateAnswer(index, option.value)}
                      >
                        <div
                          className={`size-4 rounded-full border flex items-center justify-center ${
                            values[index] === option.value
                              ? 'border-primary'
                              : 'border-gray-300'
                          }`}
                        >
                          {values[index] === option.value && (
                            <div className='size-2 rounded-full bg-primary' />
                          )}
                        </div>
                        <span>{option.label}</span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className='text-gray-500'>No options available</div>
                )}
                {question.allowMultiple && values.length > 1 && (
                  <Button
                    type='button'
                    variant='ghost'
                    size='sm'
                    onClick={() => onRemoveAnswer(index)}
                  >
                    <Trash2 className='h-4 w-4' />
                  </Button>
                )}
              </div>
            ))}
            {question.allowMultiple && (
              <Button
                type='button'
                variant='outline'
                size='sm'
                onClick={onAddAnswer}
                className='w-full'
              >
                <Plus className='h-4 w-4 mr-2' />
                Add Another Answer
              </Button>
            )}
          </div>
        );

      case 'select':
        return (
          <div className='space-y-3'>
            {values.map((value, index) => (
              <div key={index} className='flex items-start space-x-2'>
                {question.options && question.options.length > 0 ? (
                  <div className='w-full'>
                    <Select
                      value={(values[index] as string) || ''}
                      onValueChange={newValue =>
                        onUpdateAnswer(index, newValue)
                      }
                    >
                      <SelectTrigger
                        id={`${question.id}-select`}
                        className={`w-full ${error ? 'border-red-500' : ''}`}
                      >
                        <SelectValue placeholder='Select an option...' />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup>
                          {question.options.map(option => (
                            <SelectItem key={option.id} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  </div>
                ) : (
                  <div className='text-gray-500'>No options available</div>
                )}
                {question.allowMultiple && values.length > 1 && (
                  <Button
                    type='button'
                    variant='ghost'
                    size='sm'
                    onClick={() => onRemoveAnswer(index)}
                  >
                    <Trash2 className='h-4 w-4' />
                  </Button>
                )}
              </div>
            ))}
            {question.allowMultiple && (
              <Button
                type='button'
                variant='outline'
                size='sm'
                onClick={onAddAnswer}
                className='w-full'
              >
                <Plus className='h-4 w-4 mr-2' />
                Add Another Answer
              </Button>
            )}
          </div>
        );

      case 'date':
        return (
          <div className='space-y-3'>
            {values.map((value, index) => {
              const handleDateChange = (date: Date | undefined) => {
                // Convert Date to ISO string for storage, or empty string if no date
                const dateValue = date ? date.toISOString().split('T')[0] : '';
                onUpdateAnswer(index, dateValue);
              };

              // Convert stored string value back to Date object for DatePicker
              const dateValue =
                value && typeof value === 'string'
                  ? new Date(value)
                  : undefined;

              return (
                <div key={index} className='flex items-center space-x-2'>
                  <div className='flex-1'>
                    <DatePicker
                      date={dateValue}
                      setDate={handleDateChange}
                      className={`w-full ${error ? 'border-red-500' : ''}`}
                      showYearPicker={true}
                      showMonthPicker={true}
                      yearRange={{
                        from: 1900,
                        to: new Date().getFullYear() + 10,
                      }}
                    />
                  </div>
                  {question.allowMultiple && values.length > 1 && (
                    <Button
                      type='button'
                      variant='ghost'
                      size='sm'
                      onClick={() => onRemoveAnswer(index)}
                    >
                      <Trash2 className='h-4 w-4' />
                    </Button>
                  )}
                </div>
              );
            })}
            {question.allowMultiple && (
              <Button
                type='button'
                variant='outline'
                size='sm'
                onClick={onAddAnswer}
                className='w-full'
              >
                <Plus className='h-4 w-4 mr-2' />
                Add Another Date
              </Button>
            )}
          </div>
        );

      case 'phone':
        return (
          <div className='space-y-3'>
            {values.map((value, index) => (
              <div key={index} className='flex items-center space-x-2'>
                <div className='flex-1 relative'>
                  <Phone className='absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground' />
                  <Input
                    id={`${question.id}-${index}`}
                    type='tel'
                    value={(value as string) || '+1'}
                    onChange={e => {
                      const formattedValue = formatPhoneNumber(e.target.value);
                      onUpdateAnswer(index, formattedValue);
                    }}
                    placeholder='+1XXXXXXXXXX'
                    maxLength={12}
                    className={`pl-10 ${error ? 'border-red-500' : ''}`}
                  />
                </div>
                {question.allowMultiple && values.length > 1 && (
                  <Button
                    type='button'
                    variant='ghost'
                    size='sm'
                    onClick={() => onRemoveAnswer(index)}
                  >
                    <Trash2 className='h-4 w-4' />
                  </Button>
                )}
              </div>
            ))}
            {question.allowMultiple && (
              <Button
                type='button'
                variant='outline'
                size='sm'
                onClick={onAddAnswer}
                className='w-full'
              >
                <Plus className='h-4 w-4 mr-2' />
                Add Another Phone
              </Button>
            )}
            <p className='text-xs text-muted-foreground'>
              US phone number format: +1 followed by 10 digits
            </p>
          </div>
        );

      default:
        return (
          <div className='text-gray-500'>
            Unsupported question type: {question.type}
          </div>
        );
    }
  };

  return (
    <div className='space-y-3'>
      <Label htmlFor={question.id} className='text-base font-medium'>
        {question.text}
        {question.required && <span className='text-red-500 ml-1'>*</span>}
      </Label>

      {question.helpText && (
        <p className='text-sm text-gray-600'>{question.helpText}</p>
      )}

      {renderInput()}

      {error && (
        <Alert variant='destructive'>
          <AlertTriangle className='h-4 w-4' />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
    </div>
  );
};

export default CareDocumentPage;

const questionsExample = [
  {
    id: 'question_1753102427904',
    text: 'What is your name',
    type: 'text',
    required: true,
    placeholder: 'Enter your name',
    helpText: 'Your name',
    options: [],
    allowMultiple: false,
    order: 1,
  },
  {
    id: 'question_1753102489085',
    text: 'How old are you?',
    type: 'radio',
    required: true,
    placeholder: '',
    helpText: '',
    options: [
      {
        id: 'option_1753102465595',
        label: '30',
        value: '30',
      },
      {
        id: 'option_1753102476111',
        label: '40',
        value: '40',
      },
      {
        id: 'option_1753102480370',
        label: '50',
        value: '50',
      },
      {
        id: 'option_1753102482900',
        label: '60',
        value: '60',
      },
    ],
    allowMultiple: false,
    order: 2,
  },
  {
    id: 'question_1753102522476',
    text: 'Your photo',
    type: 'file',
    required: true,
    placeholder: '',
    helpText: 'Your photo',
    options: [],
    allowMultiple: false,
    order: 3,
  },
  {
    id: 'question_1753102546392',
    text: 'Dropdown',
    type: 'select',
    required: true,
    placeholder: '',
    helpText: 'Dropdown',
    options: [
      {
        id: 'option_1753102538695',
        label: '1',
        value: '1',
      },
      {
        id: 'option_1753102542197',
        label: '2',
        value: '2',
      },
      {
        id: 'option_1753102544378',
        label: '3',
        value: '3',
      },
    ],
    allowMultiple: false,
    order: 4,
  },
];
