'use client';

import React, { useMemo, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, FileText, CheckCircle2 } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { getMemberAvailableTemplates } from '@/lib/api/member-documents';
import { useAuth } from '@/app/context/AuthContext';
import { fetchUserProfileByCognitoId } from '@/lib/data/users';
import {
  getActiveInterviews,
  getUserInterviewProgress,
} from '@/lib/api/interview-new-user';
import { checkDocumentDuplicate } from '@/lib/api/documents';
import { finalizeDocumentsForTemplates } from '@/lib/utils/document-create/finalize-documents';
import routes from '@/utils/routes';

export default function DocumentCreatePage() {
  const router = useRouter();
  const { user } = useAuth();
  const [selectedTemplates, setSelectedTemplates] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [duplicateWarnings, setDuplicateWarnings] = useState<{
    [templateId: string]: {
      show: boolean;
      existingDocument?: any;
      suggestedVersion?: string;
    };
  }>({});

  // Map template type to valid document type enum values (same as in finalize-documents.ts)
  const mapTemplateTypeToDocumentType = (templateType: string): string => {
    const typeMapping: Record<string, string> = {
      Will: 'Will',
      Trust: 'Trust',
      'Healthcare POA': 'Healthcare_POA',
      'Financial POA': 'Financial_POA',
      'Advance Directive': 'Advance_Directive',
      POA: 'Healthcare_POA', // Default POA to Healthcare_POA
      Medical: 'Healthcare_POA',
      Financial: 'Financial_POA',
      Healthcare: 'Healthcare_POA',
    };

    const mappedType = typeMapping[templateType] || 'Other';
    console.log(
      `Mapping template type "${templateType}" to document type "${mappedType}"`
    );
    return mappedType;
  };

  // Fetch user profile to get real state data
  const { data: userProfile } = useQuery({
    queryKey: ['userProfile', user?.userId],
    queryFn: () => fetchUserProfileByCognitoId(user?.userId || ''),
    enabled: !!user?.userId,
  });

  // Get user state from profile or fallback to California
  const getUserState = () => {
    if (!userProfile?.state) return 'California';

    try {
      // If state is a JSON string (address object), parse it and extract stateProvinceCode
      const parsedState = JSON.parse(userProfile.state);
      return parsedState.stateProvinceCode || 'CA';
    } catch {
      // If it's not JSON, assume it's already a state code/name
      return userProfile.state;
    }
  };

  const userState = getUserState();

  const { data: templates = [], isLoading } = useQuery({
    queryKey: ['templates', userState],
    queryFn: () => getMemberAvailableTemplates(userState),
    enabled: !!userState,
  });

  const documentOptions = useMemo(() => {
    if (!templates || templates.length === 0) return [];

    return templates.map((template: any) => {
      const templateType = template.type;
      const templateStates = template.templateStates || [];
      const isGeneral =
        template.isGeneral === true || templateStates.includes('General');
      const hasUserState = templateStates.includes(userState);

      // Determine display suffix
      let displaySuffix = '';
      if (hasUserState) {
        displaySuffix = ` (${userState})`;
      } else if (isGeneral) {
        displaySuffix = ' (General)';
      }

      const displayName = `${templateType}${displaySuffix}`;

      return {
        id: template.id,
        name: displayName,
        type: templateType,
        template: template,
      };
    });
  }, [templates, userState]);

  const handleTemplateToggle = (templateId: string) => {
    setSelectedTemplates(prev => {
      if (prev.includes(templateId)) {
        return prev.filter(id => id !== templateId);
      } else {
        return [...prev, templateId];
      }
    });
    setError(null);
    // Clear any duplicate warnings for this template
    setDuplicateWarnings(prev => ({
      ...prev,
      [templateId]: { show: false },
    }));
  };

  const handleSelectAll = () => {
    const allTemplateIds = documentOptions.map(option => option.id);
    setSelectedTemplates(allTemplateIds);
    setError(null);
    // Clear all duplicate warnings
    setDuplicateWarnings({});
  };

  const handleDeselectAll = () => {
    setSelectedTemplates([]);
    setError(null);
    // Clear all duplicate warnings
    setDuplicateWarnings({});
  };

  const handleCreateDocuments = async (forceProceed: boolean = false) => {
    if (selectedTemplates.length === 0) {
      setError('Please select at least one document type');
      return;
    }

    try {
      setIsCreating(true);
      setError(null);
      setDuplicateWarnings({});

      // Check for duplicate documents first (unless forcing to proceed)
      if (!forceProceed && user?.userId) {
        const duplicateChecks = await Promise.all(
          selectedTemplates.map(async templateId => {
            const selectedTemplate = templates.find(
              template => template.id === templateId
            );
            if (!selectedTemplate) {
              throw new Error(`Template not found: ${templateId}`);
            }

            console.log(
              `Checking duplicates for template: ${selectedTemplate.type} (ID: ${templateId})`
            );

            // Map template type to document type before checking duplicates
            const mappedDocumentType = mapTemplateTypeToDocumentType(
              selectedTemplate.type
            );

            console.log(
              `Mapped "${selectedTemplate.type}" to "${mappedDocumentType}" for duplicate check`
            );

            const duplicateCheck = await checkDocumentDuplicate(
              mappedDocumentType,
              user.userId
            );

            console.log(
              `Duplicate check result for ${mappedDocumentType}:`,
              duplicateCheck
            );

            return {
              templateId,
              template: selectedTemplate,
              duplicateCheck,
            };
          })
        );

        // Check if any duplicates exist
        const duplicatesFound = duplicateChecks.filter(
          check => check.duplicateCheck.hasDuplicate
        );

        if (duplicatesFound.length > 0) {
          const warnings: { [key: string]: any } = {};
          duplicatesFound.forEach(({ templateId, duplicateCheck }) => {
            warnings[templateId] = {
              show: true,
              existingDocument: duplicateCheck.existingDocument,
              suggestedVersion: duplicateCheck.suggestedVersion,
            };
          });
          setDuplicateWarnings(warnings);
          setIsCreating(false);
          return;
        }
      }

      // Check if user has completed at least one interview
      const interviews = await getActiveInterviews();

      if (interviews.length === 0) {
        setError('No interviews available. Please contact support.');
        return;
      }

      // Check if user has completed any interview
      let hasCompletedInterview = false;

      for (const interview of interviews) {
        const progress = await getUserInterviewProgress(
          interview.latestVersion.id
        );
        if (progress?.isCompleted) {
          hasCompletedInterview = true;
          break;
        }
      }

      if (!hasCompletedInterview) {
        // Redirect to interview if no completed interviews found
        router.push(routes.member.interview);
        return;
      }

      // Get selected template objects
      const selectedTemplateObjects = selectedTemplates
        .map(templateId => templates.find(t => t.id === templateId))
        .filter(Boolean);

      // Create documents using the finalizeDocumentsForTemplates function
      if (!user?.userId) {
        throw new Error('User not authenticated');
      }
      await finalizeDocumentsForTemplates(user.userId, selectedTemplateObjects);

      // Redirect to documents page after successful creation
      router.push('/member/documents');
    } catch (error) {
      console.error('Error creating documents:', error);
      setError('Failed to create documents. Please try again.');
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-4xl mx-auto'>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-[var(--foreground)] mb-2'>
            Create Legal Documents
          </h1>
          <p className='text-[var(--custom-gray-dark)]'>
            Select one or more document types to create. We'll use your
            interview responses to generate personalized documents for each
            selected type.
          </p>
        </div>

        <Card className='mb-8'>
          <CardHeader>
            <CardTitle>Document Selection</CardTitle>
            <CardDescription>
              Choose one or more legal documents you want to create. Documents
              are prioritized for your state ({userState}) when available, with
              general documents as fallbacks. You can select multiple documents
              to create them all at once.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className='space-y-6'>
              <div className='space-y-4'>
                <div className='flex items-center justify-between'>
                  <label className='text-sm font-medium'>
                    Select Document Types to Create
                  </label>
                  {documentOptions.length > 0 && (
                    <div className='flex gap-2'>
                      <Button
                        type='button'
                        variant='outline'
                        size='sm'
                        onClick={handleSelectAll}
                        disabled={
                          selectedTemplates.length === documentOptions.length
                        }
                      >
                        Select All
                      </Button>
                      <Button
                        type='button'
                        variant='outline'
                        size='sm'
                        onClick={handleDeselectAll}
                        disabled={selectedTemplates.length === 0}
                      >
                        Deselect All
                      </Button>
                    </div>
                  )}
                </div>
                {isLoading ? (
                  <div className='flex items-center justify-center py-8'>
                    <div className='text-sm text-gray-500'>
                      Loading templates...
                    </div>
                  </div>
                ) : documentOptions.length === 0 ? (
                  <div className='flex items-center justify-center py-8'>
                    <div className='text-sm text-gray-500'>
                      No templates available for your state
                    </div>
                  </div>
                ) : (
                  <div className='grid gap-3'>
                    {documentOptions.map(option => (
                      <div
                        key={option.id}
                        className={`flex items-start space-x-3 rounded-lg border p-4 transition-all cursor-pointer hover:bg-gray-50 ${
                          selectedTemplates.includes(option.id)
                            ? 'border-primary bg-primary/5'
                            : 'border-gray-200'
                        }`}
                        onClick={() => handleTemplateToggle(option.id)}
                      >
                        <Checkbox
                          id={option.id}
                          checked={selectedTemplates.includes(option.id)}
                          onCheckedChange={() =>
                            handleTemplateToggle(option.id)
                          }
                          className='mt-0.5'
                        />
                        <div className='flex-1 min-w-0'>
                          <div className='flex items-center space-x-2'>
                            <FileText className='h-4 w-4 text-gray-400 flex-shrink-0' />
                            <Label
                              htmlFor={option.id}
                              className='text-sm font-medium cursor-pointer'
                            >
                              {option.name}
                            </Label>
                          </div>
                          <p className='text-xs text-gray-500 mt-1'>
                            {option.type} document template
                          </p>
                        </div>
                        {selectedTemplates.includes(option.id) && (
                          <CheckCircle2 className='h-4 w-4 text-primary flex-shrink-0' />
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>

              <div className='space-y-2'>
                <label className='text-sm font-medium'>State</label>
                <div className='p-3 border rounded-md bg-gray-50'>
                  <p>Your state is {userState}</p>
                  <p className='text-xs text-[var(--custom-gray-dark)] mt-1'>
                    This is the state of residence listed in your profile. When
                    available, you’ll see documents tailored to your state’s
                    laws. Otherwise, you’ll see general documents that apply
                    nationwide.
                  </p>
                </div>
              </div>

              {error && (
                <Alert variant='destructive'>
                  <AlertCircle className='h-4 w-4' />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
            </div>
          </CardContent>
          <CardFooter className='flex justify-between items-center'>
            <div className='text-sm text-gray-500'>
              {selectedTemplates.length > 0 && (
                <span>
                  {selectedTemplates.length} document
                  {selectedTemplates.length === 1 ? '' : 's'} selected
                </span>
              )}
            </div>
            <Button
              onClick={() => handleCreateDocuments()}
              disabled={
                isCreating || selectedTemplates.length === 0 || isLoading
              }
            >
              {isCreating
                ? 'Creating Documents...'
                : `Create ${selectedTemplates.length || ''} Document${
                    selectedTemplates.length === 1 ? '' : 's'
                  }`}
            </Button>
          </CardFooter>
        </Card>

        {/* Duplicate Document Warnings */}
        {Object.entries(duplicateWarnings).map(
          ([templateId, warning]) =>
            warning.show && (
              <Alert
                key={templateId}
                className='border-yellow-200 bg-yellow-50 mb-6'
              >
                <AlertCircle className='h-4 w-4 text-yellow-600' />
                <AlertTitle className='text-yellow-800'>
                  Document Already Exists
                </AlertTitle>
                <AlertDescription className='text-yellow-700'>
                  <div className='space-y-3'>
                    <p>
                      You already have a document of type "
                      {warning.existingDocument?.type}" that is not yet
                      finalized.
                      {warning.existingDocument && (
                        <>
                          <br />
                          <strong>Existing document:</strong>{' '}
                          {warning.existingDocument.title}
                          <br />
                          <strong>Status:</strong>{' '}
                          {warning.existingDocument.status}
                          <br />
                          <strong>Created:</strong>{' '}
                          {new Date(
                            warning.existingDocument.dateCreated
                          ).toLocaleDateString()}
                        </>
                      )}
                    </p>
                    <p>
                      You can only create a new document of the same type after
                      the existing one is archived, signed, or shipped.
                    </p>
                    <div className='flex gap-2 mt-4'>
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() =>
                          setDuplicateWarnings(prev => ({
                            ...prev,
                            [templateId]: { show: false },
                          }))
                        }
                      >
                        Cancel
                      </Button>
                      <Button
                        size='sm'
                        onClick={() => {
                          // Remove this template from selection
                          setSelectedTemplates(prev =>
                            prev.filter(id => id !== templateId)
                          );
                          setDuplicateWarnings(prev => ({
                            ...prev,
                            [templateId]: { show: false },
                          }));
                        }}
                      >
                        Remove from Selection
                      </Button>
                    </div>
                  </div>
                </AlertDescription>
              </Alert>
            )
        )}

        <div className='bg-blue-50 p-6 rounded-lg border border-blue-100'>
          <h3 className='text-lg font-medium text-blue-800 mb-2'>
            About Multiple Document Creation
          </h3>
          <p className='text-blue-700 mb-4'>
            You can now create all the legal documents you need in one go —
            saving you time and effort. Our system uses the information you
            shared during your interview to make sure everything meets your
            state’s requirements.
          </p>
          <ul className='list-disc list-inside text-blue-700 space-y-2'>
            <li>
              Pick as many document types as you want, and we’ll create them all
              at once.
            </li>
            <li>
              Whenever possible, we’ll use templates tailored to your state; if
              none are available, we’ll use our standard templates.
            </li>
            <li>
              Your documents are securely stored, so you can access them
              anytime.
            </li>
            <li>
              We’ll let you know if state laws change and your documents need an
              update.
            </li>
          </ul>
        </div>

        {error && (
          <Alert className='border-red-200 bg-red-50'>
            <AlertCircle className='h-4 w-4 text-red-600' />
            <AlertTitle className='text-red-800'>Error</AlertTitle>
            <AlertDescription className='text-red-700'>
              {error}
            </AlertDescription>
          </Alert>
        )}
      </div>
    </div>
  );
}
