'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { DocumentStatusCard } from '@/components/documents/document-status-card';
import { Document } from '@/types/documents';
import {
  FileText,
  Search,
  Filter,
  Plus,
  Eye,
  PenTool,
  Truck,
} from 'lucide-react';
import routes from '@/utils/routes';
import { useUserContextOptional } from '@/components/welon-trust/user-context';
import { useQuery, useMutation } from '@tanstack/react-query';
import {
  getUserDocuments,
  getWelonTrustDocuments,
  getDocumentsForSelectedUser,
  DocumentResponse,
  resubmitDocument,
  recreateDocument,
  sendDocumentToWelon,
  retractDocument,
  shipDocumentByMail,
  deleteDocument,
  permanentlyDeleteDocument,
} from '@/lib/api/documents';
import { useAuth } from '@/context/AuthContext';
import { isWelonTrust } from '@/lib/utils/admin-utils';
import { downloadDocument } from '@/lib/utils/document-download';
import { toast } from 'sonner';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { getCurrentUser } from 'aws-amplify/auth';
import { useDocumentSigning } from '@/hooks/useDocumentSigning';
import Link from 'next/link';
import { createNotificationWithEmail } from '@/lib/api/notifications';

const client = generateClient<Schema>();

function DocumentsContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { selectedUser } = useUserContextOptional();
  const { user, userRoles } = useAuth();
  const [documents, setDocuments] = useState<Document[]>([]);
  const [filteredDocuments, setFilteredDocuments] = useState<Document[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<'all' | Document['type']>('all');
  const [showArchivedDocuments, setShowArchivedDocuments] = useState(false);
  const [sortBy, setSortBy] = useState<'dateCreated' | 'version'>(
    'dateCreated'
  );
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [showUploadModal, setShowUploadModal] = useState(false);

  // Function to format document type for display
  const formatDocumentType = (type: Document['type']): string => {
    switch (type) {
      case 'Healthcare_POA':
        return 'Healthcare POA';
      case 'Financial_POA':
        return 'Financial POA';
      case 'Advance_Directive':
        return 'Advance Directive';
      default:
        return type;
    }
  };

  // Function to extract document type from title
  const extractTypeFromTitle = (title: string): Document['type'] => {
    const titleLower = title.toLowerCase();

    if (titleLower.includes('will')) {
      return 'Will';
    } else if (titleLower.includes('trust')) {
      return 'Trust';
    } else if (
      titleLower.includes('healthcare') ||
      titleLower.includes('medical')
    ) {
      return 'Healthcare_POA';
    } else if (
      titleLower.includes('financial') ||
      (titleLower.includes('poa') && !titleLower.includes('healthcare'))
    ) {
      return 'Financial_POA';
    } else if (
      titleLower.includes('advance directive') ||
      titleLower.includes('directive')
    ) {
      return 'Advance_Directive';
    } else {
      return 'Other';
    }
  };

  //LOADING STATES
  const [downloadingDocuments, setDownloadingDocuments] = useState<Set<string>>(
    new Set()
  );
  const [resubmittingDocuments, setResubmittingDocuments] = useState<
    Set<string>
  >(new Set());
  const [retractingDocuments, setRetractingDocuments] = useState<Set<string>>(
    new Set()
  );
  const [shippingDocuments, setShippingDocuments] = useState<Set<string>>(
    new Set()
  );
  const [deletingDocuments, setDeletingDocuments] = useState<Set<string>>(
    new Set()
  );

  // State for bulk shipping
  const [selectedDocuments, setSelectedDocuments] = useState<Set<string>>(
    new Set()
  );
  const [isBulkShipping, setIsBulkShipping] = useState<boolean>(false);

  // Determine if this is a Welon Trust user using the utility function
  const isWelonTrustUser = isWelonTrust(userRoles);

  // Determine which function to use based on user role and selected user
  const getQueryFunction = () => {
    if (!isWelonTrustUser) {
      return getUserDocuments;
    }

    // For Welon Trust users, check if a user is selected
    if (selectedUser) {
      return () => getDocumentsForSelectedUser(selectedUser.id);
    }

    // Default to all assigned users' documents
    return getWelonTrustDocuments;
  };

  // Query for real documents - use different function based on user role and selected user
  const {
    data: realDocuments,
    isLoading: realDocumentsLoading,
    error: realDocumentsError,
    refetch: refetchDocuments,
  } = useQuery({
    queryKey: isWelonTrustUser
      ? selectedUser
        ? ['selected-user-documents', selectedUser.id]
        : ['welon-trust-documents']
      : ['user-documents'],
    queryFn: getQueryFunction(),
    enabled: !!user, // Only fetch for authenticated users
    refetchOnWindowFocus: false,
  });

  // Mutation for recreating rejected documents
  const recreateMutation = useMutation({
    mutationFn: (docId: string) => recreateDocument(docId),
  });

  // Handle URL filter parameter
  useEffect(() => {
    const filterParam = searchParams.get('filter');
    if (filterParam && filterParam !== 'all') {
      setStatusFilter(filterParam);
    }
  }, [searchParams]);

  // Load real documents from database
  useEffect(() => {
    if (realDocuments) {
      // Convert real documents from database
      const convertedDocuments: Document[] = realDocuments.map(
        (doc: DocumentResponse) => ({
          id: doc.id,
          title: doc.title,
          type: extractTypeFromTitle(doc.title), // Parse type from title
          status: doc.status as Document['status'],
          dateCreated: doc.dateCreated,
          lastModified: doc.lastModified || doc.dateCreated,
          version: doc.version,
          content: doc.content,
          userId: doc.userId,
          fileUrl: doc.fileUrl,
          trackingNumber: doc.trackingNumber,
          signatureType: doc.signatureType as Document['signatureType'],
          executionDate: doc.executionDate,
          rejectionReason: doc.rejectionReason,
          templateId: doc.templateId,
          templateVersion: doc.templateVersion,
          // Signed document fields
          signedDocumentUrl: doc.signedDocumentUrl,
          signedAt: doc.signedAt,
          documentHash: doc.documentHash,
        })
      );

      setDocuments(convertedDocuments);
      setFilteredDocuments(convertedDocuments);
    } else if (!realDocuments && !realDocumentsLoading) {
      // No documents yet
      setDocuments([]);
      setFilteredDocuments([]);
    }
  }, [realDocuments, realDocumentsLoading]);

  // Determine loading state
  const loading = realDocumentsLoading;

  // Filter documents based on search and filters
  useEffect(() => {
    let filtered = documents;

    // Show only archived documents when showArchivedDocuments is true, otherwise hide them
    if (showArchivedDocuments) {
      filtered = filtered.filter(doc => doc.status === 'archived');
    } else {
      filtered = filtered.filter(doc => doc.status !== 'archived');
    }

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(
        doc =>
          doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          doc.type.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(doc => doc.status === statusFilter);
    }

    // Type filter
    if (typeFilter !== 'all') {
      filtered = filtered.filter(doc => doc.type === typeFilter);
    }

    // Sort documents
    filtered.sort((a, b) => {
      let comparison = 0;

      if (sortBy === 'dateCreated') {
        const dateA = new Date(a.dateCreated || a.lastModified || '').getTime();
        const dateB = new Date(b.dateCreated || b.lastModified || '').getTime();
        comparison = dateA - dateB;
      } else if (sortBy === 'version') {
        // Parse version numbers (e.g., "1.0", "2.1") for proper numeric comparison
        const parseVersion = (version: string) => {
          const parts = version.split('.').map(Number);
          return parts[0] * 1000 + (parts[1] || 0);
        };
        const versionA = parseVersion(a.version || '1.0');
        const versionB = parseVersion(b.version || '1.0');
        comparison = versionA - versionB;
      }

      return sortOrder === 'desc' ? -comparison : comparison;
    });

    setFilteredDocuments(filtered);
  }, [
    documents,
    searchTerm,
    statusFilter,
    typeFilter,
    showArchivedDocuments,
    sortBy,
    sortOrder,
  ]);

  const handlePreview = (document: Document) => {
    router.push(`${routes.documentsReview}?id=${document.id}&mode=preview`);
  };

  const handleReview = (document: Document) => {
    router.push(`${routes.documentsReview}?id=${document.id}`);
  };

  const handleSign = (document: Document) => {
    router.push(`${routes.documentsSign}?id=${document.id}`);
  };

  const handleRecreate = async (document: Document) => {
    try {
      // Add document to resubmitting set
      setResubmittingDocuments(prev => new Set(prev).add(document.id));

      await recreateMutation.mutateAsync(document.id);

      toast.success(
        `Document "${document.title}" has been recreated with fresh data for review`
      );
      refetchDocuments(); // Refresh document data to show updated status
    } catch (error) {
      console.error('Error recreating document:', error);
      toast.error('Failed to recreate document. Please try again.');
    } finally {
      // Remove document from resubmitting set
      setResubmittingDocuments(prev => {
        const newSet = new Set(prev);
        newSet.delete(document.id);
        return newSet;
      });
    }
  };

  const handleRetract = async (document: Document) => {
    try {
      // Add document to retracting set
      setRetractingDocuments(prev => new Set(prev).add(document.id));

      await retractDocument(document.id);

      // Update local state
      const updatedDocuments = documents.map(doc =>
        doc.id === document.id
          ? {
              ...doc,
              status: 'draft' as Document['status'],
              lastModified: new Date().toISOString(),
            }
          : doc
      );
      setDocuments(updatedDocuments);
      setFilteredDocuments(updatedDocuments);

      toast.success(
        `Document "${document.title}" has been retracted from review`
      );
    } catch (error) {
      console.error('Error retracting document:', error);
      toast.error('Failed to retract document. Please try again.');
    } finally {
      // Remove document from retracting set
      setRetractingDocuments(prev => {
        const newSet = new Set(prev);
        newSet.delete(document.id);
        return newSet;
      });
    }
  };

  // Functions for bulk document selection
  const handleDocumentSelect = (documentId: string, selected: boolean) => {
    setSelectedDocuments(prev => {
      const newSet = new Set(prev);
      if (selected) {
        newSet.add(documentId);
      } else {
        newSet.delete(documentId);
      }
      return newSet;
    });
  };

  const handleSelectAll = (documents: Document[]) => {
    const signedDocuments = documents.filter(doc => doc.status === 'signed');
    setSelectedDocuments(new Set(signedDocuments.map(doc => doc.id)));
  };

  const handleDeselectAll = () => {
    setSelectedDocuments(new Set());
  };

  const getSignedDocuments = () => {
    return filteredDocuments.filter(doc => doc.status === 'signed');
  };

  // Bulk shipping function
  const handleBulkShip = async () => {
    if (selectedDocuments.size === 0) {
      toast.error('Please select at least one document to ship');
      return;
    }

    const documentsToShip = filteredDocuments.filter(doc =>
      selectedDocuments.has(doc.id)
    );

    try {
      setIsBulkShipping(true);

      // Get current user
      const currentUser = await getCurrentUser();

      // Get current user data
      const { data: userList, errors: listErrors } =
        await client.models.User.list({
          filter: { cognitoId: { eq: currentUser.userId } },
          selectionSet: [
            'id',
            'cognitoId',
            'firstName',
            'lastName',
            'phoneNumber',
            'assignedWelonTrustId',
          ],
        });

      if (listErrors) {
        console.error('❌ User query errors:', listErrors);
        throw new Error('Failed to fetch user data');
      }

      if (!userList || userList.length === 0) {
        console.error('❌ No user found with cognitoId:', currentUser.userId);
        throw new Error('User not found');
      }

      const userData = userList[0];

      const { data: userAddresses, errors: userAddressErrors } =
        await client.models.UserAddress.list({
          filter: { userId: { eq: userData.id } },
        });

      if (userAddressErrors) {
        console.error('❌ User address query errors:', userAddressErrors);
        throw new Error('Failed to fetch user address');
      }

      const userAddress =
        userAddresses?.find(addr => addr.isDefault) || userAddresses?.[0];
      if (!userAddress) {
        console.error('❌ No address found for user:', userData.id);
        throw new Error(
          'User address not found. Please save your address first.'
        );
      }

      if (!userData.assignedWelonTrustId) {
        console.error(
          '❌ No assignedWelonTrustId found for user:',
          userData.id
        );
        throw new Error('No assigned Welon Trust found');
      }

      let welonUser = null;

      const { data: welonUserList, errors: welonErrors } =
        await client.models.User.list({
          filter: { cognitoId: { eq: userData.assignedWelonTrustId } },
          selectionSet: [
            'id',
            'cognitoId',
            'firstName',
            'lastName',
            'phoneNumber',
            'role',
          ],
        });

      if (welonErrors) {
        console.error('❌ Method 1 errors:', welonErrors);
      }

      welonUser = welonUserList?.[0];

      // Second try: Search by ID (fallback if assignedWelonTrustId stores ID instead of cognitoId)
      if (!welonUser) {
        const { data: welonUserById, errors: welonByIdErrors } =
          await client.models.User.get(
            {
              id: userData.assignedWelonTrustId,
            },
            {
              selectionSet: [
                'id',
                'cognitoId',
                'firstName',
                'lastName',
                'phoneNumber',
                'role',
              ],
            }
          );

        if (welonByIdErrors) {
          console.error('❌ Method 2 errors:', welonByIdErrors);
        }

        welonUser = welonUserById;
      }

      if (!welonUser) {
        console.error(
          '❌ No Welon Trust user found with either cognitoId or ID:',
          userData.assignedWelonTrustId
        );
        throw new Error('Assigned Welon Trust user not found');
      }

      const { data: welonAddresses, errors: welonAddressErrors } =
        await client.models.UserAddress.list({
          filter: { userId: { eq: welonUser.id } },
        });

      if (welonAddressErrors) {
        console.error(
          '❌ Welon Trust address query errors:',
          welonAddressErrors
        );
        throw new Error('Failed to fetch Welon Trust address');
      }

      const welonAddress =
        welonAddresses?.find(addr => addr.isDefault) || welonAddresses?.[0];
      if (!welonAddress) {
        console.error(
          '❌ No address found for Welon Trust user:',
          welonUser.id
        );
        throw new Error('Welon Trust address not found');
      }

      // Prepare addresses for UPS API
      const fromAddress = {
        name: `${userData.firstName} ${userData.lastName}`,
        phone: userData.phoneNumber || '',
        addressLine1: userAddress.addressLine1,
        addressLine2: userAddress.addressLine2 || '',
        city: userAddress.city,
        stateProvinceCode: userAddress.stateProvinceCode,
        postalCode: userAddress.postalCode,
        countryCode: userAddress.countryCode,
      };

      const toAddress = {
        name: `${welonUser.firstName} ${welonUser.lastName}`,
        phone: welonUser.phoneNumber || '',
        addressLine1: welonAddress.addressLine1,
        addressLine2: welonAddress.addressLine2 || '',
        city: welonAddress.city,
        stateProvinceCode: welonAddress.stateProvinceCode,
        postalCode: welonAddress.postalCode,
        countryCode: welonAddress.countryCode,
      };

      // Create package description with all document titles
      const documentTitles = documentsToShip.map(doc => doc.title).join(', ');
      const packageDescription = `Legal Documents: ${documentTitles}`;

      // Calculate weight based on number of documents (assuming 0.5 lbs per document)
      const estimatedWeight = Math.max(
        1,
        documentsToShip.length * 0.5
      ).toString();

      // Create UPS shipping label
      const upsResponse = await fetch('/api/ups/create-label', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fromAddress,
          toAddress,
          serviceCode: '03', // UPS Ground
          packageOptions: {
            description: packageDescription,
            weight: estimatedWeight,
          },
        }),
      });

      if (!upsResponse.ok) {
        const errorData = await upsResponse.json();
        throw new Error('Failed to create shipping label: ' + errorData.error);
      }

      const upsResponse_data = await upsResponse.json();

      // Extract the actual data from the API response
      const upsResult = upsResponse_data.data || upsResponse_data;

      const shippingLabelData: any = {
        userId: userData.id,
        assignedWelonTrustId: welonUser.id,
        documentIds: Array.from(selectedDocuments), // Array of document IDs
        trackingNumber: upsResult.trackingNumber,
        labelUrl: upsResult.labelUrl,
        fromAddress: JSON.stringify(fromAddress),
        toAddress: JSON.stringify(toAddress),
        serviceCode: '03',
        cost: JSON.stringify(upsResult.cost),
        status: 'created',
        createdAt: new Date().toISOString(),
        packageDescription: packageDescription,
        totalDocuments: documentsToShip.length,
      };

      if (upsResult.estimatedDelivery) {
        shippingLabelData.estimatedDelivery = upsResult.estimatedDelivery;
      }

      const { data: shippingLabel, errors: labelErrors } =
        await client.models.ShippingLabel.create(shippingLabelData);

      if (labelErrors) {
        console.error('Error saving shipping label:', labelErrors);
        throw new Error('Failed to save shipping label');
      }

      // Update all selected documents status to 'shipped'
      const updatePromises = documentsToShip.map(document =>
        client.models.Document.update({
          id: document.id,
          status: 'shipped',
          trackingNumber: upsResult.trackingNumber,
        })
      );

      const updateResults = await Promise.all(updatePromises);
      const updateErrors = updateResults.filter(result => result.errors);

      if (updateErrors.length > 0) {
        console.error('Error updating some document statuses:', updateErrors);
      }

      toast.success(
        `${documentsToShip.length} document(s) have been shipped! Tracking: ${upsResult.trackingNumber}`
      );

      // Send notification about bulk shipping
      try {
        const currentUser = await getCurrentUser();
        const documentTitles = documentsToShip.map(doc => doc.title).join(', ');
        const notificationMessage = `Your documents (${documentTitles}) have been shipped successfully. Tracking number: ${upsResult.trackingNumber}`;
        const emailSubject = 'Documents Shipped - ChildFree Legacy';

        // Get user data for email
        const { data: userList } = await client.models.User.list({
          filter: { cognitoId: { eq: currentUser.userId } },
          selectionSet: ['email', 'firstName', 'lastName'],
        });

        const userData = userList?.[0];
        if (userData?.email) {
          await createNotificationWithEmail(
            notificationMessage,
            currentUser.userId,
            userData.email,
            emailSubject,
            'system-bulk-shipping'
          );
        }
      } catch (notificationError) {
        console.error(
          'Error creating bulk shipping notification:',
          notificationError
        );
        // Continue even if notification fails
      }

      // Clear selection and refresh documents
      setSelectedDocuments(new Set());
      refetchDocuments();
    } catch (error) {
      console.error('Error shipping documents:', error);
      toast.error(
        'Failed to ship documents: ' +
          (error instanceof Error ? error.message : 'Unknown error')
      );
    } finally {
      setIsBulkShipping(false);
    }
  };

  const handleShip = async (document: Document) => {
    try {
      // Add document to shipping set
      setShippingDocuments(prev => new Set(prev).add(document.id));

      // Handle different actions based on document status
      if (document.status === 'draft') {
        // Send for electronic review
        await sendDocumentToWelon(document.id);

        // Update local state
        const updatedDocuments = documents.map(doc =>
          doc.id === document.id
            ? {
                ...doc,
                status: 'sent' as Document['status'],
                lastModified: new Date().toISOString(),
              }
            : doc
        );
        setDocuments(updatedDocuments);
        setFilteredDocuments(updatedDocuments);

        toast.success('Document sent for electronic review!');
        return;
      }

      if (document.status === 'signed') {
        // Toggle document in bulk shipping selection
        setSelectedDocuments(prev => {
          const newSet = new Set(prev);
          if (newSet.has(document.id)) {
            newSet.delete(document.id);
            toast.success(
              `Document "${document.title}" removed from shipping queue`
            );
          } else {
            newSet.add(document.id);
            toast.success(
              `Document "${document.title}" added to shipping queue`
            );

            // Scroll to bulk shipping section
            setTimeout(() => {
              const bulkShippingElement = window.document.querySelector(
                '[data-bulk-shipping]'
              );
              if (bulkShippingElement) {
                bulkShippingElement.scrollIntoView({
                  behavior: 'smooth',
                  block: 'center',
                });
              }
            }, 100);
          }
          return newSet;
        });

        return;
      } else {
        toast.error('Document cannot be shipped in current status');
        return;
      }
    } catch (error) {
      console.error('Error adding document to shipping:', error);
      toast.error('Failed to add document to shipping queue');
    } finally {
      // Remove document from shipping set
      setShippingDocuments(prev => {
        const newSet = new Set(prev);
        newSet.delete(document.id);
        return newSet;
      });
    }
  };

  const handleDownload = async (document: Document) => {
    try {
      // Add document to downloading set
      setDownloadingDocuments(prev => new Set(prev).add(document.id));
      await downloadDocument(document);
    } catch (error) {
      console.error(error);
      toast.error('Something went wrong.');
    } finally {
      // Remove document from downloading set
      setDownloadingDocuments(prev => {
        const newSet = new Set(prev);
        newSet.delete(document.id);
        return newSet;
      });
    }
  };

  const handleDelete = async (document: Document) => {
    // Show confirmation dialog for archiving
    const confirmed = window.confirm(
      `Are you sure you want to archive "${document.title}"?`
    );

    if (!confirmed) return;

    try {
      // Add document to deleting set
      setDeletingDocuments(prev => new Set(prev).add(document.id));

      await deleteDocument(document.id);

      // Update document status in local state instead of removing it
      const updatedDocuments = documents.map(doc =>
        doc.id === document.id
          ? {
              ...doc,
              status: 'archived' as Document['status'],
              lastModified: new Date().toISOString(),
            }
          : doc
      );
      setDocuments(updatedDocuments);
      setFilteredDocuments(updatedDocuments);

      toast.success('Document archived successfully');
    } catch (error) {
      console.error('Error archiving document:', error);
      toast.error('Failed to archive document. Please try again.');
    } finally {
      // Remove document from deleting set
      setDeletingDocuments(prev => {
        const newSet = new Set(prev);
        newSet.delete(document.id);
        return newSet;
      });
    }
  };

  const handlePermanentDelete = async (document: Document) => {
    // Show strong confirmation dialog for permanent deletion
    const confirmed = window.confirm(
      `⚠️ PERMANENT DELETION WARNING ⚠️\n\nAre you absolutely sure you want to permanently delete "${document.title}"?\n\nThis action CANNOT be undone and the document will be lost forever.\n\nClick OK only if you are certain you want to proceed.`
    );

    if (!confirmed) return;

    try {
      // Add document to deleting set
      setDeletingDocuments(prev => new Set(prev).add(document.id));

      await permanentlyDeleteDocument(document.id);

      // Remove document from local state completely
      const updatedDocuments = documents.filter(doc => doc.id !== document.id);
      setDocuments(updatedDocuments);
      setFilteredDocuments(updatedDocuments);

      toast.success('Document permanently deleted');
    } catch (error) {
      console.error('Error permanently deleting document:', error);
      toast.error('Failed to permanently delete document. Please try again.');
    } finally {
      // Remove document from deleting set
      setDeletingDocuments(prev => {
        const newSet = new Set(prev);
        newSet.delete(document.id);
        return newSet;
      });
    }
  };

  const handleUploadDocument = () => {
    if (!selectedUser) {
      alert('Please select a member first');
      return;
    }
    setShowUploadModal(true);
  };

  const handleUploadSuccess = (newDocument: Document) => {
    // Add the new document to the list
    const updatedDocuments = [...documents, newDocument];
    setDocuments(updatedDocuments);
    setFilteredDocuments(updatedDocuments);
    alert(`Document "${newDocument.title}" uploaded successfully!`);
  };

  const handleUpdateStatus = (
    document: Document,
    newStatus: Document['status']
  ) => {
    // In real implementation, this would call API to update status
    const updatedDocuments = documents.map(doc =>
      doc.id === document.id
        ? { ...doc, status: newStatus, lastModified: new Date().toISOString() }
        : doc
    );
    setDocuments(updatedDocuments);
    alert(`Document status updated to: ${newStatus}`);
  };

  // Map database status to display status for counting
  const mapStatusForCounting = (status: string) => {
    switch (status) {
      case 'draft':
        return 'ready_for_review';
      case 'ready_for_signing':
        return 'ready_for_signing';
      case 'signed':
        return 'signed';
      case 'shipped':
        return 'shipped';
      case 'received':
        return 'approved';
      case 'archived':
        return 'archived';
      default:
        return status;
    }
  };

  const getStatusCounts = () => {
    if (isWelonTrustUser) {
      // For Welon Trust users, all documents are signed, so show simpler counts
      return {
        total: documents.length,
        ready_for_review: 0,
        ready_for_signing: 0,
        shipped: 0,
        signed: documents.length, // All documents are signed for Welon Trust
        approved: 0,
      };
    }

    const counts = {
      total: documents.length,
      ready_for_review: documents.filter(
        d => mapStatusForCounting(d.status) === 'ready_for_review'
      ).length,
      ready_for_signing: documents.filter(
        d => mapStatusForCounting(d.status) === 'ready_for_signing'
      ).length,
      shipped: documents.filter(
        d => mapStatusForCounting(d.status) === 'shipped'
      ).length,
      signed: documents.filter(d => mapStatusForCounting(d.status) === 'signed')
        .length,
      approved: documents.filter(
        d => mapStatusForCounting(d.status) === 'approved'
      ).length,
    };
    return counts;
  };

  const statusCounts = getStatusCounts();
  const filterParam = searchParams.get('filter');
  const isFiltered = filterParam && filterParam !== 'all';

  // Get unique document types from actual documents
  const getUniqueDocumentTypes = (): Document['type'][] => {
    const types = [...new Set(documents.map(doc => doc.type))].filter(
      Boolean
    ) as Document['type'][];
    return types.sort();
  };

  const availableTypes = getUniqueDocumentTypes();

  // Reset type filter if selected type is not available
  React.useEffect(() => {
    if (typeFilter !== 'all' && !availableTypes.includes(typeFilter)) {
      setTypeFilter('all');
    }
  }, [availableTypes, typeFilter]);

  const getFilterTitle = () => {
    if (isWelonTrustUser) {
      if (selectedUser) {
        return `Signed Documents - ${selectedUser.name}`;
      }
      return 'Signed Documents - Assigned Members';
    }
    if (filterParam === 'ready_for_review') return 'Documents Ready for Review';
    if (filterParam === 'ready_for_signing')
      return 'Documents Ready for Signing';
    return 'Document Management';
  };

  const getFilterDescription = () => {
    if (isWelonTrustUser) {
      if (selectedUser) {
        return `View signed documents from ${selectedUser.name}`;
      }
      return 'View signed documents from members assigned to you';
    }
    if (filterParam === 'ready_for_review')
      return 'Review your documents before proceeding to signing';
    if (filterParam === 'ready_for_signing')
      return 'Sign your approved documents';
    return 'Access, download, and manage your generated legal documents. View document history, track updates, and request modifications when needed.';
  };

  if (loading) {
    return (
      <div className='flex items-center justify-center min-h-[400px]'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4'></div>
          <p className='text-muted-foreground'>Loading your documents...</p>
        </div>
      </div>
    );
  }

  // Show error state for documents query
  if (realDocumentsError) {
    return (
      <div className='flex items-center justify-center min-h-[400px]'>
        <div className='text-center'>
          <div className='text-red-500 text-6xl mb-4'>⚠️</div>
          <h3 className='text-lg font-medium text-red-800 mb-2'>
            Error Loading Documents
          </h3>
          <p className='text-red-600 mb-4'>
            {realDocumentsError instanceof Error
              ? realDocumentsError.message
              : 'Failed to load your documents. Please try again.'}
          </p>
          <Button
            onClick={() => window.location.reload()}
            variant='outline'
            className='border-red-300 text-red-700 hover:bg-red-50'
          >
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  // // Show error state for real documents query
  // if (!isWelonTrust && realDocumentsError) {
  //   return (
  //     <div className='flex items-center justify-center min-h-[400px]'>
  //       <div className='text-center'>
  //         <div className='text-red-500 text-6xl mb-4'>⚠️</div>
  //         <h3 className='text-lg font-medium text-red-800 mb-2'>
  //           Error Loading Documents
  //         </h3>
  //         <p className='text-red-600 mb-4'>
  //           {realDocumentsError instanceof Error
  //             ? realDocumentsError.message
  //             : 'Failed to load your documents. Please try again.'}
  //         </p>
  //         <Button
  //           onClick={() => window.location.reload()}
  //           variant='outline'
  //           className='border-red-300 text-red-700 hover:bg-red-50'
  //         >
  //           Try Again
  //         </Button>
  //       </div>
  //     </div>
  //   );
  // }

  return (
    <div id={'documents-to-print'} className='space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div className='flex flex-col space-y-2 mr-12'>
          <h1 className='text-3xl font-bold text-[var(--foreground)]'>
            {getFilterTitle()}
          </h1>
          <p className='text-[var(--custom-gray-dark)]'>
            {getFilterDescription()}
          </p>
          {isFiltered && (
            <div className='mt-2'>
              <Badge
                variant='outline'
                className='bg-blue-50 text-blue-700 border-blue-200'
              >
                Filtered from Dashboard
              </Badge>
            </div>
          )}
        </div>
        <div className='flex items-center gap-2'>
          {isFiltered && (
            <Button
              variant='outline'
              onClick={() => router.push(routes.documentsManage)}
              className='flex items-center gap-2'
            >
              <Filter className='h-4 w-4' />
              Clear Filter
            </Button>
          )}
          <Button
            variant='outline'
            onClick={() => refetchDocuments()}
            disabled={realDocumentsLoading}
            className='flex items-center gap-2'
          >
            <Search className='h-4 w-4' />
            {realDocumentsLoading ? 'Refreshing...' : 'Refresh'}
          </Button>
          {!isWelonTrustUser && (
            <Link href={routes.member.documentsCreate}>
              <Button className='flex items-center gap-2'>
                <Plus className='h-4 w-4' />
                Create New Document
              </Button>
            </Link>
          )}
        </div>
      </div>

      {/* Quick Actions for Filtered Views */}
      {isFiltered && (
        <div className='bg-blue-50 border border-blue-200 rounded-lg p-4'>
          <h3 className='font-semibold text-blue-900 mb-2'>Quick Actions</h3>
          <div className='flex flex-wrap gap-2'>
            {filterParam === 'ready_for_review' && (
              <>
                <Button
                  size='sm'
                  variant='outline'
                  onClick={() => {
                    const firstDoc = filteredDocuments.find(
                      d => d.status === 'ready_for_review'
                    );
                    if (firstDoc) handleReview(firstDoc);
                  }}
                  disabled={
                    !filteredDocuments.some(
                      d => d.status === 'ready_for_review'
                    )
                  }
                  className='border-blue-300 text-blue-700 hover:bg-blue-100'
                >
                  <Eye className='h-4 w-4 mr-2' />
                  Review First Document
                </Button>
              </>
            )}
            {filterParam === 'ready_for_signing' && (
              <>
                <Button
                  size='sm'
                  variant='outline'
                  onClick={() => {
                    const firstDoc = filteredDocuments.find(
                      d => d.status === 'ready_for_signing'
                    );
                    if (firstDoc) handleSign(firstDoc);
                  }}
                  disabled={
                    !filteredDocuments.some(
                      d => d.status === 'ready_for_signing'
                    )
                  }
                  className='border-green-300 text-green-700 hover:bg-green-100'
                >
                  <PenTool className='h-4 w-4 mr-2' />
                  Sign First Document
                </Button>
              </>
            )}
          </div>
        </div>
      )}

      {/* Status Overview */}
      <div
        className={`grid grid-cols-1 gap-4 ${isWelonTrustUser ? 'md:grid-cols-2' : 'md:grid-cols-5'}`}
      >
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <div className='text-2xl font-bold text-blue-600'>
                {statusCounts.total}
              </div>
              <div className='text-sm text-muted-foreground'>
                {isWelonTrustUser
                  ? selectedUser
                    ? `${selectedUser.name}'s Signed Documents`
                    : 'Total Signed Documents'
                  : 'Total Documents'}
              </div>
            </div>
          </CardContent>
        </Card>
        {!isWelonTrustUser && (
          <>
            <Card>
              <CardContent className='p-4'>
                <div className='text-center'>
                  <div className='text-2xl font-bold text-orange-600'>
                    {statusCounts.ready_for_review}
                  </div>
                  <div className='text-sm text-muted-foreground'>
                    Ready for Review
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className='p-4'>
                <div className='text-center'>
                  <div className='text-2xl font-bold text-green-600'>
                    {statusCounts.shipped}
                  </div>
                  <div className='text-sm text-muted-foreground'>Shipped</div>
                </div>
              </CardContent>
            </Card>
          </>
        )}
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <div className='text-2xl font-bold text-purple-600'>
                {statusCounts.signed}
              </div>
              <div className='text-sm text-muted-foreground'>
                {isWelonTrustUser
                  ? selectedUser
                    ? `From ${selectedUser.name}`
                    : 'From Assigned Members'
                  : 'Signed'}
              </div>
            </div>
          </CardContent>
        </Card>
        {!isWelonTrustUser && (
          <Card>
            <CardContent className='p-4'>
              <div className='text-center'>
                <div className='text-2xl font-bold text-emerald-600'>
                  {statusCounts.approved}
                </div>
                <div className='text-sm text-muted-foreground'>Approved</div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Filters & Sorting */}
      <Card>
        <CardHeader className='pb-3'>
          <CardTitle className='flex items-center gap-2 text-lg'>
            <Filter className='h-4 w-4' />
            Filter & Sort
          </CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          {/* Search */}
          <div className='relative'>
            <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground' />
            <Input
              placeholder='Search documents...'
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className='pl-10 h-9'
            />
          </div>

          {/* Filters & Sorting in one row */}
          <div className='grid grid-cols-2 md:grid-cols-4 gap-3'>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className='h-9'>
                <SelectValue placeholder='Status' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>All Statuses</SelectItem>
                <SelectItem value='draft'>Draft</SelectItem>
                <SelectItem value='ready_for_review'>
                  Ready for Review
                </SelectItem>
                <SelectItem value='ready_for_signing'>
                  Ready for Signing
                </SelectItem>
                <SelectItem value='signed'>Signed</SelectItem>
                <SelectItem value='shipped'>Shipped</SelectItem>
                <SelectItem value='received'>Received</SelectItem>
                <SelectItem value='approved'>Approved</SelectItem>
                <SelectItem value='archived'>Archived</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={typeFilter}
              onValueChange={value =>
                setTypeFilter(value as 'all' | Document['type'])
              }
            >
              <SelectTrigger className='h-9'>
                <SelectValue placeholder='Type' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>All Types</SelectItem>
                {availableTypes.map(type => (
                  <SelectItem key={type} value={type}>
                    {formatDocumentType(type)}
                  </SelectItem>
                ))}
                {availableTypes.length === 0 && (
                  <>
                    <SelectItem value='Will'>Will</SelectItem>
                    <SelectItem value='Trust'>Trust</SelectItem>
                    <SelectItem value='Healthcare POA'>
                      Healthcare POA
                    </SelectItem>
                    <SelectItem value='Financial POA'>Financial POA</SelectItem>
                  </>
                )}
              </SelectContent>
            </Select>

            <Select
              value={sortBy}
              onValueChange={(value: 'dateCreated' | 'version') =>
                setSortBy(value)
              }
            >
              <SelectTrigger className='h-9'>
                <SelectValue placeholder='Sort by' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='dateCreated'>Date</SelectItem>
                <SelectItem value='version'>Version</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={sortOrder}
              onValueChange={(value: 'asc' | 'desc') => setSortOrder(value)}
            >
              <SelectTrigger className='h-9'>
                <SelectValue placeholder='Order' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='desc'>
                  {sortBy === 'dateCreated' ? 'Newest' : 'Highest'}
                </SelectItem>
                <SelectItem value='asc'>
                  {sortBy === 'dateCreated' ? 'Oldest' : 'Lowest'}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Actions Row */}
          <div className='flex items-center justify-between pt-3 border-t'>
            <div className='flex items-center gap-2'>
              <Button
                variant={showArchivedDocuments ? 'default' : 'outline'}
                size='sm'
                onClick={() => setShowArchivedDocuments(!showArchivedDocuments)}
                className='h-8 text-xs'
              >
                {showArchivedDocuments ? 'Hide' : 'Show'} Archived
              </Button>
              {documents.filter(doc => doc.status === 'archived').length >
                0 && (
                <Badge variant='secondary' className='text-xs h-5'>
                  {documents.filter(doc => doc.status === 'archived').length}
                </Badge>
              )}
            </div>

            {(searchTerm ||
              statusFilter !== 'all' ||
              typeFilter !== 'all' ||
              sortBy !== 'dateCreated' ||
              sortOrder !== 'desc') && (
              <Button
                variant='ghost'
                size='sm'
                onClick={() => {
                  setSearchTerm('');
                  setStatusFilter('all');
                  setTypeFilter('all');
                  setSortBy('dateCreated');
                  setSortOrder('desc');
                }}
                className='h-8 text-xs text-gray-600 hover:text-gray-800'
              >
                Clear All
              </Button>
            )}
          </div>

          {/* Active Filters Status */}
          {(searchTerm ||
            statusFilter !== 'all' ||
            typeFilter !== 'all' ||
            showArchivedDocuments ||
            sortBy !== 'dateCreated' ||
            sortOrder !== 'desc') && (
            <div className='mt-3 p-2 bg-blue-50 border border-blue-200 rounded-md'>
              <div className='flex items-center justify-between'>
                <div className='flex items-center gap-1 text-xs text-blue-800 flex-wrap'>
                  <span className='font-medium'>
                    {filteredDocuments.length} of {documents.length}
                  </span>
                  {searchTerm && (
                    <Badge variant='outline' className='bg-white text-xs h-5'>
                      "{searchTerm}"
                    </Badge>
                  )}
                  {statusFilter !== 'all' && (
                    <Badge variant='outline' className='bg-white text-xs h-5'>
                      {statusFilter}
                    </Badge>
                  )}
                  {typeFilter !== 'all' && (
                    <Badge variant='outline' className='bg-white text-xs h-5'>
                      {formatDocumentType(typeFilter)}
                    </Badge>
                  )}
                  {showArchivedDocuments && (
                    <Badge
                      variant='outline'
                      className='bg-yellow-50 text-yellow-700 border-yellow-200 text-xs h-5'
                    >
                      Archived
                    </Badge>
                  )}
                  {(sortBy !== 'dateCreated' || sortOrder !== 'desc') && (
                    <Badge
                      variant='outline'
                      className='bg-purple-50 text-purple-700 border-purple-200 text-xs h-5'
                    >
                      {sortBy === 'dateCreated' ? 'Date' : 'Ver'}{' '}
                      {sortOrder === 'desc' ? '↓' : '↑'}
                    </Badge>
                  )}
                </div>
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={() => {
                    setSearchTerm('');
                    setStatusFilter('all');
                    setTypeFilter('all');
                    setShowArchivedDocuments(false);
                  }}
                  className='text-blue-600 hover:text-blue-800'
                >
                  Clear All Filters
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Bulk Shipping Section */}
      {!isWelonTrustUser && getSignedDocuments().length > 0 && (
        <Card data-bulk-shipping>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Truck className='h-5 w-5' />
              Bulk Shipping
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              <p className='text-sm text-muted-foreground'>
                Select multiple signed documents to ship them together in one
                package. Use "Add to Shipping" buttons or select documents here.
              </p>

              {/* Shipping Requirements Alert */}
              <div className='bg-blue-50 border border-blue-200 rounded-lg p-4'>
                <div className='flex items-start gap-3'>
                  <div className='flex-shrink-0'>
                    <svg
                      className='h-5 w-5 text-blue-600 mt-0.5'
                      fill='currentColor'
                      viewBox='0 0 20 20'
                    >
                      <path
                        fillRule='evenodd'
                        d='M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z'
                        clipRule='evenodd'
                      />
                    </svg>
                  </div>
                  <div className='flex-1'>
                    <h4 className='text-sm font-medium text-blue-900 mb-2'>
                      Before shipping, please ensure:
                    </h4>
                    <ul className='text-sm text-blue-800 space-y-1'>
                      <li>
                        • Both you and your Welon Trust representative have
                        correct addresses on the{' '}
                        <strong>Shipping & Tracking</strong> page
                      </li>
                      <li>
                        • Phone numbers are properly formatted (max 15 symbols)
                        in <strong>Settings</strong>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className='flex items-center gap-2'>
                <Button
                  variant='outline'
                  size='sm'
                  onClick={() => handleSelectAll(getSignedDocuments())}
                  disabled={
                    selectedDocuments.size === getSignedDocuments().length
                  }
                >
                  Select All Signed
                </Button>
                <Button
                  variant='outline'
                  size='sm'
                  onClick={handleDeselectAll}
                  disabled={selectedDocuments.size === 0}
                >
                  Deselect All
                </Button>
                <div className='flex-1' />
                {selectedDocuments.size > 0 && (
                  <Button
                    onClick={handleBulkShip}
                    disabled={isBulkShipping || selectedDocuments.size === 0}
                    className='flex items-center gap-2 bg-purple-600 hover:bg-purple-700 text-[var(--off-white)]'
                  >
                    <Truck className='h-4 w-4' />
                    {isBulkShipping
                      ? 'Shipping...'
                      : `Ship ${selectedDocuments.size} Document(s)`}
                  </Button>
                )}
              </div>

              {selectedDocuments.size > 0 && (
                <div className='bg-purple-50 border border-purple-200 rounded-lg p-3'>
                  <h4 className='font-medium text-purple-900 mb-2'>
                    Selected Documents:
                  </h4>
                  <div className='space-y-1'>
                    {Array.from(selectedDocuments).map(docId => {
                      const doc = filteredDocuments.find(d => d.id === docId);
                      return doc ? (
                        <div
                          key={docId}
                          className='flex items-center justify-between text-sm'
                        >
                          <span className='text-purple-800'>
                            {doc.title} ({formatDocumentType(doc.type)})
                          </span>
                          <Button
                            variant='ghost'
                            size='sm'
                            onClick={() => handleDocumentSelect(docId, false)}
                            className='h-6 w-6 p-0 text-purple-600 hover:text-purple-800'
                          >
                            ×
                          </Button>
                        </div>
                      ) : null;
                    })}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Documents List */}
      <div className='space-y-4'>
        {filteredDocuments.length === 0 ? (
          <Card>
            <CardContent className='p-8 text-center'>
              <FileText className='h-12 w-12 text-muted-foreground mx-auto mb-4' />
              <h3 className='text-lg font-medium text-[var(--custom-gray-dark)] mb-2'>
                No documents found
              </h3>
              <p className='text-muted-foreground mb-4'>
                {documents.length === 0
                  ? isWelonTrustUser
                    ? selectedUser
                      ? `No signed documents available from ${selectedUser.name}.`
                      : 'No signed documents available from your assigned members.'
                    : "You haven't created any documents yet."
                  : 'Try adjusting your search or filter criteria.'}
              </p>
              {documents.length === 0 && !isWelonTrustUser && (
                <Button
                  onClick={() => router.push('/member/interview/review')}
                  className='flex items-center gap-2'
                >
                  <Plus className='h-4 w-4' />
                  Generate Your Documents
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          filteredDocuments.map(document => (
            <DocumentStatusCard
              key={document.id}
              document={document}
              isDownloadingPdf={downloadingDocuments.has(document.id)}
              isResubmitting={resubmittingDocuments.has(document.id)}
              isRetracting={retractingDocuments.has(document.id)}
              isShipping={shippingDocuments.has(document.id)}
              isDeleting={deletingDocuments.has(document.id)}
              onPreview={() => handlePreview(document)}
              onReview={() => handleReview(document)}
              onSign={() => handleSign(document)}
              onDownload={() => handleDownload(document)}
              onResubmit={() => handleRecreate(document)}
              onRetract={() => handleRetract(document)}
              onShip={() => handleShip(document)}
              onDelete={() => handleDelete(document)}
              onPermanentDelete={() => handlePermanentDelete(document)}
              showBulkSelect={
                !isWelonTrustUser &&
                getSignedDocuments().length > 0 &&
                document.status === 'signed'
              }
              isSelected={selectedDocuments.has(document.id)}
              isAddedToShipping={selectedDocuments.has(document.id)}
              onBulkSelect={handleDocumentSelect}
            />
          ))
        )}
      </div>
    </div>
  );
}

export default function DocumentsPage() {
  return (
    <Suspense
      fallback={
        <div className='flex items-center justify-center min-h-[400px]'>
          <div className='text-center'>
            <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4'></div>
            <p className='text-muted-foreground'>Loading...</p>
          </div>
        </div>
      }
    >
      <DocumentsContent />
    </Suspense>
  );
}
