'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { DatePicker } from '@/components/ui/date-picker';
import { useInterviewNew } from '@/components/interview/interview-new-context';
import { InterviewQuestion } from '@/lib/api/interview-new-user';
import { RotateCcw, Edit, Check, X } from 'lucide-react';
import routes from '@/utils/routes';
import { useQuery } from '@tanstack/react-query';
import { DocumentPreviewClient } from '@/app/dashboard/member/documents/preview/[id]/document-preview-client';
import { useAuth } from '@/context/AuthContext';
import { finalizeDocumentsForTemplates } from '@/lib/utils/document-create/finalize-documents';
import {
  getMemberAvailableTemplates,
  getMemberInterviewAnswers,
} from '@/lib/api/member-documents';
import { fetchUserByCognitoId } from '@/lib/data/users';
import { checkDocumentDuplicate } from '@/lib/api/documents';

// Map template type to valid document type enum values (same as in finalize-documents.ts)
const mapTemplateTypeToDocumentType = (templateType: string): string => {
  const typeMapping: Record<string, string> = {
    Will: 'Will',
    Trust: 'Trust',
    'Healthcare POA': 'Healthcare_POA',
    'Financial POA': 'Financial_POA',
    'Advance Directive': 'Advance_Directive',
    POA: 'Healthcare_POA', // Default POA to Healthcare_POA
    Medical: 'Healthcare_POA',
    Financial: 'Financial_POA',
    Healthcare: 'Healthcare_POA',
  };

  const mappedType = typeMapping[templateType] || 'Other';
  console.log(
    `[Interview Review] Mapping template type "${templateType}" to document type "${mappedType}"`
  );
  return mappedType;
};

// Helper function to parse options (based on question-new.tsx)
const parseOptions = (
  options?: string[] | any[]
): Array<{
  id: string;
  label: string;
  value: string;
  nextQuestionId?: string;
}> => {
  if (!options || !Array.isArray(options)) return [];

  return options.map((option, index) => {
    // Handle JSON string options (from the database)
    if (typeof option === 'string') {
      try {
        // Try to parse as JSON first
        const parsed = JSON.parse(option);
        if (typeof parsed === 'object' && parsed !== null) {
          return {
            id: parsed.id || `option_${index}`,
            label: parsed.label || parsed.value || String(parsed),
            value: parsed.value || parsed.label || String(parsed),
            nextQuestionId: parsed.nextQuestionId,
          };
        }
      } catch (e) {
        // If JSON parsing fails, treat as simple string
      }

      // Simple string option
      return {
        id: `option_${index}`,
        label: option,
        value: option,
      };
    } else if (typeof option === 'object' && option !== null) {
      // Already parsed object
      return {
        id: option.id || `option_${index}`,
        label: option.label || option.value || String(option),
        value: option.value || option.label || String(option),
        nextQuestionId: option.nextQuestionId,
      };
    } else {
      return {
        id: `option_${index}`,
        label: String(option),
        value: String(option),
      };
    }
  });
};

// Component for rendering different input types
const EditableInput: React.FC<{
  question: InterviewQuestion;
  value: string;
  onChange: (value: string) => void;
  disabled?: boolean;
}> = ({ question, value, onChange, disabled }) => {
  const parsedOptions = parseOptions(question.options);

  // Debug logging
  console.log(
    'EditableInput - Question:',
    question.questionId,
    'Type:',
    question.type,
    'Options:',
    question.options,
    'Parsed:',
    parsedOptions
  );

  switch (question.type) {
    case 'text':
      return (
        <Input
          value={value}
          onChange={e => onChange(e.target.value)}
          disabled={disabled}
          className='w-full'
        />
      );

    case 'date':
      const handleDateChange = (date: Date | undefined) => {
        if (date) {
          // Format date as YYYY-MM-DD
          const formattedDate = date.toISOString().split('T')[0];
          onChange(formattedDate);
        } else {
          onChange('');
        }
      };

      const dateValue = value ? new Date(value) : undefined;

      return <DatePicker date={dateValue} setDate={handleDateChange} />;

    case 'radio':
      return (
        <RadioGroup
          value={value}
          onValueChange={onChange}
          disabled={disabled}
          className='space-y-2'
        >
          {parsedOptions.map(option => (
            <div key={option.id} className='flex items-center space-x-2'>
              <RadioGroupItem value={option.value} id={option.id} />
              <Label htmlFor={option.id}>{option.label}</Label>
            </div>
          ))}
        </RadioGroup>
      );

    case 'select':
      return (
        <Select value={value} onValueChange={onChange} disabled={disabled}>
          <SelectTrigger className='w-full'>
            <SelectValue placeholder='Select an option...' />
          </SelectTrigger>
          <SelectContent>
            {parsedOptions.map(option => (
              <SelectItem key={option.id} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      );

    case 'checkbox':
      const selectedValues = value ? value.split(',').map(v => v.trim()) : [];

      const handleCheckboxChange = (optionValue: string, checked: boolean) => {
        let newValues = [...selectedValues];
        if (checked) {
          if (!newValues.includes(optionValue)) {
            newValues.push(optionValue);
          }
        } else {
          newValues = newValues.filter(v => v !== optionValue);
        }
        onChange(newValues.join(', '));
      };

      return (
        <div className='space-y-2'>
          {parsedOptions.map(option => (
            <div key={option.id} className='flex items-center space-x-2'>
              <Checkbox
                id={option.id}
                checked={selectedValues.includes(option.value)}
                onCheckedChange={checked =>
                  handleCheckboxChange(option.value, !!checked)
                }
                disabled={disabled}
              />
              <Label htmlFor={option.id}>{option.label}</Label>
            </div>
          ))}
        </div>
      );

    default:
      return (
        <Input
          value={value}
          onChange={e => onChange(e.target.value)}
          disabled={disabled}
          className='w-full'
        />
      );
  }
};

const ResponseSection: React.FC<{
  title: string;
  questions: InterviewQuestion[];
  getAnswerForQuestion: (questionId: string) => string | undefined;
  updateSingleAnswer: (questionId: string, answer: string) => Promise<void>;
}> = ({ title, questions, getAnswerForQuestion, updateSingleAnswer }) => {
  const [editingQuestionId, setEditingQuestionId] = useState<string | null>(
    null
  );
  const [editingAnswer, setEditingAnswer] = useState<string>('');
  const [isUpdating, setIsUpdating] = useState<boolean>(false);

  const handleStartEdit = (questionId: string) => {
    const currentAnswer = getAnswerForQuestion(questionId) || '';
    setEditingQuestionId(questionId);
    setEditingAnswer(currentAnswer);
  };

  const handleSaveEdit = async () => {
    if (!editingQuestionId) return;

    try {
      setIsUpdating(true);
      await updateSingleAnswer(editingQuestionId, editingAnswer);
      setEditingQuestionId(null);
      setEditingAnswer('');
    } catch (error) {
      console.error('Failed to update answer:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleCancelEdit = () => {
    setEditingQuestionId(null);
    setEditingAnswer('');
  };

  return (
    <div className='mb-8'>
      <h2 className='text-xl font-bold text-black-6c mb-4 border-b pb-2'>
        {title}
      </h2>
      <div className='space-y-4'>
        {questions.map(question => {
          const response = getAnswerForQuestion(question.questionId);
          if (!response) return null;

          const isEditing = editingQuestionId === question.questionId;

          return (
            <div
              key={question.questionId}
              className='flex justify-between items-start p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors'
            >
              <div className='flex-1'>
                <p className='font-medium mb-2'>{question.text}</p>
                {isEditing ? (
                  <div className='space-y-2'>
                    <EditableInput
                      question={question}
                      value={editingAnswer}
                      onChange={setEditingAnswer}
                      disabled={isUpdating}
                    />
                    <div className='flex gap-2'>
                      <Button
                        size='sm'
                        onClick={handleSaveEdit}
                        disabled={isUpdating}
                      >
                        <Check className='w-4 h-4 mr-1' />
                        {isUpdating ? 'Saving...' : 'Save'}
                      </Button>
                      <Button
                        size='sm'
                        variant='outline'
                        onClick={handleCancelEdit}
                        disabled={isUpdating}
                      >
                        <X className='w-4 h-4 mr-1' />
                        Cancel
                      </Button>
                    </div>
                  </div>
                ) : (
                  <p className='text-[var(--custom-gray-medium)]'>{response}</p>
                )}
              </div>
              {!isEditing && (
                <Button
                  variant='outline'
                  size='sm'
                  onClick={() => handleStartEdit(question.questionId)}
                  className='ml-4 flex-shrink-0'
                >
                  <Edit className='w-4 h-4 mr-1' />
                  Edit
                </Button>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

// Document preview component
const DocumentPreview: React.FC<{
  getAnswerForQuestion: (questionId: string) => string | undefined;
}> = ({ getAnswerForQuestion }) => {
  const { user } = useAuth();

  const { data: userData, isLoading: isLoadingUser } = useQuery({
    queryKey: ['user-data', user?.userId],
    queryFn: () => fetchUserByCognitoId(user?.userId || ''),
    enabled: !!user?.userId,
  });

  const { data: templates, isLoading: isLoadingTemplate } = useQuery({
    queryKey: ['templates', userData?.state],
    queryFn: () => getMemberAvailableTemplates(userData?.state),
    enabled: !!userData?.state,
  });

  const { data: userAnswers, isLoading: isLoadingAnswerers } = useQuery({
    queryKey: ['users-answers'],
    queryFn: getMemberInterviewAnswers,
  });

  if (isLoadingTemplate || isLoadingAnswerers || isLoadingUser) {
    return (
      <div className='flex items-center justify-center p-8'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2'></div>
          <p className='text-gray-600'>Loading templates...</p>
        </div>
      </div>
    );
  }

  if (!templates || !userAnswers || !userData) {
    return null;
  }

  const documentType = 'Document';
  const documentState = userData.state || 'Unknown State';
  const templateName = `${documentState} ${documentType}`;

  return templates.map(template => {
    const templateContent = template.latestVersion?.content || '';

    return (
      <DocumentPreviewClient
        key={template.id}
        templateId={template.id}
        documentType={documentType}
        documentState={documentState}
        templateName={templateName}
        templateContent={templateContent}
        template={template}
        answersMapping={userAnswers}
        isPreviewMode={true}
      />
    );
  });
};

const ReviewContent: React.FC = () => {
  const router = useRouter();
  const { user } = useAuth();

  const [isFinalizing, setIsFinalizing] = useState<boolean>(false);
  const [showPreview, setShowPreview] = useState<boolean>(false);

  const {
    getAnswerForQuestion,
    getVisibleQuestions,
    isComplete,
    resetInterview,
    updateSingleAnswer,
    isLoadingUserProgress,
    isLoading,
  } = useInterviewNew();

  // Get only visible questions (those that should be shown based on conditional logic)
  const visibleQuestions = getVisibleQuestions();

  const handleResetInterview = async () => {
    await resetInterview();
    router.push(routes.member.interview);
  };

  const handleFinalize = async () => {
    try {
      if (!user?.userId) return;
      setIsFinalizing(true);

      // Get user data first to get their state
      const userData = await fetchUserByCognitoId(user.userId);
      if (!userData) {
        throw new Error('User data not found');
      }

      // Get templates based on user's state
      const templates = await getMemberAvailableTemplates(
        userData.state || 'California'
      );

      // Check for active documents and filter out templates that already have active documents
      const duplicateChecks = await Promise.all(
        templates.map(async template => {
          try {
            console.log(
              `[Interview Review] Checking duplicates for template: ${template.type} (ID: ${template.id})`
            );

            // Map template type to document type before checking duplicates
            const mappedDocumentType = mapTemplateTypeToDocumentType(
              template.type
            );

            console.log(
              `[Interview Review] Mapped "${template.type}" to "${mappedDocumentType}" for duplicate check`
            );

            const duplicateCheck = await checkDocumentDuplicate(
              mappedDocumentType,
              user.userId
            );

            console.log(
              `[Interview Review] Duplicate check result for ${mappedDocumentType}:`,
              duplicateCheck
            );

            return {
              template: template,
              hasDuplicate: duplicateCheck.hasDuplicate,
            };
          } catch (error) {
            console.error(
              `Error checking duplicate for ${template.type}:`,
              error
            );
            return { template: template, hasDuplicate: false };
          }
        })
      );

      // Filter out templates that have active duplicates
      const templatesToCreate = duplicateChecks
        .filter(check => !check.hasDuplicate)
        .map(check => check.template);

      const skippedTypes = duplicateChecks
        .filter(check => check.hasDuplicate)
        .map(check => check.template.type);

      if (skippedTypes.length > 0) {
        console.log(
          `Skipping creation of existing document types: ${skippedTypes.join(', ')}`
        );
      }

      if (templatesToCreate.length === 0) {
        alert(
          'All document types already exist. No new documents will be created.'
        );
        router.push(routes.member.documents);
        return;
      }

      console.log(
        '===> 1 CREATING DOCUMENTS FOR TEMPLATES:',
        templatesToCreate
      );

      // Create documents only for templates that don't have active duplicates
      await finalizeDocumentsForTemplates(user.userId, templatesToCreate);
      router.push(routes.member.documents);
    } catch (error) {
      console.error(error);
    } finally {
      setIsFinalizing(false);
    }
  };

  if (isLoading || isLoadingUserProgress) {
    return null;
  }

  if (!isComplete) {
    router.push(routes.member.interview);
    return null;
  }

  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-4xl mx-auto'>
        <div className='mb-8 mr-12'>
          <h1 className='text-3xl font-bold text-[var(--foreground)] mb-2'>
            Review Your Information
          </h1>
          <p className='text-[var(--custom-gray-dark)]'>
            Please review the information you've provided. You can edit any
            section by clicking the "Edit" button.
          </p>
        </div>

        <div className='mb-8 flex justify-end space-x-4'>
          <Button
            variant='outline'
            onClick={() => setShowPreview(!showPreview)}
          >
            {showPreview ? 'Hide Document Preview' : 'Show Document Preview'}
          </Button>
        </div>

        {showPreview ? (
          <div className='mb-8'>
            <DocumentPreview getAnswerForQuestion={getAnswerForQuestion} />
          </div>
        ) : (
          <>
            <ResponseSection
              title='Your Responses'
              questions={visibleQuestions}
              getAnswerForQuestion={getAnswerForQuestion}
              updateSingleAnswer={updateSingleAnswer}
            />

            <div className='mt-12 flex justify-between'>
              <Button
                variant='outline'
                onClick={async () => {
                  if (
                    confirm(
                      'Are you sure you want to retake the interview? This will reset all your current answers.'
                    )
                  ) {
                    await handleResetInterview();
                  }
                }}
                className='flex items-center gap-2'
              >
                <RotateCcw className='w-4 h-4' />
                Retake Interview
              </Button>

              <Button disabled={isFinalizing} onClick={handleFinalize}>
                {isFinalizing ? 'Loading...' : 'Finalize Documents'}
              </Button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default function ReviewPage() {
  return <ReviewContent />;
}
