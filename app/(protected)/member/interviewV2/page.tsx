'use client';

import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import { RotateCcw, ArrowLeft, ArrowRight, Loader2 } from 'lucide-react';
import ProgressSidebar from '@/components/interview-v2/ProgressSidebar';
import ProfileTab from '@/components/interview-v2/tabs/ProfileTab';
import PeopleTab from '@/components/interview-v2/tabs/PeopleTab';
import WillTab from '@/components/interview-v2/tabs/WillTab';
import TrustTab from '@/components/interview-v2/tabs/TrustTab';
import FinancialPOATab from '@/components/interview-v2/tabs/FinancialPOATab';
import MedicalPOATab from '@/components/interview-v2/tabs/MedicalPOATab';
import ExecutionTab from '@/components/interview-v2/tabs/ExecutionTab';
import ReviewTab from '@/components/interview-v2/tabs/ReviewTab';

import {
  loadInterviewProgressV2,
  saveInterviewProgressV2,
  resetInterviewProgressV2,
} from '@/app/utils/interviewV2Progress';

type StepsData = {
  profile?: {
    firstName?: string;
    lastName?: string;
    state?: string;
    dob?: string;
  };
  people?: { firstName?: string; lastName?: string; relationship?: string };
  will?: { executor?: string; altExecutor?: string };
  trust?: {
    trustName?: string;
    trustDate?: string;
    holdAge?: string;
    primaryTrustee?: string;
    backupTrustee?: string;
    scheduleAAsset?: string;
  };
  financial?: { agent?: string; altAgent?: string };
  medical?: { healthcareAgent?: string; altHealthcareAgent?: string };
  execution?: { executionState?: string; executionCounty?: string };
  review?: Record<string, never>;
};

export default function InterviewV2Page() {
  const baseItems = useMemo(
    () => [
      { key: 'profile', label: 'Your Profile' },
      { key: 'people', label: 'People Library' },
      { key: 'will', label: 'Will' },
      { key: 'trust', label: 'Trust' },
      { key: 'financial', label: 'Financial POA' },
      { key: 'medical', label: 'Medical POA' },
      { key: 'execution', label: 'Execution' },
      { key: 'review', label: 'Review & Generate' },
    ],
    []
  );

  const [active, setActive] = useState<string>('profile');
  const [stepsData, setStepsData] = useState<StepsData>({});
  const [doneStepsData, setDoneStepsData] = useState<StepsData>({});

  const [savingNext, setSavingNext] = useState<boolean>(false);
  const [resetting, setResetting] = useState<boolean>(false);

  const items = useMemo(
    () =>
      baseItems.map(it => ({
        ...it,
        completed:
          !!doneStepsData[it.key as keyof StepsData] &&
          Object.values(doneStepsData[it.key as keyof StepsData] || {}).some(
            Boolean
          ),
      })),
    [baseItems, doneStepsData]
  );

  // React Query: load interview progress
  const queryClient = useQueryClient();
  const {
    data: progress,
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery({
    queryKey: ['interviewV2Progress'],
    queryFn: loadInterviewProgressV2,
    staleTime: 1000 * 60 * 5,
    gcTime: 1000 * 60 * 10,
    retry: 2,
  });

  // Initialize local UI state from loaded progress once
  const initializedRef = useRef(false);
  useEffect(() => {
    if (progress && !initializedRef.current) {
      setActive(progress.currentStep || 'profile');
      setStepsData((progress.stepsData || {}) as StepsData);
      setDoneStepsData((progress.stepsData || {}) as StepsData);
      initializedRef.current = true;
    }
  }, [progress]);

  const updateStep = useCallback(
    (key: keyof StepsData, partial: Record<string, any>) => {
      setStepsData(prev => ({
        ...prev,
        [key]: { ...(prev[key] as object | undefined), ...partial },
      }));
    },
    []
  );

  // Mutations
  const saveMutation = useMutation({
    mutationFn: async (payload: {
      nextStep?: string;
      stepsData: StepsData;
    }) => {
      return await saveInterviewProgressV2({
        status: 'in_progress',
        currentStep: payload.nextStep ?? active,
        stepsData: payload.stepsData,
      });
    },
    onSuccess: data => {
      queryClient.setQueryData(['interviewV2Progress'], data);
      setDoneStepsData((data?.stepsData || {}) as StepsData);
    },
  });

  const resetMutation = useMutation({
    mutationFn: resetInterviewProgressV2,
    onSuccess: data => {
      queryClient.setQueryData(['interviewV2Progress'], data);
      setDoneStepsData({});
    },
  });

  const persist = useCallback(
    async (nextStep?: string) => {
      try {
        await saveMutation.mutateAsync({ nextStep, stepsData });
      } catch (e) {
        console.error('Failed to save interview progress v2', e);
      }
    },
    [saveMutation, stepsData]
  );

  const isSaving = saveMutation.isPending || resetMutation.isPending;
  const isNextDisabled =
    savingNext ||
    resetMutation.isPending ||
    items.findIndex(i => i.key === active) === items.length - 1;
  const isResetDisabled = resetting || saveMutation.isPending;

  if (isLoading && !initializedRef.current) {
    return (
      <div className='container mx-auto py-8 px-4'>
        <div className='grid grid-cols-1 md:grid-cols-12 gap-6'>
          <div className='md:col-span-3'>
            <div className='space-y-3'>
              <div className='h-6 w-40 bg-accent rounded animate-pulse' />
              {Array.from({ length: 7 }).map((_, idx) => (
                <div
                  key={idx}
                  className='h-4 w-32 bg-accent rounded animate-pulse'
                />
              ))}
            </div>
          </div>
          <div className='md:col-span-9'>
            <Card className='mb-4'>
              <CardHeader className='flex flex-col gap-1'>
                <CardTitle className='text-base'>
                  <span className='inline-flex items-center gap-2'>
                    <Loader2 className='h-4 w-4 animate-spin text-primary' />
                    Loading interview...
                  </span>
                </CardTitle>
                <div className='text-muted-foreground text-sm'>
                  Fetching your saved progress
                </div>
              </CardHeader>
              <CardContent>
                <div className='space-y-4'>
                  <div className='h-6 w-2/3 bg-accent rounded animate-pulse' />
                  <div className='h-4 w-1/2 bg-accent rounded animate-pulse' />
                  <div className='h-10 w-full bg-accent rounded animate-pulse' />
                  <div className='h-10 w-full bg-accent rounded animate-pulse' />
                  <div className='h-10 w-1/3 bg-accent rounded animate-pulse' />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  if (isError && !initializedRef.current) {
    return (
      <div className='container mx-auto py-20 px-4'>
        <div className='max-w-xl mx-auto text-center space-y-4'>
          <div className='text-red-600 font-medium'>
            Failed to load interview progress
          </div>
          <div className='text-sm text-muted-foreground'>
            {(error as Error | undefined)?.message ||
              'An unexpected error occurred.'}
          </div>
          <div className='flex items-center justify-center gap-3'>
            <Button variant='outline' onClick={() => refetch()}>
              Try again
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const goToPrev = async () => {
    const idx = items.findIndex(i => i.key === active);
    if (idx > 0) {
      const target = items[idx - 1].key;
      await persist(target);
      setActive(target);
    }
  };

  const goToNext = async () => {
    const idx = items.findIndex(i => i.key === active);
    if (idx < items.length - 1) {
      const target = items[idx + 1].key;
      try {
        setSavingNext(true);
        await persist(target);
        setActive(target);
      } finally {
        setSavingNext(false);
      }
    }
  };

  const handleReset = async () => {
    if (confirm('Reset the interview? This will clear all entered data.')) {
      try {
        setResetting(true);
        await resetMutation.mutateAsync();
        setStepsData({});
        setActive('profile');
      } catch (e) {
        console.error('Failed to reset interview progress v2', e);
      } finally {
        setResetting(false);
      }
    }
  };

  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='flex items-start justify-between gap-4 mb-6'>
        <div>
          <h1 className='text-2xl font-semibold'>
            Child-Free Estate Plan Interview
          </h1>
          <p className='text-muted-foreground'>
            Generates a Will, Trust, Financial POA, and Medical POA for your
            state.
          </p>
        </div>
        <Button
          variant='destructive'
          onClick={handleReset}
          className='flex items-center gap-2'
          disabled={isResetDisabled}
        >
          {resetting ? (
            <>
              <Loader2 className='h-4 w-4 animate-spin' /> Resetting…
            </>
          ) : (
            <>
              <RotateCcw className='h-4 w-4' /> Reset Interview
            </>
          )}
        </Button>
      </div>

      <div className='grid grid-cols-1 md:grid-cols-12 gap-6'>
        <div className='md:col-span-3'>
          <ProgressSidebar
            items={items}
            current={active}
            onSelect={async key => {
              await persist(key);
              setActive(key);
            }}
          />
        </div>

        <div className='md:col-span-9'>
          <Card className='mb-4'>
            <CardHeader>
              <CardTitle className='text-base'>
                Step {items.findIndex(i => i.key === active) + 1} of{' '}
                {items.length}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs
                value={active}
                onValueChange={async v => {
                  await persist(v);
                  setActive(v);
                }}
                className='w-full'
              >
                <TabsContent value='profile'>
                  <ProfileTab
                    value={stepsData.profile || {}}
                    onChange={p => updateStep('profile', p)}
                  />
                </TabsContent>
                <TabsContent value='people'>
                  <PeopleTab
                    value={stepsData.people || {}}
                    onChange={p => updateStep('people', p)}
                  />
                </TabsContent>
                <TabsContent value='will'>
                  <WillTab
                    value={stepsData.will || {}}
                    onChange={p => updateStep('will', p)}
                  />
                </TabsContent>
                <TabsContent value='trust'>
                  <TrustTab
                    value={stepsData.trust || {}}
                    onChange={p => updateStep('trust', p)}
                  />
                </TabsContent>
                <TabsContent value='financial'>
                  <FinancialPOATab
                    value={stepsData.financial || {}}
                    onChange={p => updateStep('financial', p)}
                  />
                </TabsContent>
                <TabsContent value='medical'>
                  <MedicalPOATab
                    value={stepsData.medical || {}}
                    onChange={p => updateStep('medical', p)}
                  />
                </TabsContent>
                <TabsContent value='execution'>
                  <ExecutionTab
                    value={stepsData.execution || {}}
                    onChange={p => updateStep('execution', p)}
                  />
                </TabsContent>
                <TabsContent value='review'>
                  <ReviewTab onSave={persist} />
                </TabsContent>
              </Tabs>

              <div className='mt-6 flex items-center justify-between'>
                <Button
                  variant='outline'
                  onClick={goToPrev}
                  disabled={
                    savingNext ||
                    resetting ||
                    items.findIndex(i => i.key === active) === 0
                  }
                >
                  <ArrowLeft className='h-4 w-4 mr-2' /> Back
                </Button>
                <Button onClick={goToNext} disabled={isNextDisabled}>
                  {savingNext ? (
                    <>
                      <Loader2 className='h-4 w-4 mr-2 animate-spin' /> Saving…
                    </>
                  ) : (
                    <>
                      Next <ArrowRight className='h-4 w-4 ml-2' />
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
