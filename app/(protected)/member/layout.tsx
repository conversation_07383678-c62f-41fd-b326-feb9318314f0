'use client';

import { useEffect } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { Sidebar } from '@/components/dashboard/sidebar';
import { useRole } from '@/lib/roles/role-context';
import { useAuth } from '@/app/context/AuthContext';
import { OnboardingStep } from '@/app/utils/userOnboarding';
import routes from '@/utils/routes';
import { isAdmin, isWelonTrust } from '@/lib/utils/admin-utils';
import { AdminContainer } from '@/components/ui/container';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const pathname = usePathname();
  const router = useRouter();
  const { userContext } = useRole();
  const { onboardingStatus, userRoles, loading: isLoadingUser } = useAuth();

  // Check if current page is onboarding
  const isOnboardingPage = pathname === routes.member.onboarding;

  // Map role context to sidebar role format
  const getSidebarRole = ():
    | 'Member'
    | 'Administrator'
    | 'Welon Trust'
    | 'Professional' => {
    if (!userContext) return 'Member';

    switch (userContext.role) {
      case 'administrator':
        return 'Administrator';
      case 'welon_trust':
        return 'Welon Trust';
      case 'linked_account':
        return 'Member';
      default:
        return 'Member';
    }
  };

  const userRole = getSidebarRole();

  if (isLoadingUser) return;

  // Check onboarding status and redirect if needed
  useEffect(() => {
    if (!userContext) return;

    const shouldRedirect = pathname === '/dashboard';
    if (!shouldRedirect) return;

    if (
      !isAdmin(userRoles) &&
      !isWelonTrust(userRoles) &&
      onboardingStatus !== OnboardingStep.COMPLETED
    ) {
      router.push(routes.member.onboarding);
      return;
    }

    switch (userContext.role) {
      case 'administrator':
        router.push('/admin');
        break;
      case 'welon_trust':
        router.push('/emergency');
        break;
      case 'linked_account':
        router.push('/linked');
        break;
      default:
        break;
    }
  }, [
    userContext,
    pathname,
    onboardingStatus,
    userRoles,
    router,
    isLoadingUser,
  ]);

  // Show layout without sidebar for onboarding pages
  if (isOnboardingPage) {
    return (
      <div className='min-h-screen bg-background'>
        <main>
          <AdminContainer>{children}</AdminContainer>
        </main>
      </div>
    );
  }

  return (
    <div className='min-h-screen flex bg-background'>
      <Sidebar userRole={userRole} />
      <div className='flex-1'>
        <main>
          <AdminContainer>{children}</AdminContainer>
        </main>
      </div>
    </div>
  );
}
