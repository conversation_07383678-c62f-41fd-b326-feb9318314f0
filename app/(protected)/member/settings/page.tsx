'use client';

import { useState, useEffect, useMemo } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { DatePicker } from '@/components/ui/date-picker';
import {
  User,
  Shield,
  Bell,
  Accessibility,
  HelpCircle,
  History,
  Globe,
  Monitor,
  Smartphone,
  Tablet,
  LogOut,
  Save,
  RefreshCw,
  FileText,
} from 'lucide-react';

import ChangePasswordDialog from '@/components/ChangePasswordDialog';
import {
  fetchDevices,
  signOut,
  forgetDevice,
  updateUserAttributes,
} from 'aws-amplify/auth';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { format } from 'date-fns';
import { useAuth } from '@/app/context/AuthContext';
import { useRouter } from 'next/navigation';
import routes from '@/utils/routes';
import { fetchLoginHistory, parseDeviceInfo } from '@/utils/loginHistory';
import {
  fetchUserProfileByCognitoId,
  updateUserProfile,
} from '@/lib/data/users';
import { useModal } from '@/hooks/use-modal';
import { ConfirmationDialog } from '@/components/ui/confirmation-dialog';
import { generateClient } from 'aws-amplify/data';
import { Schema } from '@/amplify/data/resource';
import SharedAnswersTable from '@/components/linked-accounts/shared-answers-table';
import Invitations from '@/components/linked-accounts/invitations';
import { getCurrentUser, fetchUserAttributes } from 'aws-amplify/auth';

interface AccessibilitySettings {
  fontSize: 'small' | 'medium' | 'large' | 'extra-large';
  highContrast: boolean;
  screenReader: boolean;
  reducedMotion: boolean;
}

export default function SettingsPage() {
  const { refreshUser, user, logout } = useAuth();
  const queryClient = useQueryClient();
  const router = useRouter();
  const [isSigningOut, setIsSigningOut] = useState(false);
  const [isSavingProfile, setIsSavingProfile] = useState(false);

  // Pagination state for login history
  const [loginHistoryPage, setLoginHistoryPage] = useState(1);
  const loginHistoryPerPage = 10;

  // Modal for age restriction
  const ageRestrictionModal = useModal();

  // Amplify client for mutations
  const client = generateClient<Schema>();

  // AWS Cognito device tracking
  const {
    data: userDevices,
    isLoading: isLoadingDevices,
    refetch: refetchDevices,
  } = useQuery({
    queryKey: ['userDevices'],
    queryFn: fetchDevices,
  });

  // Fetch real user profile data
  const {
    data: userProfile,
    isLoading: isLoadingProfile,
    error: profileError,
    refetch: refetchProfile,
  } = useQuery({
    queryKey: ['userProfile', user?.userId],
    queryFn: () => fetchUserProfileByCognitoId(user?.userId || ''),
    enabled: !!user?.userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Fetch real login history
  const {
    data: loginHistory,
    isLoading: isLoadingLoginHistory,
    error: loginHistoryError,
  } = useQuery({
    queryKey: ['loginHistory', user?.signInDetails?.loginId],
    queryFn: () => fetchLoginHistory(user?.signInDetails?.loginId || ''),
    enabled: !!user?.signInDetails?.loginId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });

  // Helper function to get device display name
  const getDeviceDisplayName = (device: any) => {
    const userAgent = device.deviceAttributes?.user_agent || '';

    if (userAgent) {
      const deviceInfo = parseDeviceInfo(userAgent);
      return `${deviceInfo.os} • ${deviceInfo.browser}`;
    }

    // Fallback to device name if available
    if (device.name) {
      return `Device ${device.name.slice(0, 20)}`;
    }

    return 'Unknown Device';
  };

  // Helper function to get device type for icon
  const getDeviceType = (device: any) => {
    const userAgent = device.deviceAttributes?.user_agent || '';

    if (userAgent) {
      const deviceInfo = parseDeviceInfo(userAgent);
      return deviceInfo.device;
    }

    // Fallback: try to guess from device name
    const deviceName = device.name?.toLowerCase() || '';
    if (
      deviceName.includes('android') ||
      deviceName.includes('iphone') ||
      deviceName.includes('mobile')
    ) {
      return 'Mobile';
    }
    if (deviceName.includes('ipad') || deviceName.includes('tablet')) {
      return 'Tablet';
    }

    return 'Desktop';
  };

  // Helper function to get device icon based on device type
  const getDeviceIcon = (device: any) => {
    const deviceType = getDeviceType(device);

    switch (deviceType) {
      case 'Mobile':
        return Smartphone;
      case 'Tablet':
        return Tablet;
      default:
        return Monitor;
    }
  };

  // Helper function to check if device is current
  const isCurrentDevice = (device: any) => {
    if (!userDevices || userDevices.length === 0) return false;

    // Since AWS Cognito doesn't store User Agent in deviceAttributes,
    // we'll use a different approach based on device name and timing
    if (typeof window !== 'undefined') {
      const currentUserAgent = navigator.userAgent;
      const currentDeviceInfo = parseDeviceInfo(currentUserAgent);

      // Try to match based on device name patterns
      const deviceName = device.name || '';

      // Parse device info from device name (AWS Cognito sometimes stores browser info in name)
      const deviceInfo = parseDeviceInfo(deviceName);

      // Check if device name contains current browser/OS info
      const deviceNameLower = deviceName.toLowerCase();
      const currentBrowserBase = currentDeviceInfo.browser
        .split(' ')[0]
        .toLowerCase();
      const currentOSBase = currentDeviceInfo.os.split(' ')[0].toLowerCase();

      // Check for browser match (including chromium as chrome)
      const nameContainsBrowser =
        deviceNameLower.includes(currentBrowserBase) ||
        (currentBrowserBase === 'chrome' &&
          deviceNameLower.includes('chromium'));

      const nameContainsOS = deviceNameLower.includes(currentOSBase);

      // If device name contains current browser and OS, it's likely current
      if (nameContainsBrowser && nameContainsOS) {
        return true;
      }

      // Alternative: check if parsed device info from name matches current
      if (deviceInfo.browser && deviceInfo.os) {
        const browserMatch = deviceInfo.browser
          .toLowerCase()
          .includes(currentBrowserBase);
        const osMatch = deviceInfo.os.toLowerCase().includes(currentOSBase);

        if (browserMatch && osMatch) {
          return true;
        }
      }
    }

    // Final fallback: if only one device exists, it must be current
    if (userDevices.length === 1) {
      return true;
    }

    return false;
  };

  // Local state for form data (editable fields)
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    phoneNumber: '',
    birthdate: '',
  });

  // State for date picker
  const [birthdayDate, setBirthdayDate] = useState<Date | undefined>(undefined);

  // State for phone verification
  const [isPhoneVerified, setIsPhoneVerified] = useState(false);
  const [isVerifyingPhone, setIsVerifyingPhone] = useState(false);
  const [verificationCode, setVerificationCode] = useState('');
  const [showVerificationInput, setShowVerificationInput] = useState(false);
  const [isSubmittingCode, setIsSubmittingCode] = useState(false);

  // Function to calculate age from birthday
  const calculateAge = (birthDate: Date | string): number => {
    if (!birthDate) return 0;

    const date =
      typeof birthDate === 'string' ? new Date(birthDate) : birthDate;
    const today = new Date();
    let age = today.getFullYear() - date.getFullYear();
    const monthDiff = today.getMonth() - date.getMonth();

    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < date.getDate())
    ) {
      age--;
    }

    return age;
  };

  // Function to get the maximum allowed birth date (18 years ago from today)
  const getMaxBirthDate = (): Date => {
    const today = new Date();
    const maxDate = new Date(today);
    maxDate.setFullYear(today.getFullYear() - 18);
    // Add one day to the date to make it inclusive
    maxDate.setDate(maxDate.getDate());
    return maxDate;
  };

  // Function to get the maximum allowed birth year
  const getMaxBirthYear = (): number => {
    const today = new Date();
    return today.getFullYear() - 18;
  };

  // Function to format phone number with +1 prefix
  const formatPhoneNumber = (value: string): string => {
    // If user tries to clear the field or remove +1, reset to +1
    if (value.length < 2 || !value.startsWith('+1')) {
      return '+1';
    }

    // Remove all non-digit characters except the + at the beginning
    const digits = value.slice(1).replace(/\D/g, '');

    // If no digits after +, return +1
    if (digits.length === 0) return '+1';

    // If starts with 1, keep it as is
    if (digits.startsWith('1')) {
      // Limit to 11 digits total (1 + 10 digits)
      const limitedDigits = digits.slice(0, 11);
      return `+${limitedDigits}`;
    } else {
      // If doesn't start with 1, add 1 prefix and limit to 10 additional digits
      const limitedDigits = digits.slice(0, 10);
      return `+1${limitedDigits}`;
    }
  };

  // Function to initiate phone verification
  const handleInitiatePhoneVerification = async () => {
    if (!formData.phoneNumber || formData.phoneNumber === '+1') {
      toast.error('Please enter a valid phone number');
      return;
    }

    // Validate phone number format
    const phoneDigits = formData.phoneNumber.replace(/\D/g, '');
    if (phoneDigits.length !== 11 || !phoneDigits.startsWith('1')) {
      toast.error(
        'Phone number must be in format +1XXXXXXXXXX (11 digits total)'
      );
      return;
    }

    try {
      setIsVerifyingPhone(true);
      const currentUser = await getCurrentUser();

      const client = generateClient<Schema>({
        authMode: 'userPool',
      });

      const result = await client.mutations.initiatePhoneVerification({
        phoneNumber: formData.phoneNumber,
        email: currentUser.signInDetails?.loginId || '',
      });

      const resultData = result.data as any;
      let parsedResultData = resultData;
      if (typeof resultData === 'string') {
        try {
          parsedResultData = JSON.parse(resultData);
        } catch (e) {
          console.log('Failed to parse result data as JSON:', e);
        }
      }

      if (parsedResultData?.success) {
        setShowVerificationInput(true);
        toast.success('Verification code sent to your phone');
      } else {
        toast.error(
          parsedResultData?.error || 'Failed to send verification code'
        );
      }
    } catch (error) {
      console.error('Error initiating phone verification:', error);
      toast.error('Failed to send verification code. Please try again.');
    } finally {
      setIsVerifyingPhone(false);
    }
  };

  // Function to verify phone code
  const handleVerifyPhoneCode = async () => {
    if (!verificationCode || verificationCode.length !== 6) {
      toast.error('Please enter a valid 6-digit verification code');
      return;
    }

    try {
      setIsSubmittingCode(true);
      const currentUser = await getCurrentUser();

      const client = generateClient<Schema>({
        authMode: 'userPool',
      });

      const result = await client.mutations.verifyPhoneCode({
        phoneNumber: formData.phoneNumber,
        verificationCode: verificationCode,
        email: currentUser.signInDetails?.loginId || '',
        userId: currentUser.username,
      });

      const resultData = result.data as any;
      let parsedResultData = resultData;
      if (typeof resultData === 'string') {
        try {
          parsedResultData = JSON.parse(resultData);
        } catch (e) {
          console.log('Failed to parse result data as JSON:', e);
        }
      }

      if (parsedResultData?.success) {
        setIsPhoneVerified(true);
        setShowVerificationInput(false);
        setVerificationCode('');
        toast.success('Phone number verified successfully');
      } else {
        toast.error(parsedResultData?.error || 'Invalid verification code');
      }
    } catch (error) {
      console.error('Error verifying phone code:', error);
      toast.error('Failed to verify code. Please try again.');
    } finally {
      setIsSubmittingCode(false);
    }
  };

  // Initialize form data when user profile loads
  useEffect(() => {
    if (userProfile) {
      setFormData({
        firstName: userProfile.firstName || '',
        lastName: userProfile.lastName || '',
        phoneNumber: userProfile.phoneNumber
          ? formatPhoneNumber(userProfile.phoneNumber)
          : '+1',
        birthdate: userProfile.birthdate || '',
      });

      // Set birthday date for DatePicker
      if (userProfile.birthdate) {
        setBirthdayDate(new Date(userProfile.birthdate));
      }
    }
  }, [userProfile]);

  // Check phone verification status when user loads
  useEffect(() => {
    const checkPhoneVerificationStatus = async () => {
      try {
        const userAttributes = await fetchUserAttributes();
        if (userAttributes?.phone_number_verified === 'true') {
          setIsPhoneVerified(true);
        }
      } catch (error) {
        console.log('Could not check phone verification status:', error);
      }
    };

    if (user) {
      checkPhoneVerificationStatus();
    }
  }, [user]);

  // Handle saving profile changes
  const handleSaveProfile = async () => {
    if (!userProfile?.id) {
      toast.error('User profile not loaded');
      return;
    }

    // Validate required fields
    if (!formData.firstName.trim()) {
      toast.error('First name is required');
      return;
    }
    if (!formData.lastName.trim()) {
      toast.error('Last name is required');
      return;
    }
    if (!formData.phoneNumber.trim()) {
      toast.error('Phone number is required');
      return;
    }

    // Validate phone number format (+1 followed by 10 digits)
    const phoneDigits = formData.phoneNumber.replace(/\D/g, '');
    if (phoneDigits.length !== 11 || !phoneDigits.startsWith('1')) {
      toast.error(
        'Phone number must be in format +1XXXXXXXXXX (11 digits total)'
      );
      return;
    }

    if (!birthdayDate) {
      toast.error('Date of birth is required');
      return;
    }

    // Check if user is at least 18 years old for service eligibility (only for Member role)
    if (userProfile?.role === 'Member') {
      const age = calculateAge(birthdayDate);
      if (age < 18) {
        ageRestrictionModal.open();
        return;
      }
    }

    try {
      setIsSavingProfile(true);

      // Format birthdate for storage
      const birthdateString = birthdayDate.toISOString().split('T')[0];

      // Update user profile in DynamoDB
      await updateUserProfile(userProfile.id, {
        firstName: formData.firstName.trim(),
        lastName: formData.lastName.trim(),
        phoneNumber: formData.phoneNumber.trim(),
        birthdate: birthdateString,
      } as any);

      // Update Cognito user attributes
      const cognitoAttributes = {
        given_name: formData.firstName.trim(),
        family_name: formData.lastName.trim(),
        name: `${formData.firstName.trim()} ${formData.lastName.trim()}`,
        phone_number: formData.phoneNumber.trim(),
        birthdate: birthdateString,
      };

      await updateUserAttributes({
        userAttributes: cognitoAttributes,
      });

      // Refresh the profile data and user context
      await refetchProfile();
      await refreshUser();

      toast.success('Profile updated successfully');
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('Failed to update profile. Please try again.');
    } finally {
      setIsSavingProfile(false);
    }
  };

  // Security settings (without mock login history)
  const [securitySettings, setSecuritySettings] = useState({
    mfaEnabled: false,
    rememberMe: false, // Will be initialized from localStorage
  });

  // Mock accessibility settings
  const [accessibilitySettings, setAccessibilitySettings] =
    useState<AccessibilitySettings>({
      fontSize: 'medium',
      highContrast: false,
      screenReader: false,
      reducedMotion: false,
    });

  // Initialize rememberMe from localStorage
  useEffect(() => {
    const storedRememberMe = localStorage.getItem('rememberMe');
    if (storedRememberMe !== null) {
      const rememberMeValue = storedRememberMe === 'true';
      setSecuritySettings(prev => ({
        ...prev,
        rememberMe: rememberMeValue,
      }));
    }
  }, []);

  // Handle remember me change
  const handleRememberMeChange = (checked: boolean) => {
    localStorage.setItem('rememberMe', checked.toString());
    setSecuritySettings(prev => ({
      ...prev,
      rememberMe: checked,
    }));
  };

  // Login history pagination logic
  const totalLoginHistoryPages = loginHistory
    ? Math.ceil(loginHistory.length / loginHistoryPerPage)
    : 0;

  const paginatedLoginHistory = loginHistory
    ? loginHistory.slice(
        (loginHistoryPage - 1) * loginHistoryPerPage,
        loginHistoryPage * loginHistoryPerPage
      )
    : [];

  const handleLoginHistoryPageChange = (page: number) => {
    setLoginHistoryPage(page);
  };

  // Helper function to get visible page numbers for pagination
  const getVisiblePages = (currentPage: number, totalPages: number) => {
    const pages: (number | string)[] = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      if (currentPage <= 3) {
        for (let i = 1; i <= 4; i++) {
          pages.push(i);
        }
        pages.push('...');
        pages.push(totalPages);
      } else if (currentPage >= totalPages - 2) {
        pages.push(1);
        pages.push('...');
        for (let i = totalPages - 3; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        pages.push(1);
        pages.push('...');
        for (let i = currentPage - 1; i <= currentPage + 1; i++) {
          pages.push(i);
        }
        pages.push('...');
        pages.push(totalPages);
      }
    }

    return pages;
  };

  // Device management functions
  const handleRefreshDevices = async () => {
    try {
      await refetchDevices();
      toast.success('Device list refreshed');
    } catch (error) {
      console.error('Error refreshing devices:', error);
      toast.error('Failed to refresh device list');
    }
  };

  const handleSignOutDevice = async (deviceId: string) => {
    try {
      if (!user?.username) {
        toast.error('User not authenticated');
        return;
      }

      if (!deviceId) {
        toast.error('Device ID is required');
        return;
      }

      const mutationArgs = {
        deviceId,
        username: user.username,
      };

      const result = await client.mutations.signOutDevice(mutationArgs);

      if (result.data && JSON.parse(result.data as string).success) {
        toast.success('Device signed out successfully');
        queryClient.invalidateQueries({ queryKey: ['userDevices'] });
      } else {
        throw new Error('Failed to sign out device');
      }
    } catch (error) {
      toast.error('Failed to sign out device');
    }
  };

  const handleSignOutAllDevices = async () => {
    try {
      setIsSigningOut(true);

      if (!user?.username) {
        toast.error('User not authenticated');
        setIsSigningOut(false);
        return;
      }

      if (!userDevices || userDevices.length === 0) {
        toast.info('No devices to sign out');
        setIsSigningOut(false);
        return;
      }

      // Sign out all devices except current one using our Lambda function
      const otherDevices = userDevices.filter(
        device => !isCurrentDevice(device)
      );

      if (otherDevices.length > 0) {
        // Sign out other devices first
        const signOutPromises = otherDevices.map(device =>
          client.mutations.signOutDevice({
            deviceId: device.id,
            username: user.username,
          })
        );

        await Promise.all(signOutPromises);

        // Refresh device list to show the changes
        await refetchDevices();

        toast.success(`Signed out from ${otherDevices.length} other device(s)`);
      }

      // Finally, sign out current device using global sign out
      await signOut({ global: true });
      // Clear auth context state
      await logout();
      toast.success('Successfully signed out from all devices');
      // Redirect to login page
      router.push(routes.login);
    } catch (error) {
      console.error('Error signing out from all devices:', error);
      toast.error('Failed to sign out from all devices');
      setIsSigningOut(false);
    }
  };

  return (
    <div className='container mx-auto p-4 py-8'>
      <div className='flex flex-col space-y-2 mr-12 mb-8'>
        <h1 className='text-3xl font-bold text-[var(--foreground)]'>
          Settings
        </h1>
        <p className='text-[var(--custom-gray-dark)]'>
          Manage your account preferences and settings
        </p>
      </div>

      <Tabs defaultValue='profile' className='w-full'>
        <TabsList className='grid w-full grid-cols-6 mb-6'>
          <TabsTrigger value='profile' className='cursor-pointer flex-1'>
            <User className='h-4 w-4 mr-2' />
            Profile
          </TabsTrigger>
          <TabsTrigger value='security' className='cursor-pointer flex-1'>
            <Shield className='h-4 w-4 mr-2' />
            Security
          </TabsTrigger>
          <TabsTrigger
            value='linked-accounts'
            className='cursor-pointer w-full flex items-center justify-center'
          >
            <FileText className='h-4 w-4 mr-2' />
            Linked accounts
          </TabsTrigger>
          <div className='relative group flex-1'>
            <TabsTrigger
              value='notifications'
              disabled
              className='cursor-not-allowed opacity-50 w-full flex items-center justify-center'
            >
              <Bell className='h-4 w-4 mr-2' />
              Notifications
            </TabsTrigger>
            <div className='absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50'>
              In development
            </div>
          </div>
          <div className='relative group flex-1'>
            <TabsTrigger
              value='accessibility'
              disabled
              className='cursor-not-allowed opacity-50 w-full flex items-center justify-center'
            >
              <Accessibility className='h-4 w-4 mr-2' />
              Accessibility
            </TabsTrigger>
            <div className='absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50'>
              In development
            </div>
          </div>
          <div className='relative group flex-1'>
            <TabsTrigger
              value='support'
              disabled
              className='cursor-not-allowed opacity-50 w-full flex items-center justify-center'
            >
              <HelpCircle className='h-4 w-4 mr-2' />
              Support
            </TabsTrigger>
            <div className='absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50'>
              In development
            </div>
          </div>
          <TabsTrigger value='history' className='cursor-pointer flex-1'>
            <History className='h-4 w-4 mr-2' />
            History
          </TabsTrigger>
        </TabsList>

        <TabsContent value='profile'>
          <div className='space-y-6'>
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center space-x-2'>
                  <User className='h-5 w-5' />
                  <span>Basic Information</span>
                </CardTitle>
                <CardDescription>
                  Your personal information and account details
                </CardDescription>
              </CardHeader>
              <CardContent className='space-y-4'>
                {isLoadingProfile ? (
                  <div className='py-4 text-center'>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      Loading profile information...
                    </p>
                  </div>
                ) : profileError ? (
                  <div className='py-4 text-center'>
                    <p className='text-sm text-red-500'>
                      Failed to load profile information. Please try again
                      later.
                    </p>
                  </div>
                ) : (
                  <>
                    <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                      <div className='space-y-2'>
                        <Label htmlFor='firstName'>First Name *</Label>
                        <Input
                          id='firstName'
                          value={formData.firstName}
                          onChange={e =>
                            setFormData(prev => ({
                              ...prev,
                              firstName: e.target.value,
                            }))
                          }
                          required
                        />
                      </div>
                      <div className='space-y-2'>
                        <Label htmlFor='lastName'>Last Name *</Label>
                        <Input
                          id='lastName'
                          value={formData.lastName}
                          onChange={e =>
                            setFormData(prev => ({
                              ...prev,
                              lastName: e.target.value,
                            }))
                          }
                          required
                        />
                      </div>
                    </div>

                    <div className='space-y-2'>
                      <Label htmlFor='email'>Email Address</Label>
                      <Input
                        id='email'
                        type='email'
                        value={userProfile?.email || ''}
                        disabled
                        className='bg-gray-50'
                      />
                      <p className='text-sm text-[var(--custom-gray-medium)]'>
                        Email cannot be changed. Contact support if you need to
                        update your email.
                      </p>
                    </div>

                    <div className='space-y-2'>
                      <Label htmlFor='phone'>Phone Number *</Label>
                      <Input
                        id='phone'
                        type='tel'
                        value={formData.phoneNumber}
                        onChange={e => {
                          const formattedValue = formatPhoneNumber(
                            e.target.value
                          );
                          setFormData(prev => ({
                            ...prev,
                            phoneNumber: formattedValue,
                          }));
                        }}
                        placeholder='+1XXXXXXXXXX'
                        maxLength={12}
                        required
                      />
                      <p className='text-xs text-[var(--muted-foreground)]'>
                        US phone number format: +1 followed by 10 digits
                      </p>

                      {/* Phone Verification Section */}
                      <div className='mt-3 p-3 bg-[var(--background)] rounded-lg border'>
                        <div className='flex items-center justify-between mb-2'>
                          <span className='text-sm font-medium'>
                            Phone Verification
                          </span>
                          {isPhoneVerified ? (
                            <span className='text-xs text-green-600 bg-green-100 px-2 py-1 rounded'>
                              ✓ Verified
                            </span>
                          ) : (
                            <span className='text-xs text-orange-600 bg-orange-100 px-2 py-1 rounded'>
                              Not Verified
                            </span>
                          )}
                        </div>

                        {!showVerificationInput ? (
                          <div className='space-y-2'>
                            <p className='text-xs text-[var(--custom-gray-dark)]'>
                              Verify your phone number to enable SMS
                              notifications and enhanced security.
                            </p>
                            <Button
                              type='button'
                              variant='outline'
                              size='sm'
                              onClick={handleInitiatePhoneVerification}
                              disabled={
                                isPhoneVerified ||
                                isVerifyingPhone ||
                                !formData.phoneNumber ||
                                formData.phoneNumber === '+1'
                              }
                            >
                              {isVerifyingPhone ? (
                                <>
                                  <RefreshCw className='w-4 h-4 mr-2 animate-spin' />
                                  Sending Code...
                                </>
                              ) : (
                                'Send Verification Code'
                              )}
                            </Button>
                          </div>
                        ) : (
                          <div className='space-y-2'>
                            <p className='text-xs text-[var(--muted-foreground)]'>
                              Enter the 6-digit code sent to{' '}
                              {formData.phoneNumber}
                            </p>
                            <div className='flex gap-2'>
                              <Input
                                type='text'
                                placeholder='123456'
                                value={verificationCode}
                                onChange={e =>
                                  setVerificationCode(
                                    e.target.value
                                      .replace(/\D/g, '')
                                      .slice(0, 6)
                                  )
                                }
                                maxLength={6}
                                className='flex-1'
                              />
                              <Button
                                type='button'
                                size='sm'
                                onClick={handleVerifyPhoneCode}
                                disabled={
                                  isSubmittingCode ||
                                  verificationCode.length !== 6
                                }
                              >
                                {isSubmittingCode ? (
                                  <>
                                    <RefreshCw className='w-4 h-4 mr-1 animate-spin' />
                                    Verifying...
                                  </>
                                ) : (
                                  'Verify'
                                )}
                              </Button>
                            </div>
                            <div className='flex gap-2'>
                              <Button
                                type='button'
                                variant='ghost'
                                size='sm'
                                onClick={() => {
                                  setShowVerificationInput(false);
                                  setVerificationCode('');
                                }}
                              >
                                Cancel
                              </Button>
                              <Button
                                type='button'
                                variant='ghost'
                                size='sm'
                                onClick={handleInitiatePhoneVerification}
                                disabled={isVerifyingPhone}
                              >
                                Resend Code
                              </Button>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className='space-y-2'>
                      <Label htmlFor='dateOfBirth'>Date of Birth *</Label>
                      <DatePicker
                        date={birthdayDate}
                        setDate={date => setBirthdayDate(date)}
                        showYearPicker={true}
                        showMonthPicker={true}
                        yearRange={{ from: 1900, to: getMaxBirthYear() }}
                        calendarProps={{
                          disabled: (date: Date) => date > getMaxBirthDate(),
                        }}
                      />
                      <p className='text-xs text-[var(--muted-foreground)]'>
                        You must be at least 18 years old
                      </p>
                    </div>

                    <div className='space-y-2'>
                      <Label htmlFor='state'>State</Label>
                      <Input
                        id='state'
                        value={userProfile?.state || ''}
                        disabled
                        className='bg-gray-50'
                      />
                      <p className='text-sm text-[var(--custom-gray-medium)]'>
                        State cannot be changed. Contact support if you need to
                        update your state.
                      </p>
                    </div>

                    <div className='pt-4'>
                      <Button
                        onClick={handleSaveProfile}
                        disabled={isSavingProfile}
                        className='w-full'
                      >
                        <Save className='h-4 w-4 mr-2' />
                        {isSavingProfile ? 'Saving...' : 'Save Changes'}
                      </Button>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Account Status */}
            <Card>
              <CardHeader>
                <CardTitle>Account Status</CardTitle>
                <CardDescription>
                  Your current role and account information
                </CardDescription>
              </CardHeader>
              <CardContent className='space-y-4'>
                {isLoadingProfile ? (
                  <div className='py-4 text-center'>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      Loading account information...
                    </p>
                  </div>
                ) : userProfile ? (
                  <>
                    <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                      <div className='space-y-2'>
                        <Label>Current Role</Label>
                        <div className='flex items-center space-x-2'>
                          <Badge variant='default'>{userProfile.role}</Badge>
                          {userProfile.subrole && (
                            <Badge variant='secondary'>
                              {userProfile.subrole}
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div className='space-y-2'>
                        <Label>Account Status</Label>
                        <Badge
                          variant={
                            userProfile.status === 'active'
                              ? 'default'
                              : 'destructive'
                          }
                        >
                          {userProfile.status.charAt(0).toUpperCase() +
                            userProfile.status.slice(1)}
                        </Badge>
                      </div>
                    </div>

                    <div className='space-y-2'>
                      <Label>User ID</Label>
                      <Input
                        value={userProfile.id}
                        disabled
                        className='bg-gray-50'
                      />
                    </div>

                    {userProfile.journeyStatus && (
                      <div className='space-y-2'>
                        <Label>Journey Status</Label>
                        <Badge variant='outline'>
                          {userProfile.journeyStatus}
                        </Badge>
                      </div>
                    )}
                  </>
                ) : (
                  <div className='py-4 text-center'>
                    <p className='text-sm text-red-500'>
                      Failed to load account information.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Optional Services */}
            {/* <Card>
              <CardHeader>
                <CardTitle>Optional Services</CardTitle>
                <CardDescription>
                  Manage your subscription to additional services
                </CardDescription>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label>Newsletter Subscription</Label>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      Receive our monthly newsletter with updates
                    </p>
                  </div>
                  <Switch
                    checked={true}
                    onCheckedChange={checked => {
                      // TODO: Implement optional services functionality
                    }}
                  />
                </div>

                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label>Legal Updates</Label>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      Get notified about legal changes affecting your documents
                    </p>
                  </div>
                  <Switch
                    checked={true}
                    onCheckedChange={checked => {
                      // TODO: Implement optional services functionality
                    }}
                  />
                </div>

                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label>Educational Materials</Label>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      Access to educational content and resources
                    </p>
                  </div>
                  <Switch
                    checked={false}
                    onCheckedChange={checked => {
                      // TODO: Implement optional services functionality
                    }}
                  />
                </div>
              </CardContent>
            </Card> */}
          </div>
        </TabsContent>

        <TabsContent value='linked-accounts'>
          <div className='space-y-6'>
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center space-x-2'>
                  <FileText className='h-5 w-5' />
                  <span> Link Your Account for Shared Information </span>
                </CardTitle>
                <CardDescription>
                  Share your information with other members
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Invitations />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value='security'>
          <div className='space-y-6'>
            {/* Password Settings */}
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center space-x-2'>
                  <Shield className='h-5 w-5' />
                  <span>Password & Authentication</span>
                </CardTitle>
                <CardDescription>
                  Manage your password and two-factor authentication
                </CardDescription>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='space-y-4'>
                  <div className='space-y-2'>
                    <Label>Password Management</Label>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      Regularly update your password to keep your account
                      secure.
                    </p>
                    <ChangePasswordDialog
                      trigger={
                        <Button className='w-full'>Change Password</Button>
                      }
                    />
                  </div>
                </div>

                {/* <div className='border-t pt-4'>
                  <div className='flex items-center justify-between'>
                    <div className='space-y-0.5'>
                      <Label>Two-Factor Authentication</Label>
                      <p className='text-sm text-[var(--custom-gray-medium)]'>
                        Add an extra layer of security to your account
                      </p>
                    </div>
                    <Switch
                      checked={securitySettings.mfaEnabled}
                      onCheckedChange={checked =>
                        setSecuritySettings(prev => ({
                          ...prev,
                          mfaEnabled: checked,
                        }))
                      }
                    />
                  </div>
                </div> */}

                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label>Remember Me</Label>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      Stay logged in for extended periods
                    </p>
                  </div>
                  <Switch
                    checked={securitySettings.rememberMe}
                    onCheckedChange={handleRememberMeChange}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Active Sessions */}
            <Card>
              <CardHeader>
                <div className='flex items-center justify-between'>
                  <div>
                    <CardTitle>Active Sessions</CardTitle>
                    <CardDescription>
                      View and manage your active login sessions across
                      different devices and browsers
                    </CardDescription>
                  </div>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={handleRefreshDevices}
                    disabled={isLoadingDevices}
                    className='flex items-center space-x-2'
                  >
                    <RefreshCw
                      className={`h-4 w-4 ${
                        isLoadingDevices ? 'animate-spin' : ''
                      }`}
                    />
                    <span>Refresh</span>
                  </Button>
                </div>
              </CardHeader>
              <CardContent className='space-y-4'>
                {isLoadingDevices ? (
                  <div className='py-4 text-center'>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      Loading device information...
                    </p>
                  </div>
                ) : !userDevices || userDevices.length === 0 ? (
                  <div className='py-4 text-center'>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      No devices found.
                    </p>
                  </div>
                ) : (
                  // Sort devices to show current device first, then by last authenticated date
                  [...userDevices]
                    .sort((a: any, b: any) => {
                      const aIsCurrent = isCurrentDevice(a);
                      const bIsCurrent = isCurrentDevice(b);

                      // Current device always comes first
                      if (aIsCurrent && !bIsCurrent) return -1;
                      if (!aIsCurrent && bIsCurrent) return 1;

                      // If both or neither are current, sort by last authenticated date
                      const dateA = a.lastAuthenticatedDate
                        ? new Date(a.lastAuthenticatedDate).getTime()
                        : 0;
                      const dateB = b.lastAuthenticatedDate
                        ? new Date(b.lastAuthenticatedDate).getTime()
                        : 0;
                      return dateB - dateA;
                    })
                    .map((device: any, index) => (
                      <div
                        key={device.id || index}
                        className='flex items-center justify-between p-4 border rounded-lg'
                      >
                        <div className='flex items-center space-x-3'>
                          {(() => {
                            const DeviceIcon = getDeviceIcon(device);
                            return (
                              <DeviceIcon className='h-5 w-5 text-[var(--custom-gray-medium)]' />
                            );
                          })()}
                          <div>
                            <div className='flex flex-col'>
                              <p className='font-medium'>
                                {getDeviceDisplayName(device)}
                              </p>
                              <p className='text-xs text-[var(--custom-gray-medium)]'>
                                {getDeviceType(device)} Device
                              </p>
                              {isCurrentDevice(device) && (
                                <span className='text-xs text-green-600 font-medium'>
                                  Current Session
                                </span>
                              )}
                            </div>
                            <p className='text-xs text-[var(--custom-gray-medium)]'>
                              Last used:{' '}
                              {device.lastAuthenticatedDate
                                ? format(
                                    new Date(device.lastAuthenticatedDate),
                                    'MMM d, yyyy h:mm a'
                                  )
                                : 'Unknown'}
                            </p>
                          </div>
                        </div>
                        <div className='flex items-center space-x-2'>
                          {isCurrentDevice(device) && (
                            <Badge variant='default'>Current</Badge>
                          )}
                          {!isCurrentDevice(device) && (
                            <Button
                              variant='outline'
                              size='sm'
                              onClick={() => handleSignOutDevice(device.id)}
                              disabled={isSigningOut}
                            >
                              <LogOut className='h-4 w-4 mr-1' />
                              Sign Out Device
                            </Button>
                          )}
                        </div>
                      </div>
                    ))
                )}

                <Button
                  variant='outline'
                  className='w-full'
                  onClick={handleSignOutAllDevices}
                  disabled={isSigningOut}
                >
                  <LogOut className='h-4 w-4 mr-2' />
                  {isSigningOut ? 'Signing out...' : 'Sign Out All Devices'}
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value='accessibility'>
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center space-x-2'>
                <Accessibility className='h-5 w-5' />
                <span>Accessibility Settings</span>
              </CardTitle>
              <CardDescription>
                Customize the interface for better accessibility
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='space-y-4'>
                <div className='space-y-2'>
                  <Label>Font Size</Label>
                  <div className='grid grid-cols-4 gap-2'>
                    {(['small', 'medium', 'large', 'extra-large'] as const).map(
                      size => (
                        <Button
                          key={size}
                          variant={
                            accessibilitySettings.fontSize === size
                              ? 'default'
                              : 'outline'
                          }
                          size='sm'
                          onClick={() =>
                            setAccessibilitySettings(prev => ({
                              ...prev,
                              fontSize: size,
                            }))
                          }
                          className='capitalize'
                        >
                          {size === 'extra-large' ? 'XL' : size}
                        </Button>
                      )
                    )}
                  </div>
                </div>

                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label>High Contrast Mode</Label>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      Increase contrast for better visibility
                    </p>
                  </div>
                  <Switch
                    checked={accessibilitySettings.highContrast}
                    onCheckedChange={checked =>
                      setAccessibilitySettings(prev => ({
                        ...prev,
                        highContrast: checked,
                      }))
                    }
                  />
                </div>

                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label>Screen Reader Support</Label>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      Optimize for screen reading software
                    </p>
                  </div>
                  <Switch
                    checked={accessibilitySettings.screenReader}
                    onCheckedChange={checked =>
                      setAccessibilitySettings(prev => ({
                        ...prev,
                        screenReader: checked,
                      }))
                    }
                  />
                </div>

                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label>Reduced Motion</Label>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      Minimize animations and transitions
                    </p>
                  </div>
                  <Switch
                    checked={accessibilitySettings.reducedMotion}
                    onCheckedChange={checked =>
                      setAccessibilitySettings(prev => ({
                        ...prev,
                        reducedMotion: checked,
                      }))
                    }
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='support'>
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center space-x-2'>
                <HelpCircle className='h-5 w-5' />
                <span>Support & Feedback</span>
              </CardTitle>
              <CardDescription>
                Get help or provide feedback about your experience
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <Button variant='outline' className='h-20 flex-col'>
                  <HelpCircle className='h-6 w-6 mb-2' />
                  <span>Contact Support</span>
                </Button>
                <Button variant='outline' className='h-20 flex-col'>
                  <Globe className='h-6 w-6 mb-2' />
                  <span>Knowledge Base</span>
                </Button>
              </div>

              <div className='space-y-4'>
                <Label htmlFor='feedback'>Send Feedback</Label>
                <textarea
                  id='feedback'
                  className='w-full min-h-[100px] p-3 border border-gray-300 rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
                  placeholder='Tell us about your experience or report an issue...'
                />
                <Button className='w-full'>Send Feedback</Button>
              </div>

              <div className='bg-gray-50 p-4 rounded-lg'>
                <h4 className='font-medium mb-2'>Quick Help</h4>
                <ul className='space-y-1 text-sm text-[var(--custom-gray-medium)]'>
                  <li>• Email: <EMAIL></li>
                  <li>• Phone: 1-800-LEGACY-1</li>
                  <li>• Hours: Monday-Friday, 9 AM - 6 PM EST</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='history'>
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center space-x-2'>
                <History className='h-5 w-5' />
                <span>Account History</span>
              </CardTitle>
              <CardDescription>
                View your account activity and audit trail
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='space-y-4'>
                <div className='flex items-center justify-between'>
                  <h4 className='font-medium'>Login History</h4>
                  <Badge variant='outline' className='text-xs'>
                    Successful Logins
                  </Badge>
                </div>
                {isLoadingLoginHistory ? (
                  <div className='py-4 text-center'>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      Loading login history...
                    </p>
                  </div>
                ) : loginHistoryError ? (
                  <div className='py-4 text-center'>
                    <p className='text-sm text-red-500'>
                      Failed to load login history. Please try again later.
                    </p>
                  </div>
                ) : !loginHistory || loginHistory.length === 0 ? (
                  <div className='py-4 text-center'>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      No login history found.
                    </p>
                  </div>
                ) : (
                  <>
                    {paginatedLoginHistory.map((login, index) => {
                      // Calculate the actual index in the full array
                      const actualIndex =
                        (loginHistoryPage - 1) * loginHistoryPerPage + index;
                      return (
                        <div
                          key={login.id}
                          className={`flex items-center justify-between p-3 border rounded-lg ${
                            actualIndex === 0
                              ? ' border-green-500'
                              : ' border-[var(--custom-gray-light)]'
                          }`}
                        >
                          <div className='flex items-center space-x-3'>
                            <div
                              className={`h-2 w-2 rounded-full ${
                                actualIndex === 0
                                  ? 'bg-green-500'
                                  : 'bg-[var(--custom-gray-medium)]'
                              }`}
                            />
                            <div>
                              <p className='font-medium'>{login.device}</p>
                              <p className='text-sm text-[var(--custom-gray-medium)]'>
                                {login.location} Device
                              </p>
                              <p className='text-xs text-[var(--foreground)] font-medium'>
                                {actualIndex === 0
                                  ? 'Most recent login'
                                  : 'Successful login'}
                              </p>
                            </div>
                          </div>
                          <div className='text-right'>
                            <p className='text-sm text-[var(--custom-gray-medium)]'>
                              {format(new Date(login.timestamp), 'MMM d, yyyy')}
                            </p>
                            <p className='text-xs text-[var(--custom-gray-medium)]'>
                              {format(new Date(login.timestamp), 'h:mm a')}
                            </p>
                          </div>
                        </div>
                      );
                    })}

                    {/* Pagination for login history */}
                    {totalLoginHistoryPages > 1 && (
                      <div className='mt-4 flex justify-center'>
                        <Pagination>
                          <PaginationContent>
                            <PaginationItem>
                              <PaginationPrevious
                                onClick={() =>
                                  handleLoginHistoryPageChange(
                                    Math.max(1, loginHistoryPage - 1)
                                  )
                                }
                                className={`w-auto px-3 ${
                                  loginHistoryPage === 1
                                    ? 'pointer-events-none opacity-50'
                                    : 'cursor-pointer'
                                }`}
                              />
                            </PaginationItem>

                            {getVisiblePages(
                              loginHistoryPage,
                              totalLoginHistoryPages
                            ).map((page, index) => (
                              <PaginationItem key={index}>
                                {page === '...' ? (
                                  <PaginationEllipsis />
                                ) : (
                                  <PaginationLink
                                    onClick={() =>
                                      handleLoginHistoryPageChange(
                                        page as number
                                      )
                                    }
                                    isActive={loginHistoryPage === page}
                                    className='cursor-pointer'
                                  >
                                    {page}
                                  </PaginationLink>
                                )}
                              </PaginationItem>
                            ))}

                            <PaginationItem>
                              <PaginationNext
                                onClick={() =>
                                  handleLoginHistoryPageChange(
                                    Math.min(
                                      totalLoginHistoryPages,
                                      loginHistoryPage + 1
                                    )
                                  )
                                }
                                className={`w-auto px-3 ${
                                  loginHistoryPage === totalLoginHistoryPages
                                    ? 'pointer-events-none opacity-50'
                                    : 'cursor-pointer'
                                }`}
                              />
                            </PaginationItem>
                          </PaginationContent>
                        </Pagination>
                      </div>
                    )}
                  </>
                )}
              </div>

              <div className='border-t pt-4 space-y-2'>
                <Button
                  variant='outline'
                  className='w-full'
                  onClick={() =>
                    queryClient.invalidateQueries({
                      queryKey: ['loginHistory'],
                    })
                  }
                  disabled={isLoadingLoginHistory}
                >
                  <History className='h-4 w-4 mr-2' />
                  {isLoadingLoginHistory
                    ? 'Refreshing...'
                    : 'Refresh Login History'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Age Restriction Modal */}
      <ConfirmationDialog
        {...ageRestrictionModal.modalProps}
        title='Service Eligibility'
        description='You must be at least 18 years old to use this service.'
        confirmLabel='I understand'
        onConfirm={() => ageRestrictionModal.close()}
        confirmVariant='default'
      />
    </div>
  );
}
