'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { DMSConfigForm } from '@/components/emergency/dms-configuration';
import { DMSStatusCard } from '@/components/emergency/dms-status';
import {
  CheckInFrequency,
  CommunicationMethod,
  DMSConfiguration,
  EscalationProtocol,
} from '@/components/emergency/types';
import { AlertCircle, CheckCircle2, Calendar, PauseCircle } from 'lucide-react';
import { Headline, Subhead } from '../../../../components/ui/brand/typography';
import { useDeadMansSwitch, DMSConfigData } from '@/hooks/useDeadMansSwitch';

const MS_PER_DAY = 24 * 60 * 60 * 1000;

const FREQUENCY_TO_DAYS: Record<string, number> = {
  WEEKLY: 7,
  BIWEEKLY: 14,
  MONTHLY: 30, // можеш замінити на коректне додавання місяця, якщо хочеш
};

const calculateNextCheckInDate = (
  frequency?: string,
  customDays?: number | null,
  fromDate: Date = new Date()
): Date => {
  const freq = frequency?.toUpperCase();

  if (freq === 'CUSTOM' && customDays && customDays > 0) {
    return new Date(fromDate.getTime() + customDays * MS_PER_DAY);
  }

  if (freq === 'MONTHLY') {
    const nextMonth = new Date(fromDate);
    nextMonth.setMonth(nextMonth.getMonth() + 1);
    return nextMonth;
  }

  const days = FREQUENCY_TO_DAYS[freq ?? ''] ?? 7; // дефолт 7 днів

  return new Date(fromDate.getTime() + days * MS_PER_DAY);
};

const showTemporaryAlert = (
  setAlert: React.Dispatch<
    React.SetStateAction<{ type: 'success' | 'error'; message: string } | null>
  >,
  alert: { type: 'success' | 'error'; message: string },
  duration = 5000
) => {
  setAlert(alert);
  setTimeout(() => setAlert(null), duration);
};

export default function DeadMansSwitchPage() {
  const { config: dmsConfig, loading, saveConfig } = useDeadMansSwitch();
  const [showConfigForm, setShowConfigForm] = useState(false);
  const [showPauseDialog, setShowPauseDialog] = useState(false);
  const [pauseReason, setPauseReason] = useState('');
  const [pauseUntil, setPauseUntil] = useState('');
  const [alert, setAlert] = useState<{
    type: 'success' | 'error';
    message: string;
  } | null>(null);

  console.log('===> dmsConfig', dmsConfig);

  const handleUpdateConfig = async (newConfig: Partial<DMSConfiguration>) => {
    try {
      const now = new Date();

      const frequency =
        (newConfig.frequency?.toUpperCase() as CheckInFrequency) ?? 'WEEKLY';
      const communicationMethod =
        (newConfig.communicationMethod?.toUpperCase() as CommunicationMethod) ??
        undefined;
      const escalationProtocol =
        (newConfig.escalationProtocol?.toUpperCase() as EscalationProtocol) ??
        undefined;

      const nextCheckInDate = calculateNextCheckInDate(
        frequency,
        newConfig.customFrequencyDays,
        now
      );

      const amplifyUpdate: Partial<DMSConfigData> = {
        isEnable: true,
        frequency,
        communicationMethod,
        escalationProtocol,
        personalMessage: newConfig.personalMessage ?? undefined,
        status: 'ACTIVE',
        lastCheckIn: now.toISOString(),
        nextCheckIn: nextCheckInDate.toISOString(),
      };

      if (frequency === 'CUSTOM' && newConfig.customFrequencyDays) {
        amplifyUpdate.customFrequencyDays = newConfig.customFrequencyDays;
      }

      const success = await saveConfig(amplifyUpdate);

      showTemporaryAlert(setAlert, {
        type: success ? 'success' : 'error',
        message: success
          ? 'Wellness Checks configuration updated successfully.'
          : 'Failed to update configuration.',
      });

      if (success) {
        setShowConfigForm(false);
      }
    } catch (err) {
      console.error('Failed to save config:', err);
      showTemporaryAlert(setAlert, {
        type: 'error',
        message: 'Failed to update configuration.',
      });
    }
  };

  const handleCheckIn = async () => {
    try {
      const now = new Date();
      const nextCheckInDate = calculateNextCheckInDate(
        dmsConfig?.frequency,
        dmsConfig?.customFrequencyDays,
        now
      );

      const success = await saveConfig({
        lastCheckIn: now.toISOString(),
        nextCheckIn: nextCheckInDate.toISOString(),
        status: 'ACTIVE',
      });

      showTemporaryAlert(setAlert, {
        type: success ? 'success' : 'error',
        message: success
          ? 'Check-in successful. Your Wellness Checks has been reset.'
          : 'Failed to check in. Please try again.',
      });
    } catch (error) {
      console.error('Check-in error:', error);
      showTemporaryAlert(setAlert, {
        type: 'error',
        message: 'Failed to check in. Please try again.',
      });
    }
  };

  const handlePause = () => {
    setShowPauseDialog(true);
  };

  const handlePauseConfirm = async () => {
    try {
      const updateData: Partial<DMSConfigData> = {
        status: 'PAUSED',
        pauseReason: pauseReason || null,
      };

      if (pauseUntil) {
        updateData.pauseUntil = new Date(pauseUntil).toISOString();
      }

      const success = await saveConfig(updateData);

      if (success) {
        setShowPauseDialog(false);
        setPauseReason('');
        setPauseUntil('');
        setAlert({
          type: 'success',
          message: 'Wellness Checks paused successfully.',
        });
      } else {
        setAlert({
          type: 'error',
          message: 'Failed to pause Wellness Checks. Please try again.',
        });
      }
    } catch (error) {
      console.error('Pause error:', error);
      setAlert({
        type: 'error',
        message: 'Failed to pause Wellness Checks. Please try again.',
      });
    }

    setTimeout(() => setAlert(null), 5000);
  };

  const handleResume = async () => {
    try {
      const now = new Date();
      const nextCheckInDate = calculateNextCheckInDate(
        dmsConfig?.frequency,
        dmsConfig?.customFrequencyDays,
        now
      );

      const success = await saveConfig({
        status: 'ACTIVE',
        pauseReason: null,
        pauseUntil: null,
        lastCheckIn: now.toISOString(),
        nextCheckIn: nextCheckInDate.toISOString(),
      });

      showTemporaryAlert(setAlert, {
        type: success ? 'success' : 'error',
        message: success
          ? 'Wellness Checks resumed successfully.'
          : 'Failed to resume Wellness Checks. Please try again.',
      });
    } catch (error) {
      console.error('Resume error:', error);
      showTemporaryAlert(setAlert, {
        type: 'error',
        message: 'Failed to resume Wellness Checks. Please try again.',
      });
    }
  };

  const handleTest = () => {
    setAlert({
      type: 'success',
      message:
        'Test initiated. A test notification has been sent to your emergency contacts.',
    });

    setTimeout(() => setAlert(null), 5000);
  };

  return (
    <div className='max-w-7xl mx-auto'>
      <div className='flex flex-col space-y-2 mr-12 mb-8'>
        <h1 className='text-3xl font-bold text-[var(--foreground)]'>
          Wellness Checks
        </h1>
        <p className='text-[var(--custom-gray-dark)]'>
          We'll check in regularly to be sure you're okay - and jump into action
          if you aren't.
        </p>
      </div>

      {alert && (
        <Alert
          className={`mb-6 ${
            alert.type === 'success'
              ? 'bg-green-50 text-green-800 border-green-200'
              : 'bg-destructive/10 text-destructive border-destructive/20'
          }`}
        >
          {alert.type === 'success' ? (
            <CheckCircle2 className='h-4 w-4' />
          ) : (
            <AlertCircle className='h-4 w-4' />
          )}
          <AlertTitle>
            {alert.type === 'success' ? 'Success' : 'Error'}
          </AlertTitle>
          <AlertDescription>{alert.message}</AlertDescription>
        </Alert>
      )}

      {loading ? (
        <Card className='p-6'>
          <div className='flex justify-center items-center h-40'>
            <p>Loading Wellness Checks configuration...</p>
          </div>
        </Card>
      ) : (
        <>
          {!showConfigForm && (
            <>
              <Card className='mb-8'>
                <CardHeader>
                  <CardTitle>About Wellness Checks</CardTitle>
                  <CardDescription>
                    Wellness Checks are an automated way to make sure you’re
                    safe and responsive. If you miss scheduled check-ins, the
                    system can send alerts to your chosen contacts and, if
                    needed, grant them access to certain documents you’ve
                    approved in advance.
                  </CardDescription>
                </CardHeader>
                <CardContent className='space-y-4'>
                  <div>
                    <h3 className='font-semibold mb-1'>How It Works</h3>
                    <p className='text-sm text-[var(--custom-gray-dark)]'>
                      You’ll receive regular check-in notifications based on the
                      frequency you select (daily, weekly, monthly, etc.). Each
                      check-in is quick — just confirm you’re okay. If you miss
                      a check-in, the system will follow an escalation process
                      to keep your contacts informed.
                    </p>
                  </div>
                  <div>
                    <h3 className='font-semibold mb-1'>
                      Escalation Process (Standard: 3 missed check-ins)
                    </h3>
                    <p className='text-sm text-[var(--custom-gray-dark)]'>
                      <strong>First Miss:</strong> You receive a reminder to
                      complete your check-in. <br />
                      <strong>Second Miss:</strong> Your designated contacts are
                      alerted that you haven’t checked in. <br />
                      <strong>Third Miss:</strong> The system triggers your
                      incapacitation protocol, which may grant approved contacts
                      access to your documents.
                    </p>
                  </div>
                  <div>
                    <h3 className='font-semibold mb-1'>Flexibility</h3>
                    <p className='text-sm text-[var(--custom-gray-dark)]'>
                      You can pause Wellness Checks at any time — for example,
                      if you’re traveling or unavailable — and resume them when
                      you’re ready.
                    </p>
                  </div>
                </CardContent>
              </Card>

              <DMSStatusCard
                config={dmsConfig}
                onCheckIn={handleCheckIn}
                onPause={handlePause}
                onResume={handleResume}
                onTest={handleTest}
                onConfigure={() => setShowConfigForm(true)}
              />
            </>
          )}

          {showConfigForm && (
            <DMSConfigForm
              onSubmit={data => {
                // TODO: fix this
                const mappedData: Partial<DMSConfiguration> = {
                  ...data,
                  customFrequencyDays: data.customFrequencyDays ?? undefined,
                  customEscalationSteps:
                    data.customEscalationSteps ?? undefined,
                  personalMessage: data.personalMessage ?? undefined,
                  pauseReason: data.pauseReason ?? undefined,
                  pauseUntil: data.pauseUntil ?? undefined,
                  nextCheckIn: data.nextCheckIn ?? undefined,
                  lastCheckIn: data.lastCheckIn ?? undefined,
                };
                handleUpdateConfig(mappedData);
              }}
              onCancel={() => setShowConfigForm(false)}
              initialData={dmsConfig || {}}
            />
          )}
        </>
      )}

      <Dialog open={showPauseDialog} onOpenChange={setShowPauseDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Pause Wellness Checks</DialogTitle>
            <DialogDescription>
              Your Wellness Checks will be paused and won't send check-in
              notifications until you resume it.
            </DialogDescription>
          </DialogHeader>

          <div className='space-y-4 py-4'>
            <div className='space-y-2'>
              <Label htmlFor='pauseReason'>Reason for pausing (optional)</Label>
              <Textarea
                id='pauseReason'
                value={pauseReason}
                onChange={e => setPauseReason(e.target.value)}
                placeholder='e.g., Traveling, Hospital stay'
              />
            </div>

            <div className='space-y-2'>
              <Label htmlFor='pauseUntil' className='flex items-center'>
                <Calendar className='mr-2 h-4 w-4' />
                Pause until (optional)
              </Label>
              <Input
                id='pauseUntil'
                type='date'
                value={pauseUntil}
                onChange={e => setPauseUntil(e.target.value)}
                min={new Date().toISOString().split('T')[0]}
                max={
                  new Date(Date.now() + 90 * 24 * 60 * 60 * 1000)
                    .toISOString()
                    .split('T')[0]
                } // Max 90 days
              />
              <p className='text-xs text-muted-foreground'>
                If no date is selected, the switch will remain paused until you
                manually resume it.
              </p>
            </div>
          </div>

          <DialogFooter>
            <Button variant='outline' onClick={() => setShowPauseDialog(false)}>
              Cancel
            </Button>
            <Button
              variant='outline'
              onClick={handlePauseConfirm}
              className='bg-blue-2157c hover:bg-blue-2157c/90'
            >
              <PauseCircle className='mr-2 h-4 w-4' />
              Pause Switch
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
