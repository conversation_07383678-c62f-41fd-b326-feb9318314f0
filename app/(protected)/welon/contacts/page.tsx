'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Users,
  Phone,
  Mail,
  Plus,
  Edit,
  Trash2,
  AlertCircle,
  CheckCircle2,
  ArrowLeft,
  User,
  MapPin,
  Clock,
} from 'lucide-react';
import { useUserContext } from '@/components/welon-trust/user-context';
import { useRole } from '@/lib/roles/role-context';
import { Headline, Subhead } from '@workspace/ui/brand';

interface EmergencyContact {
  id: string;
  name: string;
  relationship: string;
  phone: string;
  email: string;
  address: string;
  isPrimary: boolean;
  isAuthorized: boolean;
  addedDate: string;
  lastVerified: string;
}

// Generate mock emergency contacts for a user
const generateEmergencyContacts = (userId: string): EmergencyContact[] => {
  const seed = userId
    .split('')
    .reduce((acc, char) => acc + char.charCodeAt(0), 0);
  const random = (min: number, max: number) =>
    Math.floor((((seed * 9301 + 49297) % 233280) / 233280) * (max - min + 1)) +
    min;

  const relationships = [
    'Spouse',
    'Child',
    'Parent',
    'Sibling',
    'Friend',
    'Attorney',
    'Doctor',
  ];
  const names = [
    'Sarah Johnson',
    'Michael Davis',
    'Jennifer Wilson',
    'Robert Brown',
    'Lisa Anderson',
    'David Miller',
    'Emily Taylor',
  ];

  const contacts: EmergencyContact[] = [];
  const numContacts = random(2, 4);

  for (let i = 0; i < numContacts; i++) {
    contacts.push({
      id: `contact-${userId}-${i}`,
      name: names[i % names.length],
      relationship: relationships[i % relationships.length],
      phone: `(555) ${String(random(100, 999)).padStart(3, '0')}-${String(random(1000, 9999)).padStart(4, '0')}`,
      email: `${names[i % names.length].toLowerCase().replace(' ', '.')}@email.com`,
      address: `${random(100, 9999)} ${['Main St', 'Oak Ave', 'Pine Rd', 'Elm Dr'][i % 4]}, City, State ${String(random(10000, 99999)).padStart(5, '0')}`,
      isPrimary: i === 0,
      isAuthorized: random(1, 10) > 3,
      addedDate: new Date(
        Date.now() - random(30, 365) * 24 * 60 * 60 * 1000
      ).toISOString(),
      lastVerified: new Date(
        Date.now() - random(1, 90) * 24 * 60 * 60 * 1000
      ).toISOString(),
    });
  }

  return contacts;
};

export default function EmergencyContactsPage() {
  const router = useRouter();
  const { selectedUser } = useUserContext();
  const { hasPermission } = useRole();
  const [contacts, setContacts] = useState<EmergencyContact[]>([]);
  const [isAddingContact, setIsAddingContact] = useState(false);
  const [editingContact, setEditingContact] = useState<EmergencyContact | null>(
    null
  );

  // Check if user can manage emergency contacts
  const canManageEmergencyContacts = hasPermission('manage_emergency_contacts');

  useEffect(() => {
    if (selectedUser) {
      const userContacts = generateEmergencyContacts(selectedUser.id);
      setContacts(userContacts);
    }
  }, [selectedUser]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const handleDeleteContact = (contactId: string) => {
    if (confirm('Are you sure you want to delete this emergency contact?')) {
      setContacts(prev => prev.filter(contact => contact.id !== contactId));
    }
  };

  const handleSetPrimary = (contactId: string) => {
    setContacts(prev =>
      prev.map(contact => ({
        ...contact,
        isPrimary: contact.id === contactId,
      }))
    );
  };

  // Check permissions first
  if (!canManageEmergencyContacts) {
    return (
      <div className='container mx-auto py-8 px-4'>
        <div className='max-w-4xl mx-auto'>
          <Alert className='bg-red-50 text-red-800 border-red-200'>
            <AlertCircle className='h-4 w-4' />
            <AlertTitle>Access Denied</AlertTitle>
            <AlertDescription>
              You don't have permission to manage emergency contacts. This
              feature is only available to members.
              <br />
              <Button
                variant='outline'
                size='sm'
                className='mt-3'
                onClick={() => router.back()}
              >
                Go Back
              </Button>
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  if (!selectedUser) {
    return (
      <div className='container mx-auto py-8 px-4'>
        <div className='max-w-4xl mx-auto'>
          <Alert className='bg-amber-50 text-amber-800 border-amber-200'>
            <Users className='h-4 w-4' />
            <AlertTitle>No Member Selected</AlertTitle>
            <AlertDescription>
              Please select a member from the dropdown to view their emergency
              contacts.
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-6xl mx-auto'>
        {/* Header */}
        <div className='mb-8'>
          <div className='flex items-center gap-4 mb-4'>
            <Button
              variant='outline'
              size='sm'
              onClick={() => router.back()}
              className='flex items-center gap-2'
            >
              <ArrowLeft className='h-4 w-4' />
              Back
            </Button>
          </div>

          <Headline className='mb-2'>Emergency Contacts</Headline>
          <Subhead className='text-muted-foreground'>
            Manage emergency contacts for {selectedUser.name}
          </Subhead>
        </div>

        {/* Summary Stats */}
        <div className='grid md:grid-cols-3 gap-4 mb-8'>
          <Card>
            <CardHeader className='pb-2'>
              <CardTitle className='text-sm font-medium text-muted-foreground'>
                Total Contacts
              </CardTitle>
            </CardHeader>
            <CardContent className='pb-3'>
              <div className='flex items-center justify-between'>
                <span className='text-xl font-bold'>{contacts.length}</span>
                <Users className='h-4 w-4 text-blue-500' />
              </div>
              <p className='text-xs text-muted-foreground mt-1'>
                Registered contacts
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='pb-2'>
              <CardTitle className='text-sm font-medium text-muted-foreground'>
                Authorized
              </CardTitle>
            </CardHeader>
            <CardContent className='pb-3'>
              <div className='flex items-center justify-between'>
                <span className='text-xl font-bold'>
                  {contacts.filter(c => c.isAuthorized).length}
                </span>
                <CheckCircle2 className='h-4 w-4 text-green-500' />
              </div>
              <p className='text-xs text-muted-foreground mt-1'>
                Can access documents
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='pb-2'>
              <CardTitle className='text-sm font-medium text-muted-foreground'>
                Primary Contact
              </CardTitle>
            </CardHeader>
            <CardContent className='pb-3'>
              <div className='flex items-center justify-between'>
                <span className='text-sm font-bold'>
                  {contacts.find(c => c.isPrimary)?.name || 'None set'}
                </span>
                <User className='h-4 w-4 text-purple-500' />
              </div>
              <p className='text-xs text-muted-foreground mt-1'>
                First contact
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Add Contact Button */}
        <div className='mb-6'>
          <Button
            onClick={() => setIsAddingContact(true)}
            className='flex items-center gap-2'
          >
            <Plus className='h-4 w-4' />
            Add Emergency Contact
          </Button>
        </div>

        {/* Contacts List */}
        <div className='space-y-4'>
          {contacts.length === 0 ? (
            <Card className='bg-muted/50'>
              <CardContent className='py-8 text-center'>
                <Users className='h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50' />
                <p className='text-muted-foreground mb-4'>
                  No emergency contacts found
                </p>
                <Button onClick={() => setIsAddingContact(true)}>
                  <Plus className='h-4 w-4 mr-2' />
                  Add First Contact
                </Button>
              </CardContent>
            </Card>
          ) : (
            contacts.map(contact => (
              <Card key={contact.id} className='overflow-hidden'>
                <CardHeader className='pb-3'>
                  <div className='flex items-center justify-between'>
                    <div className='flex items-center gap-3'>
                      <div className='bg-blue-100 p-2 rounded-full'>
                        <User className='h-5 w-5 text-blue-600' />
                      </div>
                      <div>
                        <CardTitle className='text-lg flex items-center gap-2'>
                          {contact.name}
                          {contact.isPrimary && (
                            <Badge variant='default' className='text-xs'>
                              Primary
                            </Badge>
                          )}
                          {contact.isAuthorized && (
                            <Badge
                              variant='outline'
                              className='text-xs border-green-300 text-green-700'
                            >
                              Authorized
                            </Badge>
                          )}
                        </CardTitle>
                        <p className='text-sm text-muted-foreground'>
                          {contact.relationship}
                        </p>
                      </div>
                    </div>
                    <div className='flex items-center gap-2'>
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() => setEditingContact(contact)}
                      >
                        <Edit className='h-4 w-4' />
                      </Button>
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() => handleDeleteContact(contact.id)}
                        className='text-red-600 hover:text-red-700'
                      >
                        <Trash2 className='h-4 w-4' />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className='grid md:grid-cols-2 gap-4'>
                    <div className='space-y-3'>
                      <div className='flex items-center gap-2'>
                        <Phone className='h-4 w-4 text-muted-foreground' />
                        <span className='text-sm'>{contact.phone}</span>
                      </div>
                      <div className='flex items-center gap-2'>
                        <Mail className='h-4 w-4 text-muted-foreground' />
                        <span className='text-sm'>{contact.email}</span>
                      </div>
                      <div className='flex items-start gap-2'>
                        <MapPin className='h-4 w-4 text-muted-foreground mt-0.5' />
                        <span className='text-sm'>{contact.address}</span>
                      </div>
                    </div>
                    <div className='space-y-3'>
                      <div className='flex items-center gap-2'>
                        <Clock className='h-4 w-4 text-muted-foreground' />
                        <span className='text-sm'>
                          Added: {formatDate(contact.addedDate)}
                        </span>
                      </div>
                      <div className='flex items-center gap-2'>
                        <CheckCircle2 className='h-4 w-4 text-muted-foreground' />
                        <span className='text-sm'>
                          Verified: {formatDate(contact.lastVerified)}
                        </span>
                      </div>
                      {!contact.isPrimary && (
                        <Button
                          variant='outline'
                          size='sm'
                          onClick={() => handleSetPrimary(contact.id)}
                          className='text-xs'
                        >
                          Set as Primary
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>

        {/* Add/Edit Contact Modal would go here */}
        {isAddingContact && (
          <div className='fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50'>
            <Card className='w-full max-w-md'>
              <CardHeader>
                <CardTitle>Add Emergency Contact</CardTitle>
                <CardDescription>
                  Add a new emergency contact for {selectedUser.name}
                </CardDescription>
              </CardHeader>
              <CardContent className='space-y-4'>
                <p className='text-sm text-muted-foreground'>
                  This is a demo interface. In a real implementation, this would
                  be a form to add new emergency contacts.
                </p>
                <div className='flex gap-2'>
                  <Button
                    onClick={() => setIsAddingContact(false)}
                    variant='outline'
                  >
                    Cancel
                  </Button>
                  <Button onClick={() => setIsAddingContact(false)}>
                    Save Contact
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
}
