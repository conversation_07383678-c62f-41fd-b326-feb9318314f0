'use client';

import React, { useEffect, useState } from 'react';
import { useUserContext } from '@/components/welon-trust/user-context';
import { useQuery, useMutation } from '@tanstack/react-query';
import { DocumentReviewDialog } from '@/components/welon-trust/document-review-dialog';
import { DocumentPreviewDialog } from '@/components/welon-trust/document-preview-dialog';
import {
  DocumentResponse,
  getDocumentsForSelectedUser,
  getWelonTrustDocuments,
  approveDocument,
  rejectDocument,
} from '@/lib/api/documents';
import { Document } from '@/types/documents';
import { useSearchParams } from 'next/navigation';
import { Button } from '@workspace/ui/button';
import { Badge } from '@workspace/ui/badge';
import {
  Eye,
  FileText,
  Filter,
  PenTool,
  Search,
  AlertCircle,
  CheckCircle,
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@workspace/ui/card';
import { Input } from '@workspace/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@workspace/ui/select';
import { DocumentStatusCard } from '@/components/documents/document-status-card';
import { getUrl } from 'aws-amplify/storage';
import { toast } from 'sonner';
import { downloadDocument } from '@/lib/utils/document-download';
import routes from '@/utils/routes';
import { useRouter } from 'next/navigation';

export default function EmergencyDocumentsPage() {
  const { selectedUser } = useUserContext();
  const router = useRouter();

  const searchParams = useSearchParams();
  const [documents, setDocuments] = useState<Document[]>([]);
  const [filteredDocuments, setFilteredDocuments] = useState<Document[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('sent');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [isGeneratingPdf, setIsGeneratingPdf] = useState(false);
  const [reviewDialogOpen, setReviewDialogOpen] = useState(false);
  const [documentToReview, setDocumentToReview] = useState<Document | null>(
    null
  );
  const [previewDialogOpen, setPreviewDialogOpen] = useState(false);
  const [documentToPreview, setDocumentToPreview] = useState<Document | null>(
    null
  );

  const getQueryFunction = () => {
    if (selectedUser) {
      return () => getDocumentsForSelectedUser(selectedUser.id);
    }

    // Default to all assigned users' documents
    return getWelonTrustDocuments;
  };

  const {
    data: realDocuments,
    isLoading: realDocumentsLoading,
    error: realDocumentsError,
    refetch: refetchDocuments,
  } = useQuery({
    queryKey: ['welon-trust-documents', selectedUser?.id],
    queryFn: getQueryFunction(),
    refetchOnWindowFocus: false,
  });

  // Mutations for document approval/rejection
  const approveDocumentMutation = useMutation({
    mutationFn: async ({ documentId }: { documentId: string }) => {
      return await approveDocument(documentId);
    },
    onSuccess: () => {
      toast.success('Document approved successfully!');
      refetchDocuments();
    },
    onError: error => {
      console.error('Error approving document:', error);
      toast.error('Failed to approve document. Please try again.');
    },
  });

  const rejectDocumentMutation = useMutation({
    mutationFn: async ({
      documentId,
      rejectionReason,
    }: {
      documentId: string;
      rejectionReason: string;
    }) => {
      return await rejectDocument(documentId, rejectionReason);
    },
    onSuccess: () => {
      toast.success('Document rejected successfully!');
      refetchDocuments();
    },
    onError: error => {
      console.error('Error rejecting document:', error);
      toast.error('Failed to reject document. Please try again.');
    },
  });

  useEffect(() => {
    const filterParam = searchParams.get('filter');
    if (filterParam && filterParam !== 'all') {
      setStatusFilter(filterParam);
    }
  }, [searchParams]);

  useEffect(() => {
    if (realDocuments) {
      const convertedDocuments: Document[] = realDocuments.map(
        (doc: DocumentResponse) => ({
          id: doc.id,
          title: doc.title,
          type: doc.type as Document['type'],
          status: doc.status as Document['status'],
          dateCreated: doc.dateCreated,
          lastModified: doc.lastModified || doc.dateCreated,
          version: doc.version,
          content: doc.content,
          userId: doc.userId,
          fileUrl: doc.fileUrl,
          trackingNumber: doc.trackingNumber,
          signatureType: doc.signatureType as Document['signatureType'],
          executionDate: doc.executionDate,
          rejectionReason: doc.rejectionReason,
          templateId: doc.templateId,
          templateVersion: doc.templateVersion,
        })
      );

      // For Welon Trust, show documents that need review or have been reviewed
      const welonRelevantDocuments = convertedDocuments.filter(
        doc =>
          doc.status === 'sent' ||
          doc.status === 'approved' ||
          doc.status === 'rejected' ||
          doc.status === 'signed' ||
          doc.status === 'shipped' ||
          doc.status === 'received'
      );
      setDocuments(welonRelevantDocuments);

      console.log('===> realDocuments', realDocuments);

      // Initially show all relevant documents
      setFilteredDocuments(welonRelevantDocuments);
    } else if (!realDocuments && !realDocumentsLoading) {
      // No documents yet
      setDocuments([]);
      setFilteredDocuments([]);
    }
  }, [realDocuments, realDocumentsLoading]);

  // Determine loading state
  const loading = realDocumentsLoading;

  // Filter documents based on search and filters
  useEffect(() => {
    let filtered = documents;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(
        doc =>
          doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          doc.type.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(doc => doc.status === statusFilter);
    }

    // Type filter
    if (typeFilter !== 'all') {
      filtered = filtered.filter(doc => doc.type === typeFilter);
    }

    setFilteredDocuments(filtered);
  }, [documents, searchTerm, statusFilter, typeFilter]);

  console.log('===> DOCUMENTS', realDocuments);

  const getStatusCounts = () => {
    return {
      total: documents.length,
      draft: documents.filter(d => d.status === 'draft').length,
      pending_review: documents.filter(d => d.status === 'sent').length,
      approved: documents.filter(d => d.status === 'approved').length,
      rejected: documents.filter(d => d.status === 'rejected').length,
      signed: documents.filter(d => d.status === 'signed').length,
      shipped: documents.filter(d => d.status === 'shipped').length,
      received: documents.filter(d => d.status === 'received').length,
    };
  };

  const statusCounts = getStatusCounts();
  const filterParam = searchParams.get('filter');
  const isFiltered = filterParam && filterParam !== 'all';

  const getFilterTitle = () => {
    if (selectedUser) {
      return `Documents - ${selectedUser.name}`;
    }
    return 'Member Documents - Review & Manage';
  };

  const getFilterDescription = () => {
    if (selectedUser) {
      return `Review and manage documents from ${selectedUser.name}. Documents sent for electronic review will appear here for approval.`;
    }
    return 'Access and manage legal documents for your clients. Review generated documents, track client progress, and provide professional guidance.';
  };

  if (loading) {
    return (
      <div className='flex items-center justify-center min-h-[400px]'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4'></div>
          <p className='text-muted-foreground'>Loading your documents...</p>
        </div>
      </div>
    );
  }

  // Show error state for documents query
  if (realDocumentsError) {
    return (
      <div className='flex items-center justify-center min-h-[400px]'>
        <div className='text-center'>
          <div className='text-red-500 text-6xl mb-4'>⚠️</div>
          <h3 className='text-lg font-medium text-red-800 mb-2'>
            Error Loading Documents
          </h3>
          <p className='text-red-600 mb-4'>
            {realDocumentsError instanceof Error
              ? realDocumentsError.message
              : 'Failed to load your documents. Please try again.'}
          </p>
          <Button
            onClick={() => window.location.reload()}
            variant='outline'
            className='border-red-300 text-red-700 hover:bg-red-50'
          >
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  const handlePreview = (document: Document) => {
    setDocumentToPreview(document);
    setPreviewDialogOpen(true);
  };

  const handleClosePreviewDialog = () => {
    setPreviewDialogOpen(false);
    setDocumentToPreview(null);
  };

  const handleReview = (document: Document) => {
    router.push(`${routes.documentsReview}?id=${document.id}`);
  };

  const handleSign = (document: Document) => {
    router.push(`${routes.documentsSign}?id=${document.id}`);
  };

  const handleOpenReviewDialog = (document: Document) => {
    setDocumentToReview(document);
    setReviewDialogOpen(true);
  };

  const handleCloseReviewDialog = () => {
    setReviewDialogOpen(false);
    setDocumentToReview(null);
  };

  const handleApproveDocument = async (documentId: string) => {
    try {
      await approveDocumentMutation.mutateAsync({ documentId });
    } catch (error) {
      console.error('Error in handleApproveDocument:', error);
      throw error;
    }
  };

  const handleRejectDocument = async (
    documentId: string,
    rejectionReason: string
  ) => {
    try {
      await rejectDocumentMutation.mutateAsync({ documentId, rejectionReason });
    } catch (error) {
      console.error('Error in handleRejectDocument:', error);
      throw error;
    }
  };

  const handleDownload = async (document: Document) => {
    try {
      setIsGeneratingPdf(true);
      await downloadDocument(document);
    } catch (error) {
      console.error('Error downloading document:', error);
      toast.error('Failed to download document');
    } finally {
      setIsGeneratingPdf(false);
    }
  };

  // // Show error state for real documents query
  // if (!isWelonTrust && realDocumentsError) {
  //   return (
  //     <div className='flex items-center justify-center min-h-[400px]'>
  //       <div className='text-center'>
  //         <div className='text-red-500 text-6xl mb-4'>⚠️</div>
  //         <h3 className='text-lg font-medium text-red-800 mb-2'>
  //           Error Loading Documents
  //         </h3>
  //         <p className='text-red-600 mb-4'>
  //           {realDocumentsError instanceof Error
  //             ? realDocumentsError.message
  //             : 'Failed to load your documents. Please try again.'}
  //         </p>
  //         <Button
  //           onClick={() => window.location.reload()}
  //           variant='outline'
  //           className='border-red-300 text-red-700 hover:bg-red-50'
  //         >
  //           Try Again
  //         </Button>
  //       </div>
  //     </div>
  //   );
  // }

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div className='flex flex-col space-y-2 mr-12'>
          <h1 className='text-3xl font-bold text-[var(--foreground)]'>
            {getFilterTitle()}
          </h1>
          <p className='text-[var(--custom-gray-dark)]'>
            {getFilterDescription()}
          </p>
          {isFiltered && (
            <div className='mt-2'>
              <Badge
                variant='outline'
                className='bg-blue-50 text-blue-700 border-blue-200'
              >
                Filtered from Dashboard
              </Badge>
            </div>
          )}
        </div>
        <div className='flex items-center gap-2'>
          {isFiltered && (
            <Button
              variant='outline'
              // onClick={() => router.push(routes.documentsManage)}
              className='flex items-center gap-2'
            >
              <Filter className='h-4 w-4' />
              Clear Filter
            </Button>
          )}
          <Button
            variant='outline'
            onClick={() => refetchDocuments()}
            disabled={realDocumentsLoading}
            className='flex items-center gap-2'
          >
            <Search className='h-4 w-4' />
            {realDocumentsLoading ? 'Refreshing...' : 'Refresh'}
          </Button>
        </div>
      </div>

      {/* Quick Actions for Documents Awaiting Review */}
      {/* {documents.filter(d => d.status === 'shipped').length > 0 && (
        <div className='bg-orange-50 border border-orange-200 rounded-lg p-4'>
          <h3 className='font-semibold text-orange-900 mb-2'>
            Documents Awaiting Review
          </h3>
          <div className='flex flex-wrap gap-2'>
            <Button
              size='sm'
              variant='outline'
              onClick={() => setStatusFilter('shipped')}
              className='border-orange-300 text-orange-700 hover:bg-orange-100'
            >
              <AlertCircle className='h-4 w-4 mr-2' />
              View {documents.filter(d => d.status === 'shipped').length}{' '}
              Document
              {documents.filter(d => d.status === 'shipped').length !== 1
                ? 's'
                : ''}{' '}
              for Review
            </Button>
            <Button
              size='sm'
              variant='outline'
              onClick={() => {
                const firstDoc = documents.find(d => d.status === 'shipped');
                if (firstDoc) handleOpenReviewDialog(firstDoc);
              }}
              disabled={!documents.some(d => d.status === 'shipped')}
              className='border-green-300 text-green-700 hover:bg-green-100'
            >
              <CheckCircle className='h-4 w-4 mr-2' />
              Review First Document
            </Button>
          </div>
        </div>
      )} */}

      {/* Quick Actions for Filtered Views */}
      {isFiltered && (
        <div className='bg-blue-50 border border-blue-200 rounded-lg p-4'>
          <h3 className='font-semibold text-blue-900 mb-2'>Quick Actions</h3>
          <div className='flex flex-wrap gap-2'>
            {filterParam === 'ready_for_review' && (
              <>
                <Button
                  size='sm'
                  variant='outline'
                  onClick={() => {
                    const firstDoc = filteredDocuments.find(
                      d => d.status === 'ready_for_review'
                    );
                    if (firstDoc) handleReview(firstDoc);
                  }}
                  disabled={
                    !filteredDocuments.some(
                      d => d.status === 'ready_for_review'
                    )
                  }
                  className='border-blue-300 text-blue-700 hover:bg-blue-100'
                >
                  <Eye className='h-4 w-4 mr-2' />
                  Review First Document
                </Button>
              </>
            )}
            {filterParam === 'ready_for_signing' && (
              <>
                <Button
                  size='sm'
                  variant='outline'
                  onClick={() => {
                    const firstDoc = filteredDocuments.find(
                      d => d.status === 'ready_for_signing'
                    );
                    if (firstDoc) handleSign(firstDoc);
                  }}
                  disabled={
                    !filteredDocuments.some(
                      d => d.status === 'ready_for_signing'
                    )
                  }
                  className='border-green-300 text-green-700 hover:bg-green-100'
                >
                  <PenTool className='h-4 w-4 mr-2' />
                  Sign First Document
                </Button>
              </>
            )}
          </div>
        </div>
      )}

      {/* Status Overview */}
      <div className={`grid grid-cols-1 gap-4 md:grid-cols-5`}>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <div className='text-2xl font-bold text-blue-600'>
                {statusCounts.total}
              </div>
              <div className='text-sm text-muted-foreground'>
                Total Documents
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <div className='text-2xl font-bold text-orange-600'>
                {statusCounts.pending_review}
              </div>
              <div className='text-sm text-muted-foreground'>
                Pending Review
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <div className='text-2xl font-bold text-green-600'>
                {statusCounts.approved}
              </div>
              <div className='text-sm text-muted-foreground'>Approved</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <div className='text-2xl font-bold text-red-600'>
                {statusCounts.rejected}
              </div>
              <div className='text-sm text-muted-foreground'>Rejected</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <div className='text-2xl font-bold text-purple-600'>
                {statusCounts.shipped}
              </div>
              <div className='text-sm text-muted-foreground'>Shipped</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Filter className='h-5 w-5' />
            Filter Documents
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
            <div className='relative'>
              <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground' />
              <Input
                placeholder='Search documents...'
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className='pl-10'
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder='Filter by status' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>All Statuses</SelectItem>
                <SelectItem value='draft'>Draft</SelectItem>
                <SelectItem value='sent'>Pending Review</SelectItem>
                <SelectItem value='approved'>Approved</SelectItem>
                <SelectItem value='rejected'>Rejected</SelectItem>
                <SelectItem value='signed'>Signed</SelectItem>
                <SelectItem value='shipped'>Shipped</SelectItem>
                <SelectItem value='received'>Received</SelectItem>
              </SelectContent>
            </Select>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger>
                <SelectValue placeholder='Filter by type' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>All Types</SelectItem>
                <SelectItem value='Will'>Will</SelectItem>
                <SelectItem value='Trust'>Trust</SelectItem>
                <SelectItem value='POA'>Power of Attorney</SelectItem>
                <SelectItem value='HealthcareDirective'>
                  Healthcare Directive
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Documents List */}
      <div className='space-y-4'>
        {filteredDocuments.length === 0 ? (
          <Card>
            <CardContent className='p-8 text-center'>
              <FileText className='h-12 w-12 text-muted-foreground mx-auto mb-4' />
              <h3 className='text-lg font-medium text-[var(--custom-gray-dark)] mb-2'>
                No documents found
              </h3>
              <p className='text-muted-foreground mb-4'>
                {documents.length === 0
                  ? selectedUser
                    ? `No documents sent for review from ${selectedUser.name}. Documents will appear here when members send them for electronic review.`
                    : 'No documents sent for review from your assigned members. Documents will appear here when members send them for electronic review.'
                  : 'Try adjusting your search or filter criteria.'}
              </p>
            </CardContent>
          </Card>
        ) : (
          filteredDocuments.map(document => (
            <DocumentStatusCard
              key={document.id}
              document={document}
              isDownloadingPdf={isGeneratingPdf}
              isWelonTrustView={true}
              onPreview={() => handlePreview(document)}
              onReview={() => handleReview(document)}
              onSign={() => handleSign(document)}
              onDownload={() => handleDownload(document)}
              onOpenReviewDialog={() => handleOpenReviewDialog(document)}
            />
          ))
        )}
      </div>

      {/* Document Review Dialog */}
      <DocumentReviewDialog
        document={documentToReview}
        isOpen={reviewDialogOpen}
        onClose={handleCloseReviewDialog}
        onApprove={handleApproveDocument}
        onReject={handleRejectDocument}
        isProcessing={
          approveDocumentMutation.isPending || rejectDocumentMutation.isPending
        }
      />

      {/* Document Preview Dialog */}
      <DocumentPreviewDialog
        document={documentToPreview}
        isOpen={previewDialogOpen}
        onClose={handleClosePreviewDialog}
      />
    </div>
  );
}
