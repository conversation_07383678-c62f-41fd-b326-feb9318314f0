'use client';

import React from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { welonUsersAPI } from '@/lib/api/welon-users';
import { WelonDMSUserList } from '@/components/welon-trust/welon-dms-user-list';
import { AlertTriangle, Activity, CheckCircle, UserX } from 'lucide-react';

export default function WelonEmergencyPage() {
  const {
    data: dmsFailedUsers = [],
    isLoading: loadingFailedUsers,
    isRefetching: refetchingFailedUsers,
    refetch,
    error,
  } = useQuery({
    queryKey: ['welon-dms-failed-users'],
    queryFn: welonUsersAPI.fetchDMSFailedUsersAssignedToCurrentWelon,
    refetchInterval: 30000,
  });

  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-6xl mx-auto'>
        {/* Header */}
        <div className='flex flex-col space-y-2 mr-12 mb-8'>
          <h1 className='text-3xl font-bold text-[var(--foreground)]'>
            Emergency Dashboard
          </h1>
          <p className='text-[var(--custom-gray-dark)]'>
            Monitor and manage emergency situations for assigned members
          </p>
        </div>
        {/* Error Alert */}
        {error && (
          <Alert className='mb-6 bg-red-50 text-red-800 border-red-200'>
            <AlertTriangle className='h-4 w-4' />
            <AlertTitle>Error Loading Data</AlertTitle>
            <AlertDescription>
              Failed to load emergency data. Please try refreshing the page.
            </AlertDescription>
          </Alert>
        )}

        {/* Failed DMS Users Table */}
        <Card>
          <CardHeader className='flex justify-between items-center'>
            <div className='flex flex-col gap-1.5'>
              <CardTitle className='flex items-center'>
                <UserX className='mr-2 h-5 w-5 text-primary' />
                Members with Failed Wellness Checks
              </CardTitle>
              <CardDescription>
                Members assigned to you who have failed their Wellness Checks
                check-ins
              </CardDescription>
            </div>
            <Button
              variant='outline'
              size='sm'
              onClick={() => refetch()}
              disabled={refetchingFailedUsers}
            >
              <Activity className='h-4 w-4 mr-2' />
              {refetchingFailedUsers ? 'Refreshing...' : 'Refresh'}
            </Button>
          </CardHeader>
          <CardContent>
            {dmsFailedUsers.length === 0 && !loadingFailedUsers ? (
              <div className='flex justify-center items-center py-8'>
                <div className='text-center'>
                  <CheckCircle className='h-12 w-12 text-green-600 mx-auto mb-4' />
                  <h3 className='text-lg font-semibold mb-2'>
                    All Members Passing Wellness Checks
                  </h3>
                  <p className='text-sm text-[var(--custom-gray-medium)]'>
                    No members currently have failed Wellness Checks.
                  </p>
                </div>
              </div>
            ) : (
              <WelonDMSUserList
                users={dmsFailedUsers}
                loading={loadingFailedUsers}
              />
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
