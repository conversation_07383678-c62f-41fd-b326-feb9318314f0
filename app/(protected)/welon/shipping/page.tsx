'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Package, Search, History, Info } from 'lucide-react';
import TrackingStatus from '@/app/components/ups/TrackingStatus';
import { Alert, AlertDescription } from '@/components/ui/alert';
import SaveUserAddress from '@/components/ups/SaveUserAddress';
import IncomingPackages from '@/app/components/welon/IncomingPackages';
import { toast } from 'sonner';

export default function WelonShippingPage() {
  const [selectedTrackingNumber, setSelectedTrackingNumber] =
    useState<string>('');
  const [activeTab, setActiveTab] = useState<string>('create');

  return (
    <div className='container mx-auto py-6 space-y-6'>
      <div className='flex flex-col space-y-2 mr-12'>
        <h1 className='text-3xl font-bold text-[var(--foreground)]'>
          Shipping & Tracking
        </h1>
        <p className='text-[var(--custom-gray-dark)]'>
          Save your address, track packages, and view incoming packages from
          members
        </p>
      </div>

      <Alert>
        <Info className='h-4 w-4' />
        <AlertDescription>
          Save your address for future shipping labels and track your package
          delivery status.
        </AlertDescription>
      </Alert>

      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className='space-y-6'
      >
        <TabsList className='grid w-full grid-cols-3'>
          <TabsTrigger value='create' className='flex items-center gap-2'>
            <Package className='h-4 w-4' />
            Save Address
          </TabsTrigger>
          <TabsTrigger value='track' className='flex items-center gap-2'>
            <Search className='h-4 w-4' />
            Track Package
          </TabsTrigger>
          <TabsTrigger value='history' className='flex items-center gap-2'>
            <History className='h-4 w-4' />
            Incoming Packages
          </TabsTrigger>
        </TabsList>

        <TabsContent value='create' className='space-y-6'>
          <SaveUserAddress
            onAddressSaved={() => {
              toast.success('Address saved successfully!');
            }}
          />
        </TabsContent>

        <TabsContent value='track' className='space-y-6'>
          <TrackingStatus
            initialTrackingNumber={selectedTrackingNumber}
            autoRefresh={true}
            refreshInterval={300} // 5 minutes
          />
        </TabsContent>

        <TabsContent value='history' className='space-y-6'>
          <IncomingPackages />
        </TabsContent>
      </Tabs>
    </div>
  );
}
