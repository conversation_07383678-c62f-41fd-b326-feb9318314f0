'use client';

import React, { useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Headline, Subhead } from '@/components/ui/brand/typography';
import {
  Upload,
  FileText,
  CheckCircle,
  AlertCircle,
  X,
  Download,
  XCircle,
} from 'lucide-react';
import { useDocuments } from '@/hooks/useDocuments';
import { useUserContext } from '@/components/welon-trust/user-context';

// Types for document upload
interface UploadedDocument {
  id: string | null | undefined;
  file?: File;
  preview?: string;
  memberId: string;
  memberName: string;
  documentType: string | null | undefined;
  status: 'pending' | 'approved' | 'rejected';
  validationErrors: string[];
  fileName?: string;
  fileKey?: string | null | undefined;
  dateCreated?: string;
  rejectionReason?: string | null | undefined;
}

export default function StaffDocumentUploadPage() {
  const [dragActive, setDragActive] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedDocument[]>([]);
  const { documents, createDocumentWithFile, getFileUrl, deleteAllDocuments } =
    useDocuments();
  const { selectedUser } = useUserContext();

  // Combine uploaded files with documents from database
  const getAllDocuments = (): UploadedDocument[] => {
    const dbDocuments: UploadedDocument[] = documents.map(doc => {
      // Parse content to get member info and file metadata
      let memberInfo = { memberName: 'Unknown', memberEmail: '' };
      let fileMetadata = { fileName: `${doc.title}.pdf` };

      try {
        const content = JSON.parse(doc.content);
        memberInfo = {
          memberName: content.memberName || 'Unknown',
          memberEmail: content.memberEmail || '',
        };
        fileMetadata = {
          fileName: content.fileMetadata?.fileName || `${doc.title}.pdf`,
        };
      } catch (e) {
        // If content is not JSON, use title to extract member name
        const titleParts = doc.title.split(' - ');
        if (titleParts.length > 1) {
          memberInfo.memberName = titleParts[1];
        }
      }

      return {
        id: doc.id,
        memberId: doc.userId,
        memberName: memberInfo.memberName,
        documentType: doc.type,
        status: doc.status as 'approved' | 'rejected' | 'pending',
        validationErrors: [],
        fileName: fileMetadata.fileName,
        fileKey: doc.fileUrl, // fileUrl contains the S3 key
        dateCreated: doc.dateCreated,
        rejectionReason: doc.rejectionReason,
      };
    });

    // Combine with local uploaded files (avoid duplicates)
    const localFiles = uploadedFiles.filter(
      local => !dbDocuments.some(db => db.id === local.id)
    );

    return [...localFiles, ...dbDocuments];
  };
  const [currentUpload, setCurrentUpload] = useState<{
    file: File | null;
    memberId: string;
    memberName: string;
    documentType: string;
    notes: string;
    validationChecks: {
      signatures: boolean;
      notarization: boolean;
      witnesses: boolean;
      legibility: boolean;
    };
  }>({
    file: null,
    memberId: '',
    memberName: '',
    documentType: '',
    notes: '',
    validationChecks: {
      signatures: false,
      notarization: false,
      witnesses: false,
      legibility: false,
    },
  });
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(0);
  const ITEMS_PER_PAGE = 5;

  // Handle drag events
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  // Handle drop
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files[0]);
    }
  }, []);

  // Handle file selection
  const handleFileSelect = (file: File) => {
    // Validate file type
    const allowedTypes = [
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // DOCX
      'image/jpeg',
      'image/png',
      'image/jpg',
    ];
    if (!allowedTypes.includes(file.type)) {
      setError('Unsupported file format. Only PDF and DOCX are allowed.');
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      setError('File size exceeds 10MB limit. Please select a smaller file.');
      return;
    }

    setCurrentUpload(prev => ({ ...prev, file }));
    setError(null);
  };

  // Handle file input change
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFileSelect(e.target.files[0]);
    }
  };

  // Handle document download
  const handleDownloadDocument = async (doc: UploadedDocument) => {
    try {
      // For uploaded files, we need to find the corresponding document in the database
      // and get its file URL to download from S3
      const correspondingDocument = documents.find(d => d.id === doc.id);

      if (correspondingDocument && correspondingDocument.fileUrl) {
        const fileUrl = await getFileUrl(correspondingDocument.fileUrl);

        // Create a temporary link to download the file
        const link = document.createElement('a');
        link.href = fileUrl;
        link.download = doc.fileName || doc.file?.name || 'document.pdf';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else if (doc.file) {
        // Fallback: create a blob URL for the file object (for recently uploaded files)
        const url = URL.createObjectURL(doc.file);
        const link = document.createElement('a');
        link.href = url;
        link.download = doc.file.name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      } else {
        setError('File not available for download.');
      }
    } catch (error) {
      console.error('Error downloading document:', error);
      setError('Failed to download document. Please try again.');
    }
  };

  // Handle document upload with status
  const handleUploadWithStatus = async (status: 'approved' | 'rejected') => {
    // Validate required fields
    if (!selectedUser) {
      setError('Please select a member');
      return;
    }

    if (!currentUpload.documentType) {
      setError('Please select document type');
      return;
    }

    if (!currentUpload.file) {
      setError('Please select a file');
      return;
    }

    // For approval, all validation checks must be completed
    if (status === 'approved') {
      const allChecksCompleted = Object.values(
        currentUpload.validationChecks
      ).every(check => check);
      if (!allChecksCompleted) {
        setError(
          'All validation checks must be completed before approving the document'
        );
        return;
      }
    }

    // For rejection, reason must be provided
    if (status === 'rejected' && !currentUpload.notes.trim()) {
      setError('Please provide rejection reason in notes');
      return;
    }

    setIsUploading(true);
    setError(null);

    try {
      const rejectionReason =
        status === 'rejected'
          ? currentUpload.notes
          : Object.values(currentUpload.validationChecks).every(check => check)
            ? undefined
            : `Document validation failed. Missing: ${Object.entries(
                currentUpload.validationChecks
              )
                .filter(([_, valid]) => !valid)
                .map(([check, _]) => check)
                .join(', ')}`;

      const documentData = {
        title: `${currentUpload.documentType} - ${selectedUser.name}`,
        type: currentUpload.documentType as
          | 'Will'
          | 'Trust'
          | 'Healthcare_POA'
          | 'Financial_POA'
          | 'Advance_Directive'
          | 'Other',
        userId: selectedUser.id,
        userCognitoId: selectedUser.cognitoId!,
        memberName: selectedUser.name,
        memberEmail: selectedUser.email,
        documentNotes: currentUpload.notes,
        file: currentUpload.file,
        status,
        rejectionReason,
      };

      const uploadedDocument = await createDocumentWithFile(documentData);

      const newDocument: UploadedDocument = {
        id: uploadedDocument.id,
        file: currentUpload.file,
        memberId: selectedUser.id,
        memberName: selectedUser.name,
        documentType: currentUpload.documentType,
        status,
        validationErrors: [],
        rejectionReason,
      };

      setUploadedFiles(prev => [newDocument, ...prev]);
      setSuccess(`Document ${status} for ${selectedUser.name}`);

      // Reset form
      setCurrentUpload({
        file: null,
        memberId: '',
        memberName: '',
        documentType: '',
        notes: '',
        validationChecks: {
          signatures: false,
          notarization: false,
          witnesses: false,
          legibility: false,
        },
      });
    } catch (error) {
      setError('Failed to upload document');
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-4xl mx-auto'>
        <div className='flex flex-col space-y-2 mr-12 mb-8'>
          <h1 className='text-3xl font-bold text-[var(--foreground)]'>
            Document Upload - Welon Staff
          </h1>
          <p className='text-[var(--custom-gray-dark)]'>
            Upload and manage client documents, evidence, and supporting
            materials. Organize files by client and case type for efficient
            document management.
          </p>
        </div>

        {/* Success/Error Messages */}
        {success && (
          <Alert className='mb-6 border-[var(--lime)] bg-[var(--lime)]/10'>
            <CheckCircle className='h-4 w-4 text-[var(--lime)]' />
            <AlertTitle className='text-[var(--foreground)]'>
              Success
            </AlertTitle>
            <AlertDescription className='text-[var(--custom-gray-dark)]'>
              {success}
            </AlertDescription>
          </Alert>
        )}

        {error && (
          <Alert variant='destructive' className='mb-6'>
            <AlertCircle className='h-4 w-4' />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Upload Form */}
        <Card className='mb-8'>
          <CardHeader>
            <CardTitle>Upload Document</CardTitle>
            <CardDescription>
              Upload signed documents and validate them before approval
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-6'>
            {/* File Upload Area */}
            <div
              className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                dragActive
                  ? 'border-[var(--berry)] bg-[var(--berry)]/10'
                  : 'border-[var(--custom-gray-light)]'
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              {currentUpload.file ? (
                <div className='space-y-4'>
                  <div className='flex items-center justify-center gap-3'>
                    <FileText className='h-8 w-8 text-[var(--berry)]' />
                    <div>
                      <p className='font-medium'>{currentUpload.file.name}</p>
                      <p className='text-sm text-[var(--custom-gray-medium)]'>
                        {(currentUpload.file.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                    <Button
                      variant='outline'
                      size='sm'
                      onClick={() =>
                        setCurrentUpload(prev => ({ ...prev, file: null }))
                      }
                    >
                      <X className='h-4 w-4' />
                    </Button>
                  </div>
                </div>
              ) : (
                <div className='space-y-4'>
                  <Upload className='h-12 w-12 mx-auto text-[var(--custom-gray-medium)]' />
                  <div>
                    <p className='text-lg font-medium'>
                      Drop files here or click to browse
                    </p>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      Accepted formats: PDF, JPEG, PNG. Maximum size: 10MB.
                    </p>
                  </div>
                  <Input
                    type='file'
                    className='hidden'
                    id='fileInput'
                    accept='.pdf,.jpg,.jpeg,.png'
                    onChange={handleFileInputChange}
                  />
                  <Button
                    variant='outline'
                    onClick={() =>
                      document.getElementById('fileInput')?.click()
                    }
                  >
                    Select File
                  </Button>
                </div>
              )}
            </div>

            {/* Selected Member Display */}
            {selectedUser && (
              <div className='p-4 bg-[var(--custom-blue-light)] border border-[var(--berry)] rounded-lg'>
                <div className='flex items-center gap-2'>
                  <div className='h-2 w-2 bg-[var(--berry)] rounded-full'></div>
                  <span className='font-medium text-[var(--foreground)]'>
                    Document will be assigned to: {selectedUser.name}
                  </span>
                </div>
                <p className='text-sm text-[var(--custom-gray-dark)] mt-1'>
                  {selectedUser.email} • ID: {selectedUser.id}
                </p>
              </div>
            )}

            {/* Document Type */}
            <div className='space-y-2'>
              <Label htmlFor='documentType'>Document Type</Label>
              <Select
                value={currentUpload.documentType}
                onValueChange={value =>
                  setCurrentUpload(prev => ({ ...prev, documentType: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder='Select document type' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='Will'>Last Will and Testament</SelectItem>
                  <SelectItem value='Trust'>Revocable Living Trust</SelectItem>
                  <SelectItem value='Healthcare_POA'>
                    Healthcare Power of Attorney
                  </SelectItem>
                  <SelectItem value='Financial_POA'>
                    Financial Power of Attorney
                  </SelectItem>
                  <SelectItem value='Advance_Directive'>
                    Advance Directive
                  </SelectItem>
                  <SelectItem value='Other'>Other Document</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Validation Checklist */}
            <div className='space-y-4'>
              <Label>Validation Checklist</Label>
              <div className='space-y-3 p-4 bg-[var(--muted)] rounded-lg'>
                <div className='flex items-center space-x-3'>
                  <Checkbox
                    id='signatures'
                    checked={currentUpload.validationChecks.signatures}
                    onCheckedChange={checked =>
                      setCurrentUpload(prev => ({
                        ...prev,
                        validationChecks: {
                          ...prev.validationChecks,
                          signatures: !!checked,
                        },
                      }))
                    }
                  />
                  <Label htmlFor='signatures' className='text-sm'>
                    All required signatures are present and legible
                  </Label>
                </div>

                <div className='flex items-center space-x-3'>
                  <Checkbox
                    id='notarization'
                    checked={currentUpload.validationChecks.notarization}
                    onCheckedChange={checked =>
                      setCurrentUpload(prev => ({
                        ...prev,
                        validationChecks: {
                          ...prev.validationChecks,
                          notarization: !!checked,
                        },
                      }))
                    }
                  />
                  <Label htmlFor='notarization' className='text-sm'>
                    Notarization is complete and valid (if required)
                  </Label>
                </div>

                <div className='flex items-center space-x-3'>
                  <Checkbox
                    id='witnesses'
                    checked={currentUpload.validationChecks.witnesses}
                    onCheckedChange={checked =>
                      setCurrentUpload(prev => ({
                        ...prev,
                        validationChecks: {
                          ...prev.validationChecks,
                          witnesses: !!checked,
                        },
                      }))
                    }
                  />
                  <Label htmlFor='witnesses' className='text-sm'>
                    Witness signatures are present and valid (if required)
                  </Label>
                </div>

                <div className='flex items-center space-x-3'>
                  <Checkbox
                    id='legibility'
                    checked={currentUpload.validationChecks.legibility}
                    onCheckedChange={checked =>
                      setCurrentUpload(prev => ({
                        ...prev,
                        validationChecks: {
                          ...prev.validationChecks,
                          legibility: !!checked,
                        },
                      }))
                    }
                  />
                  <Label htmlFor='legibility' className='text-sm'>
                    Document is clear, legible, and complete
                  </Label>
                </div>
              </div>
            </div>

            {/* Notes */}
            <div className='space-y-2'>
              <Label htmlFor='notes'>Notes (Optional)</Label>
              <Textarea
                id='notes'
                value={currentUpload.notes}
                onChange={e =>
                  setCurrentUpload(prev => ({ ...prev, notes: e.target.value }))
                }
                placeholder='Add any notes about the document or validation process...'
                rows={3}
              />
            </div>

            {/* Action Buttons */}
            <div className='flex gap-4'>
              <Button
                onClick={() => handleUploadWithStatus('approved')}
                disabled={
                  !selectedUser ||
                  !currentUpload.documentType ||
                  !currentUpload.file ||
                  isUploading ||
                  !Object.values(currentUpload.validationChecks).every(
                    check => check
                  )
                }
                className='flex-1 bg-green-600 hover:bg-green-700 text-[var(--off-white)]'
              >
                {isUploading ? (
                  <>
                    <Upload className='h-4 w-4 mr-2 animate-pulse' />
                    Uploading...
                  </>
                ) : (
                  <>
                    <CheckCircle className='h-4 w-4 mr-2' />
                    Approve & Upload
                  </>
                )}
              </Button>

              {/* <Button
                variant='destructive'
                onClick={() => handleUploadWithStatus('rejected')}
                disabled={
                  !selectedUser ||
                  !currentUpload.documentType ||
                  !currentUpload.file ||
                  isUploading ||
                  !currentUpload.notes.trim()
                }
                className='flex-1'
              >
                <X className='h-4 w-4 mr-2' />
                Reject Document
              </Button> */}
            </div>
          </CardContent>
        </Card>

        {/* Recent Uploads */}
        {(uploadedFiles.length > 0 || documents.length > 0) && (
          <Card>
            <CardHeader className='flex flex-row items-center justify-between'>
              <div>
                <CardTitle>Recent Uploads</CardTitle>
                <CardDescription>Recently processed documents</CardDescription>
              </div>
              <Button
                variant='destructive'
                size='sm'
                onClick={async () => {
                  try {
                    await deleteAllDocuments();
                    setUploadedFiles([]);
                    setSuccess(
                      'All uploaded files cleared from database and storage'
                    );
                  } catch (error) {
                    setError('Failed to clear all files');
                  }
                }}
                disabled={getAllDocuments().length === 0}
              >
                Clear All
              </Button>
            </CardHeader>
            <CardContent>
              <div className='space-y-3'>
                {getAllDocuments()
                  .slice(
                    currentPage * ITEMS_PER_PAGE,
                    (currentPage + 1) * ITEMS_PER_PAGE
                  )
                  .map(doc => (
                    <div
                      key={doc.id}
                      className='flex flex-col p-3 bg-[var(--muted)] rounded-lg space-y-2'
                    >
                      <div className='flex items-center justify-between'>
                        <div className='flex items-center gap-3'>
                          <FileText className='h-5 w-5 text-[var(--berry)]' />
                          <div>
                            <p className='font-medium'>
                              {doc.fileName || doc.file?.name || 'Document'}
                            </p>
                            <p className='text-sm text-[var(--custom-gray-medium)]'>
                              {doc.memberName} (ID: {doc.memberId}) •{' '}
                              {doc.documentType}
                            </p>
                          </div>
                        </div>
                        <div className='flex items-center gap-2'>
                          <Badge
                            variant={
                              doc.status === 'approved'
                                ? 'default'
                                : doc.status === 'rejected'
                                  ? 'destructive'
                                  : 'secondary'
                            }
                            className={
                              doc.status === 'approved'
                                ? 'bg-[var(--off-white)] text-green-800 hover:bg-green-200'
                                : doc.status === 'rejected'
                                  ? 'bg-[var(--tangerine)]/20 text-[var(--tangerine)]'
                                  : 'bg-[var(--lemon)]/20 text-[var(--foreground)]'
                            }
                          >
                            {doc.status === 'approved'
                              ? 'Approved'
                              : doc.status === 'rejected'
                                ? 'Rejected'
                                : 'Pending'}
                          </Badge>
                          <Button
                            variant='outline'
                            size='sm'
                            onClick={() => handleDownloadDocument(doc)}
                            className='h-8 px-2'
                          >
                            <Download className='h-4 w-4' />
                          </Button>
                          <span className='text-xs text-[var(--custom-gray-medium)]'>
                            {doc.dateCreated
                              ? new Date(doc.dateCreated).toLocaleDateString()
                              : new Date().toLocaleDateString()}
                          </span>
                        </div>
                      </div>

                      {/* Rejection reason */}
                      {doc.status === 'rejected' && doc.rejectionReason && (
                        <div className='bg-[var(--tangerine)]/10 border border-[var(--tangerine)]/30 rounded-lg p-2'>
                          <div className='flex items-start gap-2 text-sm'>
                            <XCircle className='h-4 w-4 text-[var(--tangerine)] mt-0.5' />
                            <div>
                              <span className='font-medium text-[var(--foreground)]'>
                                Rejection Reason:
                              </span>
                              <p className='text-[var(--custom-gray-dark)] mt-1'>
                                {doc.rejectionReason}
                              </p>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
              </div>

              {/* Pagination */}
              {getAllDocuments().length > ITEMS_PER_PAGE && (
                <div className='flex justify-center items-center gap-2 mt-4 pt-4 border-t'>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() =>
                      setCurrentPage(prev => Math.max(0, prev - 1))
                    }
                    disabled={currentPage === 0}
                  >
                    Previous
                  </Button>
                  <span className='text-sm text-[var(--custom-gray-medium)]'>
                    Page {currentPage + 1} of{' '}
                    {Math.ceil(getAllDocuments().length / ITEMS_PER_PAGE)}
                  </span>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() =>
                      setCurrentPage(prev =>
                        Math.min(
                          Math.ceil(getAllDocuments().length / ITEMS_PER_PAGE) -
                            1,
                          prev + 1
                        )
                      )
                    }
                    disabled={
                      currentPage >=
                      Math.ceil(getAllDocuments().length / ITEMS_PER_PAGE) - 1
                    }
                  >
                    Next
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
