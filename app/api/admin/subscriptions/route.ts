import { NextRequest, NextResponse } from 'next/server';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { Amplify } from 'aws-amplify';
import outputs from '@/amplify_outputs.json';

export async function GET(request: NextRequest) {
  try {
    console.log('Admin Subscriptions API: Request received');

    // Configure Amplify for this request
    Amplify.configure(outputs, { ssr: true });

    // Generate the client with IAM auth mode
    const client = generateClient<Schema>({
      authMode: 'iam',
    });

    // Get all subscriptions from database
    const { data: subscriptions, errors } =
      await client.models.UserSubscription.list();

    if (errors) {
      console.error('Error fetching subscriptions:', errors);
      return NextResponse.json(
        { error: 'Failed to fetch subscriptions' },
        { status: 500 }
      );
    }

    console.log('Found subscriptions:', subscriptions?.length || 0);

    // Transform subscriptions to match admin page format
    const transformedSubscriptions =
      subscriptions?.map(sub => ({
        id: sub.id,
        userId: sub.userId || '',
        cognitoId: sub.cognitoId || '',
        tier:
          sub.plan === 'BASIC'
            ? 'Basic'
            : sub.plan === 'PRO'
              ? 'Pro'
              : 'Enterprise',
        status: mapSubscriptionStatus(sub.status || 'INACTIVE'),
        amount: (sub.amount || 0) / 100,
        startDate: sub.currentPeriodStart || new Date().toISOString(),
        nextBillingDate: sub.currentPeriodEnd || new Date().toISOString(),
        stripeCustomerId: sub.stripeCustomerId || '',
        stripeSubscriptionId: sub.stripeSubscriptionId || '',
      })) || [];

    // Calculate stats
    const stats = {
      totalRevenue: transformedSubscriptions.reduce(
        (sum, sub) => sum + sub.amount,
        0
      ),
      monthlyRevenue: transformedSubscriptions
        .filter(sub => sub.status === 'Active')
        .reduce((sum, sub) => sum + sub.amount, 0),
      activeSubscriptions: transformedSubscriptions.filter(
        sub => sub.status === 'Active'
      ).length,
      totalUsers: transformedSubscriptions.length,
    };

    return NextResponse.json({
      subscriptions: transformedSubscriptions,
      stats,
    });
  } catch (error) {
    console.error('Admin Subscriptions API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

function mapSubscriptionStatus(dbStatus: string): string {
  switch (dbStatus) {
    case 'ACTIVE':
      return 'Active';
    case 'CANCELED':
      return 'Canceled';
    case 'PAST_DUE':
      return 'Past Due';
    case 'INACTIVE':
      return 'Inactive';
    case 'INCOMPLETE':
      return 'Incomplete';
    case 'TRIALING':
      return 'Trialing';
    default:
      return 'Unknown';
  }
}
