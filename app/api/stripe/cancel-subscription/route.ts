import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '../../../../amplify/data/resource';
import { Amplify } from 'aws-amplify';
import outputs from '@/amplify_outputs.json';
import { getSecret } from '@/lib/utils/secrets';

// Initialize Stripe lazily with secrets from Amplify backend
let stripe: Stripe | null = null;

async function getStripeClient(): Promise<Stripe> {
  if (!stripe) {
    const secretKey = await getSecret('STRIPE_SECRET_KEY');
    stripe = new Stripe(secretKey);
  }
  return stripe;
}

Amplify.configure(outputs, { ssr: true });

// Generate the client with IAM auth mode for guest access
const client = generateClient<Schema>({
  authMode: 'iam',
});

export async function POST(request: NextRequest) {
  try {
    const { subscriptionId } = await request.json();
    const stripe = await getStripeClient();

    if (!subscriptionId) {
      return NextResponse.json(
        { error: 'Subscription ID is required' },
        { status: 400 }
      );
    }

    // Get user data from request headers
    const userId = request.headers.get('x-user-id');
    const cognitoId = request.headers.get('x-cognito-id');

    if (!userId || !cognitoId) {
      return NextResponse.json(
        { error: 'User authentication required' },
        { status: 401 }
      );
    }

    // Cancel subscription in Stripe
    const subscription = await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: true,
    });

    // Helper function to safely convert timestamp to ISO string
    const safeTimestampToISO = (
      timestamp: number | null | undefined
    ): string => {
      if (timestamp === null || timestamp === undefined || isNaN(timestamp)) {
        return new Date().toISOString();
      }
      return new Date(timestamp * 1000).toISOString();
    };

    // Update subscription in database
    try {
      const { data: subscriptions } = await client.models.UserSubscription.list(
        {
          filter: {
            stripeSubscriptionId: {
              eq: subscriptionId,
            },
            cognitoId: {
              eq: cognitoId,
            },
          },
        }
      );

      if (subscriptions && subscriptions.length > 0) {
        const userSubscription = subscriptions[0];
        await client.models.UserSubscription.update({
          id: userSubscription.id,
          cancelAtPeriodEnd: true,
          updatedAt: new Date().toISOString(),
        });
      }
    } catch (dbError) {
      console.error('Error updating subscription in database:', dbError);
      // Continue with Stripe response even if DB update fails
    }

    return NextResponse.json({
      success: true,
      cancelAtPeriodEnd: (subscription as any).cancel_at_period_end || false,
      currentPeriodEnd: safeTimestampToISO(
        (subscription as any).current_period_end
      ),
    });
  } catch (error) {
    console.error('Error canceling subscription:', error);
    return NextResponse.json(
      { error: 'Failed to cancel subscription' },
      { status: 500 }
    );
  }
}
