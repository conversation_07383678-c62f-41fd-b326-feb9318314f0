import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { Amplify } from 'aws-amplify';
import outputs from '@/amplify_outputs.json';
import { getSecret } from '@/lib/utils/secrets';

// Initialize Stripe lazily with secrets from Amplify backend
let stripe: Stripe | null = null;

async function getStripeClient(): Promise<Stripe> {
  if (!stripe) {
    const secretKey = await getSecret('STRIPE_SECRET_KEY');
    stripe = new Stripe(secretKey);
  }
  return stripe;
}

Amplify.configure(outputs, { ssr: true });

export async function POST(request: NextRequest) {
  try {
    const { plan } = await request.json();
    const stripe = await getStripeClient();

    // Validate plan
    if (!plan || !['BASIC', 'PRO'].includes(plan)) {
      return NextResponse.json(
        { error: 'Invalid plan selected' },
        { status: 400 }
      );
    }

    // Get user data from request headers
    const userEmail = request.headers.get('x-user-email');
    const userId = request.headers.get('x-user-id'); // Database user ID
    const cognitoId = request.headers.get('x-cognito-id'); // Cognito ID

    if (!userEmail || !userId || !cognitoId) {
      return NextResponse.json(
        { error: 'User authentication required' },
        { status: 401 }
      );
    }

    const user = {
      userId: userId,
      cognitoId: cognitoId,
      signInDetails: {
        loginId: userEmail,
      },
    };

    // Set price based on plan
    const priceAmount = plan === 'BASIC' ? 1000 : 2000; // $10 or $20 in cents
    const planName = plan === 'BASIC' ? 'Basic Plan' : 'Pro Plan';

    // Create or get Stripe customer
    let customer;
    try {
      const customers = await stripe.customers.list({
        email: user.signInDetails?.loginId,
        limit: 1,
      });

      if (customers.data.length > 0) {
        customer = customers.data[0];
      } else {
        customer = await stripe.customers.create({
          email: user.signInDetails?.loginId,
          metadata: {
            cognitoId: user.cognitoId,
            userId: user.userId,
          },
        });
      }
    } catch (error) {
      console.error('Error creating/finding customer:', error);
      return NextResponse.json(
        { error: 'Failed to create customer' },
        { status: 500 }
      );
    }

    // Create checkout session
    const session = await stripe.checkout.sessions.create({
      customer: customer.id,
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'usd',
            product_data: {
              name: planName,
              description: `Monthly subscription to ${planName}`,
            },
            unit_amount: priceAmount,
            recurring: {
              interval: 'month',
            },
          },
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/billing?success=true`,
      cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/billing/subscribe?canceled=true`,
      metadata: {
        cognitoId: user.cognitoId,
        userId: user.userId,
        plan: plan,
      },
    });

    return NextResponse.json({ url: session.url });
  } catch (error) {
    console.error('Error creating checkout session:', error);
    return NextResponse.json(
      { error: 'Failed to create checkout session' },
      { status: 500 }
    );
  }
}
