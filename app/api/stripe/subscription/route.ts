import { NextRequest, NextResponse } from 'next/server';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '../../../../amplify/data/resource';
import { Amplify } from 'aws-amplify';
import outputs from '@/amplify_outputs.json';

Amplify.configure(outputs, { ssr: true });

// Generate the client with IAM auth mode for guest access
const client = generateClient<Schema>({
  authMode: 'iam',
});

export async function GET(request: NextRequest) {
  try {
    console.log('Subscription API: Request received');

    // Get user data from request headers
    const userId = request.headers.get('x-user-id');
    const cognitoId = request.headers.get('x-cognito-id');

    console.log('Subscription API: Headers received', { userId, cognitoId });

    if (!userId || !cognitoId) {
      console.log('Subscription API: Missing authentication headers');
      return NextResponse.json(
        { error: 'User authentication required' },
        { status: 401 }
      );
    }

    // Get user subscription from database
    const { data: subscriptions, errors } =
      await client.models.UserSubscription.list({
        filter: {
          cognitoId: {
            eq: cognitoId,
          },
        },
      });

    // Also try to find by userId in case there are old records
    const { data: subscriptionsByUserId } =
      await client.models.UserSubscription.list({
        filter: {
          userId: {
            eq: userId, // Check if records exist with current userId
          },
        },
      });

    // Combine both results
    const allSubscriptions = [
      ...(subscriptions || []),
      ...(subscriptionsByUserId || []),
    ];

    if (errors) {
      console.error('Error fetching subscription:', errors);
      return NextResponse.json(
        { error: 'Failed to fetch subscription' },
        { status: 500 }
      );
    }

    // Return the most recent active subscription from all results
    const activeSubscription = allSubscriptions
      .filter(sub => sub.status === 'ACTIVE')
      .sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )[0];

    return NextResponse.json({
      subscription: activeSubscription || null,
      hasActiveSubscription: !!activeSubscription,
    });
  } catch (error) {
    console.error('Error in subscription API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
