import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  const requestId = Date.now().toString();

  try {
    console.log(`📥 [${requestId}] UPS Download Label API called`);

    const { labelData, trackingNumber } = await request.json();

    console.log(`📋 [${requestId}] Download request:`, {
      trackingNumber,
      hasLabelData: !!labelData,
      labelDataType: labelData?.startsWith('data:') ? 'data URL' : 'raw data',
      labelDataLength: labelData?.length || 0,
    });

    if (!labelData || !trackingNumber) {
      console.log(`❌ [${requestId}] Missing required fields:`, {
        hasLabelData: !!labelData,
        hasTrackingNumber: !!trackingNumber,
      });
      return NextResponse.json(
        { error: 'Label data and tracking number are required' },
        { status: 400 }
      );
    }

    // Extract base64 data from data URL if present
    const base64Data = labelData.startsWith('data:')
      ? labelData.split(',')[1]
      : labelData;

    console.log(`🔄 [${requestId}] Processing label data:`, {
      isDataUrl: labelData.startsWith('data:'),
      base64Length: base64Data?.length || 0,
    });

    // Convert base64 to buffer
    const buffer = Buffer.from(base64Data, 'base64');

    // Determine content type based on data URL
    const contentType = labelData.startsWith('data:image/gif')
      ? 'image/gif'
      : labelData.startsWith('data:application/pdf')
        ? 'application/pdf'
        : 'image/gif'; // default to GIF

    const fileExtension = contentType === 'application/pdf' ? 'pdf' : 'gif';

    console.log(`📄 [${requestId}] Label file details:`, {
      contentType,
      fileExtension,
      bufferSize: buffer.length,
      filename: `ups-label-${trackingNumber}.${fileExtension}`,
    });

    console.log(`✅ [${requestId}] Returning label file for download`);

    // Return the label file
    return new NextResponse(buffer, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="ups-label-${trackingNumber}.${fileExtension}"`,
        'Content-Length': buffer.length.toString(),
      },
    });
  } catch (error) {
    console.error(`❌ [${requestId}] Download label error:`, {
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
      name: error instanceof Error ? error.name : undefined,
    });

    return NextResponse.json(
      {
        error: 'Failed to download label',
        requestId,
      },
      { status: 500 }
    );
  }
}
