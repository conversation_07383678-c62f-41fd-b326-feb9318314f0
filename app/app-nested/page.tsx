import Link from 'next/link';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { FileText, Heart, Shield } from 'lucide-react';

export default function AppPage() {
  return (
    <main className='flex min-h-screen flex-col items-center justify-center bg-warm-gray p-6'>
      <div className='w-full max-w-4xl text-center space-y-10'>
        <div className='space-y-6'>
          <h1 className='text-4xl md:text-5xl font-bold text-text-black'>
            Childfree Trust
          </h1>
          <p className='text-xl md:text-2xl text-text-black max-w-2xl mx-auto leading-relaxed'>
            Plan an amazing life and leave a meaningful legacy.
          </p>
        </div>

        <div className='flex flex-col gap-6 max-w-sm mx-auto w-full'>
          <Button asChild size='lg' className='w-full'>
            <Link href='/auth/register'>Get Started</Link>
          </Button>

          <Button asChild variant='outline' className='w-full'>
            <Link href='/auth/login'>Sign In</Link>
          </Button>

          <Button asChild variant='link' className='w-full'>
            <Link href='/navigation'>Explore the Site</Link>
          </Button>
        </div>

        <div className='grid grid-cols-1 md:grid-cols-3 gap-8 mt-16'>
          <div className='bg-background p-8 rounded-xl shadow-md border border-soft-blue'>
            <div className='flex items-center mb-4'>
              <FileText className='h-8 w-8 text-dark-blue mr-3' />
              <h2 className='text-2xl font-semibold text-text-black'>
                Estate Planning
              </h2>
            </div>
            <p className='text-lg text-text-black'>
              Create comprehensive estate plans tailored specifically for
              Childfree individuals.
            </p>
          </div>

          <div className='bg-background p-8 rounded-xl shadow-md border border-soft-blue'>
            <div className='flex items-center mb-4'>
              <Heart className='h-8 w-8 text-dark-blue mr-3' />
              <h2 className='text-2xl font-semibold text-text-black'>
                Legacy Building
              </h2>
            </div>
            <p className='text-lg text-text-black'>
              Define your legacy and ensure your assets benefit the causes and
              people you care about.
            </p>
          </div>

          <div className='bg-background p-8 rounded-xl shadow-md border border-soft-blue'>
            <div className='flex items-center mb-4'>
              <Shield className='h-8 w-8 text-dark-blue mr-3' />
              <h2 className='text-2xl font-semibold text-text-black'>
                Peace of Mind
              </h2>
            </div>
            <p className='text-lg text-text-black'>
              Rest easy knowing your wishes will be carried out exactly as you
              intended.
            </p>
          </div>
        </div>
      </div>
    </main>
  );
}
