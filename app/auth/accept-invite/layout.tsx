import { <PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import { ReactNode, Suspense } from 'react';

export default function AcceptInvitePageLayout({
  children,
}: {
  children: ReactNode;
}) {
  return (
    <Suspense
      fallback={
        <Card className='w-full max-w-md mx-auto mt-8'>
          <CardHeader>
            <CardTitle>Loading...</CardTitle>
          </CardHeader>
        </Card>
      }
    >
      {children}
    </Suspense>
  );
}
