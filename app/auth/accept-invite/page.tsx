'use client';

import React, { useEffect, useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';

const client = generateClient<Schema>({
  authMode: 'iam',
});

export default function AcceptInvitePage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const token = searchParams.get('token');

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const validateInvite = async () => {
      if (!token) {
        setError('Invalid invitation link');
        setIsLoading(false);
        return;
      }

      try {
        // Find the invite by token using the actual token from URL
        const { data: invite, errors } = await client.queries.readInvite({
          token: token,
        });

        // Handle errors from the query
        if (errors && errors.length > 0) {
          const errorMessage = errors[0]?.message || 'Unknown error occurred';
          console.error('===> INVITE QUERY ERRORS:', errorMessage);

          setError(errorMessage);
          setIsLoading(false);
          return;
        }

        // Check if invite data exists (readInvite returns a single object, not an array)
        if (!invite) {
          setError('Invitation not found');
          setIsLoading(false);
          return;
        }

        // Redirect to registration with invite data
        const params = new URLSearchParams({
          email: invite.email,
          firstName: invite.firstName,
          lastName: invite.lastName,
          inviteToken: token,
          role: invite.role || 'Member',
          subrole: invite.subrole || '',
        });

        router.push(`/register?${params.toString()}`);
      } catch (err) {
        console.error('Error validating invite:', err);
      } finally {
        setIsLoading(false);
      }
    };

    validateInvite();
  }, [token, router]);

  if (isLoading) {
    return (
      <div className='min-h-screen flex items-center justify-center bg-gray-50'>
        <Card className='w-full max-w-md'>
          <CardContent className='pt-6'>
            <div className='text-center'>
              <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto'></div>
              <p className='mt-2 text-sm text-gray-600'>
                Validating invitation...
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className='min-h-screen flex items-center justify-center bg-gray-50'>
        <Card className='w-full max-w-md'>
          <CardHeader>
            <CardTitle className='text-red-600'>Invalid Invitation</CardTitle>
          </CardHeader>
          <CardContent>
            <p className='text-gray-600 mb-4'>{error}</p>
            <Button
              onClick={() => router.push('/auth/signin')}
              className='w-full'
            >
              Go to Sign In
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // This should not be reached as we redirect in useEffect
  return null;
}
