'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

export default function ForgotPasswordPage() {
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [step, setStep] = useState(1);
  const [verificationCode, setVerificationCode] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  // Password strength indicators
  const hasMinLength = newPassword.length >= 8;
  const hasUppercase = /[A-Z]/.test(newPassword);
  const hasLowercase = /[a-z]/.test(newPassword);
  const hasNumber = /[0-9]/.test(newPassword);
  const hasSpecialChar = /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(
    newPassword
  );
  const isPasswordStrong =
    hasMinLength && hasUppercase && hasLowercase && hasNumber && hasSpecialChar;
  const passwordsMatch = newPassword === confirmPassword && newPassword !== '';

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      // Step 1: Request password reset
      if (step === 1) {
        if (!email) {
          throw new Error('Please enter your email address');
        }

        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Move to verification step
        setStep(2);
        setLoading(false);
        return;
      }

      // Step 2: Verify code
      if (step === 2) {
        if (!verificationCode || verificationCode.length !== 6) {
          throw new Error('Please enter a valid 6-digit verification code');
        }

        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Move to reset password step
        setStep(3);
        setLoading(false);
        return;
      }

      // Step 3: Reset password
      if (step === 3) {
        if (!newPassword || !confirmPassword) {
          throw new Error('Please fill in all required fields');
        }

        if (!isPasswordStrong) {
          throw new Error('Password does not meet the strength requirements');
        }

        if (!passwordsMatch) {
          throw new Error('Passwords do not match');
        }

        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Move to success step
        setStep(4);
        setLoading(false);
        return;
      }

      // Step 4: Success
      if (step === 4) {
        // Redirect to login
        router.push('/login');
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred');
      setLoading(false);
    }
  };

  return (
    <div className='flex min-h-screen flex-col items-center justify-center bg-gray-6197c p-4'>
      <div className='w-full max-w-md space-y-8'>
        <div className='text-center'>
          <h1 className='text-3xl font-geologica font-semibold text-black-6c'>
            Childfree Trust
          </h1>
          <h2 className='mt-2 text-xl font-geologica font-light text-black-6c'>
            {step === 1 && 'Reset your password'}
            {step === 2 && 'Verify your email'}
            {step === 3 && 'Create new password'}
            {step === 4 && 'Password reset successful'}
          </h2>
        </div>

        {error && (
          <div className='bg-red-50 border border-red-200 text-red-700 p-4 rounded-md mb-4'>
            {error}
          </div>
        )}

        <div className='bg-background p-8 rounded-lg shadow-md'>
          <form onSubmit={handleSubmit} className='space-y-6'>
            {step === 1 && (
              <>
                <div className='space-y-2'>
                  <Label htmlFor='email'>Email address</Label>
                  <Input
                    id='email'
                    type='email'
                    placeholder='<EMAIL>'
                    value={email}
                    onChange={e => setEmail(e.target.value)}
                    required
                    className='w-full'
                  />
                </div>
                <p className='text-sm text-[var(--custom-gray-medium)]'>
                  We'll send a verification code to this email address if it's
                  associated with an account.
                </p>
              </>
            )}

            {step === 2 && (
              <div className='space-y-4'>
                <div className='text-center'>
                  <p className='text-sm text-[var(--custom-gray-medium)] mb-4'>
                    We've sent a verification code to <strong>{email}</strong>
                  </p>
                </div>

                <div className='space-y-2'>
                  <Label htmlFor='verification-code'>Verification Code</Label>
                  <Input
                    id='verification-code'
                    type='text'
                    placeholder='123456'
                    value={verificationCode}
                    onChange={e => setVerificationCode(e.target.value)}
                    maxLength={6}
                    className='w-full text-center text-xl tracking-widest'
                  />
                </div>

                <div className='text-center text-sm'>
                  <p className='text-[var(--custom-gray-medium)]'>
                    Didn't receive the code?{' '}
                    <Button
                      variant='link'
                      className='h-auto p-0 min-w-0'
                      onClick={() => {
                        // Simulate resending code
                        setLoading(true);
                        setTimeout(() => {
                          setLoading(false);
                        }, 1000);
                      }}
                    >
                      Resend
                    </Button>
                  </p>
                </div>
              </div>
            )}

            {step === 3 && (
              <>
                <div className='space-y-2'>
                  <Label htmlFor='new-password'>New password</Label>
                  <Input
                    id='new-password'
                    type='password'
                    placeholder='••••••••'
                    value={newPassword}
                    onChange={e => setNewPassword(e.target.value)}
                    required
                    className='w-full'
                  />

                  <div className='mt-2 space-y-1 text-sm'>
                    <p className='font-medium text-black-6c'>
                      Password requirements:
                    </p>
                    <div className='grid grid-cols-2 gap-1'>
                      <div
                        className={`flex items-center ${hasMinLength ? 'text-green-2010c' : 'text-[var(--custom-gray-medium)]'}`}
                      >
                        <div
                          className={`w-4 h-4 mr-2 rounded-full ${hasMinLength ? 'bg-green-2010c' : 'bg-gray-200'}`}
                        ></div>
                        <span>8+ characters</span>
                      </div>
                      <div
                        className={`flex items-center ${hasUppercase ? 'text-green-2010c' : 'text-[var(--custom-gray-medium)]'}`}
                      >
                        <div
                          className={`w-4 h-4 mr-2 rounded-full ${hasUppercase ? 'bg-green-2010c' : 'bg-gray-200'}`}
                        ></div>
                        <span>Uppercase (A-Z)</span>
                      </div>
                      <div
                        className={`flex items-center ${hasLowercase ? 'text-green-2010c' : 'text-[var(--custom-gray-medium)]'}`}
                      >
                        <div
                          className={`w-4 h-4 mr-2 rounded-full ${hasLowercase ? 'bg-green-2010c' : 'bg-gray-200'}`}
                        ></div>
                        <span>Lowercase (a-z)</span>
                      </div>
                      <div
                        className={`flex items-center ${hasNumber ? 'text-green-2010c' : 'text-[var(--custom-gray-medium)]'}`}
                      >
                        <div
                          className={`w-4 h-4 mr-2 rounded-full ${hasNumber ? 'bg-green-2010c' : 'bg-gray-200'}`}
                        ></div>
                        <span>Number (0-9)</span>
                      </div>
                      <div
                        className={`flex items-center ${hasSpecialChar ? 'text-green-2010c' : 'text-[var(--custom-gray-medium)]'}`}
                      >
                        <div
                          className={`w-4 h-4 mr-2 rounded-full ${hasSpecialChar ? 'bg-green-2010c' : 'bg-gray-200'}`}
                        ></div>
                        <span>Special character</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className='space-y-2'>
                  <Label htmlFor='confirm-password'>Confirm new password</Label>
                  <Input
                    id='confirm-password'
                    type='password'
                    placeholder='••••••••'
                    value={confirmPassword}
                    onChange={e => setConfirmPassword(e.target.value)}
                    required
                    className='w-full'
                  />
                  {confirmPassword && (
                    <p
                      className={`text-sm ${passwordsMatch ? 'text-green-2010c' : 'text-destructive'}`}
                    >
                      {passwordsMatch
                        ? 'Passwords match'
                        : 'Passwords do not match'}
                    </p>
                  )}
                </div>
              </>
            )}

            {step === 4 && (
              <div className='text-center py-4'>
                <div className='bg-green-2010c/10 text-green-2010c p-3 rounded-full inline-flex mb-4'>
                  <span className='text-xl'>✓</span>
                </div>
                <h3 className='text-lg font-medium mb-2'>
                  Password Reset Successful
                </h3>
                <p className='text-sm text-[var(--custom-gray-medium)] mb-6'>
                  Your password has been reset successfully. You can now log in
                  with your new password.
                </p>
              </div>
            )}

            <div className='flex justify-between pt-4'>
              {step > 1 && step < 4 && (
                <Button
                  type='button'
                  variant='outline'
                  onClick={() => setStep(step - 1)}
                  disabled={loading}
                >
                  Back
                </Button>
              )}

              <Button
                type='submit'
                variant='default'
                className={`${step === 1 || step === 4 ? 'w-full' : ''}`}
                disabled={loading}
              >
                {loading
                  ? 'Processing...'
                  : step === 1
                    ? 'Send Reset Link'
                    : step === 2
                      ? 'Verify Code'
                      : step === 3
                        ? 'Reset Password'
                        : 'Return to Login'}
              </Button>
            </div>
          </form>
        </div>

        <div className='text-center mt-4'>
          <p className='text-sm text-black-6c'>
            Remember your password?{' '}
            <Button asChild variant='link'>
              <Link href='/login'>Sign in</Link>
            </Button>
          </p>
        </div>
      </div>
    </div>
  );
}
