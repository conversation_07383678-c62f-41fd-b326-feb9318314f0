'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function LoginPage() {
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [showMfa, setShowMfa] = useState(false);
  const [mfaCode, setMfaCode] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      // Validate inputs
      if (!email || !password) {
        throw new Error('Please enter both email and password');
      }

      // Simulate login process
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Simulate MFA check
      if (!showMfa) {
        setShowMfa(true);
        setLoading(false);
        return;
      }

      // Simulate MFA validation
      if (showMfa && (!mfaCode || mfaCode.length !== 6)) {
        throw new Error('Please enter a valid 6-digit verification code');
      }

      // Simulate successful login
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Redirect to dashboard
      router.push('/dashboard');
    } catch (err: any) {
      setError(err.message || 'An error occurred during login');
      setLoading(false);
    }
  };

  const handleForgotPassword = () => {
    router.push('/auth/forgot-password');
  };

  return (
    <div className='flex min-h-screen flex-col items-center justify-center bg-gray-6197c p-4'>
      <div className='w-full max-w-md space-y-8'>
        <div className='text-center'>
          <h1 className='text-3xl font-geologica font-semibold text-black-6c'>
            Childfree Trust
          </h1>
          <h2 className='mt-2 text-xl font-geologica font-light text-black-6c'>
            Sign in to your account
          </h2>
        </div>

        {error && (
          <div className='bg-red-50 border border-red-200 text-red-700 p-4 rounded-md mb-4'>
            {error}
          </div>
        )}

        <div className='bg-background p-8 rounded-lg shadow-md'>
          <Tabs defaultValue='email' className='w-full'>
            <TabsContent value='email'>
              <form onSubmit={handleLogin} className='space-y-6'>
                {!showMfa ? (
                  <>
                    <div className='space-y-2'>
                      <Label htmlFor='email'>Email address</Label>
                      <Input
                        id='email'
                        type='email'
                        placeholder='<EMAIL>'
                        value={email}
                        onChange={e => setEmail(e.target.value)}
                        required
                        className='w-full'
                      />
                    </div>

                    <div className='space-y-2'>
                      <div className='flex items-center justify-between'>
                        <Label htmlFor='password'>Password</Label>
                        <Button
                          type='button'
                          onClick={handleForgotPassword}
                          variant='link'
                          className='h-auto p-0 min-w-0'
                        >
                          Forgot password?
                        </Button>
                      </div>
                      <Input
                        id='password'
                        type='password'
                        placeholder='••••••••'
                        value={password}
                        onChange={e => setPassword(e.target.value)}
                        required
                        className='w-full'
                      />
                    </div>

                    <div className='flex items-center space-x-2'>
                      <Checkbox
                        id='remember'
                        checked={rememberMe}
                        onCheckedChange={checked =>
                          setRememberMe(checked as boolean)
                        }
                      />
                      <Label htmlFor='remember' className='text-sm font-normal'>
                        Remember me for 30 days
                      </Label>
                    </div>
                  </>
                ) : (
                  <div className='space-y-4'>
                    <div>
                      <Label htmlFor='mfa-code'>Verification Code</Label>
                      <p className='text-sm text-[var(--custom-gray-medium)] mb-2'>
                        Please enter the 6-digit code sent to your device
                      </p>
                      <Input
                        id='mfa-code'
                        type='text'
                        placeholder='123456'
                        value={mfaCode}
                        onChange={e => setMfaCode(e.target.value)}
                        maxLength={6}
                        className='w-full text-center text-xl tracking-widest'
                      />
                    </div>
                    <Button
                      type='button'
                      onClick={() => setShowMfa(false)}
                      variant='link'
                      className='h-auto p-0 min-w-0'
                    >
                      Back to login
                    </Button>
                  </div>
                )}

                <Button type='submit' className='w-full' disabled={loading}>
                  {loading ? 'Signing in...' : showMfa ? 'Verify' : 'Sign in'}
                </Button>
              </form>
            </TabsContent>

            {/* <TabsContent value="social">
              <form onSubmit={handleLogin} className="space-y-6">
                <div className="space-y-4">
                  <p className="text-sm text-[var(--custom-gray-medium)] text-center mb-4">
                    Click the button below to sign in to your account
                  </p>
                </div>

                <Button
                  type="submit"
                  className="w-full mt-4"
                  disabled={loading}
                >
                  {loading ? "Signing in..." : "Sign in"}
                </Button>
              </form>
            </TabsContent> */}
          </Tabs>
        </div>

        <div className='text-center mt-4'>
          <p className='text-sm text-black-6c'>
            Don't have an account?{' '}
            <Link href='/auth/register' className='text-link hover:underline'>
              Sign up
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
