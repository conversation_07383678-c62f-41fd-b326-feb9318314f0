'use client';

import { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Eye, EyeOff, Lock } from 'lucide-react';
import PasswordStrengthIndicator from './PasswordStrengthIndicator';
import PasswordRequirements from './PasswordRequirements';
import { updatePassword } from 'aws-amplify/auth';
import { toast } from 'sonner';

interface ChangePasswordDialogProps {
  trigger?: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export default function ChangePasswordDialog({
  trigger,
  open,
  onOpenChange,
}: ChangePasswordDialogProps) {
  const [oldPassword, setOldPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showOldPassword, setShowOldPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordError, setPasswordError] = useState('');
  const [passwordSuccess, setPasswordSuccess] = useState('');
  const [isChangingPassword, setIsChangingPassword] = useState(false);

  const resetForm = () => {
    setOldPassword('');
    setNewPassword('');
    setConfirmPassword('');
    setPasswordError('');
    setPasswordSuccess('');
  };

  const handleDialogClose = (open: boolean) => {
    if (!open) {
      resetForm();
    }
    if (onOpenChange) {
      onOpenChange(open);
    }
  };

  const handleChangePassword = async () => {
    // Reset messages
    setPasswordError('');
    setPasswordSuccess('');

    // Validate passwords
    if (!oldPassword || !newPassword || !confirmPassword) {
      setPasswordError('Please fill in all password fields');
      return;
    }

    if (newPassword !== confirmPassword) {
      setPasswordError('New passwords do not match');
      return;
    }

    // Check if new password meets requirements
    const hasUppercase = /[A-Z]/.test(newPassword);
    const hasLowercase = /[a-z]/.test(newPassword);
    const hasNumber = /[0-9]/.test(newPassword);
    const hasSpecial = /[^A-Za-z0-9]/.test(newPassword);
    const hasMinLength = newPassword.length >= 8;

    if (
      !hasUppercase ||
      !hasLowercase ||
      !hasNumber ||
      !hasSpecial ||
      !hasMinLength
    ) {
      setPasswordError('New password does not meet all requirements');
      return;
    }

    setIsChangingPassword(true);

    try {
      await updatePassword({ oldPassword, newPassword });
      toast('Password changed successfully!');
      // Clear form
      setOldPassword('');
      setNewPassword('');
      setConfirmPassword('');
    } catch (error: any) {
      console.error('Error changing password:', error);
      if (error.name === 'NotAuthorizedException') {
        setPasswordError('Current password is incorrect');
      } else if (error.name === 'LimitExceededException') {
        setPasswordError('Too many attempts. Please try again later');
      } else {
        setPasswordError(error.message || 'Failed to change password');
      }
    } finally {
      setIsChangingPassword(false);
    }
  };

  const dialogContent = (
    <DialogContent className='sm:max-w-md'>
      <DialogHeader>
        <DialogTitle className='flex items-center'>
          <Lock className='mr-2 h-5 w-5' />
          Change Password
        </DialogTitle>
        <DialogDescription>
          Update your password to keep your account secure
        </DialogDescription>
      </DialogHeader>
      <div className='space-y-4 py-2'>
        <div className='space-y-2'>
          <Label htmlFor='current-password'>Current Password</Label>
          <div className='relative'>
            <Lock className='absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground' />
            <Input
              id='current-password'
              type={showOldPassword ? 'text' : 'password'}
              value={oldPassword}
              onChange={e => setOldPassword(e.target.value)}
              className='pl-10 pr-10'
              placeholder='Enter current password'
            />
            <Button
              type='button'
              variant='ghost'
              size='icon'
              className='absolute right-0 top-0 h-9 w-9'
              onClick={() => setShowOldPassword(!showOldPassword)}
            >
              {showOldPassword ? (
                <EyeOff className='h-4 w-4' />
              ) : (
                <Eye className='h-4 w-4' />
              )}
            </Button>
          </div>
        </div>

        <div className='space-y-2'>
          <Label htmlFor='new-password'>New Password</Label>
          <div className='relative'>
            <Lock className='absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground' />
            <Input
              id='new-password'
              type={showNewPassword ? 'text' : 'password'}
              value={newPassword}
              onChange={e => setNewPassword(e.target.value)}
              className='pl-10 pr-10'
              placeholder='Enter new password'
            />
            <Button
              type='button'
              variant='ghost'
              size='icon'
              className='absolute right-0 top-0 h-9 w-9'
              onClick={() => setShowNewPassword(!showNewPassword)}
            >
              {showNewPassword ? (
                <EyeOff className='h-4 w-4' />
              ) : (
                <Eye className='h-4 w-4' />
              )}
            </Button>
          </div>
        </div>

        <div className='space-y-1'>
          <PasswordStrengthIndicator password={newPassword} />
          <PasswordRequirements password={newPassword} />
        </div>

        <div className='space-y-2'>
          <Label htmlFor='confirm-password'>Confirm New Password</Label>
          <div className='relative'>
            <Lock className='absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground' />
            <Input
              id='confirm-password'
              type={showConfirmPassword ? 'text' : 'password'}
              value={confirmPassword}
              onChange={e => setConfirmPassword(e.target.value)}
              className='pl-10 pr-10'
              placeholder='Confirm new password'
            />
            <Button
              type='button'
              variant='ghost'
              size='icon'
              className='absolute right-0 top-0 h-9 w-9'
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            >
              {showConfirmPassword ? (
                <EyeOff className='h-4 w-4' />
              ) : (
                <Eye className='h-4 w-4' />
              )}
            </Button>
          </div>
        </div>

        {passwordError && (
          <p className='text-destructive text-sm'>{passwordError}</p>
        )}
        {passwordSuccess && (
          <p className='text-green-600 text-sm'>{passwordSuccess}</p>
        )}
      </div>
      <DialogFooter>
        <Button
          onClick={handleChangePassword}
          disabled={isChangingPassword}
          className='w-full'
        >
          {isChangingPassword ? 'Changing Password...' : 'Change Password'}
        </Button>
      </DialogFooter>
    </DialogContent>
  );

  // If trigger is provided, use it with DialogTrigger
  if (trigger) {
    return (
      <Dialog onOpenChange={handleDialogClose}>
        <DialogTrigger asChild>{trigger}</DialogTrigger>
        {dialogContent}
      </Dialog>
    );
  }

  // Otherwise, use controlled dialog
  return (
    <Dialog open={open} onOpenChange={handleDialogClose}>
      {dialogContent}
    </Dialog>
  );
}
