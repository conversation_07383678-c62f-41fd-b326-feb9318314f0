'use client';

import { useEffect, useState, useCallback, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/app/context/AuthContext';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { AlertTriangle, Clock } from 'lucide-react';
import routes from '@/utils/routes';

interface InactivityTimerProps {
  timeoutMinutes?: number;
  warningMinutes?: number;
}

export default function InactivityTimer({
  timeoutMinutes = 2,
  warningMinutes = 1,
}: InactivityTimerProps) {
  const { user, logout, isRememberMeSession } = useAuth();
  const router = useRouter();

  const [showWarning, setShowWarning] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState(0);

  const timeoutRef = useRef<NodeJS.Timeout>();
  const warningTimeoutRef = useRef<NodeJS.Timeout>();
  const countdownRef = useRef<NodeJS.Timeout>();
  const lastActivityRef = useRef<number>(Date.now());

  // Convert minutes to milliseconds
  const timeoutMs = timeoutMinutes * 60 * 1000;
  const warningMs = warningMinutes * 60 * 1000;
  const warningDurationMs = timeoutMs - warningMs;

  // Activity events to monitor
  const activityEvents = [
    'mousedown',
    'mousemove',
    'keypress',
    'scroll',
    'touchstart',
    'click',
  ];

  // Reset all timers and start fresh
  const resetTimers = useCallback(() => {
    // Clear existing timers
    if (timeoutRef.current) clearTimeout(timeoutRef.current);
    if (warningTimeoutRef.current) clearTimeout(warningTimeoutRef.current);
    if (countdownRef.current) clearInterval(countdownRef.current);

    // Hide warning if shown
    setShowWarning(false);

    // Update last activity time
    lastActivityRef.current = Date.now();

    // Set warning timer
    warningTimeoutRef.current = setTimeout(() => {
      setShowWarning(true);
      setTimeRemaining(Math.ceil(warningDurationMs / 1000));

      // Start countdown
      countdownRef.current = setInterval(() => {
        setTimeRemaining(prev => {
          if (prev <= 1) {
            handleTimeout();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }, warningMs);

    // Set final timeout
    timeoutRef.current = setTimeout(() => {
      handleTimeout();
    }, timeoutMs);
  }, [timeoutMs, warningMs, warningDurationMs]);

  // Handle session timeout
  const handleTimeout = useCallback(async () => {
    console.log('Session expired due to inactivity');

    // Clear all timers
    if (timeoutRef.current) clearTimeout(timeoutRef.current);
    if (warningTimeoutRef.current) clearTimeout(warningTimeoutRef.current);
    if (countdownRef.current) clearInterval(countdownRef.current);

    // Hide warning
    setShowWarning(false);

    try {
      // Logout and invalidate session
      await logout();
    } catch (error) {
      console.error('Error during logout:', error);
    } finally {
      // Redirect to login with session expired parameter
      router.push(`${window.location.href}?sessionExpired=true`);
    }
  }, [logout, router]);

  // Extend session (reset timers)
  const extendSession = useCallback(() => {
    resetTimers();
  }, [resetTimers]);

  // Handle user activity
  const handleActivity = useCallback(() => {
    const now = Date.now();
    const timeSinceLastActivity = now - lastActivityRef.current;

    // Only reset if enough time has passed to avoid excessive resets
    if (timeSinceLastActivity > 1000) {
      // 1 second threshold
      resetTimers();
    }
  }, [resetTimers]);

  // Set up activity listeners - only when Remember Me is false
  useEffect(() => {
    // Only activate inactivity timer if user is logged in AND Remember Me is false
    if (!user || isRememberMeSession) {
      // Clear any existing timers if Remember Me is enabled
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
      if (warningTimeoutRef.current) clearTimeout(warningTimeoutRef.current);
      if (countdownRef.current) clearInterval(countdownRef.current);
      setShowWarning(false);
      return;
    }

    // Add event listeners
    activityEvents.forEach(event => {
      document.addEventListener(event, handleActivity, true);
    });

    // Start initial timers
    resetTimers();

    // Cleanup
    return () => {
      activityEvents.forEach(event => {
        document.removeEventListener(event, handleActivity, true);
      });

      if (timeoutRef.current) clearTimeout(timeoutRef.current);
      if (warningTimeoutRef.current) clearTimeout(warningTimeoutRef.current);
      if (countdownRef.current) clearInterval(countdownRef.current);
    };
  }, [user, isRememberMeSession, handleActivity, resetTimers]);

  // Format time remaining for display
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Don't render anything if user is not logged in OR if Remember Me is active
  if (!user || isRememberMeSession) return null;

  return (
    <Dialog open={showWarning} onOpenChange={() => {}}>
      <DialogContent
        className='sm:max-w-md'
        onPointerDownOutside={e => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <AlertTriangle className='h-5 w-5 text-amber-500' />
            Session Timeout Warning
          </DialogTitle>
          <DialogDescription>
            Your session will expire due to inactivity. You will be
            automatically logged out in{' '}
            <strong>{formatTime(timeRemaining)}</strong>.
          </DialogDescription>
        </DialogHeader>

        <div className='flex items-center justify-center py-4'>
          <div className='flex items-center gap-2 text-lg font-mono'>
            <Clock className='h-5 w-5 text-amber-500' />
            <span className='text-amber-600 font-bold'>
              {formatTime(timeRemaining)}
            </span>
          </div>
        </div>

        <DialogFooter className='flex gap-2'>
          <Button variant='outline' onClick={handleTimeout} className='flex-1'>
            Logout Now
          </Button>
          <Button onClick={extendSession} className='flex-1'>
            Extend Session
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
