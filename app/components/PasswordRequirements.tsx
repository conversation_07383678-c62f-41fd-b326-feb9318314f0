'use client';

import { useEffect, useState } from 'react';
import { CheckCircle2, XCircle } from 'lucide-react';

interface PasswordRequirementsProps {
  password: string;
}

interface PasswordRequirementState {
  minLength: boolean;
  hasUppercase: boolean;
  hasLowercase: boolean;
  hasNumber: boolean;
  hasSpecial: boolean;
}

export default function PasswordRequirements({
  password,
}: PasswordRequirementsProps) {
  const [passwordRequirements, setPasswordRequirements] =
    useState<PasswordRequirementState>({
      minLength: false,
      hasUppercase: false,
      hasLowercase: false,
      hasNumber: false,
      hasSpecial: false,
    });

  // Check password requirements
  const checkPasswordRequirements = (password: string) => {
    setPasswordRequirements({
      minLength: password.length >= 12,
      hasUppercase: /[A-Z]/.test(password),
      hasLowercase: /[a-z]/.test(password),
      hasNumber: /[0-9]/.test(password),
      hasSpecial: /[^A-Za-z0-9]/.test(password),
    });
  };

  useEffect(() => {
    checkPasswordRequirements(password || '');
  }, [password]);

  const RequirementItem = ({ met, text }: { met: boolean; text: string }) => (
    <div className='flex items-center gap-1.5'>
      {met ? (
        <CheckCircle2 className='h-3.5 w-3.5 text-green-500' />
      ) : (
        <XCircle className='h-3.5 w-3.5 text-muted-foreground' />
      )}
      <span className={met ? 'text-green-500' : 'text-muted-foreground'}>
        {text}
      </span>
    </div>
  );

  return (
    <div className='mt-3 space-y-1.5 text-xs'>
      <p className='text-muted-foreground font-medium'>
        Password requirements:
      </p>
      <div className='grid grid-cols-2 gap-x-4 gap-y-1.5'>
        <RequirementItem
          met={passwordRequirements.minLength}
          text='At least 12 characters'
        />
        <RequirementItem
          met={passwordRequirements.hasUppercase}
          text='Uppercase letter'
        />
        <RequirementItem
          met={passwordRequirements.hasLowercase}
          text='Lowercase letter'
        />
        <RequirementItem met={passwordRequirements.hasNumber} text='Number' />
        <RequirementItem
          met={passwordRequirements.hasSpecial}
          text='Special character'
        />
      </div>
    </div>
  );
}
