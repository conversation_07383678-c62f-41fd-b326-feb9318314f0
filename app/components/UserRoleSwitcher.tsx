'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Settings,
  ChevronDown,
  User,
  LogOut,
  Shield,
  Home,
} from 'lucide-react';
import { useRole } from '@/lib/roles/role-context';
import { RoleManager, type UserRole } from '@/lib/roles/role-manager';
import type { AuthUser } from 'aws-amplify/auth';
import { useRouter } from 'next/navigation';
import routes from '@/utils/routes';
import { OnboardingStep } from '@/utils/userOnboarding';

interface UserRoleSwitcherProps {
  user: AuthUser;
  onSignOut: () => void;
  userRoles: string[];
  onboardingStatus: OnboardingStep;
}

export function UserRoleSwitcher({
  user,
  onSignOut,
  userRoles,
  onboardingStatus,
}: UserRoleSwitcherProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [showRoleSwitcher, setShowRoleSwitcher] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const router = useRouter();
  const { userContext, switchRole, getDashboardPath } = useRole();
  const roleManager = RoleManager.getInstance();

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
        setShowRoleSwitcher(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleRoleSwitch = (role: UserRole, subrole?: string) => {
    switchRole(role, subrole);
    setShowRoleSwitcher(false);
    setIsOpen(false);

    const redirectPath =
      onboardingStatus === OnboardingStep.COMPLETED
        ? getDashboardPath(role)
        : routes.member.onboarding;

    // Use router.push for better navigation experience
    router.push(redirectPath);
  };

  // Get user display name - prefer actual name over username
  const getUserDisplayName = () => {
    // Try to get actual user name from user attributes
    if (user.signInDetails?.loginId) {
      // Extract name from email if available
      const email = user.signInDetails.loginId;
      const namePart = email.split('@')[0];
      // Convert email prefix to readable name (basic transformation)
      const readableName = namePart
        .split(/[._-]/)
        .map(part => part.charAt(0).toUpperCase() + part.slice(1))
        .join(' ');
      return readableName;
    }

    // Fallback to username but make it more readable
    if (user.username) {
      // If username is a UUID, show "User" instead
      if (
        user.username.match(
          /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
        )
      ) {
        return 'User';
      }
      return user.username;
    }

    return 'User';
  };

  const currentDisplayName = userContext?.displayName || 'Member';
  const userDisplayName = getUserDisplayName();
  const availableRoles = roleManager.getAllRoles();

  const getOptimizedDashboardPath = (): string => {
    if (userRoles && userRoles.length > 0) {
      if (userRoles.includes('ADMINS')) {
        return routes.admin.dashboard;
      }
      if (userRoles.includes('WELONTRUST')) {
        return routes.welon.dashboard;
      }
    }

    if (onboardingStatus === OnboardingStep.COMPLETED) {
      return routes.member.dashboard;
    } else {
      return routes.member.onboarding;
    }
  };

  const handleGoToDashboard = () => {
    const dashboardPath = getOptimizedDashboardPath();
    router.push(dashboardPath);
    setIsOpen(false);
  };

  return (
    <div className='relative' ref={dropdownRef}>
      {/* User Button Trigger with Settings Icon */}
      <Button
        variant='ghost'
        onClick={() => setIsOpen(!isOpen)}
        className='flex items-center space-x-2 h-auto'
        aria-expanded={isOpen}
        aria-haspopup='true'
      >
        <Settings className='h-5 w-5' />
        <div className='flex flex-col items-start'>
          <span className='text-sm font-medium'>{userDisplayName}</span>
          {/*<span className='text-xs text-[var(--custom-gray-medium)]'>{currentDisplayName}</span>*/}
        </div>
        <ChevronDown
          className={`h-4 w-4 text-[var(--custom-gray-medium)] transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
        />
      </Button>

      {/* Dropdown Menu */}
      {isOpen && !showRoleSwitcher && (
        <div className='absolute right-0 mt-2 w-64 bg-background border border-gray-200 rounded-lg shadow-lg z-50'>
          <div className='py-2'>
            {/* Profile Section */}
            <div className='px-4 py-2 border-b border-gray-100'>
              <p className='text-sm font-medium'>Signed in as</p>
              <p className='text-sm text-[var(--custom-gray-medium)]'>
                {userDisplayName}
              </p>
              {/*<p className='text-xs text-[var(--custom-gray-medium)]'>*/}
              {/*  Role: {currentDisplayName}*/}
              {/*</p>*/}
            </div>

            {/* Menu Items */}
            <div className='py-1'>
              {/* Go to Dashboard */}
              <button
                onClick={handleGoToDashboard}
                className='flex items-center w-full px-4 py-2 text-sm text-[var(--foreground)] hover:bg-gray-100 hover:text-[var(--custom-gray-dark)] transition-colors cursor-pointer'
              >
                <Home className='h-4 w-4 mr-3' />
                Go to Dashboard
              </button>

              {/* Role Switcher */}
              {/*<button*/}
              {/*  onClick={() => setShowRoleSwitcher(true)}*/}
              {/*  className='flex items-center w-full px-4 py-2 text-sm text-[var(--custom-gray-dark)] hover:bg-gray-100 transition-colors cursor-pointer'*/}
              {/*>*/}
              {/*  <RefreshCw className='h-4 w-4 mr-3' />*/}
              {/*  Switch Role*/}
              {/*</button>*/}

              {/* Divider */}
              <div className='border-t border-gray-100 my-1' />

              {/* Sign Out */}
              <button
                onClick={onSignOut}
                className='flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors cursor-pointer'
              >
                <LogOut className='h-4 w-4 mr-3' />
                Sign out
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Role Switcher Submenu */}
      {isOpen && showRoleSwitcher && (
        <div className='absolute right-0 mt-2 w-72 bg-background border border-gray-200 rounded-lg shadow-lg z-50'>
          <div className='py-2'>
            {/* Header */}
            <div className='px-4 py-2 border-b border-gray-100'>
              <div className='flex items-center justify-between'>
                <p className='text-sm font-medium text-[var(--custom-gray-dark)]'>
                  Switch Role
                </p>
                <button
                  onClick={() => setShowRoleSwitcher(false)}
                  className='text-[var(--custom-gray-medium)] hover:text-[var(--custom-gray-medium)]'
                >
                  ←
                </button>
              </div>
              <p className='text-xs text-[var(--custom-gray-medium)] mt-1'>
                Choose a role to switch to
              </p>
            </div>

            {/* Role Options */}
            <div className='py-1 max-h-64 overflow-y-auto'>
              {availableRoles.map(role => {
                const isCurrentRole = userContext?.role === role.id;
                const subroles = roleManager.getSubrolesForRole(
                  role.id as UserRole
                );

                return (
                  <div key={role.id}>
                    {/* Main Role */}
                    <button
                      onClick={() => handleRoleSwitch(role.id as UserRole)}
                      disabled={isCurrentRole && !userContext?.subrole}
                      className={`flex items-center w-full px-4 py-2 text-sm transition-colors cursor-pointer ${
                        isCurrentRole && !userContext?.subrole
                          ? 'bg-blue-50 text-blue-700 cursor-default'
                          : 'text-[var(--custom-gray-dark)] hover:bg-gray-100'
                      }`}
                    >
                      <Shield className='h-4 w-4 mr-3' />
                      <div className='flex-1 text-left'>
                        <div className='font-medium'>{role.name}</div>
                        <div className='text-xs text-[var(--custom-gray-medium)]'>
                          {role.description}
                        </div>
                      </div>
                      {isCurrentRole && !userContext?.subrole && (
                        <span className='text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full'>
                          Current
                        </span>
                      )}
                    </button>

                    {/* Subroles */}
                    {subroles.map(subrole => {
                      const isCurrentSubrole =
                        userContext?.role === role.id &&
                        userContext?.subrole === subrole.id;

                      return (
                        <button
                          key={subrole.id}
                          onClick={() =>
                            handleRoleSwitch(role.id as UserRole, subrole.id)
                          }
                          disabled={isCurrentSubrole}
                          className={`flex items-center w-full px-8 py-2 text-sm transition-colors cursor-pointer ${
                            isCurrentSubrole
                              ? 'bg-blue-50 text-blue-700 cursor-default'
                              : 'text-[var(--custom-gray-medium)] hover:bg-gray-50'
                          }`}
                        >
                          <User className='h-3 w-3 mr-3' />
                          <div className='flex-1 text-left'>
                            <div className='font-medium'>{subrole.name}</div>
                            <div className='text-xs text-[var(--custom-gray-medium)]'>
                              {subrole.description}
                            </div>
                          </div>
                          {isCurrentSubrole && (
                            <span className='text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full'>
                              Current
                            </span>
                          )}
                        </button>
                      );
                    })}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
