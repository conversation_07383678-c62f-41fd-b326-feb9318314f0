'use client';

import { useState } from 'react';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DEFAULT_PERMISSIONS,
  LINK_PERMISSIONS,
  LinkRequest,
  LinkType,
} from '@/types/account';
import { useLinkedAccounts } from '../../../hooks/use-linked-accounts';

// Form validation schema
const formSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  linkType: z.enum(['primary', 'secondary', 'delegate', 'emergency'] as const),
  customPermissions: z.boolean().default(false),
  permissions: z.array(z.string()).optional(),
  expiresAt: z.string().optional(),
});

type LinkAccountFormProps = {
  onSuccess?: () => void;
  onCancel?: () => void;
};

export function LinkAccountForm({ onSuccess, onCancel }: LinkAccountFormProps) {
  const { linkAccount, loading, error } = useLinkedAccounts();

  // Form state
  const [email, setEmail] = useState('');
  const [linkType, setLinkType] = useState<LinkType>('secondary');
  const [useCustomPermissions, setUseCustomPermissions] = useState(false);
  const [permissions, setPermissions] = useState<string[]>([]);
  const [expiresAt, setExpiresAt] = useState('');
  const [formError, setFormError] = useState('');

  // Get default permissions for the selected link type
  const defaultPermissions = DEFAULT_PERMISSIONS[linkType];

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError('');

    try {
      // Validate form data
      const formData = {
        email,
        linkType,
        customPermissions: useCustomPermissions,
        permissions: useCustomPermissions ? permissions : undefined,
        expiresAt: expiresAt || undefined,
      };

      const result = formSchema.safeParse(formData);

      if (!result.success) {
        setFormError(
          result.error.errors[0]?.message || 'Please check your input'
        );
        return;
      }

      // Create link request
      const linkRequest: LinkRequest = {
        email,
        linkType,
        permissions: useCustomPermissions ? permissions : defaultPermissions,
        expiresAt: expiresAt || undefined,
      };

      // Submit the request
      await linkAccount(linkRequest);

      // Call success callback
      if (onSuccess) {
        onSuccess();
      }
    } catch (err) {
      setFormError(
        err instanceof Error ? err.message : 'Failed to link account'
      );
    }
  };

  // Handle permission checkbox change
  const handlePermissionChange = (permission: string, checked: boolean) => {
    if (checked) {
      setPermissions(prev => [...prev, permission]);
    } else {
      setPermissions(prev => prev.filter(p => p !== permission));
    }
  };

  // Handle link type change
  const handleLinkTypeChange = (value: string) => {
    setLinkType(value as LinkType);
    // Reset permissions when link type changes
    if (!useCustomPermissions) {
      setPermissions(DEFAULT_PERMISSIONS[value as LinkType]);
    }
  };

  return (
    <form onSubmit={handleSubmit} className='space-y-6'>
      <div className='space-y-4'>
        <div className='space-y-2'>
          <Label htmlFor='email'>Email Address</Label>
          <Input
            id='email'
            type='email'
            placeholder='Enter email address'
            value={email}
            onChange={e => setEmail(e.target.value)}
            required
          />
        </div>

        <div className='space-y-2'>
          <Label htmlFor='linkType'>Link Type</Label>
          <Select value={linkType} onValueChange={handleLinkTypeChange}>
            <SelectTrigger id='linkType'>
              <SelectValue placeholder='Select link type' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='primary'>Primary (Full Access)</SelectItem>
              <SelectItem value='secondary'>
                Secondary (Limited Access)
              </SelectItem>
              <SelectItem value='delegate'>
                Delegate (Document Access)
              </SelectItem>
              <SelectItem value='emergency'>Emergency Contact</SelectItem>
            </SelectContent>
          </Select>
          <p className='text-sm text-muted-foreground'>
            {linkType === 'primary' &&
              'Full access to your account, typically for spouses or partners'}
            {linkType === 'secondary' &&
              'Limited access, typically for family members or close friends'}
            {linkType === 'delegate' &&
              'Access to specific documents, typically for advisors or helpers'}
            {linkType === 'emergency' && 'Access only in emergency situations'}
          </p>
        </div>

        <div className='space-y-2'>
          <div className='flex items-center space-x-2'>
            <Checkbox
              id='customPermissions'
              checked={useCustomPermissions}
              onCheckedChange={checked =>
                setUseCustomPermissions(checked as boolean)
              }
            />
            <Label htmlFor='customPermissions' className='font-normal'>
              Customize permissions
            </Label>
          </div>
        </div>

        {useCustomPermissions && (
          <div className='space-y-2 border rounded-md p-4'>
            <Label className='block mb-2'>Permissions</Label>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-2'>
              {Object.entries(LINK_PERMISSIONS).map(([key, value]) => (
                <div key={key} className='flex items-center space-x-2'>
                  <Checkbox
                    id={`permission-${key}`}
                    checked={permissions.includes(value)}
                    onCheckedChange={checked =>
                      handlePermissionChange(value, checked as boolean)
                    }
                  />
                  <Label
                    htmlFor={`permission-${key}`}
                    className='font-normal text-sm'
                  >
                    {key
                      .split('_')
                      .map(
                        word =>
                          word.charAt(0).toUpperCase() +
                          word.slice(1).toLowerCase()
                      )
                      .join(' ')}
                  </Label>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className='space-y-2'>
          <Label htmlFor='expiresAt'>Expiration Date (Optional)</Label>
          <Input
            id='expiresAt'
            type='date'
            value={expiresAt}
            onChange={e => setExpiresAt(e.target.value)}
          />
          <p className='text-sm text-muted-foreground'>
            Leave blank for no expiration
          </p>
        </div>
      </div>

      {(formError || error) && (
        <div className='bg-red-50 border border-red-200 text-red-700 p-4 rounded-md'>
          {formError || error}
        </div>
      )}

      <div className='flex justify-end space-x-2'>
        {onCancel && (
          <Button type='button' variant='outline' onClick={onCancel}>
            Cancel
          </Button>
        )}
        <Button type='submit' disabled={loading}>
          {loading ? 'Linking...' : 'Link Account'}
        </Button>
      </div>
    </form>
  );
}
