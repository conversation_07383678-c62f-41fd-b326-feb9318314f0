'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { LINK_PERMISSIONS, LinkedAccount } from '@/types/account';
import { useLinkedAccounts } from '../../../hooks/use-linked-accounts';
import { mockUsers } from '@/lib/mock/linked-accounts';

type LinkPermissionsFormProps = {
  link: LinkedAccount;
  onSuccess?: () => void;
  onCancel?: () => void;
};

export function LinkPermissionsForm({
  link,
  onSuccess,
  onCancel,
}: LinkPermissionsFormProps) {
  const { updateLinkPermissions, loading, error } = useLinkedAccounts();

  // Form state
  const [permissions, setPermissions] = useState<string[]>([]);
  const [formError, setFormError] = useState('');

  // Get user details
  const userDetails = mockUsers.find(user => user.id === link.linkedUserId);

  // Initialize permissions from the link
  useEffect(() => {
    setPermissions(link.permissions || []);
  }, [link]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError('');

    try {
      // Update permissions
      await updateLinkPermissions(link.id, permissions);

      // Call success callback
      if (onSuccess) {
        onSuccess();
      }
    } catch (err) {
      setFormError(
        err instanceof Error ? err.message : 'Failed to update permissions'
      );
    }
  };

  // Handle permission checkbox change
  const handlePermissionChange = (permission: string, checked: boolean) => {
    if (checked) {
      setPermissions(prev => [...prev, permission]);
    } else {
      setPermissions(prev => prev.filter(p => p !== permission));
    }
  };

  return (
    <form onSubmit={handleSubmit} className='space-y-6'>
      {userDetails && (
        <div className='mb-4'>
          <p className='text-sm font-medium'>Editing permissions for:</p>
          <p className='text-lg font-semibold'>{userDetails.name}</p>
          <p className='text-sm text-muted-foreground'>{userDetails.email}</p>
        </div>
      )}

      <div className='space-y-4'>
        <div className='border rounded-md p-4'>
          <Label className='block mb-2'>Permissions</Label>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-2'>
            {Object.entries(LINK_PERMISSIONS).map(([key, value]) => (
              <div key={key} className='flex items-center space-x-2'>
                <Checkbox
                  id={`permission-${key}`}
                  checked={permissions.includes(value)}
                  onCheckedChange={checked =>
                    handlePermissionChange(value, checked as boolean)
                  }
                />
                <Label
                  htmlFor={`permission-${key}`}
                  className='font-normal text-sm'
                >
                  {key
                    .split('_')
                    .map(
                      word =>
                        word.charAt(0).toUpperCase() +
                        word.slice(1).toLowerCase()
                    )
                    .join(' ')}
                </Label>
              </div>
            ))}
          </div>
        </div>
      </div>

      {(formError || error) && (
        <div className='bg-red-50 border border-red-200 text-red-700 p-4 rounded-md'>
          {formError || error}
        </div>
      )}

      <div className='flex justify-end space-x-2'>
        {onCancel && (
          <Button type='button' variant='outline' onClick={onCancel}>
            Cancel
          </Button>
        )}
        <Button type='submit' disabled={loading}>
          {loading ? 'Updating...' : 'Update Permissions'}
        </Button>
      </div>
    </form>
  );
}
