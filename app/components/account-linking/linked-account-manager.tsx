'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Users,
  UserPlus,
  Shield,
  CheckCircle,
  XCircle,
  Clock,
  Settings,
  Trash2,
  AlertTriangle,
} from 'lucide-react';
import { useLinkedAccounts } from '../../../hooks/use-linked-accounts';
import {
  LinkType,
  LINK_PERMISSIONS,
  DEFAULT_PERMISSIONS,
} from '@/types/account';

export function LinkedAccountManager() {
  const {
    outgoingLinks,
    incomingLinks,
    loading,
    error,
    linkAccount,
    unlinkAccount,
    updateLinkStatus,
    updateLinkPermissions,
  } = useLinkedAccounts();

  const [isLinkDialogOpen, setIsLinkDialogOpen] = useState(false);
  const [linkForm, setLinkForm] = useState({
    email: '',
    linkType: 'secondary' as LinkType,
    permissions: DEFAULT_PERMISSIONS.secondary,
  });

  const handleCreateLink = async () => {
    try {
      await linkAccount({
        email: linkForm.email,
        linkType: linkForm.linkType,
        permissions: linkForm.permissions,
      });
      setIsLinkDialogOpen(false);
      setLinkForm({
        email: '',
        linkType: 'secondary',
        permissions: DEFAULT_PERMISSIONS.secondary,
      });
    } catch (err) {
      // Error is handled by the hook
    }
  };

  const handleApproveLink = async (linkId: string) => {
    try {
      await updateLinkStatus(linkId, 'active');
    } catch (err) {
      // Error is handled by the hook
    }
  };

  const handleRejectLink = async (linkId: string) => {
    try {
      await updateLinkStatus(linkId, 'revoked');
    } catch (err) {
      // Error is handled by the hook
    }
  };

  const handleUnlink = async (linkId: string) => {
    try {
      await unlinkAccount(linkId);
    } catch (err) {
      // Error is handled by the hook
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return (
          <Badge variant='default' className='bg-green-100 text-green-800'>
            <CheckCircle className='h-3 w-3 mr-1' />
            Active
          </Badge>
        );
      case 'pending':
        return (
          <Badge variant='secondary'>
            <Clock className='h-3 w-3 mr-1' />
            Pending
          </Badge>
        );
      case 'revoked':
        return (
          <Badge variant='destructive'>
            <XCircle className='h-3 w-3 mr-1' />
            Revoked
          </Badge>
        );
      default:
        return <Badge variant='outline'>{status}</Badge>;
    }
  };

  const getLinkTypeBadge = (linkType: LinkType) => {
    const colors = {
      primary: 'bg-blue-100 text-blue-800',
      secondary: 'bg-gray-100 text-[var(--custom-gray-dark)]',
      delegate: 'bg-purple-100 text-purple-800',
      emergency: 'bg-red-100 text-red-800',
    };

    return (
      <Badge variant='outline' className={colors[linkType]}>
        {linkType.charAt(0).toUpperCase() + linkType.slice(1)}
      </Badge>
    );
  };

  if (loading) {
    return (
      <Card>
        <CardContent className='p-8 text-center'>
          <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4'></div>
          <p>Loading linked accounts...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className='space-y-6'>
      {error && (
        <Alert variant='destructive'>
          <AlertTriangle className='h-4 w-4' />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Header with Create Link Button */}
      <div className='flex items-center justify-between'>
        <div>
          <h2 className='text-2xl font-bold'>Linked Account Management</h2>
          <p className='text-muted-foreground'>
            Manage account links and permissions
          </p>
        </div>

        <Dialog open={isLinkDialogOpen} onOpenChange={setIsLinkDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <UserPlus className='h-4 w-4 mr-2' />
              Link Account
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Link New Account</DialogTitle>
              <DialogDescription>
                Create a link to share access with another user
              </DialogDescription>
            </DialogHeader>

            <div className='space-y-4'>
              <div>
                <Label htmlFor='email'>Email Address</Label>
                <Input
                  id='email'
                  type='email'
                  placeholder='<EMAIL>'
                  value={linkForm.email}
                  onChange={e =>
                    setLinkForm(prev => ({ ...prev, email: e.target.value }))
                  }
                />
              </div>

              <div>
                <Label htmlFor='linkType'>Link Type</Label>
                <Select
                  value={linkForm.linkType}
                  onValueChange={(value: LinkType) => {
                    setLinkForm(prev => ({
                      ...prev,
                      linkType: value,
                      permissions: DEFAULT_PERMISSIONS[value],
                    }));
                  }}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='primary'>Primary Partner</SelectItem>
                    <SelectItem value='secondary'>Secondary Contact</SelectItem>
                    <SelectItem value='delegate'>Delegate</SelectItem>
                    <SelectItem value='emergency'>Emergency Contact</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Permissions</Label>
                <div className='space-y-2 mt-2'>
                  {Object.entries(LINK_PERMISSIONS).map(([key, permission]) => (
                    <div
                      key={permission}
                      className='flex items-center space-x-2'
                    >
                      <Checkbox
                        id={permission}
                        checked={linkForm.permissions.includes(permission)}
                        onCheckedChange={checked => {
                          if (checked) {
                            setLinkForm(prev => ({
                              ...prev,
                              permissions: [...prev.permissions, permission],
                            }));
                          } else {
                            setLinkForm(prev => ({
                              ...prev,
                              permissions: prev.permissions.filter(
                                p => p !== permission
                              ),
                            }));
                          }
                        }}
                      />
                      <Label htmlFor={permission} className='text-sm'>
                        {key
                          .replace(/_/g, ' ')
                          .toLowerCase()
                          .replace(/\b\w/g, l => l.toUpperCase())}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button
                variant='outline'
                onClick={() => setIsLinkDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button onClick={handleCreateLink} disabled={!linkForm.email}>
                Create Link
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Outgoing Links (Links you created) */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Users className='h-5 w-5' />
            Outgoing Links
          </CardTitle>
        </CardHeader>
        <CardContent>
          {outgoingLinks.length === 0 ? (
            <p className='text-muted-foreground text-center py-4'>
              No outgoing links. Create a link to share access with someone.
            </p>
          ) : (
            <div className='space-y-3'>
              {outgoingLinks.map(link => (
                <div
                  key={link.id}
                  className='flex items-center justify-between p-3 border rounded-lg'
                >
                  <div className='flex items-center gap-3'>
                    <div>
                      <p className='font-medium'>
                        User ID: {link.linkedUserId}
                      </p>
                      <div className='flex items-center gap-2 mt-1'>
                        {getLinkTypeBadge(link.linkType)}
                        {getStatusBadge(link.status)}
                      </div>
                    </div>
                  </div>
                  <div className='flex items-center gap-2'>
                    <Button variant='outline' size='sm'>
                      <Settings className='h-4 w-4' />
                    </Button>
                    <Button
                      variant='outline'
                      size='sm'
                      onClick={() => handleUnlink(link.id)}
                    >
                      <Trash2 className='h-4 w-4' />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Incoming Links (Links others created to your account) */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Shield className='h-5 w-5' />
            Incoming Link Requests
          </CardTitle>
        </CardHeader>
        <CardContent>
          {incomingLinks.length === 0 ? (
            <p className='text-muted-foreground text-center py-4'>
              No incoming link requests.
            </p>
          ) : (
            <div className='space-y-3'>
              {incomingLinks.map(link => (
                <div
                  key={link.id}
                  className='flex items-center justify-between p-3 border rounded-lg'
                >
                  <div className='flex items-center gap-3'>
                    <div>
                      <p className='font-medium'>From User ID: {link.userId}</p>
                      <div className='flex items-center gap-2 mt-1'>
                        {getLinkTypeBadge(link.linkType)}
                        {getStatusBadge(link.status)}
                      </div>
                    </div>
                  </div>
                  {link.status === 'pending' && (
                    <div className='flex items-center gap-2'>
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() => handleApproveLink(link.id)}
                      >
                        <CheckCircle className='h-4 w-4 mr-1' />
                        Approve
                      </Button>
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() => handleRejectLink(link.id)}
                      >
                        <XCircle className='h-4 w-4 mr-1' />
                        Reject
                      </Button>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
