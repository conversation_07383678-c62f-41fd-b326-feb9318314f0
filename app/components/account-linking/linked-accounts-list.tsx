'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  AlertTriangle,
  Check,
  Clock,
  Edit,
  ExternalLink,
  Link,
  Unlink,
  MoreHorizontal,
  Shield,
  User,
  UserPlus,
  X,
} from 'lucide-react';
import { LinkedAccount, LinkStatus, LinkType } from '@/types/account';
import { useLinkedAccounts } from '../../../hooks/use-linked-accounts';
import { LinkAccountForm } from './link-account-form';
import { LinkPermissionsForm } from './link-permissions-form';
import { mockUsers } from '@/lib/mock/linked-accounts';

export function LinkedAccountsList() {
  const {
    outgoingLinks,
    incomingLinks,
    loading,
    error,
    unlinkAccount,
    updateLinkStatus,
    refreshLinks,
  } = useLinkedAccounts();

  const [activeTab, setActiveTab] = useState('outgoing');
  const [showLinkForm, setShowLinkForm] = useState(false);
  const [selectedLink, setSelectedLink] = useState<LinkedAccount | null>(null);
  const [showPermissionsForm, setShowPermissionsForm] = useState(false);
  const [showConfirmUnlink, setShowConfirmUnlink] = useState(false);
  const [showConfirmStatusChange, setShowConfirmStatusChange] = useState(false);
  const [newStatus, setNewStatus] = useState<LinkStatus | null>(null);

  // Get user details for a linked account
  const getUserDetails = (userId: string) => {
    const user = mockUsers.find(user => user.id === userId);
    return user
      ? { name: user.name, email: user.email }
      : { name: 'Unknown User', email: '<EMAIL>' };
  };

  // Format date string
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
  };

  // Get badge color based on link status
  const getStatusBadge = (status: LinkStatus) => {
    switch (status) {
      case 'active':
        return <Badge className='bg-green-500'>{status}</Badge>;
      case 'pending':
        return <Badge className='bg-yellow-500'>{status}</Badge>;
      case 'revoked':
        return <Badge className='bg-red-500'>{status}</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  // Get icon based on link type
  const getLinkTypeIcon = (type: LinkType) => {
    switch (type) {
      case 'primary':
        return <User className='h-4 w-4' />;
      case 'secondary':
        return <Link className='h-4 w-4' />;
      case 'delegate':
        return <ExternalLink className='h-4 w-4' />;
      case 'emergency':
        return <Shield className='h-4 w-4' />;
      default:
        return <Link className='h-4 w-4' />;
    }
  };

  // Format link type for display
  const formatLinkType = (type: LinkType) => {
    switch (type) {
      case 'primary':
        return 'Primary';
      case 'secondary':
        return 'Secondary';
      case 'delegate':
        return 'Delegate';
      case 'emergency':
        return 'Emergency';
      default:
        return type;
    }
  };

  // Handle unlinking an account
  const handleUnlink = async () => {
    if (!selectedLink) return;

    try {
      await unlinkAccount(selectedLink.id);
      setShowConfirmUnlink(false);
      setSelectedLink(null);
    } catch (error) {
      console.error('Failed to unlink account:', error);
    }
  };

  // Handle changing link status
  const handleStatusChange = async () => {
    if (!selectedLink || !newStatus) return;

    try {
      await updateLinkStatus(selectedLink.id, newStatus);
      setShowConfirmStatusChange(false);
      setSelectedLink(null);
      setNewStatus(null);
    } catch (error) {
      console.error('Failed to update link status:', error);
    }
  };

  // Render a linked account card
  const renderLinkedAccountCard = (
    link: LinkedAccount,
    isOutgoing: boolean
  ) => {
    const userDetails = getUserDetails(
      isOutgoing ? link.linkedUserId : link.userId
    );

    return (
      <Card key={link.id} className='mb-4'>
        <CardHeader className='pb-2'>
          <div className='flex justify-between items-start'>
            <div>
              <CardTitle className='text-lg flex items-center'>
                {getLinkTypeIcon(link.linkType)}
                <span className='ml-2'>{userDetails.name}</span>
              </CardTitle>
              <CardDescription>{userDetails.email}</CardDescription>
            </div>
            <div className='flex items-center space-x-2'>
              {getStatusBadge(link.status)}
              <Badge variant='outline' className='flex items-center'>
                {getLinkTypeIcon(link.linkType)}
                <span className='ml-1'>{formatLinkType(link.linkType)}</span>
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent className='pb-2 text-sm'>
          <div className='grid grid-cols-2 gap-2'>
            <div>
              <p className='text-muted-foreground'>Created:</p>
              <p>{formatDate(link.createdAt)}</p>
            </div>
            {link.expiresAt && (
              <div>
                <p className='text-muted-foreground'>Expires:</p>
                <p>{formatDate(link.expiresAt)}</p>
              </div>
            )}
          </div>

          {link.permissions.length > 0 && (
            <div className='mt-2'>
              <p className='text-muted-foreground mb-1'>Permissions:</p>
              <div className='flex flex-wrap gap-1'>
                {link.permissions.map(permission => (
                  <Badge
                    key={permission}
                    variant='secondary'
                    className='text-xs'
                  >
                    {permission
                      .split('_')
                      .map(
                        word =>
                          word.charAt(0).toUpperCase() +
                          word.slice(1).toLowerCase()
                      )
                      .join(' ')}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </CardContent>
        <CardFooter className='pt-2'>
          <div className='flex justify-end space-x-2 w-full'>
            {isOutgoing && link.status !== 'revoked' && (
              <Button
                variant='outline'
                size='sm'
                onClick={() => {
                  setSelectedLink(link);
                  setShowPermissionsForm(true);
                }}
              >
                <Edit className='h-4 w-4 mr-1' />
                Permissions
              </Button>
            )}

            {isOutgoing && (
              <Button
                variant='outline'
                size='sm'
                className='text-red-500 hover:text-red-700'
                onClick={() => {
                  setSelectedLink(link);
                  setShowConfirmUnlink(true);
                }}
              >
                <Unlink className='h-4 w-4 mr-1' />
                Unlink
              </Button>
            )}

            {!isOutgoing && link.status === 'pending' && (
              <>
                <Button
                  variant='outline'
                  size='sm'
                  className='text-red-500 hover:text-red-700'
                  onClick={() => {
                    setSelectedLink(link);
                    setNewStatus('revoked');
                    setShowConfirmStatusChange(true);
                  }}
                >
                  <X className='h-4 w-4 mr-1' />
                  Reject
                </Button>
                <Button
                  variant='default'
                  size='sm'
                  onClick={() => {
                    setSelectedLink(link);
                    setNewStatus('active');
                    setShowConfirmStatusChange(true);
                  }}
                >
                  <Check className='h-4 w-4 mr-1' />
                  Accept
                </Button>
              </>
            )}

            {!isOutgoing && link.status === 'active' && (
              <Button
                variant='outline'
                size='sm'
                className='text-red-500 hover:text-red-700'
                onClick={() => {
                  setSelectedLink(link);
                  setNewStatus('revoked');
                  setShowConfirmStatusChange(true);
                }}
              >
                <Unlink className='h-4 w-4 mr-1' />
                Revoke
              </Button>
            )}
          </div>
        </CardFooter>
      </Card>
    );
  };

  return (
    <div className='space-y-4'>
      <div className='flex justify-between items-center'>
        <h2 className='text-2xl font-semibold'>Linked Accounts</h2>
        <Button onClick={() => setShowLinkForm(true)}>
          <UserPlus className='h-4 w-4 mr-2' />
          Link New Account
        </Button>
      </div>

      {error && (
        <div className='bg-red-50 border border-red-200 text-red-700 p-4 rounded-md'>
          {error}
        </div>
      )}

      <Tabs
        defaultValue='outgoing'
        value={activeTab}
        onValueChange={setActiveTab}
      >
        <TabsList className='grid w-full grid-cols-2'>
          <TabsTrigger value='outgoing'>Accounts I've Linked</TabsTrigger>
          <TabsTrigger value='incoming'>Accounts Linked to Me</TabsTrigger>
        </TabsList>

        <TabsContent value='outgoing' className='mt-4'>
          {loading ? (
            <p className='text-center py-4'>Loading...</p>
          ) : outgoingLinks.length === 0 ? (
            <div className='text-center py-8 border rounded-md bg-gray-50'>
              <p className='text-muted-foreground'>
                You haven't linked any accounts yet.
              </p>
              <Button variant='link' onClick={() => setShowLinkForm(true)}>
                Link an account now
              </Button>
            </div>
          ) : (
            outgoingLinks.map(link => renderLinkedAccountCard(link, true))
          )}
        </TabsContent>

        <TabsContent value='incoming' className='mt-4'>
          {loading ? (
            <p className='text-center py-4'>Loading...</p>
          ) : incomingLinks.length === 0 ? (
            <div className='text-center py-8 border rounded-md bg-gray-50'>
              <p className='text-muted-foreground'>
                No accounts are linked to you.
              </p>
            </div>
          ) : (
            incomingLinks.map(link => renderLinkedAccountCard(link, false))
          )}
        </TabsContent>
      </Tabs>

      {/* Link Account Dialog */}
      <Dialog open={showLinkForm} onOpenChange={setShowLinkForm}>
        <DialogContent className='sm:max-w-md'>
          <DialogHeader>
            <DialogTitle>Link a New Account</DialogTitle>
            <DialogDescription>
              Enter the email address of the person you want to link to your
              account.
            </DialogDescription>
          </DialogHeader>

          <LinkAccountForm
            onSuccess={() => {
              setShowLinkForm(false);
              refreshLinks();
            }}
            onCancel={() => setShowLinkForm(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Permissions Dialog */}
      <Dialog open={showPermissionsForm} onOpenChange={setShowPermissionsForm}>
        <DialogContent className='sm:max-w-md'>
          <DialogHeader>
            <DialogTitle>Edit Permissions</DialogTitle>
            <DialogDescription>
              Update the permissions for this linked account.
            </DialogDescription>
          </DialogHeader>

          {selectedLink && (
            <LinkPermissionsForm
              link={selectedLink}
              onSuccess={() => {
                setShowPermissionsForm(false);
                refreshLinks();
              }}
              onCancel={() => setShowPermissionsForm(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Confirm Unlink Dialog */}
      <Dialog open={showConfirmUnlink} onOpenChange={setShowConfirmUnlink}>
        <DialogContent className='sm:max-w-md'>
          <DialogHeader>
            <DialogTitle className='flex items-center text-red-600'>
              <AlertTriangle className='h-5 w-5 mr-2' />
              Confirm Unlink
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to unlink this account? This action cannot
              be undone.
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button
              variant='outline'
              onClick={() => setShowConfirmUnlink(false)}
            >
              Cancel
            </Button>
            <Button variant='destructive' onClick={handleUnlink}>
              Unlink Account
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Confirm Status Change Dialog */}
      <Dialog
        open={showConfirmStatusChange}
        onOpenChange={setShowConfirmStatusChange}
      >
        <DialogContent className='sm:max-w-md'>
          <DialogHeader>
            <DialogTitle>
              {newStatus === 'active' ? 'Accept Link Request' : 'Revoke Link'}
            </DialogTitle>
            <DialogDescription>
              {newStatus === 'active'
                ? 'Are you sure you want to accept this link request? This will grant the user access to your account based on the specified permissions.'
                : 'Are you sure you want to revoke this link? The user will no longer have access to your account.'}
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button
              variant='outline'
              onClick={() => setShowConfirmStatusChange(false)}
            >
              Cancel
            </Button>
            <Button
              variant={newStatus === 'active' ? 'default' : 'destructive'}
              onClick={handleStatusChange}
            >
              {newStatus === 'active' ? 'Accept' : 'Revoke'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
