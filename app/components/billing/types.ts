// Subscription Tier Types (updated for Stripe integration)
export type SubscriptionTier = 'Basic' | 'Pro';
export type StripePlan = 'BASIC' | 'PRO';

// Billing Cycle Types
export type BillingCycle = 'Monthly' | 'Annual';

// Subscription Status Types (updated for Stripe statuses)
export type SubscriptionStatus =
  | 'Active'
  | 'Inactive'
  | 'Past Due'
  | 'Canceled'
  | 'Incomplete'
  | 'Trialing';

// Stripe-specific status mapping
export type StripeSubscriptionStatus =
  | 'ACTIVE'
  | 'INACTIVE'
  | 'PAST_DUE'
  | 'CANCELED'
  | 'INCOMPLETE'
  | 'TRIALING';

// Stripe UserSubscription Interface (matches database model)
export interface StripeUserSubscription {
  id: string;
  userId: string;
  cognitoId: string;
  stripeCustomerId: string;
  stripeSubscriptionId: string;
  plan: StripePlan;
  status: StripeSubscriptionStatus;
  amount: number; // Amount in cents
  currency: string;
  currentPeriodStart: string;
  currentPeriodEnd: string;
  cancelAtPeriodEnd: boolean;
  canceledAt?: string;
  trialStart?: string;
  trialEnd?: string;
  createdAt: string;
  updatedAt?: string;
}

// Pricing Information Interface (updated for new plans)
export interface PricingInfo {
  tier: SubscriptionTier;
  stripePlan: StripePlan;
  monthlyPrice: number;
  annualPrice: number;
  features: string[];
  isPopular?: boolean;
  description?: string;
}

// Helper functions for status mapping
export function mapStripeStatusToDisplay(
  stripeStatus: StripeSubscriptionStatus
): SubscriptionStatus {
  switch (stripeStatus) {
    case 'ACTIVE':
      return 'Active';
    case 'INACTIVE':
      return 'Inactive';
    case 'PAST_DUE':
      return 'Past Due';
    case 'CANCELED':
      return 'Canceled';
    case 'INCOMPLETE':
      return 'Incomplete';
    case 'TRIALING':
      return 'Trialing';
    default:
      return 'Inactive';
  }
}

export function mapStripePlanToTier(stripePlan: StripePlan): SubscriptionTier {
  switch (stripePlan) {
    case 'BASIC':
      return 'Basic';
    case 'PRO':
      return 'Pro';
    default:
      return 'Basic';
  }
}
