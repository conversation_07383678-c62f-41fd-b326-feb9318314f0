'use client';

import React from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface DocumentTemplateSelectorProps {
  value: string;
  onChange: (value: string) => void;
  className?: string;
}

export function DocumentTemplateSelector({
  value,
  onChange,
  className = '',
}: DocumentTemplateSelectorProps) {
  return (
    <div className={`space-y-2 ${className}`}>
      <label className='text-sm font-medium'>Document Template</label>
      <Select value={value} onValueChange={onChange}>
        <SelectTrigger className='w-full'>
          <SelectValue placeholder='Select document template' />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value='emergency-contacts'>Emergency Contacts</SelectItem>
          <SelectItem value='pet-care'>Pet Care Instructions</SelectItem>
          <SelectItem value='digital-assets'>
            Digital Asset Management
          </SelectItem>
          <SelectItem value='end-of-life'>End-of-Life Wishes</SelectItem>
          <SelectItem value='medical-info'>Medical Information</SelectItem>
          <SelectItem value='custom'>Custom Document</SelectItem>
        </SelectContent>
      </Select>
      <p className='text-xs text-[var(--custom-gray-medium)]'>
        Select a template or create a custom document
      </p>
    </div>
  );
}
