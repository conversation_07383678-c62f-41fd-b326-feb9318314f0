'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { useEffect } from 'react';

const formSchema = z.object({
  title: z
    .string()
    .min(1, 'Title is required')
    .max(100, 'Title must be less than 100 characters'),
  documentType: z.enum([
    'EmergencyContacts',
    'PetCare',
    'DigitalAssets',
    'EndOfLifeWishes',
    'MedicalDirectives',
    'Other',
  ]),
  content: z.string().min(1, 'Content is required'),
  reminderFrequency: z.enum([
    'Monthly',
    'Quarterly',
    'SemiAnnually',
    'Annually',
  ]),
  status: z.enum(['Draft', 'Active']),
  isTemplate: z.boolean(),
});

type FormValues = z.infer<typeof formSchema>;

interface CareDocument {
  id: string;
  title: string;
  documentType:
    | 'EmergencyContacts'
    | 'PetCare'
    | 'DigitalAssets'
    | 'EndOfLifeWishes'
    | 'MedicalDirectives'
    | 'Other';
  content: string;
  reminderFrequency: 'Monthly' | 'Quarterly' | 'SemiAnnually' | 'Annually';
  status: 'Draft' | 'Active' | 'Archived';
  isTemplate: boolean;
}

interface CareDocumentModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (documentData: Omit<CareDocument, 'id'>) => Promise<void>;
  document?: CareDocument;
  initialValues?: Partial<FormValues>;
}

const documentTypeLabels = {
  EmergencyContacts: 'Emergency Contacts',
  PetCare: 'Pet Care Instructions',
  DigitalAssets: 'Digital Assets',
  EndOfLifeWishes: 'End of Life Wishes',
  MedicalDirectives: 'Medical Directives',
  Other: 'Other',
};

const frequencyLabels = {
  Monthly: 'Monthly',
  Quarterly: 'Every 3 months',
  SemiAnnually: 'Every 6 months',
  Annually: 'Annually',
};

export function CareDocumentModal({
  open,
  onOpenChange,
  onSave,
  document,
  initialValues,
}: CareDocumentModalProps) {
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: '',
      documentType: 'Other',
      content: '',
      reminderFrequency: 'SemiAnnually',
      status: 'Active',
      isTemplate: false,
    },
  });

  // Reset form when document changes or modal opens/closes
  useEffect(() => {
    if (document) {
      form.reset({
        title: document.title,
        documentType: document.documentType,
        content: document.content,
        reminderFrequency: document.reminderFrequency,
        status: document.status === 'Archived' ? 'Active' : document.status,
        isTemplate: document.isTemplate,
      });
    } else {
      const defaultValues = {
        title: '',
        documentType: 'Other' as const,
        content: '',
        reminderFrequency: 'SemiAnnually' as const,
        status: 'Active' as const,
        isTemplate: false,
      };

      form.reset({
        ...defaultValues,
        ...initialValues,
      });
    }
  }, [document, open, form, initialValues]);

  const onSubmit = async (values: FormValues) => {
    try {
      await onSave({
        title: values.title,
        documentType: values.documentType,
        content: values.content,
        reminderFrequency: values.reminderFrequency,
        status: values.status,
        isTemplate: values.isTemplate,
      });
      onOpenChange(false);
    } catch (error) {
      console.error('Error saving document:', error);
      // Error handling could be improved with toast notifications
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='sm:max-w-[600px] max-h-[90vh] overflow-y-auto'>
        <DialogHeader>
          <DialogTitle>
            {document ? 'Edit Care Document' : 'Create Care Document'}
          </DialogTitle>
          <DialogDescription>
            {document
              ? 'Update your care document information.'
              : 'Create a new care document to store important information that needs regular updates.'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
            <FormField
              control={form.control}
              name='title'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Document Title</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='e.g., My Emergency Contacts, Pet Care Plan'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='documentType'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Document Type</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder='Select document type' />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {Object.entries(documentTypeLabels).map(
                        ([value, label]) => (
                          <SelectItem key={value} value={value}>
                            {label}
                          </SelectItem>
                        )
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='content'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Content</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder='Enter the document content...'
                      className='min-h-[120px]'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='reminderFrequency'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Review Reminder Frequency</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder='Select reminder frequency' />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {Object.entries(frequencyLabels).map(([value, label]) => (
                        <SelectItem key={value} value={value}>
                          {label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='status'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Status</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder='Select status' />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value='Draft'>Draft</SelectItem>
                      <SelectItem value='Active'>Active</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='isTemplate'
              render={({ field }) => (
                <FormItem className='flex flex-row items-start space-x-3 space-y-0'>
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className='space-y-1 leading-none'>
                    <FormLabel>Save as template</FormLabel>
                    <p className='text-sm text-muted-foreground'>
                      This document can be used as a template for creating
                      similar documents.
                    </p>
                  </div>
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type='button'
                variant='outline'
                onClick={() => onOpenChange(false)}
              >
                Cancel
              </Button>
              <Button type='submit'>
                {document ? 'Update Document' : 'Create Document'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
