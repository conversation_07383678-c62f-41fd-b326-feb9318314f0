'use client';

import React, { useMemo } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table/data-table';
import { DataTableColumnHeader } from '@/components/ui/data-table/data-table-column-header';
import type { DataTableConfig } from '@/components/ui/data-table/data-table';
import type { DMSFailedUser } from '@/app/(protected)/admin/emergency/page';
import { Eye, RotateCcw } from 'lucide-react';

interface AdminDMSUserListProps {
  users: DMSFailedUser[];
  loading: boolean;
  onResetDMS: (user: DMSFailedUser) => void;
  resettingUserId: string | null;
  className?: string;
}

// Define filter configurations
const tableConfig: DataTableConfig = {
  searchColumn: 'email',
  searchPlaceholder: 'Search users...',
  filters: [
    {
      id: 'status',
      title: 'Status',
      options: [
        { value: 'active', label: 'Active' },
        { value: 'pending', label: 'Pending' },
        { value: 'inactive', label: 'Inactive' },
      ],
    },
  ],
  enableColumnVisibility: true,
  enablePagination: true,
  enableRowSelection: false,
  defaultPageSize: 10,
};

const formatDate = (dateString: string | null | undefined) => {
  if (!dateString) return 'Never';
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

export function AdminDMSUserList({
  users,
  loading,
  onResetDMS,
  resettingUserId,
  className = '',
}: AdminDMSUserListProps) {
  const columns: ColumnDef<DMSFailedUser>[] = useMemo(
    () => [
      {
        accessorKey: 'firstName',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title='Name' />
        ),
        cell: ({ row }) => (
          <div className='flex flex-col'>
            <span className='font-semibold'>
              {row.original.firstName} {row.original.lastName}
            </span>
            <span className='text-sm text-[var(--custom-gray-medium)]'>
              {row.original.email}
            </span>
          </div>
        ),
        enableSorting: true,
      },
      {
        accessorKey: 'createdAt',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title='Member Since' />
        ),
        cell: ({ row }) => (
          <span className='text-sm'>{formatDate(row.original.createdAt)}</span>
        ),
        enableSorting: true,
      },
      {
        accessorKey: 'lastCheckIn',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title='Last Check-in' />
        ),
        cell: ({ row }) => (
          <span className='text-sm'>
            {formatDate(row.original.lastCheckIn)}
          </span>
        ),
        enableSorting: true,
      },
      {
        accessorKey: 'nextCheckIn',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title='Next Check-in' />
        ),
        cell: ({ row }) => (
          <span className='text-sm'>
            {row.original.nextCheckIn
              ? formatDate(row.original.nextCheckIn)
              : 'Not set'}
          </span>
        ),
        enableSorting: true,
      },
      {
        accessorKey: 'status',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title='Status' />
        ),
        cell: ({ row }) => (
          <div className='flex items-center gap-2'>
            <Badge className='bg-red-100 text-red-800 text-xs'>
              DMS Failed
            </Badge>
            {/*<span className="text-sm capitalize">{row.original.status}</span>*/}
          </div>
        ),
        filterFn: (row, id, value) => {
          return value.includes(row.getValue(id));
        },
        enableSorting: true,
      },
      {
        id: 'actions',
        header: 'Actions',
        cell: ({ row }) => (
          <div className='flex gap-2'>
            {/*<Button variant="outline" size="sm">*/}
            {/*  <Eye className="h-4 w-4 mr-1" />*/}
            {/*  View Details*/}
            {/*</Button>*/}
            <Button
              variant='outline'
              size='sm'
              onClick={() => onResetDMS(row.original)}
              disabled={
                resettingUserId === row.original.id || !row.original.cognitoId
              }
              className='text-green-600 hover:text-green-700 hover:bg-green-50'
            >
              {resettingUserId === row.original.id ? (
                <>
                  <div className='h-4 w-4 mr-1 animate-spin rounded-full border-2 border-green-600 border-t-transparent' />
                  Resetting...
                </>
              ) : (
                <>
                  <RotateCcw className='h-4 w-4 mr-1' />
                  Reset DMS
                </>
              )}
            </Button>
          </div>
        ),
        enableSorting: false,
      },
    ],
    [onResetDMS, resettingUserId]
  );

  return (
    <DataTable
      columns={columns}
      data={users}
      config={tableConfig}
      loading={loading}
      className={className}
    />
  );
}
