'use client';

import React, { FC } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  MoreHorizontal,
  Edit,
  Settings,
  Trash2,
  FileText,
  Clock,
} from 'lucide-react';
import * as Icons from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import type { Schema } from '@/amplify/data/resource';
import { StatusBadge } from '@workspace/ui/care-document-status-badge';
import type { CareDocumentQuestion } from '@/types/care-document-builder';

// TODO need to fix ts errors here
export const GetIconComponent: FC<{ iconName: string }> = ({ iconName }) => {
  const LucideIcon = Icons[iconName as keyof typeof Icons] || Icons.FileText;
  // @ts-ignore
  return <LucideIcon className='w-5 h-5 text-gray-600' />;
};

export type CareDocumentTemplate = Schema['CareDocumentTemplate']['type'];

interface CareDocumentTemplateTableProps {
  templates: CareDocumentTemplate[];
  loading: boolean;
  actionLoading: string | null;
  onEdit: (template: CareDocumentTemplate) => void;
  onBuild: (template: CareDocumentTemplate) => void;
  onDelete: (template: CareDocumentTemplate) => void;
}

export function CareDocumentTemplateTable({
  templates,
  loading,
  actionLoading,
  onEdit,
  onBuild,
  onDelete,
}: CareDocumentTemplateTableProps) {
  const formatDate = (dateString?: string | null) => {
    if (dateString) {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      });
    } else {
      return 'Not set';
    }
  };

  const getDocumentTypeLabel = (type: string) => {
    const typeLabels: Record<string, string> = {
      EmergencyContacts: 'Emergency Contacts',
      PetCare: 'Pet Care',
      DigitalAssets: 'Digital Assets',
      EndOfLifeWishes: 'End of Life Wishes',
      MedicalDirectives: 'Medical Directives',
      Other: 'Other',
    };
    return typeLabels[type] || type;
  };

  const getQuestionsCount = (template: CareDocumentTemplate): number => {
    // Handle the questions field which should be an array of question objects
    if (!template.questions) {
      return 0;
    }

    // If questions is already an array, filter out null values and return length
    if (Array.isArray(template.questions)) {
      return template.questions.filter(q => q !== null).length;
    }

    // Fallback: if questions is stored as a JSON string (legacy support)
    try {
      const parsedQuestions = JSON.parse(template.questions as any);
      return Array.isArray(parsedQuestions) ? parsedQuestions.length : 0;
    } catch {
      return 0;
    }
  };

  if (loading) {
    return (
      <div className='space-y-3'>
        {[...Array(5)].map((_, i) => (
          <div key={i} className='h-16 bg-gray-100 rounded animate-pulse' />
        ))}
      </div>
    );
  }

  if (templates.length === 0) {
    return (
      <div className='text-center py-12'>
        <FileText className='mx-auto h-12 w-12 text-gray-400 mb-4' />
        <h3 className='text-lg font-medium text-gray-900 mb-2'>
          No templates found
        </h3>
        <p className='text-gray-500'>
          Create your first care document template to get started.
        </p>
      </div>
    );
  }

  return (
    <div className='space-y-4'>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Template Name</TableHead>
            <TableHead>Document Type</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Questions</TableHead>
            <TableHead>Version</TableHead>
            <TableHead>Last Updated</TableHead>
            <TableHead className='text-right'>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {templates.map(template => (
            <TableRow key={template.id} className='hover:bg-muted/50'>
              <TableCell>
                <div className='flex items-center space-x-3'>
                  <GetIconComponent iconName={template.icon ?? 'file-text'} />
                  <div className='flex-1'>
                    <div className='flex items-center space-x-2'>
                      <div className='font-medium text-[var(--custom-gray-dark)]'>
                        {template.title}
                      </div>
                    </div>
                    <div className='text-sm text-[var(--custom-gray-medium)] mt-1 max-w-xs truncate'>
                      {template.description}
                    </div>
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <Badge variant='outline'>
                  {getDocumentTypeLabel(template.documentType)}
                </Badge>
              </TableCell>
              <TableCell>
                <StatusBadge status={template.status || 'Draft'} />
              </TableCell>
              <TableCell>
                <div className='flex items-center text-sm text-[var(--custom-gray-medium)]'>
                  <FileText className='h-4 w-4 mr-1' />
                  <span>
                    {getQuestionsCount(template)} question
                    {getQuestionsCount(template) !== 1 ? 's' : ''}
                  </span>
                </div>
              </TableCell>
              <TableCell>
                <span className='text-sm font-mono text-[var(--custom-gray-medium)]'>
                  v{template.version}
                </span>
              </TableCell>
              <TableCell>
                <div className='flex items-center text-sm text-[var(--custom-gray-medium)]'>
                  <Clock className='h-4 w-4 mr-1' />
                  {formatDate(template.updatedAt)}
                </div>
              </TableCell>
              <TableCell className='text-right'>
                <div className='flex items-center justify-end space-x-2'>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() => onBuild(template)}
                    disabled={actionLoading === template.id}
                  >
                    <Settings className='h-4 w-4 mr-1' />
                    Build
                  </Button>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant='ghost'
                        size='sm'
                        disabled={actionLoading === template.id}
                      >
                        <MoreHorizontal className='h-4 w-4' />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align='end'>
                      <DropdownMenuItem onClick={() => onEdit(template)}>
                        <Edit className='h-4 w-4 mr-2' />
                        Edit Template
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => onDelete(template)}
                        className='text-red-600 hover:text-red-700'
                      >
                        <Trash2 className='h-4 w-4 mr-2' />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
