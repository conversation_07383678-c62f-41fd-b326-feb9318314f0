'use client';

import { Table } from '@tanstack/react-table';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { DataTableViewOptions } from './data-table-view-options';
import { DataTableFacetedFilter } from './data-table-faceted-filter';

// Define the filter options for user management
export const statuses = [
  {
    value: 'active',
    label: 'Active',
  },
  {
    value: 'inactive',
    label: 'Inactive',
  },
  {
    value: 'pending',
    label: 'Pending',
  },
];

export const roles = [
  {
    value: 'Member',
    label: 'Member',
  },
  {
    value: 'Administrator',
    label: 'Administrator',
  },
  {
    value: 'Welon Trust',
    label: 'Welon Trust',
  },
  {
    value: 'Professional',
    label: 'Professional',
  },
];

export const subroles = [
  {
    value: 'Basic Member',
    label: 'Basic Member',
  },
  {
    value: 'Advanced',
    label: 'Advanced',
  },
  {
    value: 'Emergency Service',
    label: 'Emergency Service',
  },
  {
    value: 'Medical',
    label: 'Medical',
  },
  {
    value: 'Finance',
    label: 'Finance',
  },
  {
    value: 'Reporting',
    label: 'Reporting',
  },
];

interface DataTableToolbarProps<TData> {
  table: Table<TData>;
}

export function DataTableToolbar<TData>({
  table,
}: DataTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0;

  return (
    <div className='flex items-center justify-between'>
      <div className='flex flex-1 items-center space-x-2'>
        <Input
          placeholder='Filter users...'
          value={(table.getColumn('name')?.getFilterValue() as string) ?? ''}
          onChange={event =>
            table.getColumn('name')?.setFilterValue(event.target.value)
          }
          className='h-8 w-[150px] lg:w-[250px]'
        />
        {table.getColumn('status') && (
          <DataTableFacetedFilter
            column={table.getColumn('status')}
            title='Status'
            options={statuses}
          />
        )}
        {table.getColumn('role') && (
          <DataTableFacetedFilter
            column={table.getColumn('role')}
            title='Role'
            options={roles}
          />
        )}
        {table.getColumn('subrole') && (
          <DataTableFacetedFilter
            column={table.getColumn('subrole')}
            title='Subrole'
            options={subroles}
          />
        )}
        {isFiltered && (
          <Button
            variant='ghost'
            onClick={() => table.resetColumnFilters()}
            className='h-8 px-2 lg:px-3'
          >
            Reset
            <X className='ml-2 h-4 w-4' />
          </Button>
        )}
      </div>
      <DataTableViewOptions table={table} />
    </div>
  );
}
