'use client';

import React, { useMemo, useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { Badge } from '@/components/ui/badge';
import { DataTable } from '@/components/ui/data-table/data-table';
import { DataTableColumnHeader } from '@/components/ui/data-table/data-table-column-header';
import type { DataTableConfig } from '@/components/ui/data-table/data-table';
import {
  useAdminEmergencyContacts,
  AdminEmergencyContact,
} from '@/hooks/useAdminEmergencyContacts';
import {
  CheckCircle,
  XCircle,
  Phone,
  Mail,
  FileText,
  Eye,
  Bell,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface EmergencyContactsTableProps {
  className?: string;
}

// Define filter configurations
const tableConfig: DataTableConfig = {
  searchColumn: 'fullName',
  searchPlaceholder: 'Search contacts...',
  filters: [
    {
      id: 'isVerified',
      title: 'Verification',
      options: [
        { value: 'true', label: 'Verified' },
        { value: 'false', label: 'Not Verified' },
      ],
    },
  ],
  enableColumnVisibility: true,
  enablePagination: true,
  enableRowSelection: false,
  defaultPageSize: 10,
};

export function EmergencyContactsTable({
  className = '',
}: EmergencyContactsTableProps) {
  // Use the custom hook to fetch and manage emergency contact data
  const { contacts, loading, error, sendNotificationEmail } =
    useAdminEmergencyContacts();

  // State for details dialog
  const [selectedContact, setSelectedContact] =
    useState<AdminEmergencyContact | null>(null);

  // Define columns for the data table
  const columns = useMemo<ColumnDef<AdminEmergencyContact>[]>(
    () => [
      {
        accessorKey: 'userName',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title='Member' />
        ),
        cell: ({ row }) => {
          return (
            <div className='flex flex-col min-w-0 max-w-[160px]'>
              <span
                className='font-medium truncate'
                title={row.original.userName}
              >
                {row.original.userName}
              </span>
              <span
                className='text-xs text-muted-foreground truncate'
                title={row.original.userEmail}
              >
                {row.original.userEmail}
              </span>
            </div>
          );
        },
        size: 180,
      },
      {
        accessorKey: 'fullName',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title='Contact Name' />
        ),
        cell: ({ row }) => {
          return (
            <div className='flex flex-col min-w-0 max-w-[160px]'>
              <span
                className='font-medium truncate'
                title={row.getValue('fullName') as string}
              >
                {row.getValue('fullName')}
              </span>
              <span
                className='text-xs text-muted-foreground truncate'
                title={row.original.relationship}
              >
                {row.original.relationship}
              </span>
            </div>
          );
        },
        size: 160,
      },
      {
        accessorKey: 'phoneNumber',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title='Phone' />
        ),
        cell: ({ row }) => {
          return (
            <div className='flex items-center min-w-0 max-w-[140px]'>
              <Phone className='mr-2 h-4 w-4 text-muted-foreground flex-shrink-0' />
              <span
                className='truncate'
                title={row.getValue('phoneNumber') as string}
              >
                {row.getValue('phoneNumber')}
              </span>
            </div>
          );
        },
        size: 140,
      },
      {
        accessorKey: 'isVerified',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title='Status' />
        ),
        cell: ({ row }) => {
          const isVerified = row.original.isVerified;
          const isPrimary = row.original.isPrimaryForType;
          return (
            <div className='flex flex-col gap-1 min-w-0 max-w-[100px]'>
              <div className='flex items-center gap-1'>
                {isVerified ? (
                  <CheckCircle className='h-3 w-3 text-green-500 flex-shrink-0' />
                ) : (
                  <XCircle className='h-3 w-3 text-gray-300 flex-shrink-0' />
                )}
                <span className='text-xs truncate'>
                  {isVerified ? 'Verified' : 'Unverified'}
                </span>
              </div>
              <Badge
                variant={isPrimary ? 'default' : 'outline'}
                className='text-xs w-fit'
              >
                {isPrimary ? 'Primary' : 'Secondary'}
              </Badge>
            </div>
          );
        },
        filterFn: (row, _id, value) => {
          const isVerified = row.original.isVerified ? 'true' : 'false';
          return value.includes(isVerified);
        },
        size: 100,
      },
      {
        id: 'details',
        header: 'Details',
        cell: ({ row }) => {
          return (
            <Button
              variant='outline'
              size='sm'
              onClick={() => setSelectedContact(row.original)}
              className='flex items-center gap-1'
            >
              <Eye className='h-3 w-3' />
              View
            </Button>
          );
        },
        enableSorting: false,
        size: 80,
      },
      {
        accessorKey: 'sendEmergencyNotification',
        header: 'Alert',
        cell: ({ row }) => {
          const isVerified = row.original.isVerified || false;
          const tokenExpiry = row.original.tokenExpiry
            ? new Date(row.original.tokenExpiry)
            : null;
          const currentTime = new Date();

          const isTokenActive =
            (tokenExpiry && tokenExpiry > currentTime) || false;
          const isDisabled = !isVerified || isTokenActive;

          return (
            <Button
              variant={isDisabled ? 'secondary' : 'destructive'}
              size='sm'
              onClick={() =>
                sendNotificationEmail(
                  row.original.id,
                  row.original.emailAddress,
                  row.original.userName
                )
              }
              disabled={isDisabled}
              className='flex items-center gap-1'
              title={
                !isVerified
                  ? 'Contact not verified'
                  : isTokenActive
                    ? 'Emergency access already granted'
                    : 'Send emergency alert'
              }
            >
              <Bell className='h-3 w-3' />
              Alert
            </Button>
          );
        },
        enableSorting: false,
        size: 80,
      },
    ],
    [sendNotificationEmail, setSelectedContact]
  );

  return (
    <>
      <DataTable
        columns={columns}
        data={contacts}
        config={tableConfig}
        loading={loading}
        error={error}
        className={className}
      />

      {/* Details Dialog */}
      <Dialog
        open={!!selectedContact}
        onOpenChange={() => setSelectedContact(null)}
      >
        <DialogContent className='max-w-2xl'>
          <DialogHeader>
            <DialogTitle>Emergency Contact Details</DialogTitle>
          </DialogHeader>
          {selectedContact && (
            <div className='space-y-6'>
              {/* Member Information */}
              <div className='space-y-2'>
                <h4 className='font-medium text-sm text-muted-foreground'>
                  MEMBER
                </h4>
                <div className='flex flex-col gap-1'>
                  <span className='font-medium'>
                    {selectedContact.userName}
                  </span>
                  <span className='text-sm text-muted-foreground'>
                    {selectedContact.userEmail}
                  </span>
                </div>
              </div>

              {/* Contact Information */}
              <div className='space-y-2'>
                <h4 className='font-medium text-sm text-muted-foreground'>
                  CONTACT INFORMATION
                </h4>
                <div className='grid grid-cols-2 gap-4'>
                  <div>
                    <label className='text-xs text-muted-foreground'>
                      Name
                    </label>
                    <p className='font-medium'>{selectedContact.fullName}</p>
                  </div>
                  <div>
                    <label className='text-xs text-muted-foreground'>
                      Relationship
                    </label>
                    <p>{selectedContact.relationship}</p>
                  </div>
                  <div>
                    <label className='text-xs text-muted-foreground'>
                      Phone
                    </label>
                    <div className='flex items-center gap-2'>
                      <Phone className='h-4 w-4 text-muted-foreground' />
                      <p>{selectedContact.phoneNumber}</p>
                    </div>
                  </div>
                  <div>
                    <label className='text-xs text-muted-foreground'>
                      Email
                    </label>
                    <div className='flex items-center gap-2'>
                      <Mail className='h-4 w-4 text-muted-foreground' />
                      <p>{selectedContact.emailAddress}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Access & Permissions */}
              <div className='space-y-2'>
                <h4 className='font-medium text-sm text-muted-foreground'>
                  ACCESS & PERMISSIONS
                </h4>
                <div className='grid grid-cols-2 gap-4'>
                  <div>
                    <label className='text-xs text-muted-foreground'>
                      Contact Type
                    </label>
                    <Badge
                      variant={
                        selectedContact.contactType === 'Medical'
                          ? 'default'
                          : 'outline'
                      }
                      className={
                        selectedContact.contactType === 'Medical'
                          ? 'bg-blue-500'
                          : ''
                      }
                    >
                      {selectedContact.contactType === 'Medical'
                        ? 'Medical Contact'
                        : 'Other Contact'}
                    </Badge>
                  </div>
                  <div>
                    <label className='text-xs text-muted-foreground'>
                      Priority
                    </label>
                    <Badge
                      variant={
                        selectedContact.isPrimaryForType ? 'default' : 'outline'
                      }
                      className={
                        selectedContact.isPrimaryForType ? 'bg-green-500' : ''
                      }
                    >
                      {selectedContact.isPrimaryForType
                        ? 'Primary Contact'
                        : 'Secondary Contact'}
                    </Badge>
                  </div>
                  <div>
                    <label className='text-xs text-muted-foreground'>
                      Document Type
                    </label>
                    <div className='flex items-center gap-2'>
                      <FileText className='h-4 w-4 text-muted-foreground' />
                      <span>
                        {selectedContact.documentType === 'DeadDocument'
                          ? 'Dead Document'
                          : selectedContact.documentType === 'CareDocument'
                            ? 'Care Document'
                            : 'All Documents'}
                      </span>
                    </div>
                  </div>
                  <div>
                    <label className='text-xs text-muted-foreground'>
                      Verification Status
                    </label>
                    <div className='flex items-center gap-2'>
                      {selectedContact.isVerified ? (
                        <CheckCircle className='h-4 w-4 text-green-500' />
                      ) : (
                        <XCircle className='h-4 w-4 text-gray-300' />
                      )}
                      <span>
                        {selectedContact.isVerified
                          ? 'Verified'
                          : 'Not Verified'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}
