'use client';

import React from 'react';
import { useDrag, useDrop } from 'react-dnd';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { TableCell, TableRow } from '@/components/ui/table';
import {
  Edit,
  Trash2,
  MoreHorizontal,
  ArrowRight,
  GripVertical,
  Loader2,
} from 'lucide-react';
import { Question } from '@/types/interview-builder-new';

interface DraggableQuestionRowProps {
  question: Question;
  index: number;
  isConditional?: boolean;
  onEdit: (question: Question) => void;
  onDelete: (question: Question) => void;
  onMove: (dragIndex: number, hoverIndex: number) => void;
  onDragEnd?: () => void;
  getQuestionTypeLabel: (type: string) => string;
  getQuestionTypeBadgeVariant: (
    type: string
  ) => 'default' | 'secondary' | 'outline';
  hasConditionalLogic: (question: Question) => boolean;
  getNextQuestionDisplay: (question: Question) => string;
  isLoading?: boolean;
  disabled?: boolean;
}

const ItemType = 'QUESTION';

export function DraggableQuestionRow({
  question,
  index,
  isConditional = false,
  onEdit,
  onDelete,
  onMove,
  onDragEnd,
  getQuestionTypeLabel,
  getQuestionTypeBadgeVariant,
  hasConditionalLogic,
  getNextQuestionDisplay,
  isLoading = false,
  disabled = false,
}: DraggableQuestionRowProps) {
  const [{ isDragging }, drag, preview] = useDrag({
    type: ItemType,
    item: { index, questionId: question.questionId },
    canDrag: !disabled && !isLoading,
    end: () => {
      if (onDragEnd && !disabled && !isLoading) {
        onDragEnd();
      }
    },
    collect: monitor => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const [{ isOver }, drop] = useDrop({
    accept: ItemType,
    hover: (draggedItem: { index: number; questionId: string }) => {
      if (draggedItem.index !== index && !disabled && !isLoading) {
        onMove(draggedItem.index, index);
        draggedItem.index = index;
      }
    },
    collect: monitor => ({
      isOver: monitor.isOver() && !disabled && !isLoading,
    }),
  });

  const opacity = isDragging ? 0.5 : disabled || isLoading ? 0.7 : 1;
  const backgroundColor = isOver ? 'rgba(59, 130, 246, 0.1)' : 'transparent';

  // Combine refs properly
  const ref = (node: HTMLTableRowElement | null) => {
    if (!disabled && !isLoading) {
      drag(node);
      drop(node);
    }
  };

  return (
    <TableRow
      ref={ref}
      style={{ opacity, backgroundColor }}
      className={`
        ${isConditional ? 'border-l-4 border-l-blue-200' : ''}
        ${isDragging ? 'cursor-grabbing' : disabled || isLoading ? 'cursor-not-allowed' : 'cursor-grab'}
        transition-colors duration-200
        ${disabled || isLoading ? 'opacity-70' : ''}
      `}
    >
      <TableCell>
        {isLoading ? (
          <Loader2 className='h-4 w-4 text-blue-500 animate-spin' />
        ) : (
          <GripVertical
            className={`h-4 w-4 ${
              disabled || isLoading
                ? 'text-gray-300 cursor-not-allowed'
                : 'text-gray-400 hover:text-gray-600 cursor-move'
            }`}
          />
        )}
      </TableCell>

      <TableCell>
        <div className='flex items-center space-x-2'>
          {isConditional && (
            <div className='w-4 h-4 flex items-center justify-center'>
              <ArrowRight className='h-3 w-3 text-blue-500' />
            </div>
          )}
          <Badge variant='outline' className={isConditional ? 'text-xs' : ''}>
            {question.order}
          </Badge>
        </div>
      </TableCell>

      <TableCell>
        <div className={`max-w-md ${isConditional ? 'pl-6' : ''}`}>
          <div
            className={`font-medium text-[var(--custom-gray-dark)] truncate ${isConditional ? 'text-sm' : ''}`}
          >
            {question.text}
          </div>
          {isConditional && (
            <div className='text-xs text-blue-600 mt-1'>
              Conditional Question
            </div>
          )}
        </div>
      </TableCell>

      <TableCell>
        <Badge
          variant={getQuestionTypeBadgeVariant(question.type)}
          className={isConditional ? 'text-xs' : ''}
        >
          {getQuestionTypeLabel(question.type)}
        </Badge>
      </TableCell>

      <TableCell>
        <div className='text-sm text-[var(--custom-gray-medium)]'>
          {question.questionMapping || '-'}
        </div>
      </TableCell>

      <TableCell>
        <div className='text-sm text-[var(--custom-gray-medium)]'>
          {question.options?.length || 0} options
        </div>
      </TableCell>

      <TableCell>
        {hasConditionalLogic(question) ? (
          <Badge variant='secondary' className={isConditional ? 'text-xs' : ''}>
            <ArrowRight className='mr-1 h-3 w-3' />
            Branching
          </Badge>
        ) : (
          <span className='text-sm text-[var(--custom-gray-medium)]'>
            Linear
          </span>
        )}
      </TableCell>

      <TableCell>
        <div className='text-sm text-[var(--custom-gray-medium)]'>
          {getNextQuestionDisplay(question)}
        </div>
      </TableCell>

      <TableCell>
        <Badge
          variant={question.isHeadQuestion !== false ? 'default' : 'secondary'}
          className={isConditional ? 'text-xs' : ''}
        >
          {question.isHeadQuestion !== false ? 'Head' : 'Conditional'}
        </Badge>
      </TableCell>

      <TableCell className='text-right'>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='ghost' size='sm' disabled={disabled || isLoading}>
              <MoreHorizontal className='h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end'>
            <DropdownMenuItem
              onClick={() => onEdit(question)}
              disabled={disabled || isLoading}
            >
              <Edit className='mr-2 h-4 w-4' />
              Edit Question
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => onDelete(question)}
              className='text-red-600'
              disabled={disabled || isLoading}
            >
              <Trash2 className='mr-2 h-4 w-4' />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </TableCell>
    </TableRow>
  );
}
