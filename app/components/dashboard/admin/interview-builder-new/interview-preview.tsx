'use client';

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Play,
  RotateCcw,
  ArrowRight,
  ArrowLeft,
  CheckCircle,
  AlertTriangle,
  Eye,
  Info,
} from 'lucide-react';
import {
  InterviewNew,
  InterviewVersionNew,
  Question,
  QuestionOption,
} from '@/types/interview-builder-new';
import { useEducationalContent } from '@/hooks/use-educational-content';
import { YouTubePlayer } from '@/components/education/youtube-player';
import { ContentType } from '@/types/education';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface InterviewPreviewProps {
  interview: InterviewNew;
  version: InterviewVersionNew | null;
}

interface PreviewState {
  currentQuestionIndex: number;
  responses: Record<string, any>;
  questionHistory: Array<{
    index: number;
    questionId: string;
    isConditional: boolean;
  }>;
  isComplete: boolean;
  isStarted: boolean;
  currentConditionalQuestion: Question | null;
}

interface EducationalVideoSectionProps {
  videoId: string;
}

const EducationalVideoSection: React.FC<EducationalVideoSectionProps> = ({
  videoId,
}) => {
  const [showVideo, setShowVideo] = useState(false);
  const { getContentById } = useEducationalContent();
  const videoContent = getContentById(videoId);

  if (!videoContent || videoContent.type !== 'video') {
    // Show a placeholder for missing video in preview mode
    return (
      <div className='mb-6'>
        <div className='mb-3 flex items-center justify-between'>
          <h3 className='text-lg font-semibold text-gray-900 flex items-center'>
            Video Hint (Preview)
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info className='w-4 h-4 ml-2 text-gray-500 cursor-help' />
                </TooltipTrigger>
                <TooltipContent>
                  <p className='max-w-xs'>
                    Video ID: {videoId} (not found in content library)
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </h3>
          <Button
            variant='outline'
            size='sm'
            disabled
            className='flex items-center gap-2'
          >
            <Play className='w-4 h-4' />
            Video Not Found
          </Button>
        </div>
        <Alert>
          <AlertTriangle className='h-4 w-4' />
          <AlertDescription>
            Educational video with ID "{videoId}" not found in the content
            library. Please check if the video exists and is published.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Convert Amplify content to the format expected by YouTubePlayer
  const videoForPlayer = {
    id: videoContent.id,
    type: ContentType.VIDEO,
    title: videoContent.title,
    description: videoContent.description || '',
    tags: videoContent.tags || [],
    status: videoContent.status as any,
    createdAt: videoContent.createdAt || '',
    updatedAt: videoContent.updatedAt || '',
    version: videoContent.version || 1,
    contentUrl: videoContent.contentUrl || '',
    duration: videoContent.duration || 0,
    thumbnailUrl: videoContent.thumbnailUrl || '',
  };

  // Check if video has a valid URL
  if (!videoForPlayer.contentUrl) {
    return null;
  }

  return (
    <div className='mb-6'>
      <div className='mb-3 flex items-center justify-between'>
        <h3 className='text-lg font-semibold text-gray-900 flex items-center'>
          Video Hint Available
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Info className='w-4 h-4 ml-2 text-gray-500 cursor-help' />
              </TooltipTrigger>
              <TooltipContent>
                <p className='max-w-xs'>
                  This video provides helpful information related to this
                  question
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </h3>
        <Button
          variant='outline'
          size='sm'
          onClick={() => setShowVideo(!showVideo)}
          className='flex items-center gap-2'
        >
          <Play className='w-4 h-4' />
          {showVideo ? 'Hide Hint' : 'Show Hint'}
        </Button>
      </div>
      {showVideo && <YouTubePlayer video={videoForPlayer} />}
    </div>
  );
};

export function InterviewPreview({
  interview,
  version,
}: InterviewPreviewProps) {
  const [previewState, setPreviewState] = useState<PreviewState>({
    currentQuestionIndex: 0,
    responses: {},
    questionHistory: [],
    isComplete: false,
    isStarted: false,
    currentConditionalQuestion: null,
  });

  const questions = version?.questions || [];
  const sortedQuestions = [...questions].sort((a, b) => a.order - b.order);

  // Get questions that should be shown in the main flow (head questions)
  // Head questions are those that are NOT referenced as nextQuestionId in any option
  // This fixes the issue where conditional questions appear in the main flow
  const getHeadQuestions = (): Question[] => {
    const conditionalQuestionIds = new Set<string>();

    // Collect all question IDs that are referenced as nextQuestionId
    // These are conditional questions that should only appear when branched to
    sortedQuestions.forEach(question => {
      question.options?.forEach(option => {
        if (option.nextQuestionId) {
          conditionalQuestionIds.add(option.nextQuestionId);
        }
      });
    });

    // Return questions that are not conditional (head questions)
    // These form the main interview flow
    return sortedQuestions.filter(
      question => !conditionalQuestionIds.has(question.questionId)
    );
  };

  // Get the current question based on the flow logic
  const getCurrentQuestion = (): Question | null => {
    // If we're showing a conditional question, return it
    if (previewState.currentConditionalQuestion) {
      return previewState.currentConditionalQuestion;
    }

    const headQuestions = getHeadQuestions();

    // If we're at the start or following the main flow
    if (previewState.currentQuestionIndex < headQuestions.length) {
      return headQuestions[previewState.currentQuestionIndex];
    }

    return null;
  };

  const currentQuestion = getCurrentQuestion();

  const handleStart = () => {
    setPreviewState({
      currentQuestionIndex: 0,
      responses: {},
      questionHistory: [],
      isComplete: false,
      isStarted: true,
      currentConditionalQuestion: null,
    });
  };

  const handleReset = () => {
    setPreviewState({
      currentQuestionIndex: 0,
      responses: {},
      questionHistory: [],
      isComplete: false,
      isStarted: false,
      currentConditionalQuestion: null,
    });
  };

  const handleResponseChange = (value: any) => {
    if (!currentQuestion) return;

    setPreviewState(prev => ({
      ...prev,
      responses: {
        ...prev.responses,
        [currentQuestion.questionId]: value,
      },
    }));
  };

  const getNextQuestionIndex = (
    question: Question,
    response: any
  ): { index: number; isConditional: boolean } => {
    // Check for branching logic
    if (question.type === 'radio' || question.type === 'select') {
      const selectedOption = question.options?.find(
        opt => opt.value === response
      );
      if (selectedOption?.nextQuestionId) {
        // This is a conditional branch - we need to handle it specially
        return { index: -1, isConditional: true };
      }
    }

    // Default: next question in main flow
    const headQuestions = getHeadQuestions();
    const currentHeadIndex = headQuestions.findIndex(
      q => q.questionId === question.questionId
    );

    if (
      currentHeadIndex !== -1 &&
      currentHeadIndex < headQuestions.length - 1
    ) {
      return { index: currentHeadIndex + 1, isConditional: false };
    }

    // End of interview
    return { index: headQuestions.length, isConditional: false };
  };

  const handleNext = () => {
    if (!currentQuestion) return;

    const response = previewState.responses[currentQuestion.questionId];

    // Save current state to history for back navigation
    const currentState = {
      index: previewState.currentQuestionIndex,
      questionId: currentQuestion.questionId,
      isConditional: !!previewState.currentConditionalQuestion,
    };

    // BRANCHING LOGIC: Check if current question has conditional branches
    if (
      (currentQuestion.type === 'radio' || currentQuestion.type === 'select') &&
      response
    ) {
      const selectedOption = currentQuestion.options?.find(
        opt => opt.value === response
      );

      if (selectedOption?.nextQuestionId) {
        // Find the conditional question to branch to
        const conditionalQuestion = sortedQuestions.find(
          q => q.questionId === selectedOption.nextQuestionId
        );

        if (conditionalQuestion) {
          // BRANCH: Show the conditional question immediately
          // This fixes the issue where conditional questions appeared in wrong order
          setPreviewState(prev => ({
            ...prev,
            currentConditionalQuestion: conditionalQuestion,
            questionHistory: [...prev.questionHistory, currentState],
          }));
          return;
        }
      }
    }

    // RETURN FROM CONDITIONAL: If we're currently showing a conditional question,
    // return to the main flow at the next head question
    if (previewState.currentConditionalQuestion) {
      const headQuestions = getHeadQuestions();
      const nextMainIndex = previewState.currentQuestionIndex + 1;

      setPreviewState(prev => ({
        ...prev,
        currentQuestionIndex: nextMainIndex,
        currentConditionalQuestion: null,
        questionHistory: [...prev.questionHistory, currentState],
        isComplete: nextMainIndex >= headQuestions.length,
      }));
      return;
    }

    // NORMAL FLOW: Go to next head question in the main sequence
    const headQuestions = getHeadQuestions();
    const nextIndex = previewState.currentQuestionIndex + 1;

    setPreviewState(prev => ({
      ...prev,
      currentQuestionIndex: nextIndex,
      questionHistory: [...prev.questionHistory, currentState],
      isComplete: nextIndex >= headQuestions.length,
    }));
  };

  const handlePrevious = () => {
    if (previewState.questionHistory.length === 0) return;

    const previousState =
      previewState.questionHistory[previewState.questionHistory.length - 1];

    setPreviewState(prev => ({
      ...prev,
      currentQuestionIndex: previousState.index,
      currentConditionalQuestion: previousState.isConditional
        ? sortedQuestions.find(
            q => q.questionId === previousState.questionId
          ) || null
        : null,
      questionHistory: prev.questionHistory.slice(0, -1),
      isComplete: false,
    }));
  };

  const canProceed = () => {
    if (!currentQuestion) return false;
    const response = previewState.responses[currentQuestion.questionId];
    // For now, allow proceeding without validation. Required field logic can be added later.
    return true;
  };

  const getProgressInfo = () => {
    const headQuestions = getHeadQuestions();
    const totalMainQuestions = headQuestions.length;
    const currentMainIndex = previewState.currentQuestionIndex;
    const answeredConditional = previewState.currentConditionalQuestion ? 1 : 0;

    return {
      current: currentMainIndex + answeredConditional,
      total: totalMainQuestions,
      percentage: Math.round(
        ((currentMainIndex + answeredConditional) / totalMainQuestions) * 100
      ),
    };
  };

  const renderQuestionInput = (question: Question) => {
    const response = previewState.responses[question.questionId] || '';

    switch (question.type) {
      case 'text':
        return (
          <Input
            value={response}
            onChange={e => handleResponseChange(e.target.value)}
            placeholder='Enter your answer...'
            className='w-full'
          />
        );

      case 'radio':
        return (
          <RadioGroup
            value={response}
            onValueChange={handleResponseChange}
            className='space-y-2'
          >
            {question.options?.map(option => (
              <div key={option.id} className='flex items-center space-x-2'>
                <RadioGroupItem value={option.value} id={option.id} />
                <Label htmlFor={option.id} className='flex-1 cursor-pointer'>
                  {option.label}
                  {option.nextQuestionId && (
                    <Badge variant='outline' className='ml-2 text-xs'>
                      <ArrowRight className='mr-1 h-3 w-3' />
                      Branches
                    </Badge>
                  )}
                </Label>
              </div>
            ))}
          </RadioGroup>
        );

      case 'select':
        return (
          <Select value={response} onValueChange={handleResponseChange}>
            <SelectTrigger>
              <SelectValue placeholder='Select an option...' />
            </SelectTrigger>
            <SelectContent>
              {question.options?.map(option => (
                <SelectItem key={option.id} value={option.value}>
                  <div className='flex items-center justify-between w-full'>
                    <span>{option.label}</span>
                    {option.nextQuestionId && (
                      <Badge variant='outline' className='ml-2 text-xs'>
                        <ArrowRight className='mr-1 h-3 w-3' />
                        Branches
                      </Badge>
                    )}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case 'checkbox':
        const checkboxResponses = Array.isArray(response) ? response : [];
        return (
          <div className='space-y-2'>
            {question.options?.map(option => (
              <div key={option.id} className='flex items-center space-x-2'>
                <Checkbox
                  id={option.id}
                  checked={checkboxResponses.includes(option.value)}
                  onCheckedChange={checked => {
                    const newResponses = checked
                      ? [...checkboxResponses, option.value]
                      : checkboxResponses.filter(v => v !== option.value);
                    handleResponseChange(newResponses);
                  }}
                />
                <Label htmlFor={option.id} className='flex-1 cursor-pointer'>
                  {option.label}
                </Label>
              </div>
            ))}
          </div>
        );

      default:
        return <div>Unsupported question type: {question.type}</div>;
    }
  };

  const headQuestions = getHeadQuestions();

  if (sortedQuestions.length === 0) {
    return (
      <Card>
        <CardContent className='text-center py-12'>
          <Eye className='mx-auto h-12 w-12 text-[var(--custom-gray-medium)]' />
          <h3 className='mt-2 text-sm font-medium text-[var(--custom-gray-dark)]'>
            No questions to preview
          </h3>
          <p className='mt-1 text-sm text-[var(--custom-gray-medium)]'>
            Add some questions to see the interview preview.
          </p>
        </CardContent>
      </Card>
    );
  }

  if (!previewState.isStarted) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center'>
            <Eye className='mr-2 h-5 w-5' />
            Interview Preview
          </CardTitle>
          <CardDescription>
            Test your interview flow and see how it will appear to users
          </CardDescription>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='bg-blue-50 border border-blue-200 rounded-lg p-4'>
            <h3 className='font-medium text-blue-900 mb-2'>{interview.name}</h3>
            {interview.description && (
              <p className='text-blue-700 text-sm mb-3'>
                {interview.description}
              </p>
            )}
            <div className='flex items-center justify-between text-sm text-blue-600'>
              <span>
                {headQuestions.length} main questions
                {sortedQuestions.length > headQuestions.length &&
                  ` + ${sortedQuestions.length - headQuestions.length} conditional`}
              </span>
              {version && (
                <Badge variant='outline'>Version {version.versionNumber}</Badge>
              )}
            </div>
          </div>

          <Button onClick={handleStart} className='w-full'>
            <Play className='mr-2 h-4 w-4' />
            Start Preview
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (previewState.isComplete) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center text-green-600'>
            <CheckCircle className='mr-2 h-5 w-5' />
            Interview Complete
          </CardTitle>
          <CardDescription>
            You've reached the end of the interview
          </CardDescription>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='bg-green-50 border border-green-200 rounded-lg p-4'>
            <h3 className='font-medium text-green-900 mb-2'>
              Responses Summary
            </h3>
            <div className='space-y-2 text-sm'>
              {Object.entries(previewState.responses).map(
                ([questionId, response]) => {
                  const question = sortedQuestions.find(
                    q => q.questionId === questionId
                  );
                  if (!question) return null;

                  return (
                    <div key={questionId} className='flex justify-between'>
                      <span className='text-green-700 truncate max-w-xs'>
                        {question.text}
                      </span>
                      <span className='text-green-600 font-medium'>
                        {Array.isArray(response)
                          ? response.join(', ')
                          : response}
                      </span>
                    </div>
                  );
                }
              )}
            </div>
          </div>

          <div className='flex space-x-2'>
            <Button
              onClick={handlePrevious}
              variant='outline'
              className='flex-1'
            >
              <ArrowLeft className='mr-2 h-4 w-4' />
              Go Back
            </Button>
            <Button onClick={handleReset} className='flex-1'>
              <RotateCcw className='mr-2 h-4 w-4' />
              Start Over
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className='flex items-center justify-between'>
          <CardTitle className='flex items-center'>
            <Eye className='mr-2 h-5 w-5' />
            {previewState.currentConditionalQuestion ? (
              <span className='flex items-center'>
                Conditional Question
                <Badge variant='secondary' className='ml-2'>
                  Branch
                </Badge>
              </span>
            ) : (
              `Question ${previewState.currentQuestionIndex + 1} of ${headQuestions.length}`
            )}
          </CardTitle>
          <Button onClick={handleReset} variant='outline' size='sm'>
            <RotateCcw className='mr-2 h-4 w-4' />
            Reset
          </Button>
        </div>
        <CardDescription>
          Preview how this question will appear to users
        </CardDescription>

        {/* Progress Bar */}
        <div className='mt-4'>
          <div className='flex justify-between text-sm text-gray-600 mb-2'>
            <span>Progress</span>
            <span>{getProgressInfo().percentage}%</span>
          </div>
          <div className='w-full bg-gray-200 rounded-full h-2'>
            <div
              className='bg-blue-600 h-2 rounded-full transition-all duration-300'
              style={{ width: `${getProgressInfo().percentage}%` }}
            />
          </div>
        </div>
      </CardHeader>
      <CardContent className='space-y-6'>
        {currentQuestion && (
          <>
            <div className='space-y-4'>
              <div>
                <Label className='text-base font-medium'>
                  {currentQuestion.text}
                </Label>
                {previewState.currentConditionalQuestion && (
                  <div className='mt-2'>
                    <Alert>
                      <AlertTriangle className='h-4 w-4' />
                      <AlertDescription>
                        This is a conditional question that appears based on
                        your previous answer.
                      </AlertDescription>
                    </Alert>
                  </div>
                )}
              </div>

              {/* Educational Video */}
              {currentQuestion.educationalVideoId && (
                <EducationalVideoSection
                  videoId={currentQuestion.educationalVideoId}
                />
              )}

              {renderQuestionInput(currentQuestion)}
            </div>

            <div className='flex justify-between'>
              <Button
                onClick={handlePrevious}
                variant='outline'
                disabled={previewState.questionHistory.length === 0}
              >
                <ArrowLeft className='mr-2 h-4 w-4' />
                Previous
              </Button>

              <Button onClick={handleNext} disabled={!canProceed()}>
                {previewState.currentConditionalQuestion
                  ? 'Continue'
                  : previewState.currentQuestionIndex ===
                      headQuestions.length - 1
                    ? 'Complete'
                    : 'Next'}
                <ArrowRight className='ml-2 h-4 w-4' />
              </Button>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
