'use client';

import React, { useState, useCallback } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Plus,
  MessageSquare,
  AlertTriangle,
  Edit,
  Trash2,
  MoreHorizontal,
  ArrowRight,
  Loader2,
} from 'lucide-react';
import {
  InterviewNew,
  InterviewVersionNew,
  Question,
} from '@/types/interview-builder-new';
import {
  deleteQuestion,
  reorderQuestions,
} from '@/lib/api/interview-builder-new';
import { QuestionFormDialog } from './question-form-dialog';
import { DraggableQuestionRow } from './draggable-question-row';

interface QuestionBuilderProps {
  interviewId: string;
  interview: InterviewNew;
  version: InterviewVersionNew | null;
  onQuestionsUpdated: () => void;
  refetch: () => void;
}

export function QuestionBuilder({
  interviewId,
  interview,
  version,
  onQuestionsUpdated,
  refetch,
}: QuestionBuilderProps) {
  const queryClient = useQueryClient();
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [editingQuestion, setEditingQuestion] = useState<Question | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [draggedQuestions, setDraggedQuestions] = useState<Question[]>([]);
  const [isReordering, setIsReordering] = useState(false);

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: ({
      interviewId,
      questionId,
    }: {
      interviewId: string;
      questionId: string;
    }) => deleteQuestion(interviewId, questionId),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['interview-with-version', interviewId],
      });
      onQuestionsUpdated();
    },
    onError: error => {
      setError('Failed to delete question. Please try again.');
      console.error('Delete question error:', error);
    },
  });

  // Reorder mutation
  const reorderMutation = useMutation({
    mutationFn: ({
      interviewId,
      questionIds,
    }: {
      interviewId: string;
      questionIds: string[];
    }) => reorderQuestions(interviewId, questionIds),
    onMutate: () => {
      setIsReordering(true);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['interview-with-version', interviewId],
      });
      refetch();
      setDraggedQuestions([]); // Clear dragged state after successful reorder
      setIsReordering(false);
    },
    onError: error => {
      setError('Failed to reorder questions. Please try again.');
      console.error('Reorder questions error:', error);
      setDraggedQuestions([]); // Clear dragged state on error
      setIsReordering(false);
    },
  });

  const questions = version?.questions || [];
  const sortedQuestions = [...questions].sort((a, b) => a.order - b.order);

  // Use dragged questions if available, otherwise use sorted questions
  const displayQuestions =
    draggedQuestions.length > 0 ? draggedQuestions : sortedQuestions;

  // Helper functions for conditional logic
  const getConditionalQuestionIds = (): Set<string> => {
    const conditionalIds = new Set<string>();
    displayQuestions.forEach(question => {
      question.options?.forEach(option => {
        if (option.nextQuestionId) {
          conditionalIds.add(option.nextQuestionId);
        }
      });
    });
    return conditionalIds;
  };

  // Get head questions (questions that are not conditional)
  const getHeadQuestions = (): Question[] => {
    return displayQuestions.filter(
      question => question.isHeadQuestion !== false
    );
  };

  // Get conditional questions (questions that are referenced by options)
  const getConditionalQuestions = (): Question[] => {
    return sortedQuestions.filter(
      question => question.isHeadQuestion === false
    );
  };

  const getParentQuestion = (
    conditionalQuestionId: string
  ): Question | null => {
    return (
      sortedQuestions.find(question =>
        question.options?.some(
          option => option.nextQuestionId === conditionalQuestionId
        )
      ) || null
    );
  };

  const getConditionalQuestionsForParent = (
    parentQuestion: Question
  ): Question[] => {
    const conditionalQuestionIds = new Set<string>();
    parentQuestion.options?.forEach(option => {
      if (option.nextQuestionId) {
        conditionalQuestionIds.add(option.nextQuestionId);
      }
    });

    return displayQuestions
      .filter(question => conditionalQuestionIds.has(question.questionId))
      .sort((a, b) => a.order - b.order);
  };

  // Drag and drop handlers
  const handleMoveQuestion = useCallback(
    (dragIndex: number, hoverIndex: number) => {
      // Prevent dragging while reordering
      if (isReordering) return;

      const currentQuestions = displayQuestions;
      const draggedQuestion = currentQuestions[dragIndex];

      if (!draggedQuestion) return;

      const newQuestions = [...currentQuestions];
      newQuestions.splice(dragIndex, 1);
      newQuestions.splice(hoverIndex, 0, draggedQuestion);

      setDraggedQuestions(newQuestions);
    },
    [displayQuestions, isReordering]
  );

  const handleDragEnd = useCallback(() => {
    // Prevent multiple reorder requests
    if (isReordering) return;

    if (draggedQuestions.length > 0) {
      // We need to send ALL questions (head + conditional) in the correct order
      // The dragged questions only contain the reordered head questions
      const reorderedHeadQuestions = draggedQuestions;
      const allQuestions: Question[] = [];

      // Add head questions in their new order
      reorderedHeadQuestions.forEach(headQuestion => {
        allQuestions.push(headQuestion);

        // Add any conditional questions that belong to this head question
        const conditionalQuestions =
          getConditionalQuestionsForParent(headQuestion);
        allQuestions.push(...conditionalQuestions);
      });

      // Add any remaining conditional questions that might not have been included
      const conditionalQuestions = getConditionalQuestions();
      conditionalQuestions.forEach(conditionalQ => {
        if (!allQuestions.find(q => q.questionId === conditionalQ.questionId)) {
          allQuestions.push(conditionalQ);
        }
      });

      const questionIds = allQuestions.map(q => q.questionId);
      reorderMutation.mutate({ interviewId, questionIds });
    }
  }, [
    draggedQuestions,
    interviewId,
    reorderMutation,
    isReordering,
    getConditionalQuestionsForParent,
    getConditionalQuestions,
  ]);

  // Get main questions (head questions) and conditional questions
  const mainQuestions = getHeadQuestions();
  const conditionalQuestionIds = getConditionalQuestionIds();

  const handleCreateQuestion = () => {
    if (isReordering) return;
    setEditingQuestion(null);
    setShowCreateDialog(true);
  };

  const handleEditQuestion = (question: Question) => {
    if (isReordering) return;
    setEditingQuestion(question);
    setShowCreateDialog(true);
  };

  const handleDeleteQuestion = async (question: Question) => {
    if (isReordering) return;
    if (
      window.confirm(
        `Are you sure you want to delete "${question.text}"? This action cannot be undone.`
      )
    ) {
      deleteMutation.mutate({ interviewId, questionId: question.questionId });
    }
  };

  const handleDialogSave = () => {
    setShowCreateDialog(false);
    setEditingQuestion(null);
    refetch();
  };

  const getQuestionTypeLabel = (type: string) => {
    const typeLabels: Record<string, string> = {
      text: 'Text Input',
      radio: 'Radio Buttons',
      select: 'Dropdown',
      checkbox: 'Checkboxes',
    };
    return typeLabels[type] || type;
  };

  const getQuestionTypeBadgeVariant = (type: string) => {
    const variants: Record<string, 'default' | 'secondary' | 'outline'> = {
      text: 'default',
      radio: 'secondary',
      select: 'outline',
      checkbox: 'secondary',
    };
    return variants[type] || 'default';
  };

  const hasConditionalLogic = (question: Question) => {
    return question.options?.some(opt => opt.nextQuestionId) || false;
  };

  const getNextQuestionDisplay = (question: Question): string => {
    if (!question.defaultNextQuestionId) {
      return 'End of interview';
    }

    const nextQuestion = sortedQuestions.find(
      q => q.questionId === question.defaultNextQuestionId
    );
    return nextQuestion
      ? nextQuestion.text.substring(0, 30) + '...'
      : 'Unknown question';
  };

  // Helper function to get which answer option a conditional question belongs to
  const getConditionalQuestionAnswerIndex = (
    conditionalQuestionId: string
  ): number => {
    for (const question of sortedQuestions) {
      if (question.options) {
        for (let i = 0; i < question.options.length; i++) {
          if (question.options[i].nextQuestionId === conditionalQuestionId) {
            return i;
          }
        }
      }
    }
    return 0; // Default to first answer if not found
  };

  // Helper function to get colors for different answer options
  const getAnswerOptionColors = (answerIndex: number) => {
    const colorSchemes = [
      {
        border: 'border-l-green-200',
        arrow: 'text-green-500',
        text: 'text-green-600',
        bg: 'bg-green-50',
      },
      {
        border: 'border-l-purple-200',
        arrow: 'text-purple-500',
        text: 'text-purple-600',
        bg: 'bg-purple-50',
      },
      {
        border: 'border-l-orange-200',
        arrow: 'text-orange-500',
        text: 'text-orange-600',
        bg: 'bg-orange-50',
      },
      {
        border: 'border-l-pink-200',
        arrow: 'text-pink-500',
        text: 'text-pink-600',
        bg: 'bg-pink-50',
      },
      {
        border: 'border-l-indigo-200',
        arrow: 'text-indigo-500',
        text: 'text-indigo-600',
        bg: 'bg-indigo-50',
      },
      {
        border: 'border-l-teal-200',
        arrow: 'text-teal-500',
        text: 'text-teal-600',
        bg: 'bg-teal-50',
      },
    ];

    // Cycle through colors if we have more answers than color schemes
    return colorSchemes[answerIndex % colorSchemes.length];
  };

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex justify-between items-center'>
        <div>
          <h2 className='text-2xl font-bold text-[var(--foreground)]'>
            Questions
          </h2>
          <p className='text-[var(--custom-gray-dark)]'>
            Manage interview questions and their flow logic
          </p>
        </div>
        <Button onClick={handleCreateQuestion} disabled={isReordering}>
          <Plus className='mr-2 h-4 w-4' />
          Add Question
        </Button>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant='destructive'>
          <AlertTriangle className='h-4 w-4' />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Questions List */}
      {sortedQuestions.length === 0 ? (
        <Card>
          <CardContent className='text-center py-12'>
            <MessageSquare className='mx-auto h-12 w-12 text-[var(--custom-gray-medium)]' />
            <h3 className='mt-2 text-sm font-medium text-[var(--custom-gray-dark)]'>
              No questions yet
            </h3>
            <p className='mt-1 text-sm text-[var(--custom-gray-medium)]'>
              Get started by adding your first interview question.
            </p>
            <div className='mt-6'>
              <Button onClick={handleCreateQuestion} disabled={isReordering}>
                <Plus className='mr-2 h-4 w-4' />
                Add First Question
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              Question Flow
              {isReordering && (
                <div className='flex items-center gap-2 text-sm text-blue-600'>
                  <Loader2 className='h-4 w-4 animate-spin' />
                  Reordering questions...
                </div>
              )}
            </CardTitle>
            <CardDescription>
              Head questions are shown first, followed by their conditional
              questions. Only head questions appear in the default flow.
              {isReordering && (
                <span className='block text-amber-600 mt-1'>
                  Please wait while questions are being reordered. Other actions
                  are temporarily disabled.
                </span>
              )}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className='overflow-x-auto'>
              <Table className='table-fixed w-full'>
                <TableHeader>
                  <TableRow>
                    <TableHead className='w-12 max-w-12'></TableHead>
                    <TableHead className='w-16 max-w-16'>Order</TableHead>
                    <TableHead className='w-[200px] max-w-[120px]'>
                      Question
                    </TableHead>
                    <TableHead className='w-[100px] max-w-[100px]'>
                      Type
                    </TableHead>
                    <TableHead className='w-[120px] max-w-[120px]'>
                      Mapping
                    </TableHead>
                    <TableHead className='w-[120px] max-w-[120px]'>
                      Options
                    </TableHead>
                    <TableHead className='w-[100px] max-w-[100px]'>
                      Logic
                    </TableHead>
                    <TableHead className='w-[120px] max-w-[120px]'>
                      Default Next
                    </TableHead>
                    <TableHead className='w-[100px] max-w-[100px]'>
                      Head Question
                    </TableHead>
                    <TableHead className='w-[100px] max-w-[100px] text-right'>
                      Actions
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {mainQuestions.map((question, index) => {
                    const conditionalQuestions =
                      getConditionalQuestionsForParent(question);

                    return (
                      <React.Fragment key={question.questionId}>
                        {/* Main Question Row - Draggable */}
                        <DraggableQuestionRow
                          question={question}
                          index={index}
                          onEdit={handleEditQuestion}
                          onDelete={handleDeleteQuestion}
                          onMove={handleMoveQuestion}
                          onDragEnd={handleDragEnd}
                          getQuestionTypeLabel={getQuestionTypeLabel}
                          getQuestionTypeBadgeVariant={
                            getQuestionTypeBadgeVariant
                          }
                          hasConditionalLogic={hasConditionalLogic}
                          getNextQuestionDisplay={getNextQuestionDisplay}
                          isLoading={isReordering}
                          disabled={isReordering}
                        />

                        {/* Conditional Questions Rows - Not draggable */}
                        {conditionalQuestions.map(conditionalQuestion => {
                          const answerIndex = getConditionalQuestionAnswerIndex(
                            conditionalQuestion.questionId
                          );
                          const colors = getAnswerOptionColors(answerIndex);

                          return (
                            <TableRow
                              key={conditionalQuestion.questionId}
                              className={`border-l-4 ${colors.border}`}
                            >
                              <TableCell>
                                {/* Empty cell for drag handle column */}
                              </TableCell>
                              <TableCell>
                                <div className='flex items-center space-x-2'>
                                  <div className='w-4 h-4 flex items-center justify-center'>
                                    <ArrowRight
                                      className={`h-3 w-3 ${colors.arrow}`}
                                    />
                                  </div>
                                  <Badge variant='outline' className='text-xs'>
                                    {conditionalQuestion.order}
                                  </Badge>
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className='max-w-md pl-6'>
                                  <div className='font-medium text-[var(--custom-gray-dark)] truncate text-sm'>
                                    {conditionalQuestion.text}
                                  </div>
                                  <div
                                    className={`text-xs ${colors.text} mt-1`}
                                  >
                                    Conditional Question (Answer{' '}
                                    {answerIndex + 1})
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell>
                                <Badge
                                  variant={getQuestionTypeBadgeVariant(
                                    conditionalQuestion.type
                                  )}
                                  className='text-xs'
                                >
                                  {getQuestionTypeLabel(
                                    conditionalQuestion.type
                                  )}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <div className='text-sm text-[var(--custom-gray-medium)]'>
                                  {conditionalQuestion.questionMapping || '-'}
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className='text-sm text-[var(--custom-gray-medium)]'>
                                  {conditionalQuestion.options?.length || 0}{' '}
                                  options
                                </div>
                              </TableCell>
                              <TableCell>
                                {hasConditionalLogic(conditionalQuestion) ? (
                                  <Badge
                                    variant='secondary'
                                    className='text-xs'
                                  >
                                    <ArrowRight className='mr-1 h-3 w-3' />
                                    Branching
                                  </Badge>
                                ) : (
                                  <span className='text-sm text-[var(--custom-gray-medium)]'>
                                    Linear
                                  </span>
                                )}
                              </TableCell>
                              <TableCell>
                                <div className='text-sm text-[var(--custom-gray-medium)]'>
                                  {getNextQuestionDisplay(conditionalQuestion)}
                                </div>
                              </TableCell>
                              <TableCell>
                                <Badge variant='secondary' className='text-xs'>
                                  Conditional
                                </Badge>
                              </TableCell>
                              <TableCell className='text-right'>
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button
                                      variant='ghost'
                                      size='sm'
                                      disabled={isReordering}
                                    >
                                      <MoreHorizontal className='h-4 w-4' />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align='end'>
                                    <DropdownMenuItem
                                      onClick={() =>
                                        handleEditQuestion(conditionalQuestion)
                                      }
                                      disabled={isReordering}
                                    >
                                      <Edit className='mr-2 h-4 w-4' />
                                      Edit Question
                                    </DropdownMenuItem>
                                    <DropdownMenuItem
                                      onClick={() =>
                                        handleDeleteQuestion(
                                          conditionalQuestion
                                        )
                                      }
                                      className='text-red-600'
                                      disabled={isReordering}
                                    >
                                      <Trash2 className='mr-2 h-4 w-4' />
                                      Delete
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </TableCell>
                            </TableRow>
                          );
                        })}
                      </React.Fragment>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Question Form Dialog */}
      <QuestionFormDialog
        interviewId={interviewId}
        question={editingQuestion}
        existingQuestions={sortedQuestions}
        isOpen={showCreateDialog}
        onClose={() => {
          setShowCreateDialog(false);
          setEditingQuestion(null);
        }}
        onSave={handleDialogSave}
      />
    </div>
  );
}
