'use client';

import React, { useState, useEffect, useMemo, useRef } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
  Save,
  AlertTriangle,
  Plus,
  Trash2,
  HelpCircle,
  ArrowRight,
  Search,
  X,
} from 'lucide-react';
import {
  Question,
  QuestionOption,
  QuestionType,
  QuestionValidationType,
  CreateQuestionNewRequest,
  UpdateQuestionNewRequest,
  QuestionFormData,
  ValidationErrors,
} from '@/types/interview-builder-new';
import {
  createQuestion,
  updateQuestion,
  validateQuestionData,
} from '@/lib/api/interview-builder-new';
import { useEducationalContent } from '@/hooks/use-educational-content';

// Interface for variables that can be inserted into text
interface Variable {
  id: string;
  label: string;
  value: string;
  category: 'user' | 'answers' | 'document';
}

interface QuestionFormDialogProps {
  interviewId: string;
  question: Question | null;
  existingQuestions: Question[];
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
}

const QUESTION_TYPES: {
  value: QuestionType;
  label: string;
  description: string;
}[] = [
  { value: 'text', label: 'Text Input', description: 'Single line text input' },
  {
    value: 'radio',
    label: 'Radio Buttons',
    description: 'Single choice from options',
  },
  {
    value: 'select',
    label: 'Dropdown',
    description: 'Single choice from dropdown',
  },
  {
    value: 'checkbox',
    label: 'Checkboxes',
    description: 'Multiple choice options',
  },
  {
    value: 'date',
    label: 'Date',
    description: 'Date picker',
  },
];

const VALIDATION_RULES: {
  value: QuestionValidationType;
  label: string;
  description: string;
}[] = [
  {
    value: 'number',
    label: 'Number',
    description: 'Only numeric values allowed',
  },
  { value: 'email', label: 'Email', description: 'Valid email address format' },
  { value: 'phone', label: 'Phone', description: 'Valid phone number format' },
  {
    value: 'date',
    label: 'Date (MM/DD/YYYY)',
    description: 'Date in MM/DD/YYYY format',
  },
  {
    value: 'domestic_address',
    label: 'US Address',
    description: 'Complete US address with ZIP code',
  },
  {
    value: 'international_address',
    label: 'International Address',
    description: 'International address format',
  },
  {
    value: 'full_name',
    label: 'Full Name',
    description: 'First and last name (letters only)',
  },
  {
    value: 'currency',
    label: 'Currency/Amount',
    description: 'Monetary amount (e.g., 100.00)',
  },
  {
    value: 'percentage',
    label: 'Percentage',
    description: 'Percentage value (0-100)',
  },
  {
    value: 'ssn',
    label: 'SSN',
    description: 'Social Security Number (XXX-XX-XXXX)',
  },
  {
    value: 'tax_id',
    label: 'Tax ID/EIN',
    description: 'Tax ID or EIN (XX-XXXXXXX)',
  },
];

// Available variables for auto-filling text inputs (only user information)
const USER_VARIABLES: Variable[] = [
  {
    id: 'user_member_name',
    label: 'Member Name',
    value: '{{user.fullName}}',
    category: 'user',
  },
  {
    id: 'user_first_name',
    label: 'First Name',
    value: '{{user.firstName}}',
    category: 'user',
  },
  {
    id: 'user_last_name',
    label: 'Last Name',
    value: '{{user.lastName}}',
    category: 'user',
  },
  {
    id: 'user_email',
    label: 'Email',
    value: '{{user.email}}',
    category: 'user',
  },
  {
    id: 'user_phone',
    label: 'Phone Number',
    value: '{{user.phone}}',
    category: 'user',
  },
  {
    id: 'user_address',
    label: 'Address',
    value: '{{user.address}}',
    category: 'user',
  },
  {
    id: 'user_state',
    label: 'State',
    value: '{{user.state}}',
    category: 'user',
  },
  {
    id: 'user_dob',
    label: 'Date of Birth',
    value: '{{user.dateOfBirth}}',
    category: 'user',
  },
  {
    id: 'user_gender',
    label: 'Gender',
    value: '{{user.gender}}',
    category: 'user',
  },
];

export function QuestionFormDialog({
  interviewId,
  question,
  existingQuestions,
  isOpen,
  onClose,
  onSave,
}: QuestionFormDialogProps) {
  const { getContentByType } = useEducationalContent();

  const availableVideos = getContentByType('video');

  const [formData, setFormData] = useState<QuestionFormData>({
    text: '',
    type: 'text',
    options: [],
    conditionalLogic: [],
    questionMapping: '',
    questionValidation: undefined,
    educationalVideoId: undefined,
    autoFillVariable: undefined,
  });

  const [errors, setErrors] = useState<ValidationErrors>({});
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('basic');
  const [videoSearchQuery, setVideoSearchQuery] = useState('');
  const [isVideoDropdownOpen, setIsVideoDropdownOpen] = useState(false);
  const [selectedVideoIndex, setSelectedVideoIndex] = useState(-1);

  // Ref for question text textarea
  const questionTextRef = useRef<HTMLTextAreaElement>(null);

  // Filter videos based on search query
  const filteredVideos = useMemo(() => {
    if (!videoSearchQuery.trim()) {
      return availableVideos;
    }

    const query = videoSearchQuery.toLowerCase();
    return availableVideos.filter(
      video =>
        video.title.toLowerCase().includes(query) ||
        (video.description && video.description.toLowerCase().includes(query))
    );
  }, [availableVideos, videoSearchQuery]);

  // Get selected video title for display
  const selectedVideo = useMemo(() => {
    if (!formData.educationalVideoId) return null;
    return availableVideos.find(
      video => video.id === formData.educationalVideoId
    );
  }, [formData.educationalVideoId, availableVideos]);

  const isEditing = !!question;

  // Helper function to strip handlebars or legacy brackets from mapping value for display
  const stripMappingHandlebars = (mapping: string): string => {
    if (!mapping) return '';
    return mapping
      .replace(/^\{\{|\}\}$/g, '') // Remove handlebars format
      .replace(/^\[|\]$/g, ''); // Remove legacy brackets format
  };

  // Function to insert variable into question text
  const insertVariableIntoText = (variable: Variable) => {
    const textarea = questionTextRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const currentText = formData.text;
    const newText =
      currentText.slice(0, start) + variable.value + currentText.slice(end);

    handleFieldChange('text', newText);

    // Set cursor position after the inserted variable
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(
        start + variable.value.length,
        start + variable.value.length
      );
    }, 0);
  };

  // Helper function to add handlebars to mapping value for saving
  const addMappingHandlebars = (mapping: string): string => {
    if (!mapping) return '';
    // Strip both handlebars and legacy brackets, then add handlebars
    const stripped = mapping
      .replace(/^\{\{|\}\}$/g, '') // Remove handlebars format
      .replace(/^\[|\]$/g, ''); // Remove legacy brackets format
    return `{{${stripped}}}`;
  };

  useEffect(() => {
    if (question) {
      const calculatedDefaultNextQuestionId = calculateDefaultNextQuestionId();
      console.log('question', question);
      setFormData({
        text: question.text,
        type: question.type,
        options: question.options || [],
        conditionalLogic: question.conditionalLogic || [],
        defaultNextQuestionId:
          question.defaultNextQuestionId || calculatedDefaultNextQuestionId,
        questionMapping: stripMappingHandlebars(question.questionMapping || ''),
        questionValidation: question.questionValidation,
        educationalVideoId: question.educationalVideoId,
        autoFillVariable: question.autoFillVariable,
      });
    } else {
      setFormData({
        text: '',
        type: 'text',
        options: [],
        conditionalLogic: [],
        defaultNextQuestionId: undefined,
        questionMapping: '',
        questionValidation: undefined,
        educationalVideoId: undefined,
        autoFillVariable: undefined,
      });
    }
    setErrors({});
    setActiveTab('basic');
    setVideoSearchQuery(''); // Clear video search when dialog opens/closes
    setIsVideoDropdownOpen(false); // Close video dropdown when dialog opens/closes
  }, [question, isOpen, existingQuestions]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (isVideoDropdownOpen && !target.closest('.video-dropdown-container')) {
        setIsVideoDropdownOpen(false);
        setSelectedVideoIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isVideoDropdownOpen]);

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isVideoDropdownOpen) return;

    const videoOptions = [null, ...filteredVideos]; // null for "No video" option

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedVideoIndex(prev =>
          prev < videoOptions.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedVideoIndex(prev => (prev > 0 ? prev - 1 : prev));
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedVideoIndex >= 0) {
          const selectedOption = videoOptions[selectedVideoIndex];
          handleFieldChange('educationalVideoId', selectedOption?.id);
          setIsVideoDropdownOpen(false);
          setVideoSearchQuery('');
          setSelectedVideoIndex(-1);
        }
        break;
      case 'Escape':
        setIsVideoDropdownOpen(false);
        setSelectedVideoIndex(-1);
        break;
    }
  };

  // Helper function to format questionMapping value
  const formatQuestionMapping = (value: string): string => {
    if (!value) return '';
    // Replace spaces with underscores if user hasn't already used underscores
    return value.replace(/\s+/g, '_').toLowerCase();
  };

  const handleFieldChange = (field: keyof QuestionFormData, value: any) => {
    setFormData(prev => {
      const newData = { ...prev, [field]: value };

      // Clear validation when question type changes from text to something else
      if (field === 'type' && value !== 'text' && prev.questionValidation) {
        newData.questionValidation = undefined;
      }

      // Clear validation when auto-fill variable is selected (they are mutually exclusive)
      if (field === 'autoFillVariable' && value && prev.questionValidation) {
        newData.questionValidation = undefined;
      }

      // Clear auto-fill variable when validation is selected (they are mutually exclusive)
      if (field === 'questionValidation' && value && prev.autoFillVariable) {
        newData.autoFillVariable = undefined;
      }

      // Special handling for questionMapping to auto-format with underscores
      if (field === 'questionMapping' && typeof value === 'string') {
        newData.questionMapping = formatQuestionMapping(value);
      }

      return newData;
    });

    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }

    // Special handling for defaultNextQuestionId on conditional questions
    if (field === 'defaultNextQuestionId' && value && isConditionalQuestion()) {
      updateParentDefaultNextQuestionId(value);
    }
  };

  // Helper function to update parent question's defaultNextQuestionId and inherit it in child
  const updateParentDefaultNextQuestionId = async (nextQuestionId: string) => {
    if (!question) return;

    // Find the parent question (question that has this conditional question in its options)
    const parentQuestion = existingQuestions.find(q =>
      q.options?.some(option => option.nextQuestionId === question.questionId)
    );

    if (parentQuestion) {
      try {
        // Update the parent question's defaultNextQuestionId
        const updateData: UpdateQuestionNewRequest = {
          questionId: parentQuestion.questionId,
          text: parentQuestion.text,
          type: parentQuestion.type,
          options: parentQuestion.options,
          conditionalLogic: parentQuestion.conditionalLogic || [],
          defaultNextQuestionId: nextQuestionId,
          questionMapping: parentQuestion.questionMapping,
        };

        await updateQuestion(interviewId, updateData);

        // Also update the current conditional question's defaultNextQuestionId to inherit from parent
        setFormData(prev => ({
          ...prev,
          defaultNextQuestionId: nextQuestionId,
        }));

        // Refresh the questions list to reflect the change
        if (onSave) {
          onSave();
        }
      } catch (error) {
        console.error('Failed to update parent question:', error);
      }
    }
  };

  const handleAddOption = () => {
    const newOption: QuestionOption = {
      id: `opt_${Date.now()}`,
      label: '',
      value: '',
    };
    setFormData(prev => ({
      ...prev,
      options: [...prev.options, newOption],
    }));
  };

  const handleUpdateOption = (
    index: number,
    field: keyof QuestionOption,
    value: string
  ) => {
    setFormData(prev => ({
      ...prev,
      options: prev.options.map((opt, i) =>
        i === index ? { ...opt, [field]: value } : opt
      ),
    }));
  };

  const handleRemoveOption = (index: number) => {
    setFormData(prev => ({
      ...prev,
      options: prev.options.filter((_, i) => i !== index),
    }));
  };

  // Helper function to determine if current question is conditional
  const isConditionalQuestion = (): boolean => {
    if (!question) return false;

    // Check if this question is referenced by other questions' options
    return existingQuestions.some(q =>
      q.options?.some(option => option.nextQuestionId === question.questionId)
    );
  };

  // Helper function to calculate default next question ID
  const calculateDefaultNextQuestionId = (): string | undefined => {
    if (isConditionalQuestion()) {
      // For conditional questions, inherit from parent question
      const parentQuestion = existingQuestions.find(q =>
        q.options?.some(
          option => option.nextQuestionId === question?.questionId
        )
      );
      return parentQuestion?.defaultNextQuestionId;
    } else {
      // For head questions, find the next head question in order
      const headQuestions = [...existingQuestions]
        .filter(q => q.isHeadQuestion !== false)
        .sort((a, b) => a.order - b.order);

      const currentIndex = question
        ? headQuestions.findIndex(q => q.questionId === question.questionId)
        : -1;

      if (currentIndex >= 0 && currentIndex < headQuestions.length - 1) {
        return headQuestions[currentIndex + 1].questionId;
      }
    }
    return undefined;
  };

  const handleSetBranching = (optionIndex: number, nextQuestionId: string) => {
    setFormData(prev => {
      const updatedOptions = [...prev.options];

      // Update the specific option only
      updatedOptions[optionIndex] = {
        ...updatedOptions[optionIndex],
        nextQuestionId: nextQuestionId || undefined,
      };

      return {
        ...prev,
        options: updatedOptions,
      };
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const validationErrors = validateQuestionData(formData);
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    try {
      setSaving(true);
      setErrors({});

      // Prepare the mapping value with handlebars for saving
      const mappingWithHandlebars = formData.questionMapping
        ? addMappingHandlebars(formData.questionMapping)
        : '';

      if (isEditing && question) {
        const updateData: UpdateQuestionNewRequest = {
          questionId: question.questionId,
          text: formData.text,
          type: formData.type,
          options: formData.options,
          conditionalLogic: formData.conditionalLogic,
          defaultNextQuestionId: formData.defaultNextQuestionId,
          questionMapping: mappingWithHandlebars,
          questionValidation: formData.questionValidation,
          educationalVideoId: formData.educationalVideoId,
          autoFillVariable: formData.autoFillVariable,
        };
        await updateQuestion(interviewId, updateData);
      } else {
        const createData: CreateQuestionNewRequest = {
          text: formData.text,
          type: formData.type,
          options: formData.options,
          conditionalLogic: formData.conditionalLogic,
          defaultNextQuestionId: formData.defaultNextQuestionId,
          questionMapping: mappingWithHandlebars,
          questionValidation: formData.questionValidation,
          educationalVideoId: formData.educationalVideoId,
          autoFillVariable: formData.autoFillVariable,
        };
        await createQuestion(interviewId, createData);
      }

      onSave();
    } catch (err) {
      setErrors({ submit: 'Failed to save question. Please try again.' });
      console.error('Error saving question:', err);
    } finally {
      setSaving(false);
    }
  };

  const needsOptions = ['radio', 'select', 'checkbox'].includes(formData.type);
  const supportsBranching = ['radio', 'select'].includes(formData.type);

  // Get available questions for branching (excluding current question)
  const availableQuestions = existingQuestions.filter(
    q => !question || q.questionId !== question.questionId
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='!max-w-[95vw] !w-[55vw] max-h-[90vh] !top-[8vh] !translate-y-0'>
        <DialogHeader>
          <DialogTitle className='flex items-center space-x-2'>
            <HelpCircle className='h-5 w-5' />
            <span>{isEditing ? 'Edit Question' : 'Create New Question'}</span>
          </DialogTitle>
          <DialogDescription>
            {isEditing
              ? 'Update question details, options, and logic.'
              : 'Create a new interview question with options and conditional logic.'}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit}>
          {errors.submit && (
            <Alert variant='destructive' className='mb-4'>
              <AlertTriangle className='h-4 w-4' />
              <AlertDescription>{errors.submit}</AlertDescription>
            </Alert>
          )}

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className='grid w-full grid-cols-5 h-auto gap-1'>
              <TabsTrigger value='basic' className='text-xs px-1 py-2 min-w-0'>
                <span className='hidden sm:inline'>Basic Info</span>
                <span className='sm:hidden'>Basic</span>
              </TabsTrigger>
              <TabsTrigger
                value='options'
                className='text-xs px-1 py-2 min-w-0'
              >
                <span className='hidden sm:inline'>Options</span>
                <span className='sm:hidden'>Opts</span>
              </TabsTrigger>
              <TabsTrigger
                value='branching'
                className='text-xs px-1 py-2 min-w-0'
              >
                <span className='hidden sm:inline'>Branching</span>
                <span className='sm:hidden'>Branch</span>
              </TabsTrigger>
              <TabsTrigger value='video' className='text-xs px-1 py-2 min-w-0'>
                <span className='hidden sm:inline'>Educational Video</span>
                <span className='sm:hidden'>Video</span>
              </TabsTrigger>
              <TabsTrigger
                value='mapping'
                className='text-xs px-1 py-2 min-w-0'
              >
                <span className='hidden sm:inline'>Mapping</span>
                <span className='sm:hidden'>Map</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value='basic' className='space-y-4'>
              <Card>
                <CardHeader>
                  <CardTitle className='text-lg'>Question Details</CardTitle>
                </CardHeader>
                <CardContent className='space-y-4'>
                  <div className='space-y-2'>
                    <div className='flex items-center justify-between'>
                      <Label htmlFor='text'>
                        Question Text <span className='text-red-500'>*</span>
                      </Label>
                      <Select
                        onValueChange={value => {
                          const variable = USER_VARIABLES.find(
                            v => v.value === value
                          );
                          if (variable) insertVariableIntoText(variable);
                        }}
                      >
                        <SelectTrigger className='w-48'>
                          <SelectValue placeholder='Insert variable' />
                        </SelectTrigger>
                        <SelectContent>
                          <div className='max-h-[300px] overflow-y-auto'>
                            {USER_VARIABLES.map(variable => (
                              <SelectItem
                                key={variable.id}
                                value={variable.value}
                              >
                                {variable.label}
                              </SelectItem>
                            ))}
                          </div>
                        </SelectContent>
                      </Select>
                    </div>
                    <Textarea
                      ref={questionTextRef}
                      id='text'
                      value={formData.text}
                      onChange={e => handleFieldChange('text', e.target.value)}
                      placeholder='What is your full legal name?'
                      rows={2}
                    />
                    {errors.text && (
                      <p className='text-sm text-red-600'>{errors.text}</p>
                    )}
                  </div>

                  <div className='space-y-2'>
                    <Label htmlFor='type'>
                      Question Type <span className='text-red-500'>*</span>
                    </Label>
                    <Select
                      value={formData.type}
                      onValueChange={(value: QuestionType) =>
                        handleFieldChange('type', value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder='Select question type'>
                          {
                            QUESTION_TYPES.find(t => t.value === formData.type)
                              ?.label
                          }
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent>
                        {QUESTION_TYPES.map(type => (
                          <SelectItem key={type.value} value={type.value}>
                            <div>
                              <div className='font-medium'>{type.label}</div>
                              <div className='text-sm text-gray-500'>
                                {type.description}
                              </div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.type && (
                      <p className='text-sm text-red-600'>{errors.type}</p>
                    )}
                  </div>

                  {/* Validation Rule Selection - Only for text type questions */}
                  {formData.type === 'text' && (
                    <div className='space-y-2'>
                      <Label htmlFor='questionValidation'>
                        Validation Rule
                      </Label>
                      <Select
                        value={formData.questionValidation || '__none__'}
                        onValueChange={(value: string) =>
                          handleFieldChange(
                            'questionValidation',
                            value === '__none__'
                              ? undefined
                              : (value as QuestionValidationType)
                          )
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder='Select validation rule (optional)' />
                        </SelectTrigger>
                        <SelectContent>
                          <div className='max-h-[300px] overflow-y-auto'>
                            <SelectItem value='__none__'>
                              No validation
                            </SelectItem>
                            {VALIDATION_RULES.map(rule => (
                              <SelectItem key={rule.value} value={rule.value}>
                                {rule.label}
                              </SelectItem>
                            ))}
                          </div>
                        </SelectContent>
                      </Select>
                      {errors.questionValidation && (
                        <p className='text-sm text-red-600'>
                          {errors.questionValidation}
                        </p>
                      )}
                    </div>
                  )}

                  {/* Auto-fill Variable Selection - Only for text type questions */}
                  {formData.type === 'text' && (
                    <div className='space-y-2'>
                      <Label htmlFor='autoFillVariable'>
                        Auto-fill with User Data
                      </Label>
                      <Select
                        value={formData.autoFillVariable || '__none__'}
                        onValueChange={(value: string) =>
                          handleFieldChange(
                            'autoFillVariable',
                            value === '__none__' ? undefined : value
                          )
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder='Select user data to auto-fill (optional)'>
                            {formData.autoFillVariable
                              ? USER_VARIABLES.find(
                                  v => v.value === formData.autoFillVariable
                                )?.label
                              : 'No auto-fill'}
                          </SelectValue>
                        </SelectTrigger>
                        <SelectContent>
                          <div className='max-h-[300px] overflow-y-auto'>
                            <SelectItem value='__none__'>
                              No auto-fill
                            </SelectItem>
                            {USER_VARIABLES.map(variable => (
                              <SelectItem
                                key={variable.id}
                                value={variable.value}
                              >
                                {variable.label}
                              </SelectItem>
                            ))}
                          </div>
                        </SelectContent>
                      </Select>
                      {formData.autoFillVariable && (
                        <p className='text-sm text-blue-600'>
                          This field will be automatically filled with user data
                          when they start the interview. Validation rules are
                          disabled when auto-fill is enabled.
                        </p>
                      )}
                      {errors.autoFillVariable && (
                        <p className='text-sm text-red-600'>
                          {errors.autoFillVariable}
                        </p>
                      )}
                    </div>
                  )}

                  <div className='space-y-2'>
                    <Label htmlFor='defaultNextQuestionId'>
                      Default Next Question
                    </Label>
                    <Select
                      value={formData.defaultNextQuestionId || '__none__'}
                      onValueChange={value =>
                        handleFieldChange(
                          'defaultNextQuestionId',
                          value === '__none__' ? undefined : value
                        )
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder='Select next question' />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='__none__'>
                          {isConditionalQuestion()
                            ? 'Inherit from parent (will be saved)'
                            : 'End of interview'}
                        </SelectItem>
                        {availableQuestions.map(q => (
                          <SelectItem key={q.questionId} value={q.questionId}>
                            {q.text}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {isConditionalQuestion() && (
                      <p className='text-sm text-blue-600'>
                        This is a conditional question. When you select a next
                        question, both this question and its parent will be
                        updated to use the same defaultNextQuestionId value.
                      </p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value='options' className='space-y-4'>
              <Card>
                <CardHeader>
                  <CardTitle className='text-lg'>Answer Options</CardTitle>
                  <CardDescription>
                    {needsOptions
                      ? 'Define the available answer choices for this question.'
                      : 'This question type does not require predefined options.'}
                  </CardDescription>
                </CardHeader>
                <CardContent className='max-h-[400px] overflow-y-auto'>
                  {needsOptions ? (
                    <div className='space-y-4'>
                      {formData.options.map((option, index) => (
                        <div
                          key={option.id}
                          className='space-y-2 p-3 border rounded-lg'
                        >
                          <div className='flex items-center justify-between'>
                            <span className='text-sm font-medium'>
                              Option {index + 1}
                            </span>
                            <Button
                              type='button'
                              variant='ghost'
                              size='sm'
                              onClick={() => handleRemoveOption(index)}
                            >
                              <Trash2 className='h-4 w-4' />
                            </Button>
                          </div>

                          <div className='flex-1 grid grid-cols-2 gap-2'>
                            <Input
                              placeholder='Option label'
                              value={option.label}
                              onChange={e =>
                                handleUpdateOption(
                                  index,
                                  'label',
                                  e.target.value
                                )
                              }
                            />
                            <Input
                              placeholder='Option value'
                              value={option.value}
                              onChange={e =>
                                handleUpdateOption(
                                  index,
                                  'value',
                                  e.target.value
                                )
                              }
                            />
                          </div>
                        </div>
                      ))}

                      <Button
                        type='button'
                        variant='outline'
                        onClick={handleAddOption}
                        className='w-full'
                      >
                        <Plus className='mr-2 h-4 w-4' />
                        Add Option
                      </Button>

                      {errors.options && (
                        <p className='text-sm text-red-600'>{errors.options}</p>
                      )}
                    </div>
                  ) : (
                    <div className='text-center py-8 text-gray-500'>
                      <HelpCircle className='mx-auto h-8 w-8 mb-2' />
                      <p>
                        Text input questions don't require predefined options.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value='branching' className='space-y-4'>
              <Card>
                <CardHeader>
                  <CardTitle className='text-lg'>
                    Conditional Branching
                  </CardTitle>
                  <CardDescription>
                    {supportsBranching
                      ? 'Set up conditional logic to show different questions based on answers. When you set a specific branch for one answer, other answers will automatically continue to the next main question.'
                      : 'Branching logic is only available for radio and dropdown questions.'}
                  </CardDescription>
                </CardHeader>
                <CardContent className='max-h-[400px] overflow-y-auto'>
                  {supportsBranching && formData.options.length > 0 ? (
                    <div className='space-y-4'>
                      {formData.options.map((option, index) => {
                        return (
                          <div
                            key={option.id}
                            className='border rounded-lg p-4'
                          >
                            <div className='flex items-center justify-between mb-2'>
                              <div className='font-medium'>
                                {option.label || `Option ${index + 1}`}
                              </div>
                              <div className='flex items-center space-x-2'>
                                {option.nextQuestionId && (
                                  <Badge variant='secondary'>
                                    <ArrowRight className='mr-1 h-3 w-3' />
                                    Custom Branch
                                  </Badge>
                                )}
                              </div>
                            </div>
                            <div className='space-y-2'>
                              <Label>Next Question</Label>
                              <Select
                                value={option.nextQuestionId || '__none__'}
                                onValueChange={value =>
                                  handleSetBranching(
                                    index,
                                    value === '__none__' ? '' : value
                                  )
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder='Continue to next question' />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value='__none__'>
                                    Continue to next question
                                  </SelectItem>
                                  {availableQuestions.map(q => (
                                    <SelectItem
                                      key={q.questionId}
                                      value={q.questionId}
                                    >
                                      {q.text}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <div className='text-center py-8 text-gray-500'>
                      <ArrowRight className='mx-auto h-8 w-8 mb-2' />
                      <p>
                        {!supportsBranching
                          ? 'Branching is only available for radio and dropdown questions.'
                          : 'Add answer options first to set up branching logic.'}
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value='video' className='space-y-4'>
              <Card>
                <CardHeader>
                  <CardTitle className='text-lg'>Educational Video</CardTitle>
                  <CardDescription>
                    Select an educational video to display with this question to
                    help users understand the topic better.
                    {availableVideos.length > 0 ? (
                      <span className='text-green-600'>
                        {' '}
                        ({availableVideos.length} videos available)
                      </span>
                    ) : (
                      <span className='text-red-600'>
                        {' '}
                        (No videos available - create some in Content Management
                        first)
                      </span>
                    )}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className='space-y-4'>
                    <div className='space-y-2'>
                      <Label htmlFor='educationalVideoId'>Select Video</Label>

                      {/* Custom video search and select component */}
                      <div className='relative video-dropdown-container'>
                        <div className='relative'>
                          <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-[var(--foreground)] h-4 w-4' />
                          <Input
                            placeholder={
                              selectedVideo
                                ? selectedVideo.title
                                : 'Search and select video...'
                            }
                            value={videoSearchQuery}
                            onChange={e => {
                              setVideoSearchQuery(e.target.value);
                              setSelectedVideoIndex(-1);
                            }}
                            onFocus={() => setIsVideoDropdownOpen(true)}
                            onKeyDown={handleKeyDown}
                            className='pl-10 pr-10'
                          />
                          {videoSearchQuery && (
                            <Button
                              type='button'
                              variant='ghost'
                              size='sm'
                              className='absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0'
                              onClick={() => {
                                setVideoSearchQuery('');
                                setIsVideoDropdownOpen(true);
                              }}
                            >
                              <X className='h-4 w-4' />
                            </Button>
                          )}
                        </div>

                        {/* Dropdown with video list */}
                        {isVideoDropdownOpen && (
                          <div className='absolute top-full left-0 right-0 z-50 mt-1 bg-[var(--background)] border border-gray-200 rounded-md shadow-lg max-h-[300px] overflow-y-auto'>
                            {/* No video option */}
                            <div
                              className={`px-3 py-2  cursor-pointer border-b ${
                                selectedVideoIndex === 0
                                  ? 'bg-[var(--background)]'
                                  : ''
                              }`}
                              onClick={() => {
                                handleFieldChange(
                                  'educationalVideoId',
                                  undefined
                                );
                                setIsVideoDropdownOpen(false);
                                setVideoSearchQuery('');
                                setSelectedVideoIndex(-1);
                              }}
                            >
                              <div className='font-medium text-gray-600'>
                                No video
                              </div>
                            </div>

                            {filteredVideos.length === 0 ? (
                              <div className='px-3 py-2 text-gray-500'>
                                {videoSearchQuery
                                  ? 'No videos match your search'
                                  : 'No videos available'}
                              </div>
                            ) : (
                              filteredVideos.map((video, index) => (
                                <div
                                  key={video.id}
                                  className={`px-3 py-2 hover:bg-[var(--custom-gray-light)]/10 cursor-pointer ${
                                    formData.educationalVideoId === video.id
                                      ? 'border-l-4 border-blue-500'
                                      : ''
                                  } ${
                                    selectedVideoIndex === index + 1
                                      ? 'bg-blue-100'
                                      : ''
                                  }`}
                                  onClick={() => {
                                    handleFieldChange(
                                      'educationalVideoId',
                                      video.id
                                    );
                                    setIsVideoDropdownOpen(false);
                                    setVideoSearchQuery('');
                                    setSelectedVideoIndex(-1);
                                  }}
                                >
                                  <div className='font-medium truncate'>
                                    {video.title}
                                  </div>
                                  <div className='text-sm text-gray-500 truncate'>
                                    {video.description || 'No description'}
                                  </div>
                                </div>
                              ))
                            )}

                            {videoSearchQuery && (
                              <div className='px-3 py-2 text-xs text-gray-500 border-t bg-gray-50'>
                                Showing {filteredVideos.length} of{' '}
                                {availableVideos.length} videos
                              </div>
                            )}
                          </div>
                        )}
                      </div>

                      <p className='text-sm text-gray-500'>
                        The selected video will be displayed below the question
                        text when users are taking the interview.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value='mapping' className='space-y-4'>
              <Card>
                <CardHeader>
                  <CardTitle className='text-lg'>Question Mapping</CardTitle>
                  <CardDescription>
                    Map this question to a specific identifier for data
                    processing
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className='space-y-2'>
                    <Label htmlFor='questionMapping'>Map Name</Label>
                    <Input
                      id='questionMapping'
                      value={formData.questionMapping || ''}
                      onChange={e =>
                        handleFieldChange('questionMapping', e.target.value)
                      }
                      placeholder='e.g., full name, email address, phone number'
                    />
                    <p className='text-sm text-gray-500'>
                      Enter a unique identifier for this question. Spaces will
                      be automatically replaced with underscores. This will be
                      automatically formatted as{' '}
                      {'{{answers.your_mapping.value}}'} for use in templates
                      and data processing.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          <DialogFooter className='mt-6'>
            <Button
              type='button'
              variant='outline'
              onClick={onClose}
              disabled={saving}
            >
              Cancel
            </Button>
            <Button type='submit' disabled={saving}>
              {saving ? (
                <>
                  <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2' />
                  Saving...
                </>
              ) : (
                <>
                  <Save className='mr-2 h-4 w-4' />
                  {isEditing ? 'Update Question' : 'Create Question'}
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
