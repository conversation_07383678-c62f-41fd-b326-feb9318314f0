'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Plus,
  Edit,
  Trash2,
  GripVertical,
  MessageSquare,
  HelpCircle,
  ArrowRight,
  ArrowDown,
  Settings,
  Database,
  AlertTriangle,
  Save,
} from 'lucide-react';
import {
  Interview,
  InterviewQuestion,
  QuestionType,
  QuestionCategory,
} from '@/types/interview-builder';

interface InterviewFlowBuilderProps {
  interview: Interview;
  questions: InterviewQuestion[];
  onDataUpdated: () => void;
}

interface FlowNode {
  id: string;
  type: 'question' | 'logic' | 'variable' | 'end';
  data: any;
  position: { x: number; y: number };
  connections: string[];
}

export function InterviewFlowBuilder({
  interview,
  questions,
  onDataUpdated,
}: InterviewFlowBuilderProps) {
  const [selectedNode, setSelectedNode] = useState<FlowNode | null>(null);

  // Get the current version
  const currentVersion = interview.versions.find(
    v => v.version === interview.currentVersion
  );
  if (!currentVersion) {
    return <div>Current version not found</div>;
  }
  const [showAddQuestion, setShowAddQuestion] = useState(false);
  const [newQuestion, setNewQuestion] = useState({
    text: '',
    type: 'text' as QuestionType,
    category: 'personal' as QuestionCategory,
    required: false,
    variable: '',
    helpText: '',
    options: [] as string[],
  });

  // Convert questions to flow nodes
  const flowNodes: FlowNode[] = questions.map((question, index) => ({
    id: question.id,
    type: 'question',
    data: question,
    position: { x: 100, y: 100 + index * 150 },
    connections: [],
  }));

  const handleAddQuestion = () => {
    setShowAddQuestion(true);
    setNewQuestion({
      text: '',
      type: 'text',
      category: 'personal',
      required: false,
      variable: '',
      helpText: '',
      options: [],
    });
  };

  const handleSaveQuestion = async () => {
    // TODO: Implement save question logic
    console.log('Saving question:', newQuestion);
    setShowAddQuestion(false);
    onDataUpdated();
  };

  const handleEditQuestion = (question: InterviewQuestion) => {
    setSelectedNode({
      id: question.id,
      type: 'question',
      data: question,
      position: { x: 0, y: 0 },
      connections: [],
    });
  };

  const handleDeleteQuestion = async (questionId: string) => {
    if (confirm('Are you sure you want to delete this question?')) {
      // TODO: Implement delete logic
      console.log('Deleting question:', questionId);
      onDataUpdated();
    }
  };

  const QuestionTypeIcon = ({ type }: { type: QuestionType }) => {
    switch (type) {
      case 'text':
      case 'email':
      case 'phone':
        return <span className='text-lg'>📝</span>;
      case 'radio':
        return <span className='text-lg'>⚪</span>;
      case 'select':
        return <span className='text-lg'>📋</span>;
      case 'checkbox':
        return <span className='text-lg'>☑️</span>;
      case 'date':
        return <span className='text-lg'>📅</span>;
      case 'number':
        return <span className='text-lg'>🔢</span>;
      default:
        return <span className='text-lg'>❓</span>;
    }
  };

  return (
    <div className='space-y-6'>
      {/* Flow Builder Grid */}
      <div className='grid grid-cols-1 lg:grid-cols-3 gap-6'>
        {/* Traditional Flow View */}
        <div className='lg:col-span-2'>
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center justify-between'>
                <span className='flex items-center'>
                  <MessageSquare className='mr-2 h-5 w-5' />
                  Linear Flow View
                </span>
                <Button onClick={handleAddQuestion} size='sm'>
                  <Plus className='mr-2 h-4 w-4' />
                  Add Question
                </Button>
              </CardTitle>
              <CardDescription>
                Traditional linear view of your interview flow
              </CardDescription>
            </CardHeader>
            <CardContent>
              {questions.length === 0 ? (
                <div className='text-center py-12'>
                  <MessageSquare className='mx-auto h-12 w-12 text-[var(--custom-gray-medium)]' />
                  <h3 className='mt-2 text-sm font-medium text-[var(--custom-gray-dark)]'>
                    No questions yet
                  </h3>
                  <p className='mt-1 text-sm text-[var(--custom-gray-medium)]'>
                    Start building your interview by adding your first question.
                  </p>
                  <div className='mt-6'>
                    <Button onClick={handleAddQuestion}>
                      <Plus className='mr-2 h-4 w-4' />
                      Add First Question
                    </Button>
                  </div>
                </div>
              ) : (
                <div className='space-y-4'>
                  {/* Start Node */}
                  <div className='flex items-center'>
                    <div className='bg-green-100 border-2 border-green-300 rounded-lg p-4 min-w-[200px]'>
                      <div className='flex items-center'>
                        <div className='w-3 h-3 bg-green-500 rounded-full mr-2'></div>
                        <span className='font-medium text-green-800'>
                          Start Interview
                        </span>
                      </div>
                    </div>
                    <ArrowDown className='ml-4 h-5 w-5 text-[var(--custom-gray-medium)]' />
                  </div>

                  {/* Question Nodes */}
                  {questions.map((question, index) => (
                    <div key={question.id} className='space-y-2'>
                      <div className='flex items-center'>
                        <div className='bg-background border-2 border-blue-300 rounded-lg p-4 min-w-[300px] shadow-sm'>
                          <div className='flex items-start justify-between'>
                            <div className='flex items-start space-x-3'>
                              <QuestionTypeIcon type={question.type} />
                              <div className='flex-1'>
                                <div className='font-medium text-[var(--custom-gray-dark)] text-sm'>
                                  Q{index + 1}: {question.text}
                                </div>
                                <div className='text-xs text-[var(--custom-gray-medium)] mt-1'>
                                  Type: {question.type} | Category:{' '}
                                  {question.category}
                                </div>
                                {question.templateMapping && (
                                  <div className='text-xs text-blue-600 mt-1'>
                                    → Variable: {question.templateMapping}
                                  </div>
                                )}
                              </div>
                            </div>
                            <div className='flex items-center space-x-1'>
                              <Button
                                variant='ghost'
                                size='sm'
                                onClick={() => handleEditQuestion(question)}
                              >
                                <Edit className='h-3 w-3' />
                              </Button>
                              <Button
                                variant='ghost'
                                size='sm'
                                onClick={() =>
                                  handleDeleteQuestion(question.id)
                                }
                                className='text-red-600 hover:text-red-700'
                              >
                                <Trash2 className='h-3 w-3' />
                              </Button>
                            </div>
                          </div>
                        </div>
                        {index < questions.length - 1 && (
                          <ArrowDown className='ml-4 h-5 w-5 text-[var(--custom-gray-medium)]' />
                        )}
                      </div>
                    </div>
                  ))}

                  {/* End Node */}
                  <div className='flex items-center'>
                    <div className='bg-red-100 border-2 border-red-300 rounded-lg p-4 min-w-[200px]'>
                      <div className='flex items-center'>
                        <div className='w-3 h-3 bg-red-500 rounded-full mr-2'></div>
                        <span className='font-medium text-red-800'>
                          End Interview
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Properties Panel */}
        <div className='space-y-6'>
          {/* Add Question Form */}
          {showAddQuestion && (
            <Card>
              <CardHeader>
                <CardTitle className='text-lg'>Add New Question</CardTitle>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='space-y-2'>
                  <Label htmlFor='questionText'>Question Text</Label>
                  <Textarea
                    id='questionText'
                    value={newQuestion.text}
                    onChange={e =>
                      setNewQuestion(prev => ({
                        ...prev,
                        text: e.target.value,
                      }))
                    }
                    placeholder='What is your full legal name?'
                    rows={2}
                  />
                </div>

                <div className='grid grid-cols-2 gap-2'>
                  <div className='space-y-2'>
                    <Label htmlFor='questionType'>Type</Label>
                    <Select
                      value={newQuestion.type}
                      onValueChange={(value: QuestionType) =>
                        setNewQuestion(prev => ({ ...prev, type: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='text'>Text</SelectItem>
                        <SelectItem value='email'>Email</SelectItem>
                        <SelectItem value='phone'>Phone</SelectItem>
                        <SelectItem value='number'>Number</SelectItem>
                        <SelectItem value='date'>Date</SelectItem>
                        <SelectItem value='radio'>Radio</SelectItem>
                        <SelectItem value='select'>Select</SelectItem>
                        <SelectItem value='checkbox'>Checkbox</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className='space-y-2'>
                    <Label htmlFor='questionCategory'>Category</Label>
                    <Select
                      value={newQuestion.category}
                      onValueChange={(value: QuestionCategory) =>
                        setNewQuestion(prev => ({ ...prev, category: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='personal'>Personal</SelectItem>
                        <SelectItem value='financial'>Financial</SelectItem>
                        <SelectItem value='estate'>Estate</SelectItem>
                        <SelectItem value='emergency'>Emergency</SelectItem>
                        <SelectItem value='medical'>Medical</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className='space-y-2'>
                  <Label htmlFor='variable'>Variable Name</Label>
                  <Input
                    id='variable'
                    value={newQuestion.variable}
                    onChange={e =>
                      setNewQuestion(prev => ({
                        ...prev,
                        variable: e.target.value,
                      }))
                    }
                    placeholder='client_full_name'
                  />
                  <p className='text-xs text-[var(--custom-gray-medium)]'>
                    Variable name for storing the answer
                  </p>
                </div>

                <div className='space-y-2'>
                  <Label htmlFor='helpText'>Help Text</Label>
                  <Input
                    id='helpText'
                    value={newQuestion.helpText}
                    onChange={e =>
                      setNewQuestion(prev => ({
                        ...prev,
                        helpText: e.target.value,
                      }))
                    }
                    placeholder='Additional guidance for the user'
                  />
                </div>

                <div className='flex items-center space-x-2'>
                  <Checkbox
                    id='required'
                    checked={newQuestion.required}
                    onCheckedChange={checked =>
                      setNewQuestion(prev => ({ ...prev, required: !!checked }))
                    }
                  />
                  <Label htmlFor='required'>Required field</Label>
                </div>

                <div className='flex space-x-2'>
                  <Button onClick={handleSaveQuestion} size='sm'>
                    <Save className='mr-2 h-4 w-4' />
                    Add Question
                  </Button>
                  <Button
                    variant='outline'
                    onClick={() => setShowAddQuestion(false)}
                    size='sm'
                  >
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className='text-lg'>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className='space-y-2'>
              <Button
                variant='outline'
                className='w-full justify-start'
                onClick={handleAddQuestion}
              >
                <Plus className='mr-2 h-4 w-4' />
                Add Question
              </Button>
              <Button variant='outline' className='w-full justify-start'>
                <Database className='mr-2 h-4 w-4' />
                Add Variable
              </Button>
              <Button variant='outline' className='w-full justify-start'>
                <Settings className='mr-2 h-4 w-4' />
                Add Logic
              </Button>
            </CardContent>
          </Card>

          {/* Interview Stats */}
          <Card>
            <CardHeader>
              <CardTitle className='text-lg'>Interview Stats</CardTitle>
            </CardHeader>
            <CardContent className='space-y-3'>
              <div className='flex justify-between'>
                <span className='text-sm text-[var(--custom-gray-medium)]'>
                  Questions:
                </span>
                <span className='font-medium'>{questions.length}</span>
              </div>
              <div className='flex justify-between'>
                <span className='text-sm text-[var(--custom-gray-medium)]'>
                  Required:
                </span>
                <span className='font-medium'>
                  {questions.filter(q => q.required).length}
                </span>
              </div>
              <div className='flex justify-between'>
                <span className='text-sm text-[var(--custom-gray-medium)]'>
                  Variables:
                </span>
                <span className='font-medium'>
                  {questions.filter(q => q.templateMapping).length}
                </span>
              </div>
              <div className='flex justify-between'>
                <span className='text-sm text-[var(--custom-gray-medium)]'>
                  Est. Duration:
                </span>
                <span className='font-medium'>
                  {currentVersion.estimatedDuration} min
                </span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
