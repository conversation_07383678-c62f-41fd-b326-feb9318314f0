'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import {
  Edit,
  Copy,
  Trash2,
  Play,
  Clock,
  FileText,
  MoreHorizontal,
  ChevronLeft,
  ChevronRight,
  MessageSquare,
} from 'lucide-react';
// Temporarily removed dropdown menu - will implement later
import { Interview } from '@/types/interview-builder';

interface InterviewListTableProps {
  interviews: Interview[];
  loading: boolean;
  actionLoading: string | null;
  onEdit: (interview: Interview) => void;
  onBuild: (interview: Interview) => void;
  onPreview: (interview: Interview) => void;
  onDuplicate: (interview: Interview) => void;
  onDelete: (interview: Interview) => void;
  getStatusBadge: (
    status: 'draft' | 'published' | 'archived'
  ) => React.ReactNode;
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

export function InterviewListTable({
  interviews,
  loading,
  actionLoading,
  onEdit,
  onBuild,
  onPreview,
  onDuplicate,
  onDelete,
  getStatusBadge,
  currentPage,
  totalPages,
  onPageChange,
}: InterviewListTableProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes} min`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0
      ? `${hours}h ${remainingMinutes}m`
      : `${hours}h`;
  };

  if (loading) {
    return (
      <div className='space-y-3'>
        {[...Array(5)].map((_, i) => (
          <div key={i} className='h-16 bg-gray-100 rounded animate-pulse' />
        ))}
      </div>
    );
  }

  if (interviews.length === 0) {
    return (
      <div className='text-center py-12'>
        <FileText className='mx-auto h-12 w-12 text-[var(--custom-gray-medium)]' />
        <h3 className='mt-2 text-sm font-medium text-[var(--custom-gray-dark)]'>
          No interviews found
        </h3>
        <p className='mt-1 text-sm text-[var(--custom-gray-medium)]'>
          Get started by creating your first interview template.
        </p>
      </div>
    );
  }

  return (
    <div className='space-y-4'>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Interview Name</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Questions</TableHead>
            <TableHead>Duration</TableHead>
            <TableHead>Template</TableHead>
            <TableHead>Last Updated</TableHead>
            <TableHead>Version</TableHead>
            <TableHead className='text-right'>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {interviews.map(interview => {
            const currentVersion = interview.versions.find(
              v => v.version === interview.currentVersion
            );
            if (!currentVersion) return null;

            return (
              <TableRow key={interview.id} className='hover:bg-muted/50'>
                <TableCell>
                  <div>
                    <div className='font-medium text-[var(--custom-gray-dark)]'>
                      {currentVersion.name}
                    </div>
                    <div className='text-sm text-[var(--custom-gray-medium)] mt-1 max-w-xs truncate'>
                      {currentVersion.description}
                    </div>
                  </div>
                </TableCell>
                <TableCell>{getStatusBadge(currentVersion.status)}</TableCell>
                <TableCell>
                  <div className='flex items-center text-sm text-[var(--custom-gray-medium)]'>
                    <FileText className='h-4 w-4 mr-1' />
                    {currentVersion.totalQuestions || 0}
                  </div>
                </TableCell>
                <TableCell>
                  <div className='flex items-center text-sm text-[var(--custom-gray-medium)]'>
                    <Clock className='h-4 w-4 mr-1' />
                    {formatDuration(currentVersion.estimatedDuration)}
                  </div>
                </TableCell>
                <TableCell>
                  <div className='text-sm text-[var(--custom-gray-medium)]'>
                    {currentVersion.targetTemplate ? (
                      <Badge variant='outline' className='text-xs'>
                        {currentVersion.targetTemplate}
                      </Badge>
                    ) : (
                      <span className='text-[var(--custom-gray-medium)]'>
                        No template
                      </span>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <div className='text-sm text-[var(--custom-gray-medium)]'>
                    {formatDate(currentVersion.updatedAt)}
                  </div>
                  <div className='text-xs text-[var(--custom-gray-medium)]'>
                    by {currentVersion.createdBy || 'Unknown'}
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant='secondary' className='text-xs'>
                    v{currentVersion.version}
                  </Badge>
                </TableCell>
                <TableCell className='text-right'>
                  <div className='flex items-center space-x-1'>
                    <Button
                      variant='ghost'
                      size='sm'
                      onClick={() => onBuild(interview)}
                      disabled={actionLoading?.includes(interview.id)}
                      title='Visual Builder'
                    >
                      <MessageSquare className='h-4 w-4' />
                    </Button>
                    <Button
                      variant='ghost'
                      size='sm'
                      onClick={() => onEdit(interview)}
                      disabled={actionLoading?.includes(interview.id)}
                      title='Edit Interview'
                    >
                      <Edit className='h-4 w-4' />
                    </Button>
                    <Button
                      variant='ghost'
                      size='sm'
                      onClick={() => onPreview(interview)}
                      disabled={actionLoading?.includes(interview.id)}
                      title='Preview'
                    >
                      <Play className='h-4 w-4' />
                    </Button>
                    <Button
                      variant='ghost'
                      size='sm'
                      onClick={() => onDuplicate(interview)}
                      disabled={actionLoading === `duplicate-${interview.id}`}
                      title={
                        actionLoading === `duplicate-${interview.id}`
                          ? 'Duplicating...'
                          : 'Duplicate'
                      }
                    >
                      <Copy className='h-4 w-4' />
                    </Button>
                    <Button
                      variant='ghost'
                      size='sm'
                      onClick={() => onDelete(interview)}
                      disabled={actionLoading === `delete-${interview.id}`}
                      className='text-red-600 hover:text-red-700'
                      title={
                        actionLoading === `delete-${interview.id}`
                          ? 'Deleting...'
                          : 'Delete'
                      }
                    >
                      <Trash2 className='h-4 w-4' />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className='flex items-center justify-between'>
          <div className='text-sm text-[var(--custom-gray-medium)]'>
            Page {currentPage} of {totalPages}
          </div>
          <div className='flex items-center space-x-2'>
            <Button
              variant='outline'
              size='sm'
              onClick={() => onPageChange(currentPage - 1)}
              disabled={currentPage === 1}
            >
              <ChevronLeft className='h-4 w-4' />
              Previous
            </Button>
            <Button
              variant='outline'
              size='sm'
              onClick={() => onPageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              Next
              <ChevronRight className='h-4 w-4' />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
