'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Play,
  RotateCcw,
  ArrowLeft,
  ArrowRight,
  CheckCircle,
  HelpCircle,
  Eye,
  AlertTriangle,
} from 'lucide-react';
import {
  Interview,
  InterviewQuestion,
  QuestionType,
} from '@/types/interview-builder';

interface InterviewPreviewProps {
  interview: Interview;
  questions: InterviewQuestion[];
}

export function InterviewPreview({
  interview,
  questions,
}: InterviewPreviewProps) {
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [responses, setResponses] = useState<Record<string, any>>({});
  const [questionHistory, setQuestionHistory] = useState<number[]>([]);

  // Get the current version
  const currentVersion = interview.versions.find(
    v => v.version === interview.currentVersion
  );
  if (!currentVersion) {
    return <div>Current version not found</div>;
  }

  const sortedQuestions = [...questions].sort((a, b) => a.order - b.order);

  useEffect(() => {
    if (isPreviewMode && sortedQuestions.length > 0) {
      setCurrentQuestionIndex(0);
      setResponses({});
      setQuestionHistory([0]);
    }
  }, [isPreviewMode, sortedQuestions.length]);

  const startPreview = () => {
    if (sortedQuestions.length === 0) {
      return;
    }
    setIsPreviewMode(true);
  };

  const resetPreview = () => {
    setIsPreviewMode(false);
    setCurrentQuestionIndex(0);
    setResponses({});
    setQuestionHistory([]);
  };

  const handleResponse = (questionId: string, value: any) => {
    setResponses(prev => ({
      ...prev,
      [questionId]: value,
    }));
  };

  const goToNextQuestion = () => {
    const nextIndex = currentQuestionIndex + 1;
    if (nextIndex < sortedQuestions.length) {
      setCurrentQuestionIndex(nextIndex);
      setQuestionHistory(prev => [...prev, nextIndex]);
    }
  };

  const goToPreviousQuestion = () => {
    if (questionHistory.length > 1) {
      const newHistory = [...questionHistory];
      newHistory.pop(); // Remove current question
      const previousIndex = newHistory[newHistory.length - 1];
      setCurrentQuestionIndex(previousIndex);
      setQuestionHistory(newHistory);
    }
  };

  const isComplete = currentQuestionIndex >= sortedQuestions.length - 1;
  const progress =
    sortedQuestions.length > 0
      ? ((currentQuestionIndex + 1) / sortedQuestions.length) * 100
      : 0;

  const renderQuestionInput = (question: InterviewQuestion) => {
    const value = responses[question.id] || '';

    switch (question.type) {
      case 'text':
      case 'email':
      case 'phone':
      case 'number':
        return (
          <Input
            type={
              question.type === 'number'
                ? 'number'
                : question.type === 'email'
                  ? 'email'
                  : 'text'
            }
            value={value}
            onChange={e => handleResponse(question.id, e.target.value)}
            placeholder={
              question.placeholder ||
              `Enter your ${question.text.toLowerCase()}`
            }
            className='w-full text-lg'
          />
        );

      case 'date':
        return (
          <Input
            type='date'
            value={value}
            onChange={e => handleResponse(question.id, e.target.value)}
            className='w-full text-lg'
          />
        );

      case 'radio':
        return (
          <RadioGroup
            value={value}
            onValueChange={newValue => handleResponse(question.id, newValue)}
            className='space-y-3'
          >
            {question.options?.map(option => (
              <div key={option.id} className='flex items-center space-x-2'>
                <RadioGroupItem value={option.value} id={option.id} />
                <Label htmlFor={option.id} className='text-base cursor-pointer'>
                  {option.label}
                </Label>
              </div>
            ))}
          </RadioGroup>
        );

      case 'select':
        return (
          <Select
            value={value}
            onValueChange={newValue => handleResponse(question.id, newValue)}
          >
            <SelectTrigger className='w-full text-lg'>
              <SelectValue placeholder='Select an option' />
            </SelectTrigger>
            <SelectContent>
              {question.options?.map(option => (
                <SelectItem key={option.id} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case 'checkbox':
        const checkboxValues = Array.isArray(value) ? value : [];
        return (
          <div className='space-y-3'>
            {question.options?.map(option => (
              <div key={option.id} className='flex items-center space-x-2'>
                <Checkbox
                  id={option.id}
                  checked={checkboxValues.includes(option.value)}
                  onCheckedChange={checked => {
                    const newValues = checked
                      ? [...checkboxValues, option.value]
                      : checkboxValues.filter(
                          (v: string) => v !== option.value
                        );
                    handleResponse(question.id, newValues);
                  }}
                />
                <Label htmlFor={option.id} className='text-base cursor-pointer'>
                  {option.label}
                </Label>
              </div>
            ))}
          </div>
        );

      default:
        return (
          <div className='text-[var(--custom-gray-medium)] italic'>
            Preview not available for question type: {question.type}
          </div>
        );
    }
  };

  if (sortedQuestions.length === 0) {
    return (
      <Card>
        <CardContent className='text-center py-12'>
          <Eye className='mx-auto h-12 w-12 text-[var(--custom-gray-medium)]' />
          <h3 className='mt-2 text-sm font-medium text-[var(--custom-gray-dark)]'>
            No questions to preview
          </h3>
          <p className='mt-1 text-sm text-[var(--custom-gray-medium)]'>
            Add some questions to see how the interview will look to members.
          </p>
        </CardContent>
      </Card>
    );
  }

  if (!isPreviewMode) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center'>
            <Eye className='mr-2 h-5 w-5' />
            Interview Preview
          </CardTitle>
          <CardDescription>
            Test how the interview will appear to members
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='space-y-4'>
            <div className='grid md:grid-cols-3 gap-4'>
              <div className='text-center'>
                <div className='text-2xl font-bold text-blue-600'>
                  {sortedQuestions.length}
                </div>
                <div className='text-sm text-[var(--custom-gray-medium)]'>
                  Total Questions
                </div>
              </div>
              <div className='text-center'>
                <div className='text-2xl font-bold text-green-600'>
                  {currentVersion.estimatedDuration}
                </div>
                <div className='text-sm text-[var(--custom-gray-medium)]'>
                  Est. Minutes
                </div>
              </div>
              <div className='text-center'>
                <div className='text-2xl font-bold text-purple-600'>
                  {sortedQuestions.filter(q => q.required).length}
                </div>
                <div className='text-sm text-[var(--custom-gray-medium)]'>
                  Required
                </div>
              </div>
            </div>

            <Alert>
              <HelpCircle className='h-4 w-4' />
              <AlertDescription>
                Preview mode simulates the member experience. You can navigate
                through questions and test the flow logic.
              </AlertDescription>
            </Alert>

            <div className='flex justify-center'>
              <Button onClick={startPreview} size='lg'>
                <Play className='mr-2 h-5 w-5' />
                Start Preview
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const currentQuestion = sortedQuestions[currentQuestionIndex];

  return (
    <div className='space-y-6'>
      {/* Preview Header */}
      <Card>
        <CardContent className='p-4'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center space-x-4'>
              <Button onClick={resetPreview} variant='outline' size='sm'>
                <RotateCcw className='mr-2 h-4 w-4' />
                Reset Preview
              </Button>
              <div className='text-sm text-[var(--custom-gray-medium)]'>
                Question {currentQuestionIndex + 1} of {sortedQuestions.length}
              </div>
            </div>
            <div className='text-sm text-[var(--custom-gray-medium)]'>
              Preview Mode
            </div>
          </div>
          <div className='mt-3'>
            <Progress value={progress} className='w-full' />
          </div>
        </CardContent>
      </Card>

      {/* Question Card */}
      <Card className='max-w-2xl mx-auto'>
        <CardContent className='p-8'>
          <div className='space-y-6'>
            <div>
              <h2 className='text-2xl font-bold text-[var(--custom-gray-dark)] mb-2'>
                {currentQuestion.text}
                {currentQuestion.helpText && (
                  <div className='inline-block ml-2'>
                    <div className='group relative'>
                      <HelpCircle className='h-5 w-5 text-[var(--custom-gray-medium)] cursor-help' />
                      <div className='absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10'>
                        {currentQuestion.helpText}
                      </div>
                    </div>
                  </div>
                )}
              </h2>
              <p className='text-sm text-[var(--custom-gray-medium)]'>
                {currentQuestion.required ? '* Required' : 'Optional'}
              </p>
            </div>

            <div className='space-y-4'>
              {renderQuestionInput(currentQuestion)}
            </div>

            <div className='flex justify-between pt-4'>
              <Button
                variant='outline'
                onClick={goToPreviousQuestion}
                disabled={questionHistory.length <= 1}
              >
                <ArrowLeft className='mr-2 h-4 w-4' />
                Back
              </Button>

              {isComplete ? (
                <Button
                  onClick={resetPreview}
                  className='bg-green-600 hover:bg-green-700'
                >
                  <CheckCircle className='mr-2 h-4 w-4' />
                  Complete Preview
                </Button>
              ) : (
                <Button onClick={goToNextQuestion}>
                  Next
                  <ArrowRight className='ml-2 h-4 w-4' />
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Response Summary */}
      {Object.keys(responses).length > 0 && (
        <Card className='max-w-2xl mx-auto'>
          <CardHeader>
            <CardTitle className='text-lg'>Response Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className='space-y-2 text-sm'>
              {Object.entries(responses).map(([questionId, response]) => {
                const question = sortedQuestions.find(q => q.id === questionId);
                if (!question) return null;

                return (
                  <div key={questionId} className='flex justify-between'>
                    <span className='text-[var(--custom-gray-medium)] truncate max-w-xs'>
                      {question.text}
                    </span>
                    <span className='font-medium'>
                      {Array.isArray(response)
                        ? response.join(', ')
                        : response || '—'}
                    </span>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
