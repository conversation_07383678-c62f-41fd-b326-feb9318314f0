'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Plus,
  Edit,
  Trash2,
  GitBranch,
  ArrowRight,
  Settings,
  AlertTriangle,
  Save,
  Code,
  Zap,
} from 'lucide-react';
import {
  Interview,
  InterviewQuestion,
  ConditionalLogic,
} from '@/types/interview-builder';

interface LogicRule {
  id: string;
  name: string;
  description: string;
  sourceQuestionId: string;
  conditions: ConditionalLogic[];
  actions: LogicAction[];
  enabled: boolean;
}

interface LogicAction {
  type:
    | 'goto_question'
    | 'set_variable'
    | 'show_message'
    | 'end_interview'
    | 'skip_section';
  target: string;
  value?: any;
}

interface LogicBuilderProps {
  interview: Interview;
  questions: InterviewQuestion[];
  onDataUpdated: () => void;
}

export function LogicBuilder({
  interview,
  questions,
  onDataUpdated,
}: LogicBuilderProps) {
  const [logicRules, setLogicRules] = useState<LogicRule[]>([]);
  const [showAddRule, setShowAddRule] = useState(false);
  const [editingRule, setEditingRule] = useState<LogicRule | null>(null);
  const [newRule, setNewRule] = useState({
    name: '',
    description: '',
    sourceQuestionId: '',
    condition: 'equals' as ConditionalLogic['condition'],
    value: '',
    actionType: 'goto_question' as LogicAction['type'],
    actionTarget: '',
  });

  const handleAddRule = () => {
    setEditingRule(null);
    setNewRule({
      name: '',
      description: '',
      sourceQuestionId: '',
      condition: 'equals',
      value: '',
      actionType: 'goto_question',
      actionTarget: '',
    });
    setShowAddRule(true);
  };

  const handleEditRule = (rule: LogicRule) => {
    setEditingRule(rule);
    const firstCondition = rule.conditions[0];
    const firstAction = rule.actions[0];

    setNewRule({
      name: rule.name,
      description: rule.description,
      sourceQuestionId: rule.sourceQuestionId,
      condition: firstCondition?.condition || 'equals',
      value: firstCondition?.value?.toString() || '',
      actionType: firstAction?.type || 'goto_question',
      actionTarget: firstAction?.target || '',
    });
    setShowAddRule(true);
  };

  const handleSaveRule = () => {
    const rule: LogicRule = {
      id: editingRule?.id || `rule_${Date.now()}`,
      name: newRule.name,
      description: newRule.description,
      sourceQuestionId: newRule.sourceQuestionId,
      conditions: [
        {
          condition: newRule.condition,
          value: newRule.value,
          nextQuestionId:
            newRule.actionType === 'goto_question'
              ? newRule.actionTarget
              : null,
        },
      ],
      actions: [
        {
          type: newRule.actionType,
          target: newRule.actionTarget,
          value: newRule.value,
        },
      ],
      enabled: true,
    };

    if (editingRule) {
      setLogicRules(prev =>
        prev.map(r => (r.id === editingRule.id ? rule : r))
      );
    } else {
      setLogicRules(prev => [...prev, rule]);
    }

    setShowAddRule(false);
    onDataUpdated();
  };

  const handleDeleteRule = (ruleId: string) => {
    if (confirm('Are you sure you want to delete this logic rule?')) {
      setLogicRules(prev => prev.filter(r => r.id !== ruleId));
      onDataUpdated();
    }
  };

  const toggleRuleEnabled = (ruleId: string) => {
    setLogicRules(prev =>
      prev.map(r => (r.id === ruleId ? { ...r, enabled: !r.enabled } : r))
    );
    onDataUpdated();
  };

  const getQuestionText = (questionId: string) => {
    const question = questions.find(q => q.id === questionId);
    return question ? question.text : 'Unknown Question';
  };

  const getConditionDescription = (
    condition: ConditionalLogic['condition']
  ) => {
    switch (condition) {
      case 'equals':
        return 'equals';
      case 'not_equals':
        return 'does not equal';
      case 'contains':
        return 'contains';
      case 'greater_than':
        return 'is greater than';
      case 'less_than':
        return 'is less than';
      case 'is_empty':
        return 'is empty';
      case 'is_not_empty':
        return 'is not empty';
      default:
        return condition;
    }
  };

  const getActionDescription = (action: LogicAction) => {
    switch (action.type) {
      case 'goto_question':
        return `Go to: ${getQuestionText(action.target)}`;
      case 'set_variable':
        return `Set variable: ${action.target} = ${action.value}`;
      case 'show_message':
        return `Show message: ${action.value}`;
      case 'end_interview':
        return 'End interview';
      case 'skip_section':
        return `Skip section: ${action.target}`;
      default:
        return 'Unknown action';
    }
  };

  return (
    <div className='space-y-6'>
      {/* Traditional Logic Builder */}
      <div className='grid grid-cols-1 lg:grid-cols-3 gap-6'>
        {/* Logic Rules List */}
        <div className='lg:col-span-2'>
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center justify-between'>
                <span className='flex items-center'>
                  <GitBranch className='mr-2 h-5 w-5' />
                  Logic Rules
                </span>
                <Button onClick={handleAddRule} size='sm'>
                  <Plus className='mr-2 h-4 w-4' />
                  Add Rule
                </Button>
              </CardTitle>
              <CardDescription>
                Define conditional logic and branching for your interview
              </CardDescription>
            </CardHeader>
            <CardContent>
              {logicRules.length === 0 ? (
                <div className='text-center py-12'>
                  <GitBranch className='mx-auto h-12 w-12 text-[var(--custom-gray-medium)]' />
                  <h3 className='mt-2 text-sm font-medium text-[var(--custom-gray-dark)]'>
                    No logic rules defined
                  </h3>
                  <p className='mt-1 text-sm text-[var(--custom-gray-medium)]'>
                    Add logic rules to create conditional flows and dynamic
                    behavior.
                  </p>
                  <div className='mt-6'>
                    <Button onClick={handleAddRule}>
                      <Plus className='mr-2 h-4 w-4' />
                      Add First Rule
                    </Button>
                  </div>
                </div>
              ) : (
                <div className='space-y-4'>
                  {logicRules.map(rule => (
                    <Card
                      key={rule.id}
                      className={`border-l-4 ${rule.enabled ? 'border-l-green-500' : 'border-l-gray-300'}`}
                    >
                      <CardContent className='p-4'>
                        <div className='flex items-start justify-between'>
                          <div className='flex-1'>
                            <div className='flex items-center space-x-2 mb-2'>
                              <h4 className='font-medium text-[var(--custom-gray-dark)]'>
                                {rule.name}
                              </h4>
                              <Badge
                                variant={rule.enabled ? 'default' : 'secondary'}
                              >
                                {rule.enabled ? 'Enabled' : 'Disabled'}
                              </Badge>
                            </div>
                            <p className='text-sm text-[var(--custom-gray-medium)] mb-3'>
                              {rule.description}
                            </p>

                            <div className='space-y-2 text-sm'>
                              <div className='flex items-center space-x-2'>
                                <span className='font-medium'>When:</span>
                                <span className='text-blue-600'>
                                  {getQuestionText(rule.sourceQuestionId)}
                                </span>
                                <span>
                                  {getConditionDescription(
                                    rule.conditions[0]?.condition
                                  )}
                                </span>
                                <span className='font-mono bg-gray-100 px-2 py-1 rounded'>
                                  "{rule.conditions[0]?.value}"
                                </span>
                              </div>
                              <div className='flex items-center space-x-2'>
                                <ArrowRight className='h-4 w-4 text-[var(--custom-gray-medium)]' />
                                <span className='font-medium'>Then:</span>
                                <span className='text-green-600'>
                                  {getActionDescription(rule.actions[0])}
                                </span>
                              </div>
                            </div>
                          </div>

                          <div className='flex items-center space-x-1 ml-4'>
                            <Button
                              variant='ghost'
                              size='sm'
                              onClick={() => toggleRuleEnabled(rule.id)}
                              title={
                                rule.enabled ? 'Disable rule' : 'Enable rule'
                              }
                            >
                              <Zap
                                className={`h-4 w-4 ${rule.enabled ? 'text-green-600' : 'text-[var(--custom-gray-medium)]'}`}
                              />
                            </Button>
                            <Button
                              variant='ghost'
                              size='sm'
                              onClick={() => handleEditRule(rule)}
                            >
                              <Edit className='h-4 w-4' />
                            </Button>
                            <Button
                              variant='ghost'
                              size='sm'
                              onClick={() => handleDeleteRule(rule.id)}
                              className='text-red-600 hover:text-red-700'
                            >
                              <Trash2 className='h-4 w-4' />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Add/Edit Rule Form */}
        <div className='space-y-6'>
          {showAddRule && (
            <Card>
              <CardHeader>
                <CardTitle className='text-lg'>
                  {editingRule ? 'Edit Logic Rule' : 'Add Logic Rule'}
                </CardTitle>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='space-y-2'>
                  <Label htmlFor='ruleName'>Rule Name</Label>
                  <Input
                    id='ruleName'
                    value={newRule.name}
                    onChange={e =>
                      setNewRule(prev => ({ ...prev, name: e.target.value }))
                    }
                    placeholder='Skip to end if married'
                  />
                </div>

                <div className='space-y-2'>
                  <Label htmlFor='ruleDescription'>Description</Label>
                  <Input
                    id='ruleDescription'
                    value={newRule.description}
                    onChange={e =>
                      setNewRule(prev => ({
                        ...prev,
                        description: e.target.value,
                      }))
                    }
                    placeholder='Describe what this rule does'
                  />
                </div>

                <div className='space-y-2'>
                  <Label htmlFor='sourceQuestion'>Source Question</Label>
                  <Select
                    value={newRule.sourceQuestionId}
                    onValueChange={value =>
                      setNewRule(prev => ({ ...prev, sourceQuestionId: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select question' />
                    </SelectTrigger>
                    <SelectContent>
                      {questions.map(question => (
                        <SelectItem key={question.id} value={question.id}>
                          {question.text}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className='grid grid-cols-2 gap-2'>
                  <div className='space-y-2'>
                    <Label htmlFor='condition'>Condition</Label>
                    <Select
                      value={newRule.condition}
                      onValueChange={(value: ConditionalLogic['condition']) =>
                        setNewRule(prev => ({ ...prev, condition: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='equals'>Equals</SelectItem>
                        <SelectItem value='not_equals'>Not Equals</SelectItem>
                        <SelectItem value='contains'>Contains</SelectItem>
                        <SelectItem value='greater_than'>
                          Greater Than
                        </SelectItem>
                        <SelectItem value='less_than'>Less Than</SelectItem>
                        <SelectItem value='is_empty'>Is Empty</SelectItem>
                        <SelectItem value='is_not_empty'>
                          Is Not Empty
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className='space-y-2'>
                    <Label htmlFor='conditionValue'>Value</Label>
                    <Input
                      id='conditionValue'
                      value={newRule.value}
                      onChange={e =>
                        setNewRule(prev => ({ ...prev, value: e.target.value }))
                      }
                      placeholder='Comparison value'
                    />
                  </div>
                </div>

                <div className='space-y-2'>
                  <Label htmlFor='actionType'>Action</Label>
                  <Select
                    value={newRule.actionType}
                    onValueChange={(value: LogicAction['type']) =>
                      setNewRule(prev => ({ ...prev, actionType: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='goto_question'>
                        Go to Question
                      </SelectItem>
                      <SelectItem value='set_variable'>Set Variable</SelectItem>
                      <SelectItem value='show_message'>Show Message</SelectItem>
                      <SelectItem value='end_interview'>
                        End Interview
                      </SelectItem>
                      <SelectItem value='skip_section'>Skip Section</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {newRule.actionType === 'goto_question' && (
                  <div className='space-y-2'>
                    <Label htmlFor='targetQuestion'>Target Question</Label>
                    <Select
                      value={newRule.actionTarget}
                      onValueChange={value =>
                        setNewRule(prev => ({ ...prev, actionTarget: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder='Select target question' />
                      </SelectTrigger>
                      <SelectContent>
                        {questions.map(question => (
                          <SelectItem key={question.id} value={question.id}>
                            {question.text}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}

                <div className='flex space-x-2'>
                  <Button onClick={handleSaveRule} size='sm'>
                    <Save className='mr-2 h-4 w-4' />
                    {editingRule ? 'Update' : 'Add'} Rule
                  </Button>
                  <Button
                    variant='outline'
                    onClick={() => setShowAddRule(false)}
                    size='sm'
                  >
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Logic Templates */}
          <Card>
            <CardHeader>
              <CardTitle className='text-lg flex items-center'>
                <Code className='mr-2 h-5 w-5' />
                Logic Templates
              </CardTitle>
              <CardDescription>
                Common logic patterns you can use
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-3'>
              <div className='p-3 border rounded text-sm'>
                <div className='font-medium'>Skip if condition met</div>
                <div className='text-[var(--custom-gray-medium)] text-xs'>
                  Skip questions based on previous answers
                </div>
              </div>
              <div className='p-3 border rounded text-sm'>
                <div className='font-medium'>Conditional sections</div>
                <div className='text-[var(--custom-gray-medium)] text-xs'>
                  Show different question sets based on user type
                </div>
              </div>
              <div className='p-3 border rounded text-sm'>
                <div className='font-medium'>Variable calculations</div>
                <div className='text-[var(--custom-gray-medium)] text-xs'>
                  Set computed values based on answers
                </div>
              </div>
              <div className='p-3 border rounded text-sm'>
                <div className='font-medium'>Early termination</div>
                <div className='text-[var(--custom-gray-medium)] text-xs'>
                  End interview if certain conditions are met
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Logic Stats */}
          <Card>
            <CardHeader>
              <CardTitle className='text-lg'>Logic Stats</CardTitle>
            </CardHeader>
            <CardContent className='space-y-3'>
              <div className='flex justify-between'>
                <span className='text-sm text-[var(--custom-gray-medium)]'>
                  Total Rules:
                </span>
                <span className='font-medium'>{logicRules.length}</span>
              </div>
              <div className='flex justify-between'>
                <span className='text-sm text-[var(--custom-gray-medium)]'>
                  Enabled:
                </span>
                <span className='font-medium'>
                  {logicRules.filter(r => r.enabled).length}
                </span>
              </div>
              <div className='flex justify-between'>
                <span className='text-sm text-[var(--custom-gray-medium)]'>
                  Questions with Logic:
                </span>
                <span className='font-medium'>
                  {new Set(logicRules.map(r => r.sourceQuestionId)).size}
                </span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
