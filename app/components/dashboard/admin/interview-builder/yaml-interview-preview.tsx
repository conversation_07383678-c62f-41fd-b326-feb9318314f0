'use client';

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Play,
  ArrowRight,
  ArrowLeft,
  CheckCircle,
  AlertCircle,
  RotateCcw,
  Eye,
} from 'lucide-react';
import {
  YAMLInterviewParser,
  ParsedInterview,
  YAMLQuestion,
} from '@/lib/yaml-interview-parser';

interface YAMLInterviewPreviewProps {
  yamlContent: string;
}

interface InterviewState {
  currentQuestionIndex: number;
  answers: Record<string, any>;
  completed: boolean;
  error: string | null;
}

export function YAMLInterviewPreview({
  yamlContent,
}: YAMLInterviewPreviewProps) {
  const [parsedInterview, setParsedInterview] =
    useState<ParsedInterview | null>(null);
  const [parseError, setParseError] = useState<string | null>(null);
  const [interviewState, setInterviewState] = useState<InterviewState>({
    currentQuestionIndex: 0,
    answers: {},
    completed: false,
    error: null,
  });

  // Parse YAML when content changes
  useEffect(() => {
    try {
      if (!yamlContent.trim()) {
        setParsedInterview(null);
        setParseError(null);
        return;
      }

      const parsed = YAMLInterviewParser.parseYAML(yamlContent);
      setParsedInterview(parsed);
      setParseError(null);

      // Reset interview state
      setInterviewState({
        currentQuestionIndex: 0,
        answers: {},
        completed: false,
        error: null,
      });
    } catch (error) {
      console.error('Failed to parse YAML:', error);
      setParseError(
        error instanceof Error ? error.message : 'Unknown parsing error'
      );
      setParsedInterview(null);
    }
  }, [yamlContent]);

  const getCurrentQuestion = (): YAMLQuestion | null => {
    if (!parsedInterview || interviewState.completed) return null;

    // Filter questions based on conditions
    const availableQuestions = parsedInterview.questions.filter(question => {
      if (question.show_if) {
        return evaluateCondition(question.show_if, interviewState.answers);
      }
      if (question.depends_on) {
        return evaluateCondition(question.depends_on, interviewState.answers);
      }
      return true;
    });

    return availableQuestions[interviewState.currentQuestionIndex] || null;
  };

  const evaluateCondition = (
    condition: string,
    answers: Record<string, any>
  ): boolean => {
    try {
      // Simple condition evaluation - in a real implementation, this would be more robust
      const conditionStr = condition
        .replace(/(\w+)\s*==\s*"([^"]+)"/g, (match, field, value) => {
          return `"${answers[field]}" === "${value}"`;
        })
        .replace(/(\w+)\s*!=\s*"([^"]+)"/g, (match, field, value) => {
          return `"${answers[field]}" !== "${value}"`;
        })
        .replace(/(\w+)/g, (match, field) => {
          return answers[field] ? 'true' : 'false';
        });

      return eval(conditionStr);
    } catch (error) {
      console.warn('Failed to evaluate condition:', condition, error);
      return true;
    }
  };

  const handleAnswer = (field: string, value: any) => {
    setInterviewState(prev => ({
      ...prev,
      answers: {
        ...prev.answers,
        [field]: value,
      },
    }));
  };

  const nextQuestion = () => {
    if (!parsedInterview) return;

    const availableQuestions = parsedInterview.questions.filter(question => {
      if (question.show_if) {
        return evaluateCondition(question.show_if, interviewState.answers);
      }
      if (question.depends_on) {
        return evaluateCondition(question.depends_on, interviewState.answers);
      }
      return true;
    });

    if (interviewState.currentQuestionIndex < availableQuestions.length - 1) {
      setInterviewState(prev => ({
        ...prev,
        currentQuestionIndex: prev.currentQuestionIndex + 1,
      }));
    } else {
      setInterviewState(prev => ({
        ...prev,
        completed: true,
      }));
    }
  };

  const previousQuestion = () => {
    if (interviewState.currentQuestionIndex > 0) {
      setInterviewState(prev => ({
        ...prev,
        currentQuestionIndex: prev.currentQuestionIndex - 1,
        completed: false,
      }));
    }
  };

  const restartInterview = () => {
    setInterviewState({
      currentQuestionIndex: 0,
      answers: {},
      completed: false,
      error: null,
    });
  };

  const renderQuestionInput = (question: YAMLQuestion) => {
    const currentValue = interviewState.answers[question.field] || '';

    switch (question.datatype) {
      case 'date':
        return (
          <Input
            type='date'
            value={currentValue}
            onChange={e => handleAnswer(question.field, e.target.value)}
            className='mt-2'
          />
        );

      case 'number':
      case 'currency':
        return (
          <Input
            type='number'
            value={currentValue}
            onChange={e => handleAnswer(question.field, e.target.value)}
            placeholder='Enter a number'
            className='mt-2'
          />
        );

      case 'email':
        return (
          <Input
            type='email'
            value={currentValue}
            onChange={e => handleAnswer(question.field, e.target.value)}
            placeholder='Enter email address'
            className='mt-2'
          />
        );

      case 'phone':
        return (
          <Input
            type='tel'
            value={currentValue}
            onChange={e => handleAnswer(question.field, e.target.value)}
            placeholder='Enter phone number'
            className='mt-2'
          />
        );

      case 'area':
        return (
          <Textarea
            value={currentValue}
            onChange={e => handleAnswer(question.field, e.target.value)}
            placeholder='Enter your response'
            className='mt-2'
            rows={4}
          />
        );

      case 'yesno':
        return (
          <div className='mt-2 space-y-2'>
            <div className='flex items-center space-x-2'>
              <Checkbox
                id={`${question.field}_yes`}
                checked={currentValue === true}
                onCheckedChange={() => handleAnswer(question.field, true)}
              />
              <label htmlFor={`${question.field}_yes`}>Yes</label>
            </div>
            <div className='flex items-center space-x-2'>
              <Checkbox
                id={`${question.field}_no`}
                checked={currentValue === false}
                onCheckedChange={() => handleAnswer(question.field, false)}
              />
              <label htmlFor={`${question.field}_no`}>No</label>
            </div>
          </div>
        );

      case 'checkboxes':
        return (
          <div className='mt-2 space-y-2'>
            {question.choices?.map((choice, index) => (
              <div key={index} className='flex items-center space-x-2'>
                <Checkbox
                  id={`${question.field}_${index}`}
                  checked={
                    Array.isArray(currentValue) &&
                    currentValue.includes(choice.value)
                  }
                  onCheckedChange={checked => {
                    const currentArray = Array.isArray(currentValue)
                      ? currentValue
                      : [];
                    if (checked) {
                      handleAnswer(question.field, [
                        ...currentArray,
                        choice.value,
                      ]);
                    } else {
                      handleAnswer(
                        question.field,
                        currentArray.filter(v => v !== choice.value)
                      );
                    }
                  }}
                />
                <label htmlFor={`${question.field}_${index}`}>
                  {choice.label}
                </label>
              </div>
            ))}
          </div>
        );

      default:
        if (question.choices && question.choices.length > 0) {
          return (
            <div className='mt-2 space-y-2'>
              {question.choices.map((choice, index) => (
                <div key={index} className='flex items-center space-x-2'>
                  <input
                    type='radio'
                    id={`${question.field}_${index}`}
                    name={question.field}
                    value={choice.value}
                    checked={currentValue === choice.value}
                    onChange={() => handleAnswer(question.field, choice.value)}
                  />
                  <label htmlFor={`${question.field}_${index}`}>
                    {choice.label}
                  </label>
                </div>
              ))}
            </div>
          );
        }

        return (
          <Input
            type='text'
            value={currentValue}
            onChange={e => handleAnswer(question.field, e.target.value)}
            placeholder='Enter your response'
            className='mt-2'
          />
        );
    }
  };

  if (parseError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center'>
            <AlertCircle className='mr-2 h-5 w-5 text-red-500' />
            Preview Error
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='text-red-600'>
            <p className='font-medium'>
              Cannot preview interview due to YAML errors:
            </p>
            <p className='text-sm mt-2'>{parseError}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!parsedInterview) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center'>
            <Eye className='mr-2 h-5 w-5' />
            Interview Preview
          </CardTitle>
          <CardDescription>
            No interview loaded. Add YAML content to see the preview.
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  const currentQuestion = getCurrentQuestion();
  const availableQuestions = parsedInterview.questions.filter(question => {
    if (question.show_if) {
      return evaluateCondition(question.show_if, interviewState.answers);
    }
    if (question.depends_on) {
      return evaluateCondition(question.depends_on, interviewState.answers);
    }
    return true;
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex items-center justify-between'>
          <span className='flex items-center'>
            <Play className='mr-2 h-5 w-5' />
            {parsedInterview.metadata.title}
          </span>
          <div className='flex items-center space-x-2'>
            <Badge variant='outline'>
              {interviewState.completed
                ? 'Completed'
                : `${interviewState.currentQuestionIndex + 1} of ${availableQuestions.length}`}
            </Badge>
            <Button onClick={restartInterview} size='sm' variant='outline'>
              <RotateCcw className='mr-2 h-4 w-4' />
              Restart
            </Button>
          </div>
        </CardTitle>
        <CardDescription>
          {parsedInterview.metadata.description}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {interviewState.completed ? (
          <div className='text-center py-8'>
            <CheckCircle className='mx-auto h-12 w-12 text-green-500 mb-4' />
            <h3 className='text-lg font-medium text-[var(--custom-gray-dark)] mb-2'>
              Interview Completed!
            </h3>
            <p className='text-[var(--custom-gray-medium)] mb-4'>
              Thank you for completing the interview.
            </p>

            <div className='text-left bg-gray-50 p-4 rounded-md mt-6'>
              <h4 className='font-medium mb-2'>Collected Answers:</h4>
              <div className='space-y-1 text-sm'>
                {Object.entries(interviewState.answers).map(
                  ([field, value]) => (
                    <div key={field} className='flex justify-between'>
                      <span className='font-mono text-[var(--custom-gray-medium)]'>
                        {field}:
                      </span>
                      <span>{String(value)}</span>
                    </div>
                  )
                )}
              </div>
            </div>
          </div>
        ) : currentQuestion ? (
          <div className='space-y-6'>
            <div>
              <h3 className='text-lg font-medium text-[var(--custom-gray-dark)] mb-2'>
                {currentQuestion.question}
              </h3>
              {currentQuestion.subquestion && (
                <p className='text-[var(--custom-gray-medium)] text-sm mb-4'>
                  {currentQuestion.subquestion}
                </p>
              )}
              {currentQuestion.required && (
                <Badge variant='destructive' className='text-xs mb-2'>
                  Required
                </Badge>
              )}

              {renderQuestionInput(currentQuestion)}
            </div>

            <div className='flex justify-between'>
              <Button
                onClick={previousQuestion}
                variant='outline'
                disabled={interviewState.currentQuestionIndex === 0}
              >
                <ArrowLeft className='mr-2 h-4 w-4' />
                Previous
              </Button>

              <Button
                onClick={nextQuestion}
                disabled={
                  currentQuestion.required &&
                  !interviewState.answers[currentQuestion.field]
                }
              >
                {interviewState.currentQuestionIndex ===
                availableQuestions.length - 1
                  ? 'Complete'
                  : 'Next'}
                <ArrowRight className='ml-2 h-4 w-4' />
              </Button>
            </div>
          </div>
        ) : (
          <div className='text-center py-8'>
            <AlertCircle className='mx-auto h-12 w-12 text-yellow-500 mb-4' />
            <h3 className='text-lg font-medium text-[var(--custom-gray-dark)] mb-2'>
              No Questions Available
            </h3>
            <p className='text-[var(--custom-gray-medium)]'>
              The interview doesn't contain any questions to display.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
