'use client';

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  AlertTriangle,
  Check,
  Plus,
  Trash2,
  Edit,
  ExternalLink,
  User,
  Calendar,
  Mail,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';
import { toast } from 'sonner';
import {
  getNonChildfreeUsers,
  getAdminResources,
  createAdminResource,
  updateAdminResource,
  deleteAdminResource,
  type NonChildfreeUser,
  type AdminResource,
} from '@/app/utils/adminResources';

interface NonChildfreeResponsesModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function NonChildfreeResponsesModal({
  open,
  onOpenChange,
}: NonChildfreeResponsesModalProps) {
  const [nonChildfreeUsers, setNonChildfreeUsers] = useState<
    NonChildfreeUser[]
  >([]);
  const [adminResources, setAdminResources] = useState<AdminResource[]>([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('users');

  // Pagination state for users
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);

  // Resource form state
  const [isAddingResource, setIsAddingResource] = useState(false);
  const [editingResource, setEditingResource] = useState<AdminResource | null>(
    null
  );
  const [resourceForm, setResourceForm] = useState({
    title: '',
    description: '',
    url: '',
    resourceType: 'website' as AdminResource['resourceType'],
    displayOrder: 0,
  });

  // Load data when modal opens
  useEffect(() => {
    if (open) {
      loadData();
      setCurrentPage(1); // Reset to first page when modal opens
    }
  }, [open]);

  // Calculate pagination data for users
  const totalUsers = nonChildfreeUsers.length;
  const totalPages = Math.ceil(totalUsers / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedUsers = nonChildfreeUsers.slice(startIndex, endIndex);

  const handlePreviousPage = () => {
    setCurrentPage(prev => Math.max(prev - 1, 1));
  };

  const handleNextPage = () => {
    setCurrentPage(prev => Math.min(prev + 1, totalPages));
  };

  const loadData = async () => {
    setLoading(true);
    try {
      const [users, resources] = await Promise.all([
        getNonChildfreeUsers(),
        getAdminResources(),
      ]);
      setNonChildfreeUsers(users);
      setAdminResources(resources);
    } catch (error) {
      console.error('Error loading data:', error);
      toast.error('Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveResource = async () => {
    try {
      if (editingResource) {
        // Update existing resource
        const updated = await updateAdminResource(editingResource.id, {
          ...resourceForm,
          isActive: true,
        });
        setAdminResources(prev =>
          prev.map(r => (r.id === editingResource.id ? updated : r))
        );
        toast.success('Resource updated successfully');
      } else {
        // Create new resource
        const newResource = await createAdminResource({
          ...resourceForm,
          isActive: true,
        });
        setAdminResources(prev => [...prev, newResource]);
        toast.success('Resource created successfully');
      }

      // Reset form
      setResourceForm({
        title: '',
        description: '',
        url: '',
        resourceType: 'website',
        displayOrder: 0,
      });
      setIsAddingResource(false);
      setEditingResource(null);
    } catch (error) {
      console.error('Error saving resource:', error);
      toast.error('Failed to save resource');
    }
  };

  const handleEditResource = (resource: AdminResource) => {
    setResourceForm({
      title: resource.title,
      description: resource.description || '',
      url: resource.url,
      resourceType: resource.resourceType,
      displayOrder: resource.displayOrder,
    });
    setEditingResource(resource);
    setIsAddingResource(true);
  };

  const handleDeleteResource = async (resourceId: string) => {
    try {
      await deleteAdminResource(resourceId);
      setAdminResources(prev => prev.filter(r => r.id !== resourceId));
      toast.success('Resource deleted successfully');
    } catch (error) {
      console.error('Error deleting resource:', error);
      toast.error('Failed to delete resource');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getResourceTypeIcon = (type: AdminResource['resourceType']) => {
    switch (type) {
      case 'person':
        return <User className='h-4 w-4' />;
      case 'organization':
        return <User className='h-4 w-4' />;
      default:
        return <ExternalLink className='h-4 w-4' />;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='!max-w-[95vw] !w-[50vw] max-h-[90vh] overflow-y-auto top-[8vh] !translate-y-0'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <AlertTriangle className='h-5 w-5 text-orange-500' />
            Non-Childfree Responses Management
          </DialogTitle>
          <DialogDescription>
            Manage users who indicated they have children and configure
            resources to show them.
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className='w-full'>
          <TabsList className='grid w-full grid-cols-2'>
            <TabsTrigger value='users'>
              Non-Childfree Users ({nonChildfreeUsers.length})
            </TabsTrigger>
            <TabsTrigger value='resources'>
              Admin Resources ({adminResources.length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value='users' className='space-y-4'>
            <Card>
              <CardHeader>
                <CardTitle className='text-lg'>
                  Users with Non-Childfree Responses
                </CardTitle>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className='text-center py-8'>Loading...</div>
                ) : nonChildfreeUsers.length === 0 ? (
                  <div className='text-center py-8 text-muted-foreground'>
                    No users with non-childfree responses found.
                  </div>
                ) : (
                  <div className='space-y-4'>
                    <div className='overflow-x-auto'>
                      <Table className='table-fixed w-full'>
                        <TableHeader>
                          <TableRow>
                            <TableHead className='w-[100px] max-w-[100px]'>
                              User
                            </TableHead>
                            <TableHead className='w-[150px] max-w-[150px]'>
                              Email
                            </TableHead>
                            <TableHead className='w-[180px] max-w-[180px]'>
                              Response Date
                            </TableHead>
                            <TableHead className='w-[120px] max-w-[120px]'>
                              Status
                            </TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {paginatedUsers.map(user => (
                            <TableRow key={user.id}>
                              <TableCell className='w-[100px] max-w-[100px] overflow-hidden'>
                                <div className='flex items-center gap-2 min-w-0'>
                                  <User className='h-4 w-4 text-muted-foreground flex-shrink-0' />
                                  <span
                                    className='font-medium truncate min-w-0'
                                    title={`${user.firstName} ${user.lastName}`}
                                  >
                                    {user.firstName} {user.lastName}
                                  </span>
                                </div>
                              </TableCell>
                              <TableCell className='w-[150px] max-w-[150px] overflow-hidden'>
                                <div className='flex items-center gap-2 min-w-0'>
                                  <Mail className='h-4 w-4 text-muted-foreground flex-shrink-0' />
                                  <span
                                    className='truncate min-w-0'
                                    title={user.email}
                                  >
                                    {user.email}
                                  </span>
                                </div>
                              </TableCell>
                              <TableCell className='w-[180px] max-w-[180px] overflow-hidden'>
                                <div className='flex items-center gap-2 min-w-0'>
                                  <Calendar className='h-4 w-4 text-muted-foreground flex-shrink-0' />
                                  <span className='truncate min-w-0'>
                                    {formatDate(user.createdAt)}
                                  </span>
                                </div>
                              </TableCell>
                              <TableCell className='w-[120px] max-w-[120px] overflow-hidden'>
                                <Badge
                                  className='text-[var(--off-white)] truncate'
                                  variant='destructive'
                                >
                                  Has Children
                                </Badge>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>

                    {/* Pagination Controls */}
                    {totalPages > 1 && (
                      <div className='flex items-center justify-between px-2'>
                        <div className='text-sm text-muted-foreground'>
                          Showing {startIndex + 1} to{' '}
                          {Math.min(endIndex, totalUsers)} of {totalUsers} users
                        </div>
                        <div className='flex items-center gap-2'>
                          <Button
                            variant='outline'
                            size='sm'
                            onClick={handlePreviousPage}
                            disabled={currentPage === 1}
                            className='flex items-center gap-1'
                          >
                            <ChevronLeft className='h-3 w-3' />
                            Previous
                          </Button>
                          <div className='flex items-center gap-1'>
                            <span className='text-sm'>
                              Page {currentPage} of {totalPages}
                            </span>
                          </div>
                          <Button
                            variant='outline'
                            size='sm'
                            onClick={handleNextPage}
                            disabled={currentPage === totalPages}
                            className='flex items-center gap-1'
                          >
                            Next
                            <ChevronRight className='h-3 w-3' />
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value='resources' className='space-y-4'>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between'>
                <CardTitle className='text-lg'>Admin Resources</CardTitle>
                <Button
                  onClick={() => setIsAddingResource(true)}
                  size='sm'
                  className='flex items-center gap-1'
                >
                  <Plus className='h-3 w-3' />
                  Add Resource
                </Button>
              </CardHeader>
              <CardContent>
                {isAddingResource && (
                  <div className='border rounded-lg p-4 mb-4 space-y-4'>
                    <h4 className='font-medium'>
                      {editingResource ? 'Edit Resource' : 'Add New Resource'}
                    </h4>
                    <div className='grid grid-cols-2 gap-4'>
                      <div>
                        <Label className='pb-1' htmlFor='title'>
                          Title
                        </Label>
                        <Input
                          id='title'
                          value={resourceForm.title}
                          onChange={e =>
                            setResourceForm(prev => ({
                              ...prev,
                              title: e.target.value,
                            }))
                          }
                          placeholder='Resource title'
                        />
                      </div>
                      <div>
                        <Label className='pb-1' htmlFor='url'>
                          URL
                        </Label>
                        <Input
                          id='url'
                          value={resourceForm.url}
                          onChange={e =>
                            setResourceForm(prev => ({
                              ...prev,
                              url: e.target.value,
                            }))
                          }
                          placeholder='https://example.com'
                        />
                      </div>
                    </div>
                    <div>
                      <Label className='pb-1' htmlFor='description'>
                        Description
                      </Label>
                      <Textarea
                        id='description'
                        value={resourceForm.description}
                        onChange={e =>
                          setResourceForm(prev => ({
                            ...prev,
                            description: e.target.value,
                          }))
                        }
                        placeholder='Brief description of the resource'
                        rows={2}
                      />
                    </div>
                    <div className='grid grid-cols-2 gap-4'>
                      <div>
                        <Label className='pb-1' htmlFor='resourceType'>
                          Type
                        </Label>
                        <Select
                          value={resourceForm.resourceType}
                          onValueChange={(
                            value: AdminResource['resourceType']
                          ) =>
                            setResourceForm(prev => ({
                              ...prev,
                              resourceType: value,
                            }))
                          }
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value='website'>Website</SelectItem>
                            <SelectItem value='person'>Person</SelectItem>
                            <SelectItem value='organization'>
                              Organization
                            </SelectItem>
                            <SelectItem value='other'>Other</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label className='pb-1' htmlFor='displayOrder'>
                          Display Order
                        </Label>
                        <Input
                          id='displayOrder'
                          type='number'
                          value={resourceForm.displayOrder}
                          onChange={e =>
                            setResourceForm(prev => ({
                              ...prev,
                              displayOrder: parseInt(e.target.value) || 0,
                            }))
                          }
                          placeholder='0'
                        />
                      </div>
                    </div>
                    <div className='flex gap-2'>
                      <Button onClick={handleSaveResource} size='sm'>
                        {editingResource ? 'Update' : 'Save'} Resource
                      </Button>
                      <Button
                        onClick={() => {
                          setIsAddingResource(false);
                          setEditingResource(null);
                          setResourceForm({
                            title: '',
                            description: '',
                            url: '',
                            resourceType: 'website',
                            displayOrder: 0,
                          });
                        }}
                        variant='outline'
                        size='sm'
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                )}

                {loading ? (
                  <div className='text-center py-8'>Loading...</div>
                ) : adminResources.length === 0 ? (
                  <div className='text-center py-8 text-muted-foreground'>
                    No resources configured yet.
                  </div>
                ) : (
                  <div className='overflow-x-auto'>
                    <Table className='table-fixed w-full'>
                      <TableHeader>
                        <TableRow>
                          <TableHead className='w-[150px] max-w-[150px]'>
                            Title
                          </TableHead>
                          <TableHead className='w-[120px] max-w-[120px]'>
                            Type
                          </TableHead>
                          <TableHead className='w-[120px] max-w-[120px]'>
                            URL
                          </TableHead>
                          <TableHead className='w-[80px] max-w-[80px]'>
                            Order
                          </TableHead>
                          <TableHead className='w-[120px] max-w-[120px]'>
                            Actions
                          </TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {adminResources.map(resource => (
                          <TableRow key={resource.id}>
                            <TableCell className='w-[150px] max-w-[150px] overflow-hidden'>
                              <div className='min-w-0'>
                                <div
                                  className='font-medium truncate min-w-0'
                                  title={resource.title}
                                >
                                  {resource.title}
                                </div>
                                {resource.description && (
                                  <div
                                    className='text-sm text-muted-foreground truncate min-w-0'
                                    title={resource.description}
                                  >
                                    {resource.description}
                                  </div>
                                )}
                              </div>
                            </TableCell>
                            <TableCell className='w-[120px] max-w-[120px] overflow-hidden'>
                              <div className='flex items-center gap-2 min-w-0'>
                                {getResourceTypeIcon(resource.resourceType)}
                                <span className='capitalize truncate min-w-0'>
                                  {resource.resourceType}
                                </span>
                              </div>
                            </TableCell>
                            <TableCell className='w-[120px] max-w-[120px] overflow-hidden'>
                              <a
                                href={resource.url}
                                target='_blank'
                                rel='noopener noreferrer'
                                className='text-blue-600 hover:underline flex items-center gap-1 min-w-0'
                                title={resource.url}
                              >
                                <span className='truncate min-w-0'>
                                  {resource.url}
                                </span>
                                <ExternalLink className='h-3 w-3 flex-shrink-0' />
                              </a>
                            </TableCell>
                            <TableCell className='w-[80px] max-w-[80px] overflow-hidden'>
                              {resource.displayOrder}
                            </TableCell>
                            <TableCell className='w-[120px] max-w-[120px] overflow-hidden'>
                              <div className='flex gap-1'>
                                <Button
                                  size='sm'
                                  variant='outline'
                                  onClick={() => handleEditResource(resource)}
                                >
                                  <Edit className='h-3 w-3' />
                                </Button>
                                <Button
                                  size='sm'
                                  variant='outline'
                                  onClick={() =>
                                    handleDeleteResource(resource.id)
                                  }
                                >
                                  <Trash2 className='h-3 w-3' />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
