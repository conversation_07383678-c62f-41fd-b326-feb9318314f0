'use client';

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Save, AlertTriangle, Shield, Users } from 'lucide-react';
import {
  SubRole,
  RolePermission,
  mockPermissions,
} from './role-management-table';

interface PermissionEditorProps {
  role: SubRole | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (updatedRole: SubRole) => void;
}

// Permission conflicts validation
const conflictingPermissions = [
  {
    permissions: ['user_delete', 'user_view'],
    message: 'Delete permissions should not be combined with view-only access',
  },
  {
    permissions: ['system_settings', 'audit_view'],
    message:
      'System settings access conflicts with audit view-only permissions',
  },
];

export function PermissionEditor({
  role,
  isOpen,
  onClose,
  onSave,
}: PermissionEditorProps) {
  const [editedRole, setEditedRole] = useState<SubRole | null>(null);
  const [conflicts, setConflicts] = useState<string[]>([]);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    if (role) {
      setEditedRole({ ...role });
      setHasChanges(false);
    }
  }, [role]);

  useEffect(() => {
    if (editedRole) {
      validatePermissions(editedRole.permissions);
    }
  }, [editedRole?.permissions]);

  const validatePermissions = (permissions: string[]) => {
    const foundConflicts: string[] = [];

    conflictingPermissions.forEach(conflict => {
      const hasAllConflictingPerms = conflict.permissions.every(perm =>
        permissions.includes(perm)
      );
      if (hasAllConflictingPerms) {
        foundConflicts.push(conflict.message);
      }
    });

    setConflicts(foundConflicts);
  };

  const handlePermissionChange = (permissionId: string, checked: boolean) => {
    if (!editedRole) return;

    const updatedPermissions = checked
      ? [...editedRole.permissions, permissionId]
      : editedRole.permissions.filter(p => p !== permissionId);

    setEditedRole({
      ...editedRole,
      permissions: updatedPermissions,
    });
    setHasChanges(true);
  };

  const handleFieldChange = (field: keyof SubRole, value: any) => {
    if (!editedRole) return;

    setEditedRole({
      ...editedRole,
      [field]: value,
    });
    setHasChanges(true);
  };

  const handleSave = () => {
    if (!editedRole || conflicts.length > 0) return;

    onSave(editedRole);
    setHasChanges(false);
    onClose();
  };

  const groupedPermissions = mockPermissions.reduce(
    (groups, permission) => {
      const category = permission.category;
      if (!groups[category]) {
        groups[category] = [];
      }
      groups[category].push(permission);
      return groups;
    },
    {} as Record<string, RolePermission[]>
  );

  if (!editedRole) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-w-4xl max-h-[90vh] overflow-y-auto'>
        <DialogHeader>
          <DialogTitle className='flex items-center space-x-2'>
            <Shield className='h-5 w-5' />
            <span>Edit Role: {editedRole.name}</span>
          </DialogTitle>
          <DialogDescription>
            Modify role details and permission assignments. Changes will affect
            all users with this role.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue='details' className='w-full'>
          <TabsList className='grid w-full grid-cols-3'>
            <TabsTrigger value='details'>Role Details</TabsTrigger>
            <TabsTrigger value='permissions'>Permissions</TabsTrigger>
            <TabsTrigger value='users'>Assigned Users</TabsTrigger>
          </TabsList>

          <TabsContent value='details' className='space-y-4'>
            <div className='grid grid-cols-2 gap-4'>
              <div className='space-y-2'>
                <Label htmlFor='roleName'>Role Name</Label>
                <Input
                  id='roleName'
                  value={editedRole.name}
                  onChange={e => handleFieldChange('name', e.target.value)}
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='masterRole'>Master Role</Label>
                <Input
                  id='masterRole'
                  value={editedRole.masterRole}
                  disabled
                  className='bg-gray-50'
                />
              </div>
            </div>

            <div className='space-y-2'>
              <Label htmlFor='description'>Description</Label>
              <Textarea
                id='description'
                value={editedRole.description}
                onChange={e => handleFieldChange('description', e.target.value)}
                rows={3}
              />
            </div>

            <Card>
              <CardHeader>
                <CardTitle className='text-sm'>Current Statistics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className='grid grid-cols-2 gap-4 text-sm'>
                  <div className='flex items-center space-x-2'>
                    <Users className='h-4 w-4 text-[var(--custom-gray-medium)]' />
                    <span>{editedRole.userCount} users assigned</span>
                  </div>
                  <div className='flex items-center space-x-2'>
                    <Shield className='h-4 w-4 text-[var(--custom-gray-medium)]' />
                    <span>{editedRole.permissions.length} permissions</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value='permissions' className='space-y-4'>
            {conflicts.length > 0 && (
              <Alert variant='destructive'>
                <AlertTriangle className='h-4 w-4' />
                <AlertDescription>
                  <div className='space-y-1'>
                    <p className='font-medium'>
                      Permission Conflicts Detected:
                    </p>
                    {conflicts.map((conflict, index) => (
                      <p key={index} className='text-sm'>
                        • {conflict}
                      </p>
                    ))}
                  </div>
                </AlertDescription>
              </Alert>
            )}

            <div className='space-y-6'>
              {Object.entries(groupedPermissions).map(
                ([category, permissions]) => (
                  <Card key={category}>
                    <CardHeader>
                      <CardTitle className='text-lg'>{category}</CardTitle>
                      <CardDescription>
                        {permissions.length} permission(s) in this category
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className='grid grid-cols-1 gap-3'>
                        {permissions.map(permission => (
                          <div
                            key={permission.id}
                            className='flex items-start space-x-3'
                          >
                            <Checkbox
                              id={permission.id}
                              checked={editedRole.permissions.includes(
                                permission.id
                              )}
                              onCheckedChange={checked =>
                                handlePermissionChange(
                                  permission.id,
                                  checked as boolean
                                )
                              }
                            />
                            <div className='flex-1 min-w-0'>
                              <Label
                                htmlFor={permission.id}
                                className='text-sm font-medium cursor-pointer'
                              >
                                {permission.name}
                              </Label>
                              <p className='text-xs text-[var(--custom-gray-medium)] mt-1'>
                                {permission.description}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )
              )}
            </div>
          </TabsContent>

          <TabsContent value='users' className='space-y-4'>
            <Card>
              <CardHeader>
                <CardTitle>Users with this Role</CardTitle>
                <CardDescription>
                  {editedRole.userCount} user(s) currently assigned to this role
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className='space-y-2'>
                  {/* Mock user list - in real implementation, this would fetch actual users */}
                  {Array.from({ length: editedRole.userCount }, (_, i) => (
                    <div
                      key={i}
                      className='flex items-center justify-between p-3 border rounded-lg'
                    >
                      <div className='flex items-center space-x-3'>
                        <div className='w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center'>
                          <Users className='h-4 w-4 text-[var(--custom-gray-medium)]' />
                        </div>
                        <div>
                          <p className='text-sm font-medium'>User {i + 1}</p>
                          <p className='text-xs text-[var(--custom-gray-medium)]'>
                            user{i + 1}@example.com
                          </p>
                        </div>
                      </div>
                      <Badge variant='outline'>Active</Badge>
                    </div>
                  ))}

                  {editedRole.userCount === 0 && (
                    <div className='text-center py-8 text-[var(--custom-gray-medium)]'>
                      <Users className='h-12 w-12 mx-auto mb-2 text-[var(--custom-gray-light)]' />
                      <p>No users assigned to this role</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter className='flex items-center justify-between'>
          <div className='flex items-center space-x-2'>
            {hasChanges && (
              <Badge variant='outline' className='text-orange-600'>
                Unsaved changes
              </Badge>
            )}
          </div>

          <div className='flex space-x-2'>
            <Button variant='outline' onClick={onClose}>
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              disabled={conflicts.length > 0 || !hasChanges}
              className='flex items-center space-x-2'
            >
              <Save className='h-4 w-4' />
              <span>Save Changes</span>
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
