'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Clock, AlertCircle, Bell } from 'lucide-react';

export interface DocumentUpdateNotificationProps {
  documentId: string;
  documentType: string;
  lastUpdated: string;
  daysUntilReview: number;
  onSnooze?: () => void;
}

export function DocumentUpdateNotification({
  documentId,
  documentType,
  lastUpdated,
  daysUntilReview,
  onSnooze,
}: DocumentUpdateNotificationProps) {
  const router = useRouter();

  const getNotificationVariant = () => {
    if (daysUntilReview < 0) {
      return 'review-needed';
    } else if (daysUntilReview <= 30) {
      return 'review-soon';
    } else {
      return 'current';
    }
  };

  const getNotificationContent = () => {
    const variant = getNotificationVariant();

    switch (variant) {
      case 'review-needed':
        return {
          title: 'Document Review Required',
          description: `Your ${documentType} is due for review. It was last updated on ${lastUpdated}.`,
          icon: <AlertCircle className='h-4 w-4' />,
          className: 'bg-red-50 text-red-800 border-red-200',
        };
      case 'review-soon':
        return {
          title: 'Document Review Coming Soon',
          description: `Your ${documentType} will be due for review in ${daysUntilReview} days. It was last updated on ${lastUpdated}.`,
          icon: <Clock className='h-4 w-4' />,
          className: 'bg-amber-50 text-amber-800 border-amber-200',
        };
      default:
        return {
          title: 'Document Up to Date',
          description: `Your ${documentType} is current. It was last updated on ${lastUpdated}.`,
          icon: <Bell className='h-4 w-4' />,
          className: 'bg-green-50 text-green-800 border-green-200',
        };
    }
  };

  const handleReviewNow = () => {
    router.push(`/dashboard/member/documents/update/${documentId}`);
  };

  const content = getNotificationContent();

  return (
    <Alert className={`mb-6 ${content.className}`}>
      {content.icon}
      <AlertTitle>{content.title}</AlertTitle>
      <AlertDescription className='flex flex-col sm:flex-row sm:items-center sm:justify-between'>
        <span>{content.description}</span>
        <div className='mt-3 sm:mt-0 flex space-x-2'>
          {getNotificationVariant() !== 'current' && (
            <>
              <Button size='sm' variant='default' onClick={handleReviewNow}>
                Review Now
              </Button>
              {onSnooze && (
                <Button size='sm' variant='outline' onClick={onSnooze}>
                  Snooze
                </Button>
              )}
            </>
          )}
        </div>
      </AlertDescription>
    </Alert>
  );
}
