'use client';

import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Eye, RotateCw, Download } from 'lucide-react';

export interface DocumentVersion {
  id: string;
  documentType: string;
  createdAt: string;
  status: 'current' | 'archived';
  changes: string;
}

interface DocumentVersionListProps {
  versions: DocumentVersion[];
  onView: (versionId: string) => void;
  onRegenerate: (versionId: string) => void;
  onDownload: (versionId: string) => void;
  className?: string;
}

export function DocumentVersionList({
  versions,
  onView,
  onRegenerate,
  onDownload,
  className = '',
}: DocumentVersionListProps) {
  return (
    <div className={className}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Version</TableHead>
            <TableHead>Document Type</TableHead>
            <TableHead>Created</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Changes</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {versions.map(version => (
            <TableRow key={version.id}>
              <TableCell className='font-medium'>{version.id}</TableCell>
              <TableCell>{version.documentType}</TableCell>
              <TableCell>{version.createdAt}</TableCell>
              <TableCell>
                {version.status === 'current' ? (
                  <Badge className='bg-green-2010c'>Current</Badge>
                ) : (
                  <Badge variant='outline'>Archived</Badge>
                )}
              </TableCell>
              <TableCell>{version.changes}</TableCell>
              <TableCell>
                <div className='flex space-x-2'>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() => onView(version.id)}
                  >
                    <Eye className='h-4 w-4 mr-1' />
                    View
                  </Button>

                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() => onRegenerate(version.id)}
                  >
                    <RotateCw className='h-4 w-4 mr-1' />
                    Regenerate
                  </Button>

                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() => onDownload(version.id)}
                  >
                    <Download className='h-4 w-4 mr-1' />
                    Download
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
