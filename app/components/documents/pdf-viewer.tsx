'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  ZoomIn,
  ZoomOut,
  RotateCw,
  Download,
  FileText,
  Calendar,
  User,
  Layout,
  FileStack,
} from 'lucide-react';
import { Document } from '@/types/documents';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { downloadDocument } from '@/lib/utils/document-download';
import { DocumentStatusBadge } from './document-status-badge';

// Generate the client
const client = generateClient<Schema>();

interface PDFViewerProps {
  documentHtml: Document;
  onDownload?: () => void;
  isDownloading?: boolean;
  className?: string;
}

export function PDFViewer({
  documentHtml,
  onDownload,
  isDownloading = false,
  className = '',
}: PDFViewerProps) {
  // Show watermark if document is not signed or shipped
  const shouldShowWatermark =
    documentHtml.status !== 'signed' && documentHtml.status !== 'shipped';

  const [zoom, setZoom] = useState(100);
  const [rotation, setRotation] = useState(0);
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
  const [pdfError, setPdfError] = useState<string | null>(null);
  const [showSections, setShowSections] = useState(true);

  const handleZoomIn = () => setZoom(prev => Math.min(prev + 25, 200));
  const handleZoomOut = () => setZoom(prev => Math.max(prev - 25, 50));
  const handleRotate = () => setRotation(prev => (prev + 90) % 360);

  const handleDownloadFromHTML = async () => {
    setIsGeneratingPDF(true);
    setPdfError(null);

    try {
      await downloadDocument(documentHtml);
      console.log('===> PDF DOWNLOAD COMPLETED');
    } catch (error) {
      console.error('Error downloading PDF:', error);
      setPdfError(
        error instanceof Error ? error.message : 'Failed to download PDF'
      );
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Parse document content into sections using markers
  const parseDocumentSections = (content: string) => {
    if (!content) return { startPage: '', mainContent: '', endPage: '' };

    // Try to parse using section markers first
    const startPageMatch = content.match(
      /<!-- START_PAGE_SECTION -->\n([\s\S]*?)\n<!-- END_START_PAGE_SECTION -->/
    );
    const mainContentMatch = content.match(
      /<!-- MAIN_CONTENT_SECTION -->\n([\s\S]*?)\n<!-- END_MAIN_CONTENT_SECTION -->/
    );
    const endPageMatch = content.match(
      /<!-- END_PAGE_SECTION -->\n([\s\S]*?)\n<!-- END_END_PAGE_SECTION -->/
    );

    if (startPageMatch || mainContentMatch || endPageMatch) {
      return {
        startPage: startPageMatch ? startPageMatch[1].trim() : '',
        mainContent: mainContentMatch ? mainContentMatch[1].trim() : '',
        endPage: endPageMatch ? endPageMatch[1].trim() : '',
      };
    }

    // Fallback: Split by double line breaks (our separator)
    const sections = content.split('\n\n').filter(section => section.trim());

    // If we have 3 sections, assume start + main + end
    if (sections.length === 3) {
      return {
        startPage: sections[0],
        mainContent: sections[1],
        endPage: sections[2],
      };
    }
    // If we have 2 sections, could be start + main OR main + end
    else if (sections.length === 2) {
      // For now, assume it's main + end (most common case)
      return {
        startPage: '',
        mainContent: sections[0],
        endPage: sections[1],
      };
    }
    // If we have 1 section, it's just main content
    else if (sections.length === 1) {
      return {
        startPage: '',
        mainContent: sections[0],
        endPage: '',
      };
    }

    // Fallback: treat everything as main content
    return {
      startPage: '',
      mainContent: content,
      endPage: '',
    };
  };

  const renderDocumentSection = (
    title: string,
    content: string,
    sectionType: 'start' | 'main' | 'end'
  ) => {
    if (!content.trim()) return null;

    const sectionColors = {
      start: 'border-blue-200 bg-gradient-to-r from-blue-50 to-blue-25',
      main: 'border-gray-200 bg-gradient-to-r from-gray-50 to-white',
      end: 'border-green-200 bg-gradient-to-r from-green-50 to-green-25',
    };

    const sectionIcons = {
      start: '📄',
      main: '📋',
      end: '📝',
    };

    const sectionTitles = {
      start: 'First Page',
      main: 'Main Document',
      end: 'Last Page',
    };

    return (
      <div
        className={`border-2 rounded-xl p-6 mb-6 shadow-sm ${sectionColors[sectionType]}`}
      >
        <div className='flex items-center gap-3 mb-4 pb-3 border-b-2 border-gray-300'>
          <div className='flex items-center justify-center w-8 h-8 rounded-full bg-white shadow-sm'>
            <span className='text-lg'>{sectionIcons[sectionType]}</span>
          </div>
          <div className='flex-1'>
            <h3 className='text-lg font-bold text-gray-800'>
              {sectionTitles[sectionType]}
            </h3>
            <p className='text-xs text-gray-600 mt-1'>
              {sectionType === 'start' && 'Cover page and introduction'}
              {sectionType === 'main' && 'Primary document content'}
              {sectionType === 'end' && 'Closing statements and signatures'}
            </p>
          </div>
          <div className='text-xs text-gray-500 bg-white px-3 py-1 rounded-full border shadow-sm'>
            Page{' '}
            {sectionType === 'start'
              ? '1'
              : sectionType === 'main'
                ? '2+'
                : 'Last'}
          </div>
        </div>
        <div
          className='prose max-w-none text-sm leading-relaxed text-gray-700 bg-white/50 rounded-lg p-4'
          dangerouslySetInnerHTML={{ __html: content }}
        />
        {/* Page break indicator for PDF */}
        <div className='mt-4 pt-3 border-t-2 border-dashed border-gray-400 text-center'>
          <span className='text-xs text-gray-500 bg-white px-3 py-1 rounded-full border shadow-sm'>
            📄 Page Break
          </span>
        </div>
      </div>
    );
  };

  const renderDocumentContent = () => {
    const sections = parseDocumentSections(documentHtml.content || '');
    const hasMultipleSections = sections.startPage || sections.endPage;

    return (
      <div className='bg-background border rounded-lg shadow-inner p-8 min-h-[800px] relative overflow-hidden'>
        {/* Draft Watermark */}
        {shouldShowWatermark && (
          <div
            className='absolute inset-0 flex items-center justify-center pointer-events-none z-30'
            style={{
              transform: 'rotate(45deg)',
              fontSize: '120px',
              fontWeight: 'bold',
              color: 'rgba(239, 68, 68, 0.3)',
              fontFamily: 'Arial, sans-serif',
              textShadow: '2px 2px 4px rgba(0,0,0,0.1)',
            }}
          >
            DRAFT
          </div>
        )}

        <div
          className='transition-transform duration-200 w-full relative z-20'
          style={{
            transform: `scale(${zoom / 100}) rotate(${rotation}deg)`,
            transformOrigin: 'center center',
          }}
        >
          {/* Real document content */}
          <div className='max-w-2xl mx-auto space-y-6 text-[var(--off-black)]'>
            <div className='text-center border-b pb-4 mb-8'>
              <h1 className='text-2xl font-bold text-[var(--foreground)]'>
                {documentHtml.title}
              </h1>
              <p className='text-sm text-[var(--foreground)] mt-2'>
                Document Type: {documentHtml.type}
              </p>
              <p className='text-xs text-[var(--foreground)] mt-1'>
                Version {documentHtml.version} • Created{' '}
                {formatDate(documentHtml.dateCreated)}
              </p>
              {hasMultipleSections && (
                <div className='mt-3 text-xs text-blue-600 bg-blue-50 px-3 py-1 rounded-full inline-block'>
                  📑 Multi-section document with{' '}
                  {sections.startPage ? 'cover page' : ''}
                  {sections.startPage && sections.endPage ? ', ' : ''}
                  {sections.endPage ? 'closing page' : ''}
                </div>
              )}
            </div>

            {/* Document sections */}
            {documentHtml.content ? (
              showSections && hasMultipleSections ? (
                <div className='space-y-0'>
                  {renderDocumentSection(
                    'First Page',
                    sections.startPage,
                    'start'
                  )}
                  {renderDocumentSection(
                    'Main Document',
                    sections.mainContent,
                    'main'
                  )}
                  {renderDocumentSection('Last Page', sections.endPage, 'end')}
                </div>
              ) : (
                <div className='border rounded-lg p-6 bg-white'>
                  <div
                    className='prose max-w-none text-sm leading-relaxed'
                    dangerouslySetInnerHTML={{ __html: documentHtml.content }}
                  />
                </div>
              )
            ) : (
              <div className='text-center text-[var(--custom-gray-medium)] py-8'>
                <p className='text-lg mb-2'>No content available</p>
                <p className='text-sm'>
                  This document does not have any content yet.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <Card className={`w-full ${className}`}>
      <CardHeader className='pb-4'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-3'>
            <FileText className='h-6 w-6 text-blue-600' />
            <div>
              <CardTitle className='text-xl'>{documentHtml.title}</CardTitle>
              <div className='flex items-center gap-4 mt-1 text-sm text-muted-foreground'>
                <div className='flex items-center gap-1'>
                  <User className='h-4 w-4' />
                  <span>{documentHtml.type}</span>
                </div>
                <div className='flex items-center gap-1'>
                  <Calendar className='h-4 w-4' />
                  <span>Updated {formatDate(documentHtml.lastModified)}</span>
                </div>
              </div>
            </div>
          </div>
          <DocumentStatusBadge status={documentHtml.status} />
        </div>

        {/* Viewer Controls */}
        <div className='flex items-center gap-2 pt-4 border-t'>
          {/*<Button*/}
          {/*  variant='outline'*/}
          {/*  size='sm'*/}
          {/*  onClick={handleZoomOut}*/}
          {/*  disabled={zoom <= 50}*/}
          {/*>*/}
          {/*  <ZoomOut className='h-4 w-4' />*/}
          {/*</Button>*/}
          {/*<span className='text-sm font-medium min-w-[60px] text-center'>*/}
          {/*  {zoom}%*/}
          {/*</span>*/}
          {/*<Button*/}
          {/*  variant='outline'*/}
          {/*  size='sm'*/}
          {/*  onClick={handleZoomIn}*/}
          {/*  disabled={zoom >= 200}*/}
          {/*>*/}
          {/*  <ZoomIn className='h-4 w-4' />*/}
          {/*</Button>*/}
          {/*<Button variant='outline' size='sm' onClick={handleRotate}>*/}
          {/*  <RotateCw className='h-4 w-4' />*/}
          {/*</Button>*/}

          {/* Section view toggle - only show if document has multiple sections */}
          {(() => {
            const sections = parseDocumentSections(documentHtml.content || '');
            const hasMultipleSections = sections.startPage || sections.endPage;
            return hasMultipleSections ? (
              <Button
                variant='outline'
                size='sm'
                onClick={() => setShowSections(!showSections)}
                className='flex items-center gap-1'
              >
                {showSections ? (
                  <Layout className='h-4 w-4' />
                ) : (
                  <FileStack className='h-4 w-4' />
                )}
                <span className='text-xs'>
                  {showSections ? 'Combined' : 'Sections'}
                </span>
              </Button>
            ) : null;
          })()}

          {onDownload && (
            <Button
              variant='outline'
              size='sm'
              onClick={handleDownloadFromHTML}
              disabled={isDownloading || isGeneratingPDF}
              className='ml-auto'
            >
              <Download className='h-4 w-4 mr-2' />
              {isGeneratingPDF
                ? 'Generating PDF...'
                : isDownloading
                  ? 'Signing & Downloading...'
                  : 'Download PDF'}
            </Button>
          )}
        </div>

        {/* Error Message */}
        {pdfError && (
          <div className='mt-2 p-3 bg-red-50 border border-red-200 rounded-md'>
            <p className='text-sm text-red-600'>
              <strong>PDF Generation Error:</strong> {pdfError}
            </p>
          </div>
        )}
      </CardHeader>
      <CardContent id={'content-to-print'} className='p-0'>
        {renderDocumentContent()}
      </CardContent>
    </Card>
  );
}
