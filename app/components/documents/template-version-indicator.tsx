'use client';

import React, { useEffect, useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, Calendar, RefreshCw } from 'lucide-react';
import {
  getCachedTemplateVersionInfo,
  type TemplateVersionInfo,
} from '@/app/utils/template-version-utils';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface TemplateVersionIndicatorProps {
  documentVersion: string;
  templateVersion?: number | null;
  templateId?: string | null;
  className?: string;
  showTooltip?: boolean;
}

export function TemplateVersionIndicator({
  documentVersion,
  templateVersion,
  templateId,
  className = '',
  showTooltip = true,
}: TemplateVersionIndicatorProps) {
  const [versionInfo, setVersionInfo] = useState<TemplateVersionInfo | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const fetchVersionInfo = async () => {
      if (!templateId) {
        setVersionInfo({
          documentVersion,
          templateVersion: templateVersion || null,
          latestTemplateVersion: null,
          hasNewerTemplateVersion: false,
        });
        return;
      }

      setIsLoading(true);
      try {
        const info = await getCachedTemplateVersionInfo(
          documentVersion,
          templateVersion,
          templateId
        );
        setVersionInfo(info);
      } catch (error) {
        console.error('Error fetching version info:', error);
        setVersionInfo({
          documentVersion,
          templateVersion: templateVersion || null,
          latestTemplateVersion: null,
          hasNewerTemplateVersion: false,
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchVersionInfo();
  }, [documentVersion, templateVersion, templateId]);

  if (!versionInfo) {
    return (
      <div className={`flex items-center gap-1 ${className}`}>
        <Calendar className='h-4 w-4' />
        <span>v{documentVersion}</span>
        {isLoading && <RefreshCw className='h-3 w-3 animate-spin' />}
      </div>
    );
  }

  const VersionDisplay = () => (
    <div className={`flex items-center gap-1 ${className}`}>
      <Calendar className='h-4 w-4' />
      <span>v{versionInfo.documentVersion}</span>
      {versionInfo.hasNewerTemplateVersion && (
        <Badge
          variant='outline'
          className='ml-1 px-1.5 py-0.5 text-xs bg-amber-50 text-amber-700 border-amber-200 hover:bg-amber-100'
        >
          <AlertCircle className='h-3 w-3 mr-1' />
          Template Update
        </Badge>
      )}
    </div>
  );

  if (!showTooltip || !versionInfo.hasNewerTemplateVersion) {
    return <VersionDisplay />;
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className='cursor-help'>
            <VersionDisplay />
          </div>
        </TooltipTrigger>
        <TooltipContent side='top' className='max-w-xs'>
          <div className='space-y-1'>
            <p className='font-medium'>Template Update Available</p>
            {versionInfo.templateVersion && (
              <p className='text-sm'>
                Created with template: v{versionInfo.templateVersion}
              </p>
            )}
            {versionInfo.latestTemplateVersion && (
              <p className='text-sm'>
                Latest template: v{versionInfo.latestTemplateVersion}
              </p>
            )}
            {versionInfo.templateName && (
              <p className='text-xs'>Template: {versionInfo.templateName}</p>
            )}
            <p className='text-xs mt-2'>
              Consider recreating this document with the latest template version
              for updated legal requirements.
            </p>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

// Компонент для відображення тільки індикатора оновлення (без версії)
export function TemplateUpdateIndicator({
  templateVersion,
  templateId,
  className = '',
}: {
  templateVersion?: number | null;
  templateId?: string | null;
  className?: string;
}) {
  const [hasUpdate, setHasUpdate] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [latestVersion, setLatestVersion] = useState<number | null>(null);

  useEffect(() => {
    const checkForUpdate = async () => {
      if (!templateId || !templateVersion) return;

      setIsLoading(true);
      try {
        const info = await getCachedTemplateVersionInfo(
          '',
          templateVersion,
          templateId
        );
        setHasUpdate(info.hasNewerTemplateVersion);
        setLatestVersion(info.latestTemplateVersion);
      } catch (error) {
        console.error('Error checking for updates:', error);
        setHasUpdate(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkForUpdate();
  }, [templateVersion, templateId]);

  if (isLoading) {
    return <RefreshCw className={`h-3 w-3 animate-spin ${className}`} />;
  }

  if (!hasUpdate) {
    return null;
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge
            variant='outline'
            className={`px-1.5 py-0.5 text-xs bg-amber-50 text-amber-700 border-amber-200 hover:bg-amber-100 cursor-help ${className}`}
          >
            <AlertCircle className='h-3 w-3 mr-1' />
            Template Update
          </Badge>
        </TooltipTrigger>
        <TooltipContent side='top'>
          <div className='space-y-1'>
            <p className='text-sm font-medium'>Template Update Available</p>
            <p className='text-sm'>
              Current: v{templateVersion} → Latest: v{latestVersion}
            </p>
            <p className='text-xs text-muted-foreground'>
              Consider recreating this document with the latest template.
            </p>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

// Хук для використання в інших компонентах
export function useTemplateVersionInfo(
  documentVersion: string,
  templateVersion?: number | null,
  templateId?: string | null
) {
  const [versionInfo, setVersionInfo] = useState<TemplateVersionInfo | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const fetchVersionInfo = async () => {
      setIsLoading(true);
      try {
        const info = await getCachedTemplateVersionInfo(
          documentVersion,
          templateVersion,
          templateId
        );
        setVersionInfo(info);
      } catch (error) {
        console.error('Error fetching version info:', error);
        setVersionInfo({
          documentVersion,
          templateVersion: templateVersion || null,
          latestTemplateVersion: null,
          hasNewerTemplateVersion: false,
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchVersionInfo();
  }, [documentVersion, templateVersion, templateId]);

  return { versionInfo, isLoading };
}
