'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  MessageCircle,
  Send,
  Mic,
  MicOff,
  Volume2,
  VolumeX,
  User,
  Bot,
  X,
  Minimize2,
} from 'lucide-react';

interface Message {
  id: string;
  text: string;
  sender: 'user' | 'avatar';
  timestamp: Date;
}

interface AvatarAssistantProps {
  isMinimized?: boolean;
  onToggleMinimize?: () => void;
  onClose?: () => void;
}

// Mock FAQ data
const FAQ_RESPONSES = {
  'what is a will':
    'A will is a legal document that specifies how you want your assets distributed after your death. It also allows you to name an executor and guardians for minor children.',
  'do i need a trust':
    'Whether you need a trust depends on your specific situation. Trusts can help avoid probate, provide privacy, and offer more control over asset distribution.',
  'power of attorney':
    'A power of attorney is a legal document that gives someone you trust the authority to make decisions on your behalf if you become unable to do so.',
  'how to start estate planning':
    'Start by taking inventory of your assets, deciding who you want as beneficiaries, and choosing trusted people for key roles like executor and power of attorney.',
};

export function AvatarAssistant({
  isMinimized = false,
  onToggleMinimize,
  onClose,
}: AvatarAssistantProps) {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: 'welcome',
      text: "Hi! I'm your estate planning assistant. I can answer questions about wills, trusts, power of attorney, and more. How can I help you today?",
      sender: 'avatar',
      timestamp: new Date(),
    },
  ]);
  const [inputText, setInputText] = useState('');
  const [isListening, setIsListening] = useState(false);
  const [audioEnabled, setAudioEnabled] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const findBestResponse = (userInput: string): string => {
    const input = userInput.toLowerCase();

    for (const [key, response] of Object.entries(FAQ_RESPONSES)) {
      if (
        input.includes(key) ||
        key.split(' ').some(word => input.includes(word))
      ) {
        return response;
      }
    }

    return "I understand you're asking about estate planning. While I can help with basic questions about wills, trusts, and power of attorney, I'd recommend speaking with a qualified estate planning attorney for personalized advice.";
  };

  const handleSendMessage = () => {
    if (!inputText.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: inputText,
      sender: 'user',
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');

    setTimeout(() => {
      const responseText = findBestResponse(inputText);
      const avatarMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: responseText,
        sender: 'avatar',
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, avatarMessage]);
    }, 1000);
  };

  const handleVoiceInput = () => {
    if (
      !('webkitSpeechRecognition' in window) &&
      !('SpeechRecognition' in window)
    ) {
      alert('Speech recognition is not supported in your browser');
      return;
    }

    const SpeechRecognition =
      (window as any).webkitSpeechRecognition ||
      (window as any).SpeechRecognition;
    const recognition = new SpeechRecognition();

    recognition.continuous = false;
    recognition.interimResults = false;
    recognition.lang = 'en-US';

    recognition.onstart = () => setIsListening(true);
    recognition.onend = () => setIsListening(false);

    recognition.onresult = (event: any) => {
      const transcript = event.results[0][0].transcript;
      setInputText(transcript);
    };

    recognition.onerror = (event: any) => {
      console.error('Speech recognition error:', event.error);
      setIsListening(false);
    };

    recognition.start();
  };

  if (isMinimized) {
    return (
      <div className='fixed bottom-4 right-4 z-50'>
        <Button
          onClick={onToggleMinimize}
          className='rounded-full h-14 w-14 bg-[var(--eggplant)] hover:bg-[#5C9509] shadow-lg'
        >
          <MessageCircle className='h-6 w-6 text-white' />
        </Button>
      </div>
    );
  }

  return (
    <div className='fixed bottom-4 right-4 z-50 w-96 h-[500px] flex flex-col'>
      <Card className='flex-1 flex flex-col shadow-xl'>
        <CardHeader className='pb-2 bg-[var(--eggplant)] text-white rounded-t-lg'>
          <div className='flex justify-between items-center'>
            <div className='flex items-center gap-2'>
              <div className='w-8 h-8 bg-background rounded-full flex items-center justify-center'>
                <Bot className='h-5 w-5 text-[var(--lime)]' />
              </div>
              <div>
                <CardTitle className='text-sm'>
                  Estate Planning Assistant
                </CardTitle>
                <CardDescription className='text-xs text-green-100'>
                  Online
                </CardDescription>
              </div>
            </div>
            <div className='flex gap-1'>
              <Button
                variant='ghost'
                size='sm'
                onClick={() => setAudioEnabled(!audioEnabled)}
                className='h-8 w-8 p-0 text-white hover:bg-background/20'
              >
                {audioEnabled ? (
                  <Volume2 className='h-4 w-4' />
                ) : (
                  <VolumeX className='h-4 w-4' />
                )}
              </Button>
              <Button
                variant='ghost'
                size='sm'
                onClick={onToggleMinimize}
                className='h-8 w-8 p-0 text-white hover:bg-background/20'
              >
                <Minimize2 className='h-4 w-4' />
              </Button>
              <Button
                variant='ghost'
                size='sm'
                onClick={onClose}
                className='h-8 w-8 p-0 text-white hover:bg-background/20'
              >
                <X className='h-4 w-4' />
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className='flex-1 flex flex-col p-0'>
          <div className='flex-1 overflow-y-auto p-4 space-y-4'>
            {messages.map(message => (
              <div
                key={message.id}
                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`flex gap-2 max-w-[80%] ${message.sender === 'user' ? 'flex-row-reverse' : 'flex-row'}`}
                >
                  <div
                    className={`w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 ${
                      message.sender === 'user'
                        ? 'bg-blue-500'
                        : 'bg-[var(--eggplant)]'
                    }`}
                  >
                    {message.sender === 'user' ? (
                      <User className='h-3 w-3 text-white' />
                    ) : (
                      <Bot className='h-3 w-3 text-white' />
                    )}
                  </div>
                  <div
                    className={`rounded-lg p-3 ${
                      message.sender === 'user'
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-100 text-[var(--custom-gray-dark)]'
                    }`}
                  >
                    <p className='text-sm'>{message.text}</p>
                    <p className='text-xs opacity-70 mt-1'>
                      {message.timestamp.toLocaleTimeString([], {
                        hour: '2-digit',
                        minute: '2-digit',
                      })}
                    </p>
                  </div>
                </div>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>

          <div className='p-4 border-t bg-gray-50'>
            <p className='text-xs text-[var(--custom-gray-medium)] mb-2'>
              Quick questions:
            </p>
            <div className='flex flex-wrap gap-1'>
              {[
                'What is a will?',
                'Do I need a trust?',
                'Power of attorney?',
              ].map(question => (
                <Button
                  key={question}
                  variant='outline'
                  size='sm'
                  onClick={() => {
                    setInputText(question);
                    setTimeout(handleSendMessage, 100);
                  }}
                  className='text-xs h-6'
                >
                  {question}
                </Button>
              ))}
            </div>
          </div>

          <div className='p-4 border-t'>
            <div className='flex gap-2'>
              <Input
                value={inputText}
                onChange={e => setInputText(e.target.value)}
                placeholder='Ask me about estate planning...'
                onKeyPress={e => e.key === 'Enter' && handleSendMessage()}
                className='flex-1'
              />
              <Button
                variant='ghost'
                size='sm'
                onClick={handleVoiceInput}
                disabled={isListening}
                className={`h-10 w-10 p-0 ${isListening ? 'bg-red-100 text-red-600' : ''}`}
              >
                {isListening ? (
                  <MicOff className='h-4 w-4' />
                ) : (
                  <Mic className='h-4 w-4' />
                )}
              </Button>
              <Button
                onClick={handleSendMessage}
                disabled={!inputText.trim()}
                className='h-10 w-10 p-0'
              >
                <Send className='h-4 w-4' />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export function useAvatarAssistant() {
  const [isVisible, setIsVisible] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);

  const showAvatar = () => {
    setIsVisible(true);
    setIsMinimized(false);
  };

  const hideAvatar = () => {
    setIsVisible(false);
    setIsMinimized(false);
  };

  const toggleMinimize = () => {
    setIsMinimized(!isMinimized);
  };

  return {
    isVisible,
    isMinimized,
    showAvatar,
    hideAvatar,
    toggleMinimize,
  };
}
