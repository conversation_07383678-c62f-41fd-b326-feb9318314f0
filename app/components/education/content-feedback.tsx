/**
 * ContentFeedback Component
 *
 * A component for collecting user feedback on educational content.
 */

'use client';

import React, { useState } from 'react';
import { useEducationalContent } from '../../../hooks/use-educational-content';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
} from '@/components/ui/card';

interface ContentFeedbackProps {
  contentId: string;
  onFeedbackSubmitted?: () => void;
}

export function ContentFeedback({
  contentId,
  onFeedbackSubmitted,
}: ContentFeedbackProps) {
  const { submitContentFeedback, contentAnalytics } = useEducationalContent();
  const [rating, setRating] = useState<number | null>(null);
  const [comment, setComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  // Get analytics for this content
  const analytics = contentAnalytics(contentId);

  // Handle rating selection
  const handleRatingSelect = (value: number) => {
    setRating(value);
  };

  // Handle comment change
  const handleCommentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setComment(e.target.value);
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (rating === null) return;

    setIsSubmitting(true);

    // Submit feedback
    submitContentFeedback(contentId, rating, comment);

    // Simulate API call delay
    setTimeout(() => {
      setIsSubmitting(false);
      setIsSubmitted(true);

      if (onFeedbackSubmitted) {
        onFeedbackSubmitted();
      }
    }, 1000);
  };

  // Render submitted state
  if (isSubmitted) {
    return (
      <Card>
        <CardHeader>
          <h3 className='font-semibold text-lg'>Thank You!</h3>
        </CardHeader>
        <CardContent>
          <p>Your feedback has been submitted. We appreciate your input!</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <h3 className='font-semibold text-lg'>Was this content helpful?</h3>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit}>
          {/* Star Rating */}
          <div className='flex items-center justify-center space-x-1 mb-4'>
            {[1, 2, 3, 4, 5].map(value => (
              <Button
                key={value}
                type='button'
                variant='ghost'
                onClick={() => handleRatingSelect(value)}
                className={`h-auto p-0 min-w-0 text-2xl ${
                  rating !== null && value <= rating
                    ? 'text-yellow-400'
                    : 'text-[var(--custom-gray-light)]'
                }`}
                aria-label={`Rate ${value} stars`}
              >
                ★
              </Button>
            ))}
          </div>

          {/* Comment */}
          <Textarea
            placeholder='Do you have any additional feedback? (optional)'
            value={comment}
            onChange={handleCommentChange}
            className='w-full mb-4'
          />

          {/* Submit Button */}
          <Button
            type='submit'
            variant='default'
            disabled={rating === null || isSubmitting}
            className='w-full'
          >
            {isSubmitting ? 'Submitting...' : 'Submit Feedback'}
          </Button>
        </form>
      </CardContent>

      {analytics && (
        <>
          <div className='h-px w-full bg-gray-200 my-4'></div>
          <CardFooter className='flex flex-col items-start'>
            <h4 className='font-medium text-sm mb-2'>Content Statistics</h4>
            <div className='grid grid-cols-3 w-full text-center'>
              <div>
                <p className='text-lg font-semibold'>{analytics.views}</p>
                <p className='text-xs text-muted-foreground'>Views</p>
              </div>
              <div>
                <p className='text-lg font-semibold'>
                  {analytics.completionRate}%
                </p>
                <p className='text-xs text-muted-foreground'>Completion</p>
              </div>
              <div>
                <p className='text-lg font-semibold'>
                  {analytics.averageRating.toFixed(1)}
                </p>
                <p className='text-xs text-muted-foreground'>Avg. Rating</p>
              </div>
            </div>
          </CardFooter>
        </>
      )}
    </Card>
  );
}
