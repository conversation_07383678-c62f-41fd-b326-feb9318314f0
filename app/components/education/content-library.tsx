/**
 * ContentLibrary Component
 *
 * A component for displaying and filtering educational content.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Content, ContentFilters } from '@/types/education';
import { useEducationalContent } from '../../../hooks/use-educational-content';
import { ContentCard } from './content-card';
import { ContentSearch } from './content-search';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface ContentLibraryProps {
  initialFilters?: ContentFilters;
}

export function ContentLibrary({ initialFilters = {} }: ContentLibraryProps) {
  const { allContent, filteredContent, filters, setFilters, isLoading, error } =
    useEducationalContent();

  // Get all unique tags from content
  const allTags = React.useMemo(() => {
    const tags = new Set<string>();
    allContent.forEach(item => {
      item.tags.forEach(tag => tags.add(tag));
    });
    return Array.from(tags).sort();
  }, [allContent]);

  // Handle filter changes
  const handleFiltersChange = (newFilters: ContentFilters) => {
    setFilters(newFilters);
  };

  // Set initial filters
  useEffect(() => {
    if (Object.keys(initialFilters).length > 0) {
      setFilters(initialFilters);
    }
  }, []);

  // Render loading state
  if (isLoading) {
    return (
      <div className='space-y-8'>
        <div className='h-12 w-full bg-gray-200 animate-pulse rounded'></div>
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
          {Array.from({ length: 6 }).map((_, i) => (
            <div
              key={i}
              className='h-64 w-full bg-gray-200 animate-pulse rounded'
            ></div>
          ))}
        </div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className='p-8 text-center'>
        <h3 className='text-lg font-semibold text-red-500'>Error</h3>
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div className='space-y-8'>
      {/* Search and Filters */}
      <ContentSearch
        filters={filters}
        onFiltersChange={handleFiltersChange}
        availableTags={allTags}
      />

      {/* Content Tabs */}
      <Tabs defaultValue='all' className='w-full'>
        <TabsList className='mb-6'>
          <TabsTrigger value='all'>All Content</TabsTrigger>
          <TabsTrigger value='videos'>Videos</TabsTrigger>
          <TabsTrigger value='articles'>Articles</TabsTrigger>
          <TabsTrigger value='infographics'>Infographics</TabsTrigger>
          <TabsTrigger value='interactive'>Interactive</TabsTrigger>
        </TabsList>

        {/* All Content */}
        <TabsContent value='all'>
          {filteredContent.length === 0 ? (
            <div className='text-center py-12'>
              <h3 className='text-lg font-semibold'>No content found</h3>
              <p className='text-muted-foreground'>
                Try adjusting your filters or search terms.
              </p>
            </div>
          ) : (
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
              {filteredContent.map(content => (
                <ContentCard key={content.id} content={content} />
              ))}
            </div>
          )}
        </TabsContent>

        {/* Videos */}
        <TabsContent value='videos'>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
            {filteredContent
              .filter(content => content.type === 'video')
              .map(content => (
                <ContentCard key={content.id} content={content} />
              ))}
          </div>
        </TabsContent>

        {/* Articles */}
        <TabsContent value='articles'>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
            {filteredContent
              .filter(content => content.type === 'article')
              .map(content => (
                <ContentCard key={content.id} content={content} />
              ))}
          </div>
        </TabsContent>

        {/* Infographics */}
        <TabsContent value='infographics'>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
            {filteredContent
              .filter(content => content.type === 'infographic')
              .map(content => (
                <ContentCard key={content.id} content={content} />
              ))}
          </div>
        </TabsContent>

        {/* Interactive */}
        <TabsContent value='interactive'>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
            {filteredContent
              .filter(content => ['avatar', 'tooltip'].includes(content.type))
              .map(content => (
                <ContentCard key={content.id} content={content} />
              ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
