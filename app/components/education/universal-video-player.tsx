/**
 * UniversalVideoPlayer Component
 *
 * A fallback video player that handles unknown video sources with graceful degradation
 */

'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Play,
  Pause,
  Volume2,
  VolumeX,
  Maximize,
  AlertTriangle,
  ExternalLink,
  RefreshCw,
} from 'lucide-react';

interface UniversalVideoPlayerProps {
  url: string;
  title?: string;
  poster?: string;
  autoPlay?: boolean;
  onError?: (error: string) => void;
  onLoad?: () => void;
  className?: string;
}

export function UniversalVideoPlayer({
  url,
  title = 'Video',
  poster,
  autoPlay = false,
  onError,
  onLoad,
  className = '',
}: UniversalVideoPlayerProps) {
  const [playerState, setPlayerState] = useState<
    'loading' | 'iframe' | 'html5' | 'error' | 'external'
  >('loading');
  const [isPlaying, setIsPlaying] = useState(autoPlay);
  const [isMuted, setIsMuted] = useState(false);
  const [showControls, setShowControls] = useState(true);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [retryCount, setRetryCount] = useState(0);

  const iframeRef = useRef<HTMLIFrameElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);

  // Try different approaches to load the video
  useEffect(() => {
    if (!url) {
      setPlayerState('error');
      setErrorMessage('No video URL provided');
      return;
    }

    // Reset state
    setPlayerState('loading');
    setErrorMessage('');

    // First, try iframe approach
    tryIframeLoad();
  }, [url, retryCount]);

  const tryIframeLoad = () => {
    setPlayerState('iframe');

    // Set a timeout to detect if iframe fails to load
    const timeout = setTimeout(() => {
      if (playerState === 'iframe') {
        tryHtml5Load();
      }
    }, 5000);

    // Cleanup timeout if component unmounts
    return () => clearTimeout(timeout);
  };

  const tryHtml5Load = () => {
    setPlayerState('html5');

    // If HTML5 also fails, we'll handle it in the video's onError event
  };

  const handleIframeLoad = () => {
    if (onLoad) onLoad();
  };

  const handleIframeError = () => {
    tryHtml5Load();
  };

  const handleVideoLoad = () => {
    if (onLoad) onLoad();
  };

  const handleVideoError = () => {
    setPlayerState('error');
    setErrorMessage(
      'Unable to load video. The video format may not be supported or the URL may be incorrect.'
    );
    if (onError) onError('Video load failed');
  };

  const handleRetry = () => {
    setRetryCount(prev => prev + 1);
  };

  const handleOpenExternal = () => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  const togglePlay = () => {
    const video = videoRef.current;
    if (!video) return;

    if (isPlaying) {
      video.pause();
    } else {
      video.play();
    }
    setIsPlaying(!isPlaying);
  };

  const toggleMute = () => {
    const video = videoRef.current;
    if (!video) return;

    video.muted = !isMuted;
    setIsMuted(!isMuted);
  };

  const toggleFullscreen = () => {
    const video = videoRef.current;
    if (!video) return;

    if (document.fullscreenElement) {
      document.exitFullscreen();
    } else {
      video.requestFullscreen();
    }
  };

  // Render loading state
  if (playerState === 'loading') {
    return (
      <div
        className={`relative w-full aspect-video rounded-lg overflow-hidden bg-gray-900 flex items-center justify-center ${className}`}
      >
        <div className='text-center text-white'>
          <RefreshCw className='h-8 w-8 animate-spin mx-auto mb-2' />
          <p>Loading video...</p>
        </div>
      </div>
    );
  }

  // Render iframe player
  if (playerState === 'iframe') {
    return (
      <div
        className={`relative w-full aspect-video rounded-lg overflow-hidden bg-black ${className}`}
      >
        <iframe
          ref={iframeRef}
          src={url}
          title={title}
          className='w-full h-full'
          allow='accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture'
          allowFullScreen
          onLoad={handleIframeLoad}
          onError={handleIframeError}
        />
      </div>
    );
  }

  // Render HTML5 video player
  if (playerState === 'html5') {
    return (
      <div
        className={`relative w-full aspect-video rounded-lg overflow-hidden bg-black group ${className}`}
      >
        <video
          ref={videoRef}
          src={url}
          poster={poster}
          className='w-full h-full'
          autoPlay={autoPlay}
          onLoadedData={handleVideoLoad}
          onError={handleVideoError}
          onPlay={() => setIsPlaying(true)}
          onPause={() => setIsPlaying(false)}
        />

        {/* Custom controls */}
        {showControls && (
          <div className='absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4 opacity-0 group-hover:opacity-100 transition-opacity'>
            <div className='flex items-center justify-between'>
              <div className='flex items-center space-x-2'>
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={togglePlay}
                  className='text-white hover:bg-white/20'
                >
                  {isPlaying ? (
                    <Pause className='h-4 w-4' />
                  ) : (
                    <Play className='h-4 w-4' />
                  )}
                </Button>
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={toggleMute}
                  className='text-white hover:bg-white/20'
                >
                  {isMuted ? (
                    <VolumeX className='h-4 w-4' />
                  ) : (
                    <Volume2 className='h-4 w-4' />
                  )}
                </Button>
              </div>
              <Button
                variant='ghost'
                size='sm'
                onClick={toggleFullscreen}
                className='text-white hover:bg-white/20'
              >
                <Maximize className='h-4 w-4' />
              </Button>
            </div>
          </div>
        )}
      </div>
    );
  }

  // Render error state
  return (
    <div
      className={`relative w-full aspect-video rounded-lg overflow-hidden bg-gray-100 ${className}`}
    >
      <Alert className='h-full flex flex-col items-center justify-center text-center'>
        <AlertTriangle className='h-12 w-12 text-amber-500 mb-4' />
        <AlertDescription className='mb-4 max-w-md'>
          <strong>Unable to load video</strong>
          <br />
          {errorMessage}
        </AlertDescription>
        <div className='flex space-x-2'>
          <Button variant='outline' size='sm' onClick={handleRetry}>
            <RefreshCw className='h-4 w-4 mr-2' />
            Retry
          </Button>
          <Button variant='outline' size='sm' onClick={handleOpenExternal}>
            <ExternalLink className='h-4 w-4 mr-2' />
            Open in New Tab
          </Button>
        </div>
      </Alert>
    </div>
  );
}
