/**
 * VideoPlayer Component
 *
 * A component for playing video content with accessibility features.
 */

'use client';

import React, { useState, useRef, useEffect } from 'react';
import { VideoContent } from '@/types/education';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';

interface VideoPlayerProps {
  video: VideoContent;
  onComplete?: () => void;
  autoPlay?: boolean;
}

export function VideoPlayer({
  video,
  onComplete,
  autoPlay = false,
}: VideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(autoPlay);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [showCaptions, setShowCaptions] = useState(true);
  const [playbackRate, setPlaybackRate] = useState(1);

  // Initialize video
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleLoadedMetadata = () => {
      setDuration(video.duration);
    };

    const handleTimeUpdate = () => {
      setCurrentTime(video.currentTime);

      // Check if video is complete (within 1 second of the end)
      if (video.currentTime >= video.duration - 1 && onComplete) {
        onComplete();
      }
    };

    video.addEventListener('loadedmetadata', handleLoadedMetadata);
    video.addEventListener('timeupdate', handleTimeUpdate);

    return () => {
      video.removeEventListener('loadedmetadata', handleLoadedMetadata);
      video.removeEventListener('timeupdate', handleTimeUpdate);
    };
  }, [onComplete]);

  // Handle play/pause
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    if (isPlaying) {
      video.play().catch(() => setIsPlaying(false));
    } else {
      video.pause();
    }
  }, [isPlaying]);

  // Handle playback rate change
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    video.playbackRate = playbackRate;
  }, [playbackRate]);

  // Format time (seconds to MM:SS)
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Handle slider change
  const handleSliderChange = (value: number[]) => {
    const video = videoRef.current;
    if (!video) return;

    video.currentTime = value[0];
    setCurrentTime(value[0]);
  };

  return (
    <div className='flex flex-col space-y-4 w-full max-w-3xl mx-auto'>
      <div className='relative rounded-lg overflow-hidden bg-black'>
        <video
          ref={videoRef}
          src={video.url}
          className='w-full'
          poster={video.thumbnailUrl}
          controls={false}
        >
          {video.hasCaptions && showCaptions && (
            <track
              kind='captions'
              src={`${video.url.replace('.mp4', '')}.vtt`}
              label='English'
              default
            />
          )}
        </video>
      </div>

      <div className='flex flex-col space-y-2'>
        <div className='flex items-center justify-between'>
          <span className='text-sm'>{formatTime(currentTime)}</span>
          <input
            type='range'
            value={currentTime}
            min={0}
            max={duration || 100}
            step={1}
            onChange={e => handleSliderChange([Number(e.target.value)])}
            className='mx-4 flex-1'
          />
          <span className='text-sm'>{formatTime(duration)}</span>
        </div>

        <div className='flex items-center justify-between'>
          <div className='flex space-x-2'>
            <Button
              variant='outline'
              size='icon'
              onClick={() => setIsPlaying(!isPlaying)}
              aria-label={isPlaying ? 'Pause' : 'Play'}
            >
              {isPlaying ? (
                <span className='h-4 w-4'>⏸️</span>
              ) : (
                <span className='h-4 w-4'>▶️</span>
              )}
            </Button>

            <Button
              variant='outline'
              size='icon'
              onClick={() => {
                if (videoRef.current) {
                  videoRef.current.currentTime = 0;
                  setCurrentTime(0);
                }
              }}
              aria-label='Restart'
            >
              <span className='h-4 w-4'>🔄</span>
            </Button>
          </div>

          <div className='flex items-center space-x-4'>
            {video.hasCaptions && (
              <div className='flex items-center space-x-2'>
                <input
                  type='checkbox'
                  id='captions'
                  checked={showCaptions}
                  onChange={e => setShowCaptions(e.target.checked)}
                  className='h-4 w-4'
                />
                <Label htmlFor='captions'>Captions</Label>
              </div>
            )}

            <select
              value={playbackRate}
              onChange={e => setPlaybackRate(Number(e.target.value))}
              className='bg-background border rounded px-2 py-1 text-sm'
              aria-label='Playback speed'
            >
              <option value={0.5}>0.5x</option>
              <option value={0.75}>0.75x</option>
              <option value={1}>1x</option>
              <option value={1.25}>1.25x</option>
              <option value={1.5}>1.5x</option>
              <option value={2}>2x</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  );
}
