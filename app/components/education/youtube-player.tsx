/**
 * YouTubePlayer Component (Universal Video Player)
 *
 * A component for playing videos from multiple platforms (YouTube, Vimeo, HTML5)
 * with proper embedding and size controls.
 */

'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Monitor } from 'lucide-react';
import {
  getSafeVideoInfo,
  formatVideoDuration,
  getPlayerSizeClasses,
  getPlayerSizeLabel,
  getNextPlayerSize,
} from '@/utils/video-utils';
import { UniversalVideoPlayer } from './universal-video-player';

interface YouTubePlayerProps {
  video: any;
  onComplete?: () => void;
  autoPlay?: boolean;
}

type PlayerSize = 'small' | 'medium' | 'large';

export function YouTubePlayer({
  video,
  onComplete,
  autoPlay = false,
}: YouTubePlayerProps) {
  const [playerSize, setPlayerSize] = useState<PlayerSize>('medium');

  // Get video information using safe utility function
  const videoInfo = getSafeVideoInfo(video.contentUrl, autoPlay);

  // Cycle through sizes
  const cycleSizes = () => {
    setPlayerSize(getNextPlayerSize(playerSize));
  };

  if (!video.contentUrl) {
    return (
      <div className='flex items-center justify-center h-64 bg-muted rounded-lg'>
        <p className='text-muted-foreground'>Video not available</p>
      </div>
    );
  }

  if (videoInfo.isEmbeddable) {
    return (
      <div className='flex flex-col space-y-4 w-full'>
        {/* Player Size Controls */}
        <div className='flex items-center justify-between'>
          <div className='space-y-2'>
            <h2 className='text-2xl font-bold'>{video.title}</h2>
            {video.description && (
              <p className='text-muted-foreground'>{video.description}</p>
            )}
          </div>
          <div className='flex items-center space-x-2'>
            <span className='text-sm text-muted-foreground'>
              Size: {getPlayerSizeLabel(playerSize)}
            </span>
            <Button
              variant='outline'
              size='sm'
              onClick={cycleSizes}
              className='flex items-center space-x-1'
            >
              <Monitor className='h-4 w-4' />
              <span>Resize</span>
            </Button>
          </div>
        </div>

        {/* Video Player */}
        <div className={getPlayerSizeClasses(playerSize)}>
          <div className='relative w-full aspect-video rounded-lg overflow-hidden bg-black'>
            <iframe
              src={videoInfo.embedUrl}
              title={video.title}
              className='w-full h-full'
              allow='accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture'
              allowFullScreen
            />
          </div>
        </div>

        {/* Video Info */}
        <div className='space-y-2'>
          {video.duration && (
            <p className='text-sm text-muted-foreground'>
              Duration: {formatVideoDuration(video.duration)}
            </p>
          )}
          <p className='text-xs text-muted-foreground capitalize'>
            {videoInfo.type} video
          </p>
        </div>
      </div>
    );
  }

  // Fallback for non-embeddable videos or unknown types
  return (
    <div className='flex flex-col space-y-4 w-full'>
      {/* Player Size Controls */}
      <div className='flex items-center justify-between'>
        <div className='space-y-2'>
          <h2 className='text-2xl font-bold'>{video.title}</h2>
          {video.description && (
            <p className='text-muted-foreground'>{video.description}</p>
          )}
        </div>
        <div className='flex items-center space-x-2'>
          <span className='text-sm text-muted-foreground'>
            Size: {getPlayerSizeLabel(playerSize)}
          </span>
          <Button
            variant='outline'
            size='sm'
            onClick={cycleSizes}
            className='flex items-center space-x-1'
          >
            <Monitor className='h-4 w-4' />
            <span>Resize</span>
          </Button>
        </div>
      </div>

      {/* Universal Video Player */}
      <div className={getPlayerSizeClasses(playerSize)}>
        <UniversalVideoPlayer
          url={video.contentUrl}
          title={video.title}
          poster={video.thumbnailUrl}
          autoPlay={autoPlay}
          onLoad={() => {
            // Video loaded successfully
            console.log('Video loaded:', video.title);
          }}
          onError={error => {
            // Handle video error
            console.error('Video error:', error, video.title);
            if (onComplete) onComplete(); // Call completion callback on error too
          }}
        />
      </div>

      {/* Video Info */}
      <div className='space-y-2'>
        {video.duration && (
          <p className='text-sm text-muted-foreground'>
            Duration: {formatVideoDuration(video.duration)}
          </p>
        )}
        <p className='text-xs text-muted-foreground capitalize'>
          {videoInfo.type} video
          {videoInfo.fallbackMessage && (
            <span className='block text-amber-600 mt-1'>
              {videoInfo.fallbackMessage}
            </span>
          )}
        </p>
      </div>
    </div>
  );
}
