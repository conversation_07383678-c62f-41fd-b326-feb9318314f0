'use client';

import React from 'react';
import { EmergencyContact, VerificationStatus } from './types';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Edit, Trash2, Star, Mail, Phone, RefreshCw } from 'lucide-react';

interface ContactListProps {
  contacts: EmergencyContact[];
  onEdit: (contact: EmergencyContact) => void;
  onDelete: (contactId: string) => void;
  onResendVerification: (contactId: string) => void;
}

export function ContactList({
  contacts,
  onEdit,
  onDelete,
  onResendVerification,
}: ContactListProps) {
  // Group contacts by type
  const medicalContacts = contacts.filter(
    contact => contact.type === 'Medical'
  );
  const otherContacts = contacts.filter(contact => contact.type === 'Other');

  const renderContactCard = (contact: EmergencyContact) => {
    const getStatusBadge = (status: VerificationStatus) => {
      switch (status) {
        case 'Verified':
          return <Badge className='bg-green-2010c'>Verified</Badge>;
        case 'Pending':
          return (
            <Badge
              variant='outline'
              className='text-blue-2157c border-blue-2157c'
            >
              Pending
            </Badge>
          );
        case 'Expired':
          return <Badge variant='destructive'>Expired</Badge>;
        default:
          return null;
      }
    };

    return (
      <Card key={contact.id} className='mb-4'>
        <CardContent className='pt-6'>
          <div className='flex justify-between items-start'>
            <div className='flex-1'>
              <div className='flex items-center gap-2'>
                <h3 className='text-lg font-semibold'>{contact.name}</h3>
                {contact.isPrimary && (
                  <Star
                    className='h-4 w-4 text-yellow-500'
                    fill='currentColor'
                  />
                )}
                {getStatusBadge(contact.status)}
              </div>
              <p className='text-sm text-muted-foreground'>
                {contact.relationship}
              </p>

              <div className='mt-2 space-y-1'>
                <div className='flex items-center gap-2'>
                  <Mail className='h-4 w-4 text-muted-foreground' />
                  <span className='text-sm'>{contact.email}</span>
                </div>
                <div className='flex items-center gap-2'>
                  <Phone className='h-4 w-4 text-muted-foreground' />
                  <span className='text-sm'>{contact.phone}</span>
                </div>
              </div>
            </div>

            <div className='flex gap-2'>
              <Button
                variant='outline'
                size='sm'
                onClick={() => onEdit(contact)}
              >
                <Edit className='h-4 w-4' />
              </Button>
              <Button
                variant='outline'
                size='sm'
                onClick={() => onDelete(contact.id)}
                className='text-destructive border-destructive hover:bg-destructive/10'
              >
                <Trash2 className='h-4 w-4' />
              </Button>
              {contact.status !== 'Verified' && (
                <Button
                  variant='outline'
                  size='sm'
                  onClick={() => onResendVerification(contact.id)}
                  className='text-blue-2157c border-blue-2157c hover:bg-blue-2157c/10'
                >
                  <RefreshCw className='h-4 w-4' />
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className='space-y-6'>
      {medicalContacts.length > 0 && (
        <div>
          <CardHeader className='px-0 pb-2'>
            <CardTitle className='text-xl'>Medical Contacts</CardTitle>
          </CardHeader>
          <div>{medicalContacts.map(renderContactCard)}</div>
        </div>
      )}

      {otherContacts.length > 0 && (
        <div>
          <CardHeader className='px-0 pb-2'>
            <CardTitle className='text-xl'>Other Contacts</CardTitle>
          </CardHeader>
          <div>{otherContacts.map(renderContactCard)}</div>
        </div>
      )}

      {contacts.length === 0 && (
        <Card className='bg-muted/50'>
          <CardContent className='py-6 text-center'>
            <p className='text-muted-foreground'>
              No emergency contacts added yet.
            </p>
            <p className='text-sm text-muted-foreground mt-1'>
              Add contacts to ensure your information can be shared in case of
              emergency.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
