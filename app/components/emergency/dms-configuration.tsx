'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Save } from 'lucide-react';
import { DMSConfigData } from '@/hooks/useDeadMansSwitch';
import { DMSStatus } from './types';
import { PauseCircle, PlayCircle } from 'lucide-react';

// Define the form schema using zod
const dmsFormSchema = z.object({
  id: z.string().optional(),
  userId: z.string().optional(),
  isEnable: z.boolean().default(false),
  frequency: z.enum(['WEEKLY', 'BIWEEKLY', 'MONTHLY', 'CUSTOM']),
  customFrequencyDays: z.number().nullable().optional(),
  communicationMethod: z.enum(['EMAIL', 'SMS', 'BOTH']),
  escalationProtocol: z.enum(['STANDARD', 'SENSITIVE', 'RELAXED', 'CUSTOM']),
  customEscalationSteps: z.number().nullable().optional(),
  personalMessage: z.string().nullable().optional(),
  status: z.string().optional(),
  pauseReason: z.string().nullable().optional(),
  pauseUntil: z.string().nullable().optional(),
  nextCheckIn: z.string().nullable().optional(),
  lastCheckIn: z.string().nullable().optional(),
});

type DMSFormValues = z.infer<typeof dmsFormSchema>;

interface DMSConfigFormProps {
  onSubmit: (config: Partial<DMSConfigData>) => void;
  onCancel: () => void;
  initialData?: Partial<DMSConfigData>;
}

export function DMSConfigForm({
  onSubmit,
  onCancel,
  initialData = {},
}: DMSConfigFormProps) {
  const [isSaving, setIsSaving] = useState(false);

  // Initialize form with React Hook Form
  const form = useForm({
    resolver: zodResolver(dmsFormSchema),
    defaultValues: {
      id: initialData.id,
      userId: initialData.userId,
      isEnable: initialData.isEnable ?? false,
      frequency: (initialData.frequency as any) ?? 'WEEKLY',
      customFrequencyDays: initialData.customFrequencyDays ?? null,
      communicationMethod: (initialData.communicationMethod as any) ?? 'EMAIL',
      escalationProtocol: (initialData.escalationProtocol as any) ?? 'STANDARD',
      customEscalationSteps: initialData.customEscalationSteps ?? null,
      personalMessage: initialData.personalMessage ?? '',
      status: initialData.status ?? 'Active',
      pauseReason: initialData.pauseReason ?? null,
      pauseUntil: initialData.pauseUntil ?? null,
      nextCheckIn: initialData.nextCheckIn ?? null,
      lastCheckIn: initialData.lastCheckIn ?? null,
    },
  });

  // Watch form values for conditional rendering
  const watchFrequency = form.watch('frequency');
  const watchEscalationProtocol = form.watch('escalationProtocol');

  // Handle form submission
  const handleSubmit = async (data: DMSFormValues) => {
    setIsSaving(true);
    try {
      // Ensure customFrequencyDays is included when frequency is CUSTOM
      const customData = {
        ...data,
        status: data.status as DMSStatus,
      };
      onSubmit(customData);
    } catch (error) {
      console.error('Failed to save DMS configuration:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const cancelPause = () => {
    // Update form values to cancel the pause
    form.setValue('status', 'ACTIVE');
    form.setValue('pauseReason', null);
    form.setValue('pauseUntil', null);

    // If we want to immediately save the changes
    const formValues = form.getValues();
    handleSubmit({
      ...formValues,
      isEnable: Boolean(formValues.isEnable),
      status: 'ACTIVE',
      pauseReason: null,
      pauseUntil: null,
    });
  };

  return (
    <Card className='w-full'>
      <CardHeader>
        <CardTitle className='text-xl font-bold'>
          Configure Wellness Checks
        </CardTitle>
      </CardHeader>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)}>
          <CardContent className='space-y-6'>
            {/* Enable/Disable Switch */}
            {/* <FormField
              control={form.control}
              name="isEnable"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base font-semibold">
                      Enable Dead Man's Switch
                    </FormLabel>
                    <FormDescription>
                      When enabled, the system will monitor your check-ins
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            /> */}
            {form.watch('status') === 'PAUSED' && (
              <div className='bg-blue-50 p-4 rounded-md mb-6 border border-blue-100'>
                <div className='flex justify-between items-center'>
                  <div>
                    <h3 className='font-medium flex items-center'>
                      <PauseCircle className='mr-2 h-4 w-4 text-blue-500' />
                      Wellness Checks is Paused
                    </h3>
                    {form.watch('pauseUntil') && (
                      <p className='text-sm text-muted-foreground mt-1'>
                        Paused until:{' '}
                        {initialData.pauseUntil
                          ? new Date(
                              initialData.pauseUntil
                            ).toLocaleDateString()
                          : 'N/A'}
                      </p>
                    )}
                    {form.watch('pauseReason') && (
                      <p className='text-sm text-muted-foreground mt-1'>
                        Reason: {form.watch('pauseReason')}
                      </p>
                    )}
                  </div>
                  <Button
                    type='button'
                    variant='outline'
                    size='sm'
                    onClick={cancelPause}
                  >
                    <PlayCircle className='mr-2 h-4 w-4' />
                    Cancel Pause
                  </Button>
                </div>
              </div>
            )}
            {/* Check-In Frequency */}
            <FormField
              control={form.control}
              name='frequency'
              render={({ field }) => (
                <FormItem className='space-y-4'>
                  <FormLabel className='text-base font-semibold'>
                    Check-In Frequency
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className='space-y-2'
                    >
                      <FormItem className='flex items-center space-x-2'>
                        <FormControl>
                          <RadioGroupItem value='WEEKLY' id='weekly' />
                        </FormControl>
                        <FormLabel htmlFor='weekly' className='font-normal'>
                          Weekly
                        </FormLabel>
                      </FormItem>
                      <FormItem className='flex items-center space-x-2'>
                        <FormControl>
                          <RadioGroupItem value='BIWEEKLY' id='biweekly' />
                        </FormControl>
                        <FormLabel htmlFor='biweekly' className='font-normal'>
                          Bi-weekly
                        </FormLabel>
                      </FormItem>
                      <FormItem className='flex items-center space-x-2'>
                        <FormControl>
                          <RadioGroupItem value='MONTHLY' id='monthly' />
                        </FormControl>
                        <FormLabel htmlFor='monthly' className='font-normal'>
                          Monthly
                        </FormLabel>
                      </FormItem>
                      <FormItem className='flex items-center space-x-2'>
                        <FormControl>
                          <RadioGroupItem value='CUSTOM' id='custom' />
                        </FormControl>
                        <FormLabel htmlFor='custom' className='font-normal'>
                          Custom
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Custom Frequency Days */}
            {watchFrequency === 'CUSTOM' && (
              <FormField
                control={form.control}
                name='customFrequencyDays'
                render={({ field }) => (
                  <FormItem className='pl-6 pt-2'>
                    <FormLabel htmlFor='customDays'>Every</FormLabel>
                    <div className='flex items-center gap-2 mt-1'>
                      <FormControl>
                        <Input
                          id='customDays'
                          type='number'
                          min={1}
                          max={90}
                          value={field.value || ''}
                          onChange={e => {
                            const value = e.target.value
                              ? parseInt(e.target.value)
                              : null;
                            field.onChange(value);
                          }}
                          className='w-20'
                        />
                      </FormControl>
                      <span>days</span>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* Communication Method */}
            <FormField
              control={form.control}
              name='communicationMethod'
              render={({ field }) => (
                <FormItem className='space-y-4'>
                  <FormLabel className='text-base font-semibold'>
                    Communication Method
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className='space-y-2'
                    >
                      <FormItem className='flex items-center space-x-2'>
                        <FormControl>
                          <RadioGroupItem value='EMAIL' id='email' />
                        </FormControl>
                        <FormLabel htmlFor='email' className='font-normal'>
                          Email
                        </FormLabel>
                      </FormItem>
                      <FormItem className='flex items-center space-x-2'>
                        <FormControl>
                          <RadioGroupItem value='SMS' id='sms' />
                        </FormControl>
                        <FormLabel htmlFor='sms' className='font-normal'>
                          SMS (Text Message)
                        </FormLabel>
                      </FormItem>
                      <FormItem className='flex items-center space-x-2'>
                        <FormControl>
                          <RadioGroupItem value='BOTH' id='both' />
                        </FormControl>
                        <FormLabel htmlFor='both' className='font-normal'>
                          Both Email and SMS
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Escalation Protocol */}
            <FormField
              control={form.control}
              name='escalationProtocol'
              render={({ field }) => (
                <FormItem className='space-y-4'>
                  <FormLabel className='text-base font-semibold'>
                    Escalation Protocol
                  </FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder='Select escalation protocol' />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value='STANDARD'>
                        Standard (3 missed check-ins)
                      </SelectItem>
                      <SelectItem value='SENSITIVE'>
                        Sensitive (2 missed check-ins)
                      </SelectItem>
                      <SelectItem value='RELAXED'>
                        Relaxed (4 missed check-ins)
                      </SelectItem>
                      <SelectItem value='CUSTOM'>Custom</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Custom Escalation Protocol Info */}
            {watchEscalationProtocol === 'CUSTOM' && (
              <div className='pt-2'>
                <p className='text-sm text-muted-foreground mb-2'>
                  Custom escalation protocols can be configured by contacting
                  support.
                </p>
              </div>
            )}

            {/* Personal Message */}
            <FormField
              control={form.control}
              name='personalMessage'
              render={({ field }) => (
                <FormItem className='space-y-2'>
                  <FormLabel
                    htmlFor='message'
                    className='text-base font-semibold'
                  >
                    Personal Message (Optional)
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      id='message'
                      value={field.value || ''}
                      onChange={e => field.onChange(e.target.value)}
                      placeholder='Add a personal message to be sent to your emergency contacts'
                      className='min-h-[100px]'
                    />
                  </FormControl>
                  <FormDescription>
                    This message will be included in notifications to your
                    emergency contacts.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>

          <CardFooter className='flex justify-between'>
            <Button
              type='button'
              variant='outline'
              onClick={onCancel}
              disabled={isSaving}
            >
              Cancel
            </Button>
            <Button type='submit' disabled={isSaving}>
              <Save className='mr-2 h-4 w-4' />
              {isSaving ? 'Saving...' : 'Save Configuration'}
            </Button>
          </CardFooter>
        </form>
      </Form>
    </Card>
  );
}
