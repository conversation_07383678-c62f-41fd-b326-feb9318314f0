'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON>ooter,
  Card<PERSON>eader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Document, AccessTrigger } from './types';
import {
  FileText,
  Download,
  Eye,
  Calendar,
  AlertTriangle,
  Lock,
} from 'lucide-react';

interface DocumentAccessProps {
  documents: Document[];
  accessType: AccessTrigger;
  expiryDate?: string;
  onView: (documentId: string) => void;
  onDownload: (documentId: string) => void;
}

export function DocumentAccess({
  documents,
  accessType,
  expiryDate,
  onView,
  onDownload,
}: DocumentAccessProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <div className='space-y-6'>
      <div className='flex justify-between items-center'>
        <h2 className='text-xl font-semibold'>Available Documents</h2>
        <Badge
          className={
            accessType === 'Incapacitation' ? 'bg-blue-2157c' : 'bg-destructive'
          }
        >
          {accessType === 'Incapacitation'
            ? 'Temporary Access'
            : 'Permanent Access'}
        </Badge>
      </div>

      {accessType === 'Incapacitation' && expiryDate ? (
        <div className='flex items-center text-sm text-muted-foreground mb-4'>
          <Calendar className='mr-2 h-4 w-4' />
          Access expires on {formatDate(expiryDate)}
        </div>
      ) : (
        accessType === 'Death' && (
          <Card className='mb-4 border-destructive/20 bg-destructive/5'>
            <CardContent className='py-4 flex items-start gap-3'>
              <div className='bg-destructive/10 p-2 rounded-full'>
                <Lock className='h-5 w-5 text-destructive' />
              </div>
              <div>
                <h3 className='font-semibold mb-1 text-destructive'>
                  Permanent Document Access
                </h3>
                <p className='text-sm text-muted-foreground'>
                  You have been granted permanent access to these documents
                  following verification of a death certificate. These documents
                  are now immutable and cannot be modified.
                </p>
              </div>
            </CardContent>
          </Card>
        )
      )}

      {documents.length === 0 ? (
        <Card className='bg-muted/50'>
          <CardContent className='py-6 text-center'>
            <p className='text-muted-foreground'>No documents available.</p>
          </CardContent>
        </Card>
      ) : (
        <div className='grid gap-4'>
          {documents.map(document => (
            <Card key={document.id} className='overflow-hidden'>
              <CardHeader className='pb-2'>
                <CardTitle className='text-lg flex items-center'>
                  <FileText className='mr-2 h-5 w-5 text-muted-foreground' />
                  {document.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className='flex justify-between items-center text-sm'>
                  <span className='text-muted-foreground'>{document.type}</span>
                  <span className='text-muted-foreground'>
                    Last updated: {formatDate(document.updatedAt)}
                  </span>
                </div>
              </CardContent>
              <CardFooter className='bg-muted/20 pt-2 pb-2 flex justify-end gap-2'>
                <Button
                  variant='outline'
                  size='sm'
                  onClick={() => onView(document.id)}
                >
                  <Eye className='mr-1 h-4 w-4' />
                  View
                </Button>
                <Button
                  variant='outline'
                  size='sm'
                  onClick={() => onDownload(document.id)}
                >
                  <Download className='mr-1 h-4 w-4' />
                  Download
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
