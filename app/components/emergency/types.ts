// Emergency Contact Types
export type ContactType = 'Medical' | 'Other';
export type VerificationStatus = 'Pending' | 'Verified' | 'Expired';

export interface EmergencyContact {
  id: string;
  name: string;
  relationship: string;
  phone: string;
  email: string;
  type: ContactType;
  status: VerificationStatus;
  isPrimary?: boolean;
}

// Dead Man's Switch Types
export type CheckInFrequency = 'WEEKLY' | 'BIWEEKLY' | 'MONTHLY' | 'CUSTOM';

// Define enum types if they're not properly imported
export type CommunicationMethod = 'EMAIL' | 'SMS' | 'BOTH';
export type EscalationProtocol = 'STANDARD' | 'SENSITIVE' | 'RELAXED' | 'CUSTOM';
export type DMSStatus = 'ACTIVE' | 'DISABLED' | 'PAUSED';

export interface DMSConfiguration {
  id: string;
  userId: string;
  frequency: CheckInFrequency;
  customFrequencyDays?: number;
  communicationMethod: CommunicationMethod;
  escalationProtocol: EscalationProtocol;
  customEscalationSteps?: number;
  personalMessage?: string;
  status: DMSStatus;
  pauseReason?: string;
  pauseUntil?: string;
  nextCheckIn?: string;
  lastCheckIn?: string;
}

// Emergency Access Types
export type AccessTrigger = 'Incapacitation' | 'Death';
export type AccessStatus = 'Pending' | 'Approved' | 'Rejected' | 'Expired';

export interface EmergencyAccess {
  id: string;
  userId: string;
  contactId: string;
  trigger: AccessTrigger;
  status: AccessStatus;
  expiryDate?: string;
  evidenceUrl?: string;
}

export interface Document {
  id: string;
  userId: string;
  title: string;
  type: string;
  url: string;
  createdAt: string;
  updatedAt: string;
}
