'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  <PERSON>,
  CardContent,
  <PERSON><PERSON>ooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, LockKeyhole } from 'lucide-react';

interface VerificationFormProps {
  onVerify: (code: string) => void;
  onResendCode: () => void;
  embedded?: boolean; // New prop to control if it's embedded in another card
}

export function VerificationForm({
  onVerify,
  onResendCode,
  embedded = false,
}: VerificationFormProps) {
  const [verificationCode, setVerificationCode] = useState('');
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!verificationCode.trim()) {
      setError('Please enter the verification code');
      return;
    }

    // Simple validation for a 6-digit code
    if (!/^\d{6}$/.test(verificationCode)) {
      setError('Please enter a valid 6-digit verification code');
      return;
    }

    setError(null);
    onVerify(verificationCode);
  };

  // If embedded, return just the form content without Card wrapper
  if (embedded) {
    return (
      <form onSubmit={handleSubmit} className='space-y-4'>
        {error && (
          <Alert variant='destructive'>
            <AlertCircle className='h-4 w-4' />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className='space-y-2'>
          <Label htmlFor='verificationCode'>Verification Code</Label>
          <Input
            id='verificationCode'
            value={verificationCode}
            onChange={e => setVerificationCode(e.target.value)}
            placeholder='Enter 6-digit code'
            className={error ? 'border-destructive' : ''}
            maxLength={6}
          />
          <p className='text-xs text-muted-foreground'>
            Enter the 6-digit verification code sent to your email or phone.
          </p>
        </div>

        <div className='flex flex-col space-y-3'>
          <Button type='submit' className='w-full'>
            Verify Identity
          </Button>
          <div className='text-center'>
            <span
              className='text-sm text-muted-foreground hover:text-foreground cursor-pointer underline-offset-4 hover:underline'
              onClick={onResendCode}
            >
              Didn't receive the code? Resend
            </span>
          </div>
        </div>
      </form>
    );
  }

  // Original standalone Card version
  return (
    <Card className='w-full max-w-md mx-auto'>
      <CardHeader>
        <CardTitle className='text-xl font-bold flex items-center'>
          <LockKeyhole className='mr-2 h-5 w-5' />
          Verify Your Identity
        </CardTitle>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent className='space-y-4'>
          {error && (
            <Alert variant='destructive'>
              <AlertCircle className='h-4 w-4' />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className='space-y-2'>
            <Label htmlFor='verificationCode'>Verification Code</Label>
            <Input
              id='verificationCode'
              value={verificationCode}
              onChange={e => setVerificationCode(e.target.value)}
              placeholder='Enter 6-digit code'
              className={error ? 'border-destructive' : ''}
              maxLength={6}
            />
            <p className='text-xs text-muted-foreground'>
              Enter the 6-digit verification code sent to your email or phone.
            </p>
          </div>
        </CardContent>

        <CardFooter className='flex flex-col space-y-4'>
          <div className='w-full text-center'>
            <span
              className='text-link underline cursor-pointer text-sm font-medium'
              onClick={onResendCode}
            >
              Didn't receive the code? Resend
            </span>
          </div>
          <Button type='submit'>Verify</Button>
        </CardFooter>
      </form>
    </Card>
  );
}
