'use client';

import React from 'react';
import { Badge } from '@/components/ui/badge';

export type ProgressItem = {
  key: string;
  label: string;
  completed?: boolean;
};

interface ProgressSidebarProps {
  items: ProgressItem[];
  current: string;
  onSelect: (key: string) => void;
}

export function ProgressSidebar({
  items,
  current,
  onSelect,
}: ProgressSidebarProps) {
  return (
    <aside className='w-full sm:w-64 bg-white rounded-xl border shadow-sm p-4'>
      <h3 className='font-semibold mb-3'>Interview Progress</h3>
      <ol className='space-y-1'>
        {items.map((item, idx) => (
          <li key={item.key}>
            <button
              className={`w-full flex items-center justify-between rounded-md px-3 py-2 text-left text-sm transition-colors hover:bg-muted ${
                current === item.key ? 'bg-muted' : ''
              }`}
              onClick={() => onSelect(item.key)}
            >
              <span className='flex items-center gap-2'>
                <span className='text-muted-foreground'>{idx + 1}.</span>
                <span>{item.label}</span>
              </span>
              {item.completed ? (
                <Badge className='bg-emerald-600 text-white border-transparent'>
                  Done
                </Badge>
              ) : (
                <Badge variant='destructive'>•</Badge>
              )}
            </button>
          </li>
        ))}
      </ol>
    </aside>
  );
}

export default ProgressSidebar;
