'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

type Status = 'complete' | 'incomplete' | 'warning' | undefined;

interface SectionCardProps {
  title: string;
  description?: string;
  status?: Status;
  className?: string;
  children: React.ReactNode;
  right?: React.ReactNode;
}

const statusLabel = (status: Status) => {
  if (!status) return null;
  switch (status) {
    case 'complete':
      return (
        <Badge className='bg-emerald-600 text-white border-transparent'>
          Complete
        </Badge>
      );
    case 'warning':
      return (
        <Badge className='bg-amber-500 text-white border-transparent'>
          Needs attention
        </Badge>
      );
    default:
      return <Badge variant='outline'>Incomplete</Badge>;
  }
};

export function SectionCard({
  title,
  description,
  status,
  className,
  children,
  right,
}: SectionCardProps) {
  return (
    <Card className={className}>
      <CardHeader className='flex flex-row items-start justify-between gap-4'>
        <div>
          <CardTitle className='text-lg'>{title}</CardTitle>
          {description && (
            <p className='text-sm text-muted-foreground mt-1'>{description}</p>
          )}
        </div>
        <div className='flex items-center gap-2'>
          {right}
          {statusLabel(status)}
        </div>
      </CardHeader>
      <CardContent>{children}</CardContent>
    </Card>
  );
}

export default SectionCard;
