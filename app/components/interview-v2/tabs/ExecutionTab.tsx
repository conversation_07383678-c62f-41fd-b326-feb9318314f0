'use client';

import React from 'react';
import SectionCard from '../SectionCard';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';

interface ExecutionTabProps {
  value: { executionState?: string; executionCounty?: string };
  onChange: (partial: Partial<ExecutionTabProps['value']>) => void;
}

export function ExecutionTab({ value, onChange }: ExecutionTabProps) {
  return (
    <div className='space-y-6'>
      <SectionCard
        title='Execution'
        description='Preferences for how you will sign and execute your documents.'
        status='incomplete'
      >
        <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
          <div className='space-y-2'>
            <Label>State for Execution</Label>
            <Input
              placeholder='e.g., Alabama'
              value={value.executionState || ''}
              onChange={e => onChange({ executionState: e.target.value })}
            />
          </div>
          <div className='space-y-2'>
            <Label>County (optional)</Label>
            <Input
              placeholder='e.g., Jefferson County'
              value={value.executionCounty || ''}
              onChange={e => onChange({ executionCounty: e.target.value })}
            />
          </div>
        </div>
      </SectionCard>
    </div>
  );
}

export default ExecutionTab;
