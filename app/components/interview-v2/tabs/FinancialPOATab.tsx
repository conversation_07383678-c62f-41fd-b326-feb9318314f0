'use client';

import React from 'react';
import SectionCard from '../SectionCard';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';

interface FinancialPOATabProps {
  value: { agent?: string; altAgent?: string };
  onChange: (partial: Partial<FinancialPOATabProps['value']>) => void;
}

export function FinancialPOATab({ value, onChange }: FinancialPOATabProps) {
  return (
    <div className='space-y-6'>
      <SectionCard
        title='Financial Power of Attorney'
        description='Choose who can manage your finances if you are unable to.'
        status='incomplete'
      >
        <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
          <div className='space-y-2'>
            <Label>Agent</Label>
            <Input
              placeholder='Full name'
              value={value.agent || ''}
              onChange={e => onChange({ agent: e.target.value })}
            />
          </div>
          <div className='space-y-2'>
            <Label>Alternate Agent</Label>
            <Input
              placeholder='Full name'
              value={value.altAgent || ''}
              onChange={e => onChange({ altAgent: e.target.value })}
            />
          </div>
        </div>
      </SectionCard>
    </div>
  );
}

export default FinancialPOATab;
