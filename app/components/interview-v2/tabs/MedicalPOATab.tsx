'use client';

import React from 'react';
import SectionCard from '../SectionCard';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';

interface MedicalPOATabProps {
  value: { healthcareAgent?: string; altHealthcareAgent?: string };
  onChange: (partial: Partial<MedicalPOATabProps['value']>) => void;
}

export function MedicalPOATab({ value, onChange }: MedicalPOATabProps) {
  return (
    <div className='space-y-6'>
      <SectionCard
        title='Medical Power of Attorney'
        description='Choose a healthcare proxy.'
        status='incomplete'
      >
        <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
          <div className='space-y-2'>
            <Label>Healthcare Agent</Label>
            <Input
              placeholder='Full name'
              value={value.healthcareAgent || ''}
              onChange={e => onChange({ healthcareAgent: e.target.value })}
            />
          </div>
          <div className='space-y-2'>
            <Label>Alternate Healthcare Agent</Label>
            <Input
              placeholder='Full name'
              value={value.altHealthcareAgent || ''}
              onChange={e => onChange({ altHealthcareAgent: e.target.value })}
            />
          </div>
        </div>
      </SectionCard>
    </div>
  );
}

export default MedicalPOATab;
