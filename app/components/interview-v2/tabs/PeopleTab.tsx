'use client';

import React from 'react';
import SectionCard from '../SectionCard';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';

interface PeopleTabProps {
  value: { firstName?: string; lastName?: string; relationship?: string };
  onChange: (partial: Partial<PeopleTabProps['value']>) => void;
}

export function PeopleTab({ value, onChange }: PeopleTabProps) {
  return (
    <div className='space-y-6'>
      <SectionCard
        title='People Library'
        description='Add the important people in your life so you can reference them in later steps.'
        status='incomplete'
        // right={
        //   <Button size='sm' variant='secondary'>
        //     Add Person
        //   </Button>
        // }
      >
        <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
          <div className='space-y-2'>
            <Label>First Name</Label>
            <Input
              placeholder='e.g., Alex'
              value={value.firstName || ''}
              onChange={e => onChange({ firstName: e.target.value })}
            />
          </div>
          <div className='space-y-2'>
            <Label>Last Name</Label>
            <Input
              placeholder='e.g., Johnson'
              value={value.lastName || ''}
              onChange={e => onChange({ lastName: e.target.value })}
            />
          </div>
          <div className='space-y-2'>
            <Label>Relationship</Label>
            <Input
              placeholder='e.g., Friend, Sibling'
              value={value.relationship || ''}
              onChange={e => onChange({ relationship: e.target.value })}
            />
          </div>
        </div>
      </SectionCard>
    </div>
  );
}

export default PeopleTab;
