'use client';

import React from 'react';
import SectionCard from '../SectionCard';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { DatePicker } from '@/components/ui/date-picker';

interface ProfileTabProps {
  value: {
    firstName?: string;
    lastName?: string;
    state?: string;
    dob?: string;
  };
  onChange: (partial: Partial<ProfileTabProps['value']>) => void;
}

export function ProfileTab({ value, onChange }: ProfileTabProps) {
  const dobDate = value.dob ? new Date(value.dob) : undefined;

  return (
    <div className='space-y-6'>
      <SectionCard
        title='Your Profile'
        description='Tell us a bit about you.'
        status='incomplete'
      >
        <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
          <div className='space-y-2'>
            <Label htmlFor='firstName'>First Name</Label>
            <Input
              id='firstName'
              placeholder='First name'
              value={value.firstName || ''}
              onChange={e => onChange({ firstName: e.target.value })}
            />
          </div>
          <div className='space-y-2'>
            <Label htmlFor='lastName'>Last Name</Label>
            <Input
              id='lastName'
              placeholder='Last name'
              value={value.lastName || ''}
              onChange={e => onChange({ lastName: e.target.value })}
            />
          </div>
          <div className='space-y-2'>
            <Label htmlFor='state'>State</Label>
            <Input
              id='state'
              placeholder='Select your state'
              value={value.state || ''}
              onChange={e => onChange({ state: e.target.value })}
            />
          </div>
          <div className='space-y-2'>
            <Label>Date of Birth</Label>
            <DatePicker
              date={dobDate}
              setDate={d => onChange({ dob: d ? d.toISOString() : undefined })}
            />
          </div>
        </div>
      </SectionCard>
    </div>
  );
}

export default ProfileTab;
