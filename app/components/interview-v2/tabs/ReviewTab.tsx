'use client';

import React from 'react';
import SectionCard from '../SectionCard';
import { Button } from '@/components/ui/button';

interface ReviewTabProps {
  onSave: (nextStep?: string) => Promise<any>;
}

export function ReviewTab({ onSave }: ReviewTabProps) {
  return (
    <div className='space-y-6'>
      <SectionCard
        title='Review & Generate'
        description='Review your information and prepare to generate your documents.'
        status='incomplete'
        right={
          <Button
            variant='destructive'
            size='sm'
            onClick={() => onSave('profile')}
          >
            Reset Interview
          </Button>
        }
      >
        <div className='space-y-2 text-sm text-muted-foreground'>
          <p>We will show a summary of your answers here in a future step.</p>
        </div>
        <div className='mt-4 flex gap-2'>
          <Button onClick={() => onSave()}>Save and Exit</Button>
          <Button disabled>Generate Documents</Button>
        </div>
      </SectionCard>
    </div>
  );
}

export default ReviewTab;
