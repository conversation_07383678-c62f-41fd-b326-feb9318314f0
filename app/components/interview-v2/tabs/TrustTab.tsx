'use client';

import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { DatePicker } from '@/components/ui/date-picker';
import SectionCard from '../SectionCard';

interface TrustTabProps {
  value: {
    trustName?: string;
    trustDate?: string;
    holdAge?: string;
    primaryTrustee?: string;
    backupTrustee?: string;
    scheduleAAsset?: string;
  };
  onChange: (partial: Partial<TrustTabProps['value']>) => void;
}

export function TrustTab({ value, onChange }: TrustTabProps) {
  const trustDate = value.trustDate ? new Date(value.trustDate) : undefined;

  return (
    <div className='space-y-6'>
      <SectionCard
        title='Trust: Core Settings'
        description='Set up baseline information for your trust.'
        status='incomplete'
      >
        <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
          <div className='space-y-2'>
            <Label htmlFor='trustName'>Trust Name</Label>
            <Input
              id='trustName'
              placeholder='e.g., The Smith Family Trust'
              value={value.trustName || ''}
              onChange={e => onChange({ trustName: e.target.value })}
            />
          </div>
          <div className='space-y-2'>
            <Label>Trust Date</Label>
            <DatePicker
              date={trustDate}
              setDate={d =>
                onChange({ trustDate: d ? d.toISOString() : undefined })
              }
            />
          </div>
          <div className='space-y-2 md:col-span-2'>
            <Label htmlFor='holdAge'>Hold Assets Until Age</Label>
            <Input
              id='holdAge'
              placeholder='e.g., 19'
              value={value.holdAge || ''}
              onChange={e => onChange({ holdAge: e.target.value })}
            />
            <p className='text-xs text-muted-foreground'>
              Assets are held in trust for young beneficiaries until this age
              before any staged distributions begin.
            </p>
          </div>
        </div>
      </SectionCard>

      <SectionCard
        title='Trust: Schedule A (Funding)'
        description='List the initial assets you are placing into the trust.'
        status='incomplete'
        right={
          <Button variant='secondary' size='sm'>
            Add Schedule A Entry
          </Button>
        }
      >
        <div className='space-y-3'>
          <div className='space-y-2'>
            <Label>Asset Description</Label>
            <Input
              placeholder='e.g., Ten Dollars ($10.00) or 123 Main St Property'
              value={value.scheduleAAsset || ''}
              onChange={e => onChange({ scheduleAAsset: e.target.value })}
            />
          </div>
        </div>
      </SectionCard>

      <SectionCard
        title='Trust: Trustee'
        description='Appoint a Successor Trustee to manage your trust.'
        status='incomplete'
      >
        <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
          <div className='space-y-2'>
            <Label>Primary Trustee</Label>
            <Input
              placeholder='Full name'
              value={value.primaryTrustee || ''}
              onChange={e => onChange({ primaryTrustee: e.target.value })}
            />
          </div>
          <div className='space-y-2'>
            <Label>Backup Trustee (optional)</Label>
            <Input
              placeholder='Full name'
              value={value.backupTrustee || ''}
              onChange={e => onChange({ backupTrustee: e.target.value })}
            />
          </div>
        </div>
      </SectionCard>
    </div>
  );
}

export default TrustTab;
