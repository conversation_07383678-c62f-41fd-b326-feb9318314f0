'use client';

import React from 'react';
import SectionCard from '../SectionCard';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';

interface WillTabProps {
  value: { executor?: string; altExecutor?: string };
  onChange: (partial: Partial<WillTabProps['value']>) => void;
}

export function WillTab({ value, onChange }: WillTabProps) {
  return (
    <div className='space-y-6'>
      <SectionCard
        title='Last Will and Testament'
        description='Set preferences for your Will.'
        status='incomplete'
      >
        <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
          <div className='space-y-2'>
            <Label>Executor</Label>
            <Input
              placeholder='Full name'
              value={value.executor || ''}
              onChange={e => onChange({ executor: e.target.value })}
            />
          </div>
          <div className='space-y-2'>
            <Label>Alternate Executor</Label>
            <Input
              placeholder='Full name'
              value={value.altExecutor || ''}
              onChange={e => onChange({ altExecutor: e.target.value })}
            />
          </div>
        </div>
      </SectionCard>
    </div>
  );
}

export default WillTab;
