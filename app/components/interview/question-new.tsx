'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { useInterviewNew } from './interview-new-context';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { DatePicker } from '@/components/ui/date-picker';
import {
  ChevronLeft,
  ChevronRight,
  Info,
  AlertTriangle,
  Play,
} from 'lucide-react';
import {
  Toolt<PERSON>,
  Toolt<PERSON>Content,
  Toolt<PERSON><PERSON>rovider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  validateQuestionAnswer,
  sanitizeHtml,
  sanitizeHtmlForValidation,
  type QuestionValidationType,
} from '@/app/utils/questionValidation';
import { useAuth } from '@/context/AuthContext';
import { fetchUserAttributes } from 'aws-amplify/auth';
import { useQuery } from '@tanstack/react-query';
import {
  renderTemplate,
  createHandlebarsContext,
} from '@/lib/utils/handlebars-engine';
import { useEducationalContent } from '@/hooks/use-educational-content';
import { YouTubePlayer } from '@/components/education/youtube-player';
import { ContentType } from '@/types/education';

interface QuestionTooltipProps {
  content: string;
}

const QuestionTooltip: React.FC<QuestionTooltipProps> = ({ content }) => {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Info className='w-4 h-4 ml-2 text-gray-500 cursor-help' />
        </TooltipTrigger>
        <TooltipContent>
          <p className='max-w-xs'>{content}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

interface EducationalVideoSectionProps {
  videoId: string;
}

const EducationalVideoSection: React.FC<EducationalVideoSectionProps> = ({
  videoId,
}) => {
  const [showVideo, setShowVideo] = useState(false);
  const { getContentById } = useEducationalContent();
  const videoContent = getContentById(videoId);

  if (!videoContent || videoContent.type !== 'video') {
    return null;
  }

  // Convert Amplify content to the format expected by YouTubePlayer
  const videoForPlayer = {
    id: videoContent.id,
    type: ContentType.VIDEO,
    title: videoContent.title,
    description: videoContent.description || '',
    tags: videoContent.tags || [],
    status: videoContent.status as any,
    createdAt: videoContent.createdAt || '',
    updatedAt: videoContent.updatedAt || '',
    version: videoContent.version || 1,
    contentUrl: videoContent.contentUrl || '',
    duration: videoContent.duration || 0,
    thumbnailUrl: videoContent.thumbnailUrl || '',
  };

  // Check if video has a valid URL
  if (!videoForPlayer.contentUrl) {
    return null;
  }

  return (
    <div className='mb-6'>
      <div className='mb-3 flex items-center justify-between'>
        <h3 className='text-lg font-semibold text-gray-900 flex items-center'>
          Video Hint Available
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Info className='w-4 h-4 ml-2 text-gray-500 cursor-help' />
              </TooltipTrigger>
              <TooltipContent>
                <p className='max-w-xs'>
                  This video provides helpful information related to this
                  question
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </h3>
        <Button
          variant='outline'
          size='sm'
          onClick={() => setShowVideo(!showVideo)}
          className='flex items-center gap-2'
        >
          <Play className='w-4 h-4' />
          {showVideo ? 'Hide Hint' : 'Show Hint'}
        </Button>
      </div>
      {showVideo && <YouTubePlayer video={videoForPlayer} />}
    </div>
  );
};

// Helper function to parse options from different formats
const parseOptions = (
  options?: string[] | any[]
): Array<{
  id: string;
  label: string;
  value: string;
  nextQuestionId?: string;
}> => {
  if (!options || !Array.isArray(options)) return [];

  return options.map((option, index) => {
    // Handle JSON string options (from the database)
    if (typeof option === 'string') {
      try {
        // Try to parse as JSON first
        const parsed = JSON.parse(option);
        if (typeof parsed === 'object' && parsed !== null) {
          return {
            id: parsed.id || `option_${index}`,
            label: parsed.label || parsed.value || String(parsed),
            value: parsed.value || parsed.label || String(parsed),
            nextQuestionId: parsed.nextQuestionId,
          };
        }
      } catch (e) {
        // If JSON parsing fails, treat as simple string
      }

      // Simple string option
      return {
        id: `option_${index}`,
        label: option,
        value: option,
      };
    } else if (typeof option === 'object' && option !== null) {
      // Already parsed object
      return {
        id: option.id || `option_${index}`,
        label: option.label || option.value || String(option),
        value: option.value || option.label || String(option),
        nextQuestionId: option.nextQuestionId,
      };
    } else {
      return {
        id: `option_${index}`,
        label: String(option),
        value: String(option),
      };
    }
  });
};

// Legacy placeholder functions replaced by Handlebars engine

// Additional legacy functions also replaced by Handlebars engine

const QuestionInput: React.FC<{
  questionId: string;
  type: string;
  options?: string[] | any[];
  value: string;
  onChange: (value: string) => void;
  onOptionSelect?: (option: {
    id: string;
    label: string;
    value: string;
    nextQuestionId?: string;
  }) => void;
  placeholder?: string;
  isAutoFilled?: boolean;
}> = ({
  questionId,
  type,
  options,
  value,
  onChange,
  onOptionSelect,
  placeholder,
  isAutoFilled = false,
}) => {
  const parsedOptions = parseOptions(options);
  switch (type) {
    case 'text':
      return (
        <div className='space-y-2'>
          <Input
            id={questionId}
            value={value}
            onChange={e => onChange(e.target.value)}
            placeholder={placeholder || 'Enter your answer...'}
            className={`w-full ${isAutoFilled && value ? 'bg-blue-50 border-blue-200' : ''}`}
          />
          {isAutoFilled && value && (
            <p className='text-xs text-blue-600 flex items-center gap-1'>
              <span className='inline-block w-2 h-2 bg-blue-500 rounded-full'></span>
              This field has been automatically filled with your information.
              You can edit it if needed.
            </p>
          )}
        </div>
      );

    case 'textarea':
      return (
        <Textarea
          id={questionId}
          value={value}
          onChange={e => onChange(e.target.value)}
          placeholder={placeholder || 'Enter your answer...'}
          className='w-full min-h-[100px]'
        />
      );

    case 'radio':
      const handleRadioChange = (selectedValue: string) => {
        onChange(selectedValue);
        // Find the selected option and trigger conditional logic
        const selectedOption = parsedOptions.find(
          opt => opt.value === selectedValue
        );
        if (selectedOption && onOptionSelect) {
          onOptionSelect(selectedOption);
        }
      };

      return (
        <RadioGroup value={value} onValueChange={handleRadioChange}>
          {parsedOptions.map(option => (
            <div key={option.id} className='flex items-center space-x-2'>
              <RadioGroupItem
                value={option.value}
                id={`${questionId}-${option.id}`}
              />
              <Label htmlFor={`${questionId}-${option.id}`}>
                {option.label}
              </Label>
            </div>
          ))}
        </RadioGroup>
      );

    case 'select':
      const handleSelectChange = (selectedValue: string) => {
        onChange(selectedValue);
        // Find the selected option and trigger conditional logic
        const selectedOption = parsedOptions.find(
          opt => opt.value === selectedValue
        );
        if (selectedOption && onOptionSelect) {
          onOptionSelect(selectedOption);
        }
      };

      return (
        <Select value={value} onValueChange={handleSelectChange}>
          <SelectTrigger className='w-full'>
            <SelectValue placeholder='Select an option...' />
          </SelectTrigger>
          <SelectContent>
            {parsedOptions.map(option => (
              <SelectItem key={option.id} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      );

    case 'checkbox':
      const selectedValues = value ? value.split(',').filter(Boolean) : [];

      const handleCheckboxChange = (optionValue: string, checked: boolean) => {
        console.log(
          'Checkbox change:',
          optionValue,
          checked,
          'Current selected:',
          selectedValues
        );
        let newValues: string[];
        if (checked) {
          newValues = [...selectedValues, optionValue];
        } else {
          newValues = selectedValues.filter(v => v !== optionValue);
        }
        const newValueString = newValues.join(',');
        console.log('New checkbox values:', newValueString);
        onChange(newValueString);

        // Don't trigger onOptionSelect for checkboxes as it interferes with multi-selection
        // The conditional logic should be handled differently for checkboxes
      };

      return (
        <div className='space-y-2'>
          {parsedOptions.map(option => (
            <div key={option.id} className='flex items-center space-x-2'>
              <Checkbox
                id={`${questionId}-${option.id}`}
                checked={selectedValues.includes(option.value)}
                onCheckedChange={checked =>
                  handleCheckboxChange(option.value, checked as boolean)
                }
              />
              <Label htmlFor={`${questionId}-${option.id}`}>
                {option.label}
              </Label>
            </div>
          ))}
        </div>
      );

    case 'date':
      const handleDateChange = (date: Date | undefined) => {
        // Convert Date to ISO string for storage, or empty string if no date
        const dateValue = date ? date.toISOString().split('T')[0] : '';
        onChange(dateValue);
      };

      // Convert stored string value back to Date object for DatePicker
      const dateValue = value ? new Date(value) : undefined;

      return (
        <DatePicker
          date={dateValue}
          setDate={handleDateChange}
          className='w-full'
          showYearPicker={true}
          showMonthPicker={true}
          yearRange={{ from: 1900, to: new Date().getFullYear() + 10 }}
        />
      );

    default:
      return (
        <div className='text-red-500'>Unsupported question type: {type}</div>
      );
  }
};

export const QuestionCard: React.FC = () => {
  const {
    questions,
    currentQuestionIndex,
    currentAnswer,
    isLoading,
    error,
    setCurrentAnswer,
    saveAnswer,
    saveAnswerAndNavigate,
    goToPreviousQuestion,
    getProgress,
    getHeadQuestions,
    getVisibleQuestions,
    completeInterview,
    userProgress,
  } = useInterviewNew();

  const { user } = useAuth();

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [localAnswer, setLocalAnswer] = useState<string>('');
  const [validationError, setValidationError] = useState<string>('');

  // Fetch user attributes for placeholder replacement
  const { data: userAttributes } = useQuery({
    queryKey: ['user-attributes'],
    queryFn: fetchUserAttributes,
    enabled: !!user,
  });

  // Get current question
  const currentQuestion = questions[currentQuestionIndex];
  const parsedOptions = parseOptions(currentQuestion?.options);

  // Process question text with Handlebars template replacement
  const processedQuestionText = useMemo(() => {
    if (!currentQuestion?.text) return '';

    console.log('Processing question text:', currentQuestion.text);
    console.log('User attributes:', userAttributes);

    // Create Handlebars context
    const context = createHandlebarsContext(
      userAttributes || {},
      userProgress?.answers || [],
      questions
    );

    console.log('Handlebars context:', context);

    // Use Handlebars engine for template processing
    const result = renderTemplate(currentQuestion.text, context);

    console.log('Processed question text result:', result);
    return result;
  }, [currentQuestion?.text, userAttributes, userProgress?.answers, questions]);

  // Process auto-fill value for text inputs
  const autoFillValue = useMemo(() => {
    if (!currentQuestion?.autoFillVariable || currentQuestion.type !== 'text') {
      return '';
    }

    console.log(
      'Processing auto-fill variable:',
      currentQuestion.autoFillVariable
    );

    // Create Handlebars context
    const context = createHandlebarsContext(
      userAttributes || {},
      userProgress?.answers || [],
      questions
    );

    // Use Handlebars engine to process the auto-fill variable
    return renderTemplate(currentQuestion.autoFillVariable, context);
  }, [
    currentQuestion?.autoFillVariable,
    currentQuestion?.type,
    userAttributes,
    userProgress?.answers,
    questions,
  ]);

  // Update local answer when question changes or current answer changes
  useEffect(() => {
    // If there's an auto-fill value and no current answer, use the auto-fill value
    if (autoFillValue && !currentAnswer && currentQuestion?.type === 'text') {
      setLocalAnswer(autoFillValue);
      setCurrentAnswer(autoFillValue);
    } else {
      setLocalAnswer(currentAnswer);
    }
    setValidationError(''); // Clear validation errors when question changes
  }, [
    currentAnswer,
    currentQuestionIndex,
    autoFillValue,
    currentQuestion?.type,
    setCurrentAnswer,
  ]);

  // Handle form field changes (no database save)
  const handleChange = (value: string) => {
    console.log(
      'handleChange called with:',
      value,
      'Question type:',
      currentQuestion?.type
    );
    // Sanitize input to prevent HTML injection
    const sanitizedValue = sanitizeHtml(value);
    setLocalAnswer(sanitizedValue);
    setCurrentAnswer(sanitizedValue);

    // Clear validation error when user starts typing
    if (validationError) {
      setValidationError('');
    }
  };

  // Handle option selection for conditional logic
  const handleOptionSelect = (option: {
    id: string;
    label: string;
    value: string;
    nextQuestionId?: string;
  }) => {
    // For checkbox type, don't override the value here as it's handled by handleCheckboxChange
    if (currentQuestion?.type === 'checkbox') {
      return; // Let the checkbox handler manage the value
    }

    setLocalAnswer(option.value);
    setCurrentAnswer(option.value);
  };

  // Handle next button click with database save and conditional navigation
  const handleNext = async () => {
    if (!currentQuestion) return;

    // Sanitize the answer for validation and saving
    const sanitizedAnswer = sanitizeHtmlForValidation(localAnswer);

    // Validate the answer before saving with enhanced validation
    const validationResult = validateQuestionAnswer(
      sanitizedAnswer,
      currentQuestion.questionValidation as QuestionValidationType,
      {
        required: true, // All fields are required
        maxLength: 200, // Reasonable character limit
        sanitizeHtml: false, // Already sanitized above
      }
    );

    if (!validationResult.isValid) {
      setValidationError(validationResult.errorMessage || 'Invalid input');
      return;
    }

    setIsSubmitting(true);
    try {
      // Find the option that matches the current answer to get conditional logic
      let nextQuestionId: string | undefined;

      if (parsedOptions.length > 0) {
        const matchingOption = parsedOptions.find(
          opt => opt.value === sanitizedAnswer
        );
        nextQuestionId = matchingOption?.nextQuestionId;
      }

      // Check if this is the last visible question
      if (
        isLastQuestion &&
        !nextQuestionId &&
        !currentQuestion.defaultNextQuestionId
      ) {
        // Save the final answer with completion flag
        await saveAnswer(currentQuestion.questionId, sanitizedAnswer);

        // Complete the interview
        await completeInterview();

        return;
      }

      // Save current answer and navigate based on conditional logic
      await saveAnswerAndNavigate(
        currentQuestion.questionId,
        sanitizedAnswer,
        nextQuestionId
      );
    } catch (error) {
      console.error('Error saving response:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle previous button click
  const handlePrevious = () => {
    goToPreviousQuestion();
  };

  if (isLoading) {
    return (
      <Card className='max-w-2xl mx-auto'>
        <CardContent className='p-6'>
          <div className='animate-pulse space-y-4'>
            <div className='h-4 bg-gray-200 rounded w-1/4'></div>
            <div className='h-8 bg-gray-200 rounded w-3/4'></div>
            <div className='h-20 bg-gray-200 rounded'></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className='max-w-2xl mx-auto border-red-200'>
        <CardContent className='p-6'>
          <div className='text-red-600 text-center'>
            <p className='font-semibold'>Error loading interview</p>
            <p className='text-sm mt-1'>{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!currentQuestion) {
    return (
      <Card className='max-w-2xl mx-auto'>
        <CardContent className='p-6'>
          <div className='text-center text-gray-500'>
            <p>No questions available</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const progress = getProgress();
  const headQuestions = getHeadQuestions();
  const visibleQuestions = getVisibleQuestions();
  const currentVisibleIndex = visibleQuestions.findIndex(
    q => q.questionId === questions[currentQuestionIndex]?.questionId
  );

  // Check if current question is a head question
  const isHeadQuestion = headQuestions.some(
    hq => hq.questionId === currentQuestion?.questionId
  );

  // Ensure we have a valid index, fallback to currentQuestionIndex if needed
  const safeCurrentIndex =
    currentVisibleIndex >= 0 ? currentVisibleIndex : currentQuestionIndex;
  const isFirstQuestion = safeCurrentIndex === 0;
  const isLastQuestion = safeCurrentIndex === visibleQuestions.length - 1;

  // Check if current question is new (needs highlighting)
  const isNewQuestion =
    userProgress?.newQuestionIds?.includes(currentQuestion.questionId) ?? false;

  return (
    <Card className='max-w-full mx-auto shadow-lg'>
      <CardHeader className='pb-4'>
        <div className='flex justify-between items-center mb-2'>
          <div className='flex items-center gap-2'>
            <span className='text-sm text-gray-500'>
              Question {safeCurrentIndex + 1} of {visibleQuestions.length}
            </span>
            {!isHeadQuestion && (
              <span className='text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full'>
                Conditional
              </span>
            )}
            {isNewQuestion && (
              <span className='text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full font-medium'>
                New
              </span>
            )}
          </div>
          <span className='text-sm text-gray-500'>{progress}% Complete</span>
        </div>

        {/* Progress bar */}
        <div className='w-full bg-gray-200 rounded-full h-2 mb-4'>
          <div
            className='bg-blue-600 h-2 rounded-full transition-all duration-300'
            style={{ width: `${progress}%` }}
          ></div>
        </div>

        <CardTitle className='text-xl font-bold text-gray-900 flex items-center'>
          {processedQuestionText}
          {/* Show tooltip if question has conditional logic */}
          {currentQuestion.conditionalLogic &&
            currentQuestion.conditionalLogic.length > 0 && (
              <QuestionTooltip content='This question may show additional questions based on your answer' />
            )}
          {/* Show tooltip if any options have nextQuestionId (branching logic) */}
          {parsedOptions.some(opt => opt.nextQuestionId) && (
            <QuestionTooltip content='Some answers will lead to different follow-up questions' />
          )}
        </CardTitle>

        {/* Conditional question explanation */}
        {!isHeadQuestion && (
          <div className='mt-2'>
            <Alert>
              <Info className='h-4 w-4' />
              <AlertDescription>
                This question appears based on your previous answer. It helps us
                provide more personalized recommendations.
              </AlertDescription>
            </Alert>
          </div>
        )}
      </CardHeader>

      <CardContent className='space-y-6'>
        {/* Educational Video */}
        {currentQuestion.educationalVideoId && (
          <EducationalVideoSection
            videoId={currentQuestion.educationalVideoId}
          />
        )}

        {/* Question input */}
        <div className='space-y-2'>
          <QuestionInput
            questionId={currentQuestion.questionId}
            type={currentQuestion.type}
            options={currentQuestion.options}
            value={localAnswer}
            onChange={handleChange}
            onOptionSelect={handleOptionSelect}
            placeholder={`Enter your ${
              currentQuestion.type === 'text' ? 'answer' : 'selection'
            }...`}
            isAutoFilled={!!autoFillValue && currentQuestion.type === 'text'}
          />

          {/* Validation error display */}
          {validationError && (
            <Alert variant='destructive'>
              <AlertTriangle className='h-4 w-4' />
              <AlertDescription>{validationError}</AlertDescription>
            </Alert>
          )}
        </div>

        {/* Navigation buttons */}
        <div className='flex justify-between pt-4'>
          <Button
            variant='outline'
            onClick={handlePrevious}
            disabled={isFirstQuestion || isSubmitting}
            className='flex items-center gap-2'
          >
            <ChevronLeft className='w-4 h-4' />
            Previous
          </Button>

          <Button
            onClick={handleNext}
            disabled={isSubmitting || !localAnswer.trim()}
            className='flex items-center gap-2'
          >
            {isSubmitting ? (
              'Saving...'
            ) : isLastQuestion ? (
              <>
                Finish
                <ChevronRight className='w-4 h-4' />
              </>
            ) : (
              <>
                Next
                <ChevronRight className='w-4 h-4' />
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
