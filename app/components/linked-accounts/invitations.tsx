'use client';

import React, { useState } from 'react';
import { useInvites } from '@/hooks/useInvites';
import { useLinkedAccounts } from '@/hooks/useLinkedAccounts';
import { Button } from '@/components/ui/button';
import {
  UserPlus,
  Mail,
  Trash2,
  MoreHorizontal,
  Check,
  X,
  Link,
  Link2Off,
  Eye,
} from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { toast } from 'sonner';
import { InviteUserModal } from '@/app/components/dashboard/admin/invite-user-modal';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import SharedAnswersTable from './shared-answers-table';

const Invitations = () => {
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);
  const [isSharedFieldsDialogOpen, setIsSharedFieldsDialogOpen] =
    useState(false);
  const [selectedLinkedAccount, setSelectedLinkedAccount] = useState<any>(null);
  const {
    invitesForCurrentMember: invites,
    loading: invitesLoading,
    error: invitesError,
    fetchInvitesForMember: fetchInvites,
    cancelInvite,
    resendInvite,
  } = useInvites(true);
  const {
    linkedAccounts,
    linkedByAccounts,
    loading: linkedAccountsLoading,
    error: linkedAccountsError,
    fetchLinkedAccounts,
    removeLinkedAccount,
    acceptLinkedAccount,
  } = useLinkedAccounts();

  // Format date helper
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Get status badge variant
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'pending':
        return 'secondary';
      case 'accepted':
        return 'default';
      case 'expired':
        return 'destructive';
      case 'cancelled':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  // Handle resend invite
  const handleResendInvite = async (invite: any) => {
    try {
      await resendInvite(invite);
      toast.success(`Invitation resent to ${invite.email}`);
      await fetchInvites();
    } catch (error) {
      console.error('Error resending invite:', error);
      toast.error('Failed to resend invitation');
    }
  };

  // Handle delete invite
  const handleDeleteInvite = async (invite: any) => {
    try {
      await cancelInvite(invite.id);
      toast.success('Invitation deleted successfully');
      await fetchInvites();
    } catch (error) {
      console.error('Error deleting invite:', error);
      toast.error('Failed to delete invitation');
    }
  };

  // Handle remove linked account
  const handleRemoveLinkedAccount = async (linkId: string) => {
    try {
      await removeLinkedAccount(linkId);
      toast.success('Linked account removed successfully');
    } catch (error) {
      console.error('Error removing linked account:', error);
      toast.error('Failed to remove linked account');
    }
  };

  // Handle accept linked account
  const handleAcceptLinkedAccount = async (linkId: string) => {
    try {
      await acceptLinkedAccount(linkId);
      toast.success('Linked account accepted successfully');
    } catch (error) {
      console.error('Error accepting linked account:', error);
      toast.error('Failed to accept linked account');
    }
  };

  // Handle viewing shared fields
  const handleViewSharedFields = (link: any) => {
    setSelectedLinkedAccount(link);
    setIsSharedFieldsDialogOpen(true);
  };

  return (
    <div className='space-y-8'>
      {/* Invitations Section */}
      <div className='space-y-4'>
        {/* Header with Invite Button */}
        <div className='flex items-center justify-between'>
          <div>
            <h2 className='text-xl font-semibold'>
              Linked Account Invitations
            </h2>
            <p className='text-muted-foreground'>
              Manage invitations to link accounts for shared planning
            </p>
          </div>
          <Button onClick={() => setIsInviteModalOpen(true)} variant='default'>
            <UserPlus className='mr-2 h-4 w-4' />
            Invite User
          </Button>
        </div>

        {/* Invites Table */}
        {invitesLoading ? (
          <div className='flex justify-center py-8'>
            <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary'></div>
          </div>
        ) : invitesError ? (
          <div className='bg-destructive/10 p-4 rounded-md text-destructive'>
            <p>Error loading invitations: {invitesError}</p>
          </div>
        ) : invites.length === 0 ? (
          <div className='text-center py-8 border rounded-md'>
            <p className='text-muted-foreground'>No invitations found</p>
          </div>
        ) : (
          <div className='border rounded-md'>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Invited Date</TableHead>
                  <TableHead>Expires At</TableHead>
                  <TableHead className='text-right'>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {invites.map(invite => {
                  const name = `${invite.firstName} ${invite.lastName}`;
                  const status = invite.status || 'pending';
                  const isExpired = new Date(invite.expiresAt) < new Date();

                  return (
                    <TableRow key={invite.id}>
                      <TableCell className='font-medium'>{name}</TableCell>
                      <TableCell>{invite.email}</TableCell>
                      <TableCell>
                        {invite.role === 'WelonTrust'
                          ? 'Welon Trust'
                          : invite.role}
                      </TableCell>
                      <TableCell>
                        <Badge variant={getStatusBadgeVariant(status)}>
                          {status.charAt(0).toUpperCase() + status.slice(1)}
                        </Badge>
                      </TableCell>
                      <TableCell className='text-sm text-muted-foreground'>
                        {formatDate(invite.createdAt)}
                      </TableCell>
                      <TableCell>
                        <span
                          className={`text-sm ${isExpired ? 'text-red-600' : 'text-muted-foreground'}`}
                        >
                          {formatDate(invite.expiresAt)}
                        </span>
                      </TableCell>
                      <TableCell className='text-right'>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant='ghost' className='h-8 w-8 p-0'>
                              <span className='sr-only'>Open menu</span>
                              <MoreHorizontal className='h-4 w-4' />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align='end'>
                            {status === 'pending' && (
                              <DropdownMenuItem
                                onClick={() => handleResendInvite(invite)}
                                className='text-blue-600'
                              >
                                <Mail className='mr-2 h-4 w-4' />
                                Resend Invitation
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuItem
                              onClick={() => handleDeleteInvite(invite)}
                              className='text-red-600'
                            >
                              <Trash2 className='mr-2 h-4 w-4' />
                              Delete Invitation
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        )}
      </div>

      {/* Linked Accounts Section */}
      <div className='space-y-4'>
        <div>
          <h2 className='text-xl font-semibold'>Your Linked Accounts</h2>
          <p className='text-muted-foreground'>
            Accounts you have linked for shared planning
          </p>
        </div>

        {linkedAccountsLoading ? (
          <div className='flex justify-center py-8'>
            <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary'></div>
          </div>
        ) : linkedAccountsError ? (
          <div className='bg-destructive/10 p-4 rounded-md text-destructive'>
            <p>Error loading linked accounts: {linkedAccountsError}</p>
          </div>
        ) : linkedAccounts.length === 0 ? (
          <div className='text-center py-8 border rounded-md'>
            <p className='text-muted-foreground'>No linked accounts found</p>
          </div>
        ) : (
          <div className='border rounded-md'>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created Date</TableHead>
                  <TableHead className='text-right'>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {linkedAccounts.map(link => {
                  const linkedUser = link.linkedUser;
                  const name = linkedUser
                    ? `${linkedUser.firstName} ${linkedUser.lastName}`
                    : 'Unknown User';
                  const email = linkedUser?.email || 'Unknown Email';

                  return (
                    <TableRow key={link.id}>
                      <TableCell className='font-medium'>{name}</TableCell>
                      <TableCell>{email}</TableCell>
                      <TableCell>
                        <Badge
                          variant={link.isAccepted ? 'default' : 'secondary'}
                        >
                          {link.isAccepted ? 'Accepted' : 'Pending'}
                        </Badge>
                      </TableCell>
                      <TableCell className='text-sm text-muted-foreground'>
                        {formatDate(link.createdAt)}
                      </TableCell>
                      <TableCell className='text-right'>
                        <div className='flex justify-end gap-2'>
                          <Button
                            variant='outline'
                            size='sm'
                            onClick={() => handleViewSharedFields(link)}
                            className='h-8 px-2 text-blue-600'
                          >
                            <Eye className='h-4 w-4 mr-1' />
                            View Fields
                          </Button>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant='ghost' className='h-8 w-8 p-0'>
                                <span className='sr-only'>Open menu</span>
                                <MoreHorizontal className='h-4 w-4' />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align='end'>
                              <DropdownMenuItem
                                onClick={() =>
                                  handleRemoveLinkedAccount(link.id)
                                }
                                className='text-red-600'
                              >
                                <Link2Off className='mr-2 h-4 w-4' />
                                Remove Link
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        )}
      </div>

      {/* Accounts Linked By Others Section */}
      <div className='space-y-4'>
        <div>
          <h2 className='text-xl font-semibold'>Accounts Linked To You</h2>
          <p className='text-muted-foreground'>
            Other users who have linked their account to yours
          </p>
        </div>

        {linkedAccountsLoading ? (
          <div className='flex justify-center py-8'>
            <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary'></div>
          </div>
        ) : linkedAccountsError ? (
          <div className='bg-destructive/10 p-4 rounded-md text-destructive'>
            <p>Error loading linked accounts: {linkedAccountsError}</p>
          </div>
        ) : linkedByAccounts.length === 0 ? (
          <div className='text-center py-8 border rounded-md'>
            <p className='text-muted-foreground'>
              No accounts have linked to you
            </p>
          </div>
        ) : (
          <div className='border rounded-md'>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created Date</TableHead>
                  <TableHead className='text-right'>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {linkedByAccounts.map(link => {
                  const linkingUser = link?.user || undefined;
                  const name = linkingUser
                    ? `${linkingUser.firstName} ${linkingUser.lastName}`
                    : 'Unknown User';
                  const email = linkingUser?.email || 'Unknown Email';

                  return (
                    <TableRow key={link.id}>
                      <TableCell className='font-medium'>{name}</TableCell>
                      <TableCell>{email}</TableCell>
                      <TableCell>
                        <Badge
                          variant={link.isAccepted ? 'default' : 'secondary'}
                        >
                          {link.isAccepted ? 'Accepted' : 'Pending'}
                        </Badge>
                      </TableCell>
                      <TableCell className='text-sm text-muted-foreground'>
                        {formatDate(link.createdAt)}
                      </TableCell>
                      <TableCell className='text-right'>
                        <div className='flex justify-end gap-2'>
                          <Button
                            variant='outline'
                            size='sm'
                            onClick={() => handleViewSharedFields(link)}
                            className='h-8 px-2 text-blue-600'
                          >
                            <Eye className='h-4 w-4 mr-1' />
                            View Fields
                          </Button>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant='ghost' className='h-8 w-8 p-0'>
                                <span className='sr-only'>Open menu</span>
                                <MoreHorizontal className='h-4 w-4' />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align='end'>
                              {!link.isAccepted && (
                                <DropdownMenuItem
                                  onClick={() =>
                                    handleAcceptLinkedAccount(link.id)
                                  }
                                  className='text-green-600'
                                >
                                  <Check className='mr-2 h-4 w-4' />
                                  Accept
                                </DropdownMenuItem>
                              )}
                              <DropdownMenuItem
                                onClick={() =>
                                  handleRemoveLinkedAccount(link.id)
                                }
                                className='text-red-600'
                              >
                                <X className='mr-2 h-4 w-4' />
                                Decline/Remove
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        )}
      </div>

      {/* Invite User Modal */}
      <InviteUserModal
        open={isInviteModalOpen}
        onOpenChange={setIsInviteModalOpen}
        onInviteSent={fetchInvites}
        preSignedRole='Member'
        preSignedSubrole='Basic Member'
        showSharedAnswers
      />

      {/* Shared Fields Dialog */}
      <Dialog
        open={isSharedFieldsDialogOpen}
        onOpenChange={setIsSharedFieldsDialogOpen}
      >
        <DialogContent className='min-w-4xl'>
          <DialogHeader>
            <DialogTitle>
              Shared Fields
              {selectedLinkedAccount && (
                <span className='font-normal text-muted-foreground ml-2'>
                  {selectedLinkedAccount.linkedUser
                    ? `with ${selectedLinkedAccount.linkedUser.firstName} ${selectedLinkedAccount.linkedUser.lastName}`
                    : selectedLinkedAccount.user
                      ? `with ${selectedLinkedAccount.user.firstName} ${selectedLinkedAccount.user.lastName}`
                      : ''}
                </span>
              )}
            </DialogTitle>
          </DialogHeader>
          <div className='mt-4'>
            <SharedAnswersTable compact={true} showExportButton={false} />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Invitations;
