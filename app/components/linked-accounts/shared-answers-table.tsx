'use client';

import { getMemberInterviewAnswers } from '@/lib/api/member-documents';
import { useQuery } from '@tanstack/react-query';
import { generateClient } from 'aws-amplify/api';
import { useMemo, useState, useEffect } from 'react';
import { Schema } from '@/amplify/data/resource';
import { userCareDocumentsAPI } from '@/lib/api/user-care-documents';
import { FileText, Search, Filter, Save, CheckSquare } from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';

interface SharedAnswersTableProps {
  onSelectedItemsChange?: (items: any[]) => void;
  showExportButton?: boolean;
  compact?: boolean;
}

const SharedAnswersTable = ({
  onSelectedItemsChange,
  showExportButton = true,
  compact = false,
}: SharedAnswersTableProps) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filter, setFilter] = useState<'all' | 'interview' | 'care'>('all');
  const [selectedItems, setSelectedItems] = useState<Record<string, any>>({});
  const [selectAll, setSelectAll] = useState(false);
  const client = generateClient<Schema>();

  // Fetch interview answers
  const {
    data: interviewAnswers = [],
    isLoading: isLoadingInterviewAnswers,
    error: interviewAnswersError,
  } = useQuery({
    queryKey: ['member-interview-answers'],
    queryFn: getMemberInterviewAnswers,
  });

  // Fetch care documents
  const {
    data: careDocuments = [],
    isLoading: isLoadingCareDocuments,
    error: careDocumentsError,
  } = useQuery({
    queryKey: ['user-care-documents-all'],
    queryFn: async () => {
      // Get all care document templates first
      const { data: templates } = await client.models.CareDocumentTemplate.list(
        {
          filter: {
            status: { eq: 'Active' },
          },
        }
      );

      // Fetch documents for each template
      const allDocuments = [];
      for (const template of templates) {
        const documents =
          await userCareDocumentsAPI.getUserCareDocumentsByTemplateId(
            template.id
          );
        allDocuments.push(...documents);
      }

      return allDocuments;
    },
  });

  // Process all answers into a unified format
  const allAnswers = useMemo(() => {
    const answers: any = [];

    // Process interview answers
    if (interviewAnswers && interviewAnswers.length > 0) {
      interviewAnswers.forEach(answer => {
        answers.push({
          id: answer.questionId,
          question: answer.text,
          answer: answer.answer,
          documentType: 'Interview',
          documentTitle: 'Interview Answers',
          answeredAt: answer.answeredAt,
          source: 'interview',
        });
      });
    }

    // Process care document answers
    if (careDocuments && careDocuments.length > 0) {
      careDocuments.forEach(document => {
        if (document.answers && document.answers.length > 0) {
          document.answers.forEach(answer => {
            if (!answer) return;

            // Handle multiple answers (split by separator)
            const answerValues = answer.answer
              ? answer.answer.includes('|||')
                ? answer.answer.split('|||')
                : [answer.answer]
              : [];

            answers.push({
              id: answer.questionId,
              question: answer.questionText || 'Unknown Question',
              answer: answerValues.join(', '),
              documentType: 'Care Document',
              documentTitle: document.title,
              answeredAt: document.updatedAt || document.createdAt,
              source: 'care',
              templateId: document.templateId,
            });
          });
        }
      });
    }

    return answers;
  }, [interviewAnswers, careDocuments]);

  // Filter and search answers
  const filteredAnswers = useMemo(() => {
    return allAnswers.filter((answer: any) => {
      // Apply source filter
      if (filter !== 'all' && answer.source !== filter) return false;

      // Apply search query
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        return (
          answer.question.toLowerCase().includes(query) ||
          answer.answer.toLowerCase().includes(query) ||
          answer.documentTitle.toLowerCase().includes(query)
        );
      }

      return true;
    });
  }, [allAnswers, filter, searchQuery]);

  // Handle item selection
  const toggleItemSelection = (item: any) => {
    const itemKey = `${item.id}-${item.documentTitle}`;
    setSelectedItems(prev => {
      const newSelected = { ...prev };
      if (newSelected[itemKey]) {
        delete newSelected[itemKey];
      } else {
        newSelected[itemKey] = item;
      }
      return newSelected;
    });
  };

  // Handle select all
  const toggleSelectAll = () => {
    if (selectAll) {
      // Deselect all
      setSelectedItems({});
    } else {
      // Select all filtered items
      const newSelected: Record<string, any> = {};
      filteredAnswers.forEach((item: any) => {
        const itemKey = `${item.id}-${item.documentTitle}`;
        newSelected[itemKey] = item;
      });
      setSelectedItems(newSelected);
    }
    setSelectAll(!selectAll);
  };

  // Export selected items as JSON
  const exportSelectedItems = () => {
    const selectedData = Object.values(selectedItems);
    console.log(JSON.stringify(selectedData, null, 2));
    alert(
      `Exported ${selectedData.length} items to console. Check browser console.`
    );
  };

  // Notify parent component when selected items change
  useEffect(() => {
    if (onSelectedItemsChange) {
      const selectedValues = Object.values(selectedItems);
      // Only call if the actual selected values changed
      onSelectedItemsChange(selectedValues);
    }
  }, [selectedItems]);

  // Loading state
  if (isLoadingInterviewAnswers || isLoadingCareDocuments) {
    return (
      <div className='flex justify-center items-center py-8'>
        <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary'></div>
      </div>
    );
  }

  // Error state
  if (interviewAnswersError || careDocumentsError) {
    return (
      <div className='bg-destructive/10 p-4 rounded-md text-destructive'>
        <p>Error loading answers. Please try again later.</p>
      </div>
    );
  }

  const selectedCount = Object.keys(selectedItems).length;

  return (
    <div className={`space-y-4 ${compact ? 'text-sm' : ''}`}>
      {/* Search, filter, and export controls */}
      <div className='flex flex-col sm:flex-row gap-4 justify-between'>
        <div className='flex flex-col sm:flex-row gap-4'>
          <div className='relative w-full sm:w-64'>
            <Search className='absolute left-2 top-2.5 h-4 w-4 text-muted-foreground' />
            <Input
              placeholder='Search answers...'
              className='pl-8'
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
            />
          </div>

          <div className='flex items-center gap-2'>
            <Filter className='h-4 w-4 text-muted-foreground' />
            <Select
              value={filter}
              onValueChange={value => setFilter(value as any)}
            >
              <SelectTrigger className='w-[180px]'>
                <SelectValue placeholder='Filter by source' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>All Sources</SelectItem>
                <SelectItem value='interview'>Interview Only</SelectItem>
                <SelectItem value='care'>Care Documents Only</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {showExportButton && (
          <Button
            variant='default'
            size='sm'
            onClick={exportSelectedItems}
            disabled={selectedCount === 0}
            className='flex items-center gap-2'
          >
            <Save className='h-4 w-4' />
            Export Selected ({selectedCount})
          </Button>
        )}
      </div>

      {/* Table of answers */}
      {filteredAnswers.length > 0 ? (
        <div className='border rounded-md'>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className='w-[50px]'>
                  <Checkbox
                    checked={selectAll}
                    onCheckedChange={toggleSelectAll}
                    aria-label='Select all'
                  />
                </TableHead>
                <TableHead>Question</TableHead>
                <TableHead>Answer</TableHead>
                <TableHead>Document Type</TableHead>
                <TableHead>Document</TableHead>
                <TableHead className='text-right'>Date</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredAnswers.map((answer: any) => {
                const itemKey = `${answer.id}-${answer.documentTitle}`;
                const isSelected = !!selectedItems[itemKey];

                return (
                  <TableRow
                    key={itemKey}
                    className={isSelected ? 'bg-muted/50' : undefined}
                  >
                    <TableCell>
                      <Checkbox
                        checked={isSelected}
                        onCheckedChange={() => toggleItemSelection(answer)}
                        aria-label={`Select ${answer.question}`}
                      />
                    </TableCell>
                    <TableCell className='font-medium'>
                      {answer.question}
                    </TableCell>
                    <TableCell>{answer.answer}</TableCell>
                    <TableCell>{answer.documentType}</TableCell>
                    <TableCell>{answer.documentTitle}</TableCell>
                    <TableCell className='text-right'>
                      {new Date(answer.answeredAt).toLocaleDateString()}
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
      ) : (
        <div className='text-center py-8 text-muted-foreground border rounded-md'>
          <FileText className='h-12 w-12 mx-auto mb-2 opacity-20' />
          <p>No answers found</p>
          {searchQuery && (
            <p className='text-sm mt-1'>
              Try adjusting your search or filter criteria
            </p>
          )}
        </div>
      )}
    </div>
  );
};

export default SharedAnswersTable;
