'use client';

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { StickyNote, Clock, User, Eye, AlertCircle } from 'lucide-react';
import { format } from 'date-fns';
import { getMemberNotes, type MemberNoteData } from '@/lib/api/member-notes';
import { toast } from 'sonner';

interface MemberNotesDropdownProps {
  userId: string;
  userName: string;
  trigger: React.ReactNode;
}

export function MemberNotesDropdown({
  userId,
  userName,
  trigger,
}: MemberNotesDropdownProps) {
  const [notes, setNotes] = useState<MemberNoteData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  // Load notes when dialog opens
  useEffect(() => {
    if (isOpen) {
      loadNotes();
    }
  }, [isOpen, userId]);

  const loadNotes = async () => {
    try {
      setIsLoading(true);
      const memberNotes = await getMemberNotes(userId);
      setNotes(memberNotes);
    } catch (error) {
      console.error('Failed to load member notes:', error);
      toast.error('Failed to load member notes');
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "MMM dd, yyyy 'at' h:mm a");
  };

  return (
    <>
      <div onClick={() => setIsOpen(true)}>{trigger}</div>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className='max-w-2xl max-h-[80vh] overflow-y-auto'>
          <DialogHeader>
            <DialogTitle className='flex items-center gap-2'>
              <StickyNote className='h-5 w-5' />
              Internal Notes for {userName}
            </DialogTitle>
            <DialogDescription>
              Private notes visible only to Welon Trust users and administrators
            </DialogDescription>
          </DialogHeader>

          <div className='mt-4'>
            {isLoading ? (
              <div className='py-8 text-center'>
                <p className='text-sm text-muted-foreground'>
                  Loading notes...
                </p>
              </div>
            ) : notes.length === 0 ? (
              <div className='py-8 text-center'>
                <StickyNote className='h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50' />
                <p className='text-muted-foreground mb-2'>
                  No internal notes found
                </p>
                <p className='text-sm text-muted-foreground'>
                  This member has no notes from Welon Trust users yet.
                </p>
              </div>
            ) : (
              <div className='space-y-4'>
                <div className='flex items-center justify-between mb-4'>
                  <p className='text-sm text-muted-foreground'>
                    {notes.length} note{notes.length !== 1 ? 's' : ''} found
                  </p>
                </div>

                {notes.map(note => (
                  <div
                    key={note.id}
                    className='border rounded-lg p-4 border-l-4 border-l-blue-500 bg-card'
                  >
                    <div className='flex items-start justify-between mb-3'>
                      <div className='flex items-center gap-2'>
                        <User className='h-4 w-4 text-muted-foreground' />
                        <span className='text-sm font-medium'>
                          {note.authorName}
                        </span>
                        <Badge variant='outline' className='text-xs'>
                          Welon Trust
                        </Badge>
                      </div>
                    </div>

                    <p className='text-sm mb-3 whitespace-pre-wrap leading-relaxed'>
                      {note.content}
                    </p>

                    <div className='flex items-center gap-4 text-xs text-muted-foreground'>
                      <div className='flex items-center gap-1'>
                        <Clock className='h-3 w-3' />
                        Created: {formatDate(note.createdAt)}
                      </div>
                      {note.updatedAt !== note.createdAt && (
                        <div className='flex items-center gap-1'>
                          <Clock className='h-3 w-3' />
                          Updated: {formatDate(note.updatedAt)}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className='flex justify-end mt-6'>
            <Button variant='outline' onClick={() => setIsOpen(false)}>
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
