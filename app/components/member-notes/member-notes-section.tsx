'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from '@/components/ui/dialog';
import { ConfirmationDialog } from '@/components/ui/confirmation-dialog';
import {
  Plus,
  Edit,
  Trash2,
  StickyNote,
  Clock,
  User,
  AlertCircle,
} from 'lucide-react';
import { toast } from 'sonner';
import { format } from 'date-fns';
import {
  getMemberNotes,
  createMemberNote,
  updateMemberNote,
  deleteMemberNote,
  type MemberNoteData,
} from '@/lib/api/member-notes';
import { useRole } from '@/lib/roles/role-context';

interface MemberNotesSectionProps {
  userId: string;
  userName: string;
}

export function MemberNotesSection({
  userId,
  userName,
}: MemberNotesSectionProps) {
  const [notes, setNotes] = useState<MemberNoteData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddingNote, setIsAddingNote] = useState(false);
  const [editingNote, setEditingNote] = useState<MemberNoteData | null>(null);
  const [newNoteContent, setNewNoteContent] = useState('');
  const [editNoteContent, setEditNoteContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [noteToDelete, setNoteToDelete] = useState<string | null>(null);

  const { hasPermission, userContext } = useRole();
  const canAddMemberNotes = hasPermission('add_member_notes');

  // Debug logging
  useEffect(() => {
    console.log('Current user context:', userContext);
    console.log('Can add member notes:', canAddMemberNotes);
    console.log('User permissions:', userContext?.permissions);
  }, [userContext, canAddMemberNotes]);

  // Load notes on component mount and when userId changes
  useEffect(() => {
    loadNotes();
  }, [userId]);

  const loadNotes = async () => {
    try {
      setIsLoading(true);
      const memberNotes = await getMemberNotes(userId);
      setNotes(memberNotes);
    } catch (error) {
      console.error('Failed to load member notes:', error);
      toast.error('Failed to load member notes');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddNote = async () => {
    if (!newNoteContent.trim()) {
      toast.error('Please enter note content');
      return;
    }

    try {
      setIsSubmitting(true);
      const newNote = await createMemberNote({
        userId,
        content: newNoteContent.trim(),
      });

      setNotes(prev => [newNote, ...prev]);
      setNewNoteContent('');
      setIsAddingNote(false);
      toast.success('Note added successfully');
    } catch (error) {
      console.error('Failed to add note:', error);
      toast.error('Failed to add note');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditNote = async () => {
    if (!editingNote || !editNoteContent.trim()) {
      toast.error('Please enter note content');
      return;
    }

    try {
      setIsSubmitting(true);
      const updatedNote = await updateMemberNote({
        id: editingNote.id,
        content: editNoteContent.trim(),
      });

      setNotes(prev =>
        prev.map(note => (note.id === editingNote.id ? updatedNote : note))
      );
      setEditingNote(null);
      setEditNoteContent('');
      toast.success('Note updated successfully');
    } catch (error) {
      console.error('Failed to update note:', error);
      toast.error('Failed to update note');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteNote = async (noteId: string) => {
    try {
      await deleteMemberNote(noteId);
      setNotes(prev => prev.filter(note => note.id !== noteId));
      toast.success('Note deleted successfully');
    } catch (error) {
      console.error('Failed to delete note:', error);
      toast.error('Failed to delete note');
    }
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "MMM dd, yyyy 'at' h:mm a");
  };

  // Don't show the section if user doesn't have permission
  // Temporarily disabled for testing
  // if (!canAddMemberNotes) {
  //   return null;
  // }

  return (
    <Card className='w-full'>
      <CardHeader>
        <div className='flex items-center justify-between'>
          <div>
            <CardTitle className='flex items-center gap-2'>
              <StickyNote className='h-5 w-5' />
              Internal Notes
            </CardTitle>
            <CardDescription>
              Private notes for {userName} (visible only to Welon Trust users)
            </CardDescription>
          </div>
          <Dialog open={isAddingNote} onOpenChange={setIsAddingNote}>
            <DialogTrigger asChild>
              <Button size='sm' className='flex items-center gap-2'>
                <Plus className='h-4 w-4' />
                Add Note
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add Internal Note</DialogTitle>
                <DialogDescription>
                  Add a private note for {userName}. This note will only be
                  visible to Welon Trust users.
                </DialogDescription>
              </DialogHeader>
              <div className='space-y-4'>
                <div className='space-y-2'>
                  <Label htmlFor='note-content'>Note Content</Label>
                  <Textarea
                    id='note-content'
                    value={newNoteContent}
                    onChange={e => setNewNoteContent(e.target.value)}
                    placeholder='Enter your note here...'
                    rows={4}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button
                  variant='outline'
                  onClick={() => {
                    setIsAddingNote(false);
                    setNewNoteContent('');
                  }}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button onClick={handleAddNote} disabled={isSubmitting}>
                  {isSubmitting ? 'Adding...' : 'Add Note'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className='py-8 text-center'>
            <p className='text-sm text-muted-foreground'>Loading notes...</p>
          </div>
        ) : notes.length === 0 ? (
          <div className='py-8 text-center'>
            <StickyNote className='h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50' />
            <p className='text-muted-foreground mb-4'>No internal notes yet</p>
            <Button
              onClick={() => setIsAddingNote(true)}
              variant='outline'
              size='sm'
            >
              <Plus className='h-4 w-4 mr-2' />
              Add First Note
            </Button>
          </div>
        ) : (
          <div className='space-y-4'>
            {notes.map(note => (
              <Card key={note.id} className='border-l-4 border-l-blue-500'>
                <CardContent className='pt-4'>
                  <div className='flex items-start justify-between mb-3'>
                    <div className='flex items-center gap-2'>
                      <User className='h-4 w-4 text-muted-foreground' />
                      <span className='text-sm font-medium'>
                        {note.authorName}
                      </span>
                      <Badge variant='outline' className='text-xs'>
                        Welon Trust
                      </Badge>
                    </div>
                    <div className='flex items-center gap-2'>
                      <Dialog
                        open={editingNote?.id === note.id}
                        onOpenChange={open => {
                          if (open) {
                            setEditingNote(note);
                            setEditNoteContent(note.content);
                          } else {
                            setEditingNote(null);
                            setEditNoteContent('');
                          }
                        }}
                      >
                        <DialogTrigger asChild>
                          <Button variant='ghost' size='sm'>
                            <Edit className='h-4 w-4' />
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Edit Note</DialogTitle>
                            <DialogDescription>
                              Update the internal note for {userName}.
                            </DialogDescription>
                          </DialogHeader>
                          <div className='space-y-4'>
                            <div className='space-y-2'>
                              <Label htmlFor='edit-note-content'>
                                Note Content
                              </Label>
                              <Textarea
                                id='edit-note-content'
                                value={editNoteContent}
                                onChange={e =>
                                  setEditNoteContent(e.target.value)
                                }
                                rows={4}
                              />
                            </div>
                          </div>
                          <DialogFooter>
                            <Button
                              variant='outline'
                              onClick={() => {
                                setEditingNote(null);
                                setEditNoteContent('');
                              }}
                              disabled={isSubmitting}
                            >
                              Cancel
                            </Button>
                            <Button
                              onClick={handleEditNote}
                              disabled={isSubmitting}
                            >
                              {isSubmitting ? 'Updating...' : 'Update Note'}
                            </Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>

                      <Button
                        variant='ghost'
                        size='sm'
                        className='text-red-600 hover:text-red-700'
                        onClick={() => {
                          setNoteToDelete(note.id);
                          setDeleteDialogOpen(true);
                        }}
                      >
                        <Trash2 className='h-4 w-4' />
                      </Button>
                    </div>
                  </div>

                  <p className='text-sm mb-3 whitespace-pre-wrap'>
                    {note.content}
                  </p>

                  <div className='flex items-center gap-4 text-xs text-muted-foreground'>
                    <div className='flex items-center gap-1'>
                      <Clock className='h-3 w-3' />
                      Created: {formatDate(note.createdAt)}
                    </div>
                    {note.updatedAt !== note.createdAt && (
                      <div className='flex items-center gap-1'>
                        <Clock className='h-3 w-3' />
                        Updated: {formatDate(note.updatedAt)}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </CardContent>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title='Delete Note'
        description='Are you sure you want to delete this note? This action cannot be undone.'
        confirmLabel='Delete'
        cancelLabel='Cancel'
        confirmVariant='destructive'
        onConfirm={async () => {
          if (noteToDelete) {
            await handleDeleteNote(noteToDelete);
            setDeleteDialogOpen(false);
            setNoteToDelete(null);
          }
        }}
      />
    </Card>
  );
}
