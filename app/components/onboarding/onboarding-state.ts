// Define the state type
export interface OnboardingState {
  currentStep: number;
  isChildfree: string | null;
  age: string | null;
  estateSize: string | null;
  loading: boolean;
  isComplete: boolean;
}

// Define the initial state
export const initialState: OnboardingState = {
  currentStep: 1,
  isChildfree: null,
  age: null,
  estateSize: null,
  loading: false,
  isComplete: false,
};

// Define action types
export type OnboardingAction =
  | { type: 'SET_CHILDFREE'; payload: string }
  | { type: 'SET_AGE'; payload: string }
  | { type: 'SET_ESTATE_SIZE'; payload: string }
  | { type: 'NEXT_STEP' }
  | { type: 'PREV_STEP' }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'COMPLETE' };

// Define the reducer function
export function onboardingReducer(
  state: OnboardingState,
  action: OnboardingAction
): OnboardingState {
  switch (action.type) {
    case 'SET_CHILDFREE':
      return {
        ...state,
        isChildfree: action.payload,
      };
    case 'SET_AGE':
      return {
        ...state,
        age: action.payload,
      };
    case 'SET_ESTATE_SIZE':
      return {
        ...state,
        estateSize: action.payload,
      };
    case 'NEXT_STEP':
      if (state.currentStep < 3) {
        return {
          ...state,
          currentStep: state.currentStep + 1,
        };
      }
      return state;
    case 'PREV_STEP':
      if (state.currentStep > 1) {
        return {
          ...state,
          currentStep: state.currentStep - 1,
        };
      }
      return state;
    case 'SET_LOADING':
      return {
        ...state,
        loading: action.payload,
      };
    case 'COMPLETE':
      return {
        ...state,
        isComplete: true,
        loading: true,
      };
    default:
      return state;
  }
}

// Helper function to check if the current step is complete
export function isStepComplete(state: OnboardingState): boolean {
  switch (state.currentStep) {
    case 1:
      return state.isChildfree !== null;
    case 2:
      return state.age !== null;
    case 3:
      return state.estateSize !== null;
    default:
      return false;
  }
}
