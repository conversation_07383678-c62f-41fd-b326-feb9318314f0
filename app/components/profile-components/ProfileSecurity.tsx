'use client';

import { FC, useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Shield, Smartphone, Calendar, Clock, LogOut } from 'lucide-react';
import ChangePasswordDialog from '@/components/ChangePasswordDialog';
import { Separator } from '@/components/ui/separator';
import { fetchDevices, signOut, forgetDevice } from 'aws-amplify/auth';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { format } from 'date-fns';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/app/context/AuthContext';
import routes from '@/utils/routes';

type ProfileSecurity = {};

const ProfileSecurity: FC<ProfileSecurity> = () => {
  const { refreshUser, logout } = useAuth();
  const router = useRouter();
  const queryClient = useQueryClient();
  const [isSigningOut, setIsSigningOut] = useState(false);

  const {
    data: userDevices,
    isLoading: isLoadingDevices,
    error,
  } = useQuery({
    queryKey: ['userDevices'],
    queryFn: fetchDevices,
  });

  console.log('===> USER DEVICEs', userDevices);
  console.error('===> ERROR', error);

  const handleSignOutAllDevices = async () => {
    try {
      setIsSigningOut(true);
      // Use global sign out to sign out from all devices
      await signOut({ global: true });
      // Clear auth context state
      await logout();
      toast.success('Successfully signed out from all devices');
      // Redirect to login page
      router.push(routes.login);
    } catch (error) {
      console.error('Error signing out from all devices:', error);
      toast.error('Failed to sign out from all devices');
      setIsSigningOut(false);
    }
  };

  const forgotThisDevice = async (deviceId: string) => {
    console.log('===> Forgot this device', deviceId);
    await forgetDevice({ device: { id: deviceId } });
  };

  return (
    <Card className='w-full max-w-3xl'>
      <CardHeader>
        <CardTitle className='flex items-center'>
          <Shield className='mr-2 h-5 w-5' />
          Security Settings
        </CardTitle>
        <CardDescription>Manage your account security</CardDescription>
      </CardHeader>
      <CardContent className='space-y-6'>
        <div>
          <h3 className='text-lg font-medium mb-4'>Password Management</h3>
          <p className='text-muted-foreground mb-4'>
            Regularly update your password to keep your account secure.
          </p>
          <ChangePasswordDialog
            trigger={<Button className='w-full'>Change Password</Button>}
          />
        </div>
      </CardContent>
      <Separator className='my-4' />
      <CardContent className='space-y-6'>
        <div>
          <h3 className='text-lg font-medium mb-4'>Active sessions</h3>
          <p className='text-muted-foreground mb-4'>
            View all devices that are currently signed in to your account.
          </p>

          {isLoadingDevices ? (
            <div className='py-4 text-center'>
              Loading device information...
            </div>
          ) : (
            <div className='rounded-md border'>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>
                      <div className='flex items-center'>
                        <Smartphone className='mr-2 h-4 w-4' /> Device
                      </div>
                    </TableHead>
                    <TableHead>
                      <div className='flex items-center'>
                        <Calendar className='mr-2 h-4 w-4' /> Created
                      </div>
                    </TableHead>
                    <TableHead>
                      <div className='flex items-center'>
                        <Clock className='mr-2 h-4 w-4' /> Last Used
                      </div>
                    </TableHead>
                    <TableHead>
                      <div className='flex items-center' />
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {!userDevices || userDevices.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={3} className='h-24 text-center'>
                        No devices found.
                      </TableCell>
                    </TableRow>
                  ) : (
                    userDevices.map((device: any, index) => (
                      <TableRow key={index}>
                        <TableCell className='font-medium'>
                          {`Device ${device.name.slice(0, 10)}`}
                        </TableCell>
                        <TableCell>
                          {device.createDate
                            ? format(new Date(device.createDate), 'MMM d, yyyy')
                            : 'Unknown'}
                        </TableCell>
                        <TableCell>
                          {device.lastAuthenticatedDate
                            ? format(
                                new Date(device.lastAuthenticatedDate),
                                'MMM d, yyyy h:mm a'
                              )
                            : 'Unknown'}
                        </TableCell>
                        <TableCell>
                          {/*<Button*/}
                          {/*    className="w-full flex items-center justify-center"*/}
                          {/*    onClick={async () => forgotThisDevice(device.id)}*/}
                          {/*    disabled={isSigningOut}*/}
                          {/*>*/}
                          {/*  <LogOut className="mr-2 h-4 w-4" />*/}
                          {/*  {isSigningOut ? "Signing out..." : "Forgot this device"}*/}
                          {/*</Button>*/}
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          )}

          <div className='mt-6'>
            <Button
              className='w-full flex items-center justify-center'
              onClick={handleSignOutAllDevices}
              disabled={isSigningOut}
            >
              <LogOut className='mr-2 h-4 w-4' />
              {isSigningOut ? 'Signing out...' : 'Sign Out All Devices'}
            </Button>
            <p className='text-xs text-muted-foreground mt-2'>
              This will sign you out from all devices where you are currently
              logged in.
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProfileSecurity;
