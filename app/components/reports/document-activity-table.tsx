'use client';

import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { DocumentActivity, DocumentActivityType, DocumentType } from './types';
import { Download, Eye, FileText, Pencil, Share2, Trash2 } from 'lucide-react';

interface DocumentActivityTableProps {
  activities: DocumentActivity[];
  onExport?: () => void;
}

export function DocumentActivityTable({
  activities,
  onExport,
}: DocumentActivityTableProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [activityTypeFilter, setActivityTypeFilter] = useState<string>('all');
  const [documentTypeFilter, setDocumentTypeFilter] = useState<string>('all');
  const [dateFilter, setDateFilter] = useState<string>('all');

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Get activity icon based on activity type
  const getActivityIcon = (activityType: DocumentActivityType) => {
    switch (activityType) {
      case 'created':
        return <FileText className='h-4 w-4 text-green-500' />;
      case 'updated':
        return <Pencil className='h-4 w-4 text-blue-500' />;
      case 'viewed':
        return <Eye className='h-4 w-4 text-purple-500' />;
      case 'downloaded':
        return <Download className='h-4 w-4 text-amber-500' />;
      case 'shared':
        return <Share2 className='h-4 w-4 text-indigo-500' />;
      case 'deleted':
        return <Trash2 className='h-4 w-4 text-red-500' />;
      default:
        return <FileText className='h-4 w-4' />;
    }
  };

  // Get activity badge color based on activity type
  const getActivityBadgeColor = (activityType: DocumentActivityType) => {
    switch (activityType) {
      case 'created':
        return 'bg-green-500';
      case 'updated':
        return 'bg-blue-500';
      case 'viewed':
        return 'bg-purple-500';
      case 'downloaded':
        return 'bg-amber-500';
      case 'shared':
        return 'bg-indigo-500';
      case 'deleted':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  // Filter activities based on search term and filters
  const filteredActivities = activities.filter(activity => {
    // Search term filter
    const matchesSearch =
      activity.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      activity.documentTitle.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (activity.details &&
        activity.details.toLowerCase().includes(searchTerm.toLowerCase()));

    // Activity type filter
    const matchesActivityType =
      activityTypeFilter === 'all' ||
      activity.activityType === activityTypeFilter;

    // Document type filter
    const matchesDocumentType =
      documentTypeFilter === 'all' ||
      activity.documentType === documentTypeFilter;

    // Date filter
    let matchesDate = true;
    const activityDate = new Date(activity.timestamp);
    const now = new Date();

    if (dateFilter === 'today') {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      matchesDate = activityDate >= today;
    } else if (dateFilter === 'week') {
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      matchesDate = activityDate >= weekAgo;
    } else if (dateFilter === 'month') {
      const monthAgo = new Date();
      monthAgo.setMonth(monthAgo.getMonth() - 1);
      matchesDate = activityDate >= monthAgo;
    }

    return (
      matchesSearch && matchesActivityType && matchesDocumentType && matchesDate
    );
  });

  return (
    <div className='space-y-4'>
      <div className='flex flex-row gap-3 items-center flex-wrap'>
        <Input
          placeholder='Search by user or document...'
          value={searchTerm}
          onChange={e => setSearchTerm(e.target.value)}
          className='w-[250px]'
        />

        <Select
          value={activityTypeFilter}
          onValueChange={setActivityTypeFilter}
        >
          <SelectTrigger className='w-[160px]'>
            <SelectValue placeholder='Activity Type' />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='all'>All Activities</SelectItem>
            <SelectItem value='created'>Created</SelectItem>
            <SelectItem value='updated'>Updated</SelectItem>
            <SelectItem value='viewed'>Viewed</SelectItem>
            <SelectItem value='downloaded'>Downloaded</SelectItem>
            <SelectItem value='shared'>Shared</SelectItem>
            <SelectItem value='deleted'>Deleted</SelectItem>
          </SelectContent>
        </Select>

        <Select
          value={documentTypeFilter}
          onValueChange={setDocumentTypeFilter}
        >
          <SelectTrigger className='w-[160px]'>
            <SelectValue placeholder='Document Type' />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='all'>All Documents</SelectItem>
            <SelectItem value='will'>Will</SelectItem>
            <SelectItem value='trust'>Trust</SelectItem>
            <SelectItem value='poa-healthcare'>Healthcare POA</SelectItem>
            <SelectItem value='poa-financial'>Financial POA</SelectItem>
            <SelectItem value='advance-directive'>Advance Directive</SelectItem>
            <SelectItem value='other'>Other</SelectItem>
          </SelectContent>
        </Select>

        <Select value={dateFilter} onValueChange={setDateFilter}>
          <SelectTrigger className='w-[140px]'>
            <SelectValue placeholder='Time Period' />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='all'>All Time</SelectItem>
            <SelectItem value='today'>Today</SelectItem>
            <SelectItem value='week'>Last 7 Days</SelectItem>
            <SelectItem value='month'>Last 30 Days</SelectItem>
          </SelectContent>
        </Select>

        {onExport && (
          <Button variant='outline' onClick={onExport}>
            <Download className='mr-2 h-4 w-4' />
            Export Report
          </Button>
        )}
      </div>

      <div className='rounded-md border'>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Date & Time</TableHead>
              <TableHead>User</TableHead>
              <TableHead>Document</TableHead>
              <TableHead>Activity</TableHead>
              <TableHead>Details</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredActivities.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={5}
                  className='text-center py-6 text-muted-foreground'
                >
                  No document activities found
                </TableCell>
              </TableRow>
            ) : (
              filteredActivities.map(activity => (
                <TableRow key={activity.id}>
                  <TableCell>{formatDate(activity.timestamp)}</TableCell>
                  <TableCell>{activity.userName}</TableCell>
                  <TableCell>
                    <div className='flex flex-col'>
                      <span>{activity.documentTitle}</span>
                      <span className='text-xs text-muted-foreground'>
                        {activity.documentType
                          .replace('-', ' ')
                          .replace(/\b\w/g, l => l.toUpperCase())}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge
                      className={`flex items-center gap-1 ${getActivityBadgeColor(activity.activityType)}`}
                    >
                      {getActivityIcon(activity.activityType)}
                      <span className='capitalize'>
                        {activity.activityType}
                      </span>
                    </Badge>
                  </TableCell>
                  <TableCell>{activity.details || '-'}</TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
