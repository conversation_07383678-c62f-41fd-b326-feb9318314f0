// Document activity report types

export type DocumentActivityType =
  | 'created'
  | 'updated'
  | 'viewed'
  | 'downloaded'
  | 'shared'
  | 'deleted';

export type DocumentType =
  | 'will'
  | 'trust'
  | 'poa-healthcare'
  | 'poa-financial'
  | 'advance-directive'
  | 'other';

export interface DocumentActivity {
  id: string;
  userId: string;
  userName: string;
  documentId: string;
  documentTitle: string;
  documentType: DocumentType;
  activityType: DocumentActivityType;
  timestamp: string;
  details?: string;
}

export interface DocumentActivitySummary {
  totalActivities: number;
  createdCount: number;
  updatedCount: number;
  viewedCount: number;
  downloadedCount: number;
  sharedCount: number;
  deletedCount: number;
  byDocumentType: Record<DocumentType, number>;
  byUser: Record<string, number>;
  byDay: Record<string, number>;
}

// Mock data for document activities
export const mockDocumentActivities: DocumentActivity[] = [
  {
    id: 'act-001',
    userId: 'user-001',
    userName: '<PERSON> Smith',
    documentId: 'doc-001',
    documentTitle: 'Last Will and Testament',
    documentType: 'will',
    activityType: 'created',
    timestamp: '2023-06-15T10:30:00Z',
    details: 'Initial document creation',
  },
  {
    id: 'act-002',
    userId: 'user-002',
    userName: 'Jane Doe',
    documentId: 'doc-002',
    documentTitle: 'Healthcare Power of Attorney',
    documentType: 'poa-healthcare',
    activityType: 'created',
    timestamp: '2023-06-16T14:45:00Z',
    details: 'Initial document creation',
  },
  {
    id: 'act-003',
    userId: 'user-001',
    userName: 'John Smith',
    documentId: 'doc-001',
    documentTitle: 'Last Will and Testament',
    documentType: 'will',
    activityType: 'updated',
    timestamp: '2023-06-18T09:15:00Z',
    details: 'Updated beneficiary information',
  },
  {
    id: 'act-004',
    userId: 'user-003',
    userName: 'Robert Johnson',
    documentId: 'doc-003',
    documentTitle: 'Revocable Living Trust',
    documentType: 'trust',
    activityType: 'created',
    timestamp: '2023-06-20T11:20:00Z',
    details: 'Initial document creation',
  },
  {
    id: 'act-005',
    userId: 'user-002',
    userName: 'Jane Doe',
    documentId: 'doc-002',
    documentTitle: 'Healthcare Power of Attorney',
    documentType: 'poa-healthcare',
    activityType: 'viewed',
    timestamp: '2023-06-22T16:30:00Z',
  },
  {
    id: 'act-006',
    userId: 'user-001',
    userName: 'John Smith',
    documentId: 'doc-001',
    documentTitle: 'Last Will and Testament',
    documentType: 'will',
    activityType: 'downloaded',
    timestamp: '2023-06-25T13:45:00Z',
  },
  {
    id: 'act-007',
    userId: 'user-004',
    userName: 'Sarah Williams',
    documentId: 'doc-004',
    documentTitle: 'Financial Power of Attorney',
    documentType: 'poa-financial',
    activityType: 'created',
    timestamp: '2023-06-28T10:10:00Z',
    details: 'Initial document creation',
  },
  {
    id: 'act-008',
    userId: 'user-003',
    userName: 'Robert Johnson',
    documentId: 'doc-003',
    documentTitle: 'Revocable Living Trust',
    documentType: 'trust',
    activityType: 'updated',
    timestamp: '2023-07-02T15:20:00Z',
    details: 'Updated trustee information',
  },
  {
    id: 'act-009',
    userId: 'user-005',
    userName: 'Michael Brown',
    documentId: 'doc-005',
    documentTitle: 'Advance Healthcare Directive',
    documentType: 'advance-directive',
    activityType: 'created',
    timestamp: '2023-07-05T09:30:00Z',
    details: 'Initial document creation',
  },
  {
    id: 'act-010',
    userId: 'user-004',
    userName: 'Sarah Williams',
    documentId: 'doc-004',
    documentTitle: 'Financial Power of Attorney',
    documentType: 'poa-financial',
    activityType: 'shared',
    timestamp: '2023-07-08T14:15:00Z',
    details: 'Shared with attorney',
  },
];
