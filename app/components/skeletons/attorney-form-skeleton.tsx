import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { FormSkeleton, FormFieldGrid, createFormField } from './form-skeleton';

export function AttorneyFormSkeleton() {
  const sections = [
    {
      title: 'Basic Information',
      description: "Enter the attorney's basic contact information",
      fields: [
        // Grid of name and firm
        createFormField('custom', {
          customContent: (
            <FormFieldGrid columns={2}>
              <div className='space-y-2'>
                <Skeleton className='h-4 w-20' />
                <Skeleton className='h-10 w-full' />
              </div>
              <div className='space-y-2'>
                <Skeleton className='h-4 w-16' />
                <Skeleton className='h-10 w-full' />
              </div>
            </FormFieldGrid>
          ),
        }),
        // Grid of phone and email
        createFormField('custom', {
          customContent: (
            <FormFieldGrid columns={2}>
              <div className='space-y-2'>
                <Skeleton className='h-4 w-20' />
                <Skeleton className='h-10 w-full' />
              </div>
              <div className='space-y-2'>
                <Skeleton className='h-4 w-20' />
                <Skeleton className='h-10 w-full' />
              </div>
            </FormFieldGrid>
          ),
        }),
      ],
    },
    {
      title: 'Location Information',
      description: "Enter the attorney's practice location details",
      fields: [
        // Address field
        createFormField('input', { label: 'Address' }),
        // Grid of city, state, zip
        createFormField('custom', {
          customContent: (
            <FormFieldGrid columns={3}>
              <div className='space-y-2'>
                <Skeleton className='h-4 w-12' />
                <Skeleton className='h-10 w-full' />
              </div>
              <div className='space-y-2'>
                <Skeleton className='h-4 w-12' />
                <Skeleton className='h-10 w-full' />
              </div>
              <div className='space-y-2'>
                <Skeleton className='h-4 w-16' />
                <Skeleton className='h-10 w-full' />
              </div>
            </FormFieldGrid>
          ),
        }),
      ],
    },
    {
      title: 'Professional Information',
      description:
        "Enter the attorney's professional credentials and experience",
      fields: [
        // Grid of bar number and years of experience
        createFormField('custom', {
          customContent: (
            <FormFieldGrid columns={2}>
              <div className='space-y-2'>
                <Skeleton className='h-4 w-20' />
                <Skeleton className='h-10 w-full' />
              </div>
              <div className='space-y-2'>
                <Skeleton className='h-4 w-32' />
                <Skeleton className='h-10 w-full' />
              </div>
            </FormFieldGrid>
          ),
        }),
        // Preferred attorney checkbox
        createFormField('custom', {
          customContent: (
            <div className='flex flex-row items-start space-x-3 space-y-0'>
              <Skeleton className='h-4 w-4 mt-1' />
              <div className='space-y-1'>
                <Skeleton className='h-4 w-32' />
                <Skeleton className='h-3 w-64' />
              </div>
            </div>
          ),
        }),
        // Active status checkbox
        createFormField('custom', {
          customContent: (
            <div className='flex flex-row items-start space-x-3 space-y-0'>
              <Skeleton className='h-4 w-4 mt-1' />
              <div className='space-y-1'>
                <Skeleton className='h-4 w-24' />
                <Skeleton className='h-3 w-72' />
              </div>
            </div>
          ),
        }),
      ],
    },
    {
      title: 'Legal Specialties',
      description: "Select the attorney's areas of legal expertise",
      fields: [
        // Selected specialties badges
        createFormField('custom', {
          customContent: (
            <div className='space-y-2'>
              <Skeleton className='h-4 w-32' />
              <div className='flex flex-wrap gap-2'>
                {Array.from({ length: 3 }).map((_, index) => (
                  <Badge
                    key={index}
                    variant='secondary'
                    className='flex items-center gap-1'
                  >
                    <Skeleton className='h-4 w-20' />
                  </Badge>
                ))}
              </div>
            </div>
          ),
        }),
        // Common specialties grid
        createFormField('custom', {
          customContent: (
            <div className='space-y-2'>
              <Skeleton className='h-4 w-32' />
              <div className='grid grid-cols-2 gap-2'>
                {Array.from({ length: 8 }).map((_, index) => (
                  <div key={index} className='flex items-center space-x-2'>
                    <Skeleton className='h-4 w-4' />
                    <Skeleton className='h-4 w-24' />
                  </div>
                ))}
              </div>
            </div>
          ),
        }),
        // Add custom specialty
        createFormField('custom', {
          customContent: (
            <div className='space-y-2'>
              <Skeleton className='h-4 w-32' />
              <div className='flex gap-2'>
                <Skeleton className='h-10 flex-1' />
                <Skeleton className='h-10 w-10' />
              </div>
            </div>
          ),
        }),
      ],
    },
  ];

  return (
    <FormSkeleton
      title='Edit Attorney'
      description='Update attorney information and contact details'
      backButtonText='Back to Attorneys'
      sections={sections}
      actionButtonText='Save Changes'
    />
  );
}
