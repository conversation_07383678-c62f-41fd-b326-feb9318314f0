import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

interface FormSkeletonProps {
  title?: string;
  description?: string;
  backButtonText?: string;
  sections?: FormSectionConfig[];
  showActions?: boolean;
  actionButtonText?: string;
}

interface FormSectionConfig {
  title: string;
  description: string;
  fields: FormFieldConfig[];
}

interface FormFieldConfig {
  type: 'input' | 'select' | 'textarea' | 'checkbox' | 'custom';
  label?: string;
  width?: 'full' | 'half' | 'third';
  height?: string;
  customContent?: React.ReactNode;
}

export function FormSkeleton({
  title,
  description,
  backButtonText = 'Back',
  sections = [],
  showActions = true,
  actionButtonText = 'Save Changes',
}: FormSkeletonProps) {
  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex items-center space-x-4'>
        <Button variant='ghost' size='sm' disabled>
          ← {backButtonText}
        </Button>
      </div>

      <div className='flex items-center gap-3'>
        <Skeleton className='h-8 w-8' /> {/* Icon */}
        <div>
          {title ? (
            <h1 className='text-3xl font-geologica font-semibold'>{title}</h1>
          ) : (
            <Skeleton className='h-9 w-48 mb-2' />
          )}
          {description ? (
            <p className='text-muted-foreground mt-2'>{description}</p>
          ) : (
            <Skeleton className='h-5 w-64' />
          )}
        </div>
      </div>

      {/* Form Sections */}
      <div className='space-y-6'>
        {sections.map((section, sectionIndex) => (
          <Card key={sectionIndex}>
            <CardHeader>
              <CardTitle>{section.title}</CardTitle>
              <CardDescription>{section.description}</CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              {section.fields.map((field, fieldIndex) => (
                <FormFieldSkeleton key={fieldIndex} field={field} />
              ))}
            </CardContent>
          </Card>
        ))}

        {/* Actions */}
        {showActions && (
          <div className='flex justify-end space-x-4'>
            <Button variant='outline' disabled>
              Cancel
            </Button>
            <Button disabled>{actionButtonText}</Button>
          </div>
        )}
      </div>
    </div>
  );
}

function FormFieldSkeleton({ field }: { field: FormFieldConfig }) {
  const getWidthClass = (width: FormFieldConfig['width']) => {
    switch (width) {
      case 'half':
        return 'w-1/2';
      case 'third':
        return 'w-1/3';
      default:
        return 'w-full';
    }
  };

  if (field.type === 'custom' && field.customContent) {
    return (
      <div className={getWidthClass(field.width)}>{field.customContent}</div>
    );
  }

  return (
    <div className={`space-y-2 ${getWidthClass(field.width)}`}>
      {field.label && <Skeleton className='h-4 w-20' />}

      {field.type === 'input' && <Skeleton className='h-10 w-full' />}

      {field.type === 'select' && <Skeleton className='h-10 w-full' />}

      {field.type === 'textarea' && (
        <Skeleton className={`w-full ${field.height || 'h-24'}`} />
      )}

      {field.type === 'checkbox' && (
        <div className='flex items-center space-x-2'>
          <Skeleton className='h-4 w-4' />
          <Skeleton className='h-4 w-32' />
        </div>
      )}
    </div>
  );
}

// Grid wrapper for multiple fields in a row
export function FormFieldGrid({
  children,
  columns = 2,
}: {
  children: React.ReactNode;
  columns?: number;
}) {
  const gridClass =
    columns === 2
      ? 'grid-cols-2'
      : columns === 3
        ? 'grid-cols-3'
        : 'grid-cols-1';

  return <div className={`grid ${gridClass} gap-4`}>{children}</div>;
}

// Helper function to create common field configurations
export const createFormField = (
  type: FormFieldConfig['type'],
  options: Partial<FormFieldConfig> = {}
): FormFieldConfig => ({
  type,
  width: 'full',
  ...options,
});

// Common field presets
export const commonFields = {
  name: createFormField('input', { label: 'Name' }),
  email: createFormField('input', { label: 'Email' }),
  phone: createFormField('input', { label: 'Phone' }),
  address: createFormField('input', { label: 'Address' }),
  city: createFormField('input', { label: 'City', width: 'third' }),
  state: createFormField('select', { label: 'State', width: 'third' }),
  zipCode: createFormField('input', { label: 'ZIP Code', width: 'third' }),
  checkbox: createFormField('checkbox'),
  textarea: createFormField('textarea', { height: 'h-24' }),
};
