import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

export function UserFormSkeleton() {
  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex items-center space-x-4'>
        <Button
          variant='ghost'
          size='sm'
          disabled
          icon='arrow-left'
          iconSize='sm'
        >
          Back to Users
        </Button>
      </div>

      <div>
        <Skeleton className='h-9 w-48 mb-2' /> {/* Title */}
        <Skeleton className='h-5 w-64' /> {/* Description */}
      </div>

      {/* Form */}
      <div className='space-y-6'>
        <Card>
          <CardHeader>
            <CardTitle>User Information</CardTitle>
            <CardDescription>
              Enter the user's basic information and role assignments
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-6'>
            <div className='grid grid-cols-2 gap-4'>
              {/* Name Field */}
              <div className='space-y-2'>
                <Skeleton className='h-4 w-12' /> {/* Label */}
                <Skeleton className='h-10 w-full' /> {/* Input */}
              </div>

              {/* Email Field */}
              <div className='space-y-2'>
                <Skeleton className='h-4 w-12' /> {/* Label */}
                <Skeleton className='h-10 w-full' /> {/* Input */}
              </div>
            </div>

            <div className='grid grid-cols-2 gap-4'>
              {/* Role Field */}
              <div className='space-y-2'>
                <Skeleton className='h-4 w-8' /> {/* Label */}
                <Skeleton className='h-10 w-full' /> {/* Select */}
              </div>

              {/* Subrole Field */}
              <div className='space-y-2'>
                <Skeleton className='h-4 w-16' /> {/* Label */}
                <Skeleton className='h-10 w-full' /> {/* Select */}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Permissions */}
        <Card>
          <CardHeader>
            <CardTitle>Permissions</CardTitle>
            <CardDescription>
              Select the permissions for this user based on their role
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className='space-y-2'>
              {/* Permission checkboxes */}
              {Array.from({ length: 4 }).map((_, index) => (
                <div key={index} className='flex items-center space-x-2'>
                  <Skeleton className='h-4 w-4' /> {/* Checkbox */}
                  <Skeleton className='h-4 w-32' /> {/* Permission text */}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Welon Trust Assignment */}
        <Card>
          <CardHeader>
            <CardTitle>Welon Trust Assignment</CardTitle>
            <CardDescription>
              Assign a Welon Trust to this member for estate management
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='space-y-2'>
              <Skeleton className='h-4 w-32' /> {/* Label */}
              <Skeleton className='h-10 w-full' /> {/* Select */}
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <div className='flex justify-end space-x-4'>
          <Button variant='outline' disabled icon='close'>
            Cancel
          </Button>
          <Button disabled icon='save'>
            Save Changes
          </Button>
        </div>
      </div>
    </div>
  );
}
