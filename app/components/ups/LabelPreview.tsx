'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Eye, Download, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface LabelPreviewProps {
  labelUrl: string;
  trackingNumber: string;
  onDownload?: () => void;
}

export default function LabelPreview({
  labelUrl,
  trackingNumber,
  onDownload,
}: LabelPreviewProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handlePreview = () => {
    setError(null);
    if (!labelUrl || !labelUrl.startsWith('data:')) {
      setError('Invalid label data for preview');
      return;
    }
    setIsOpen(true);
  };

  return (
    <>
      <Button variant='outline' size='sm' onClick={handlePreview}>
        <Eye className='h-4 w-4 mr-2' />
        Preview
      </Button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className='max-w-4xl max-h-[90vh]'>
          <DialogHeader>
            <DialogTitle className='flex items-center justify-between'>
              <span>Shipping Label Preview</span>
              <div className='flex gap-2'>
                <Button variant='outline' size='sm' onClick={onDownload}>
                  <Download className='h-4 w-4 mr-2' />
                  Download
                </Button>
              </div>
            </DialogTitle>
          </DialogHeader>

          <div className='space-y-4'>
            <div className='text-sm text-muted-foreground'>
              Tracking Number:{' '}
              <span className='font-mono'>{trackingNumber}</span>
            </div>

            {error ? (
              <Alert>
                <AlertCircle className='h-4 w-4' />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            ) : (
              <div className='border rounded-lg overflow-hidden bg-white p-4'>
                {labelUrl.startsWith('data:image/gif') ? (
                  <div className='flex justify-center'>
                    <img
                      src={labelUrl}
                      alt={`UPS Label ${trackingNumber}`}
                      className='max-w-full h-auto'
                      onError={() => setError('Failed to load label preview')}
                    />
                  </div>
                ) : (
                  <iframe
                    src={labelUrl}
                    className='w-full h-[600px]'
                    title={`UPS Label ${trackingNumber}`}
                    onError={() => setError('Failed to load label preview')}
                  />
                )}
              </div>
            )}

            <div className='text-xs text-muted-foreground text-center'>
              This is a preview of your UPS shipping label. Print this label and
              attach it to your package.
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
