'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Loader2, Save, MapPin } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { UPSAddress } from '@/app/types/ups';
import AddressAutocomplete from './AddressAutocomplete';
import { useAuth } from '@/app/context/AuthContext';
import { toast } from 'sonner';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { getCurrentUser } from 'aws-amplify/auth';

const client = generateClient<Schema>();

interface SaveUserAddressProps {
  onAddressSaved?: () => void;
}

export default function SaveUserAddress({
  onAddressSaved,
}: SaveUserAddressProps) {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingCurrentAddress, setIsLoadingCurrentAddress] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentAddress, setCurrentAddress] = useState<any>(null);

  const [address, setAddress] = useState<UPSAddress>({
    addressLine1: '',
    addressLine2: '',
    city: '',
    stateProvinceCode: '',
    postalCode: '',
    countryCode: 'US',
  });

  // Load current address on component mount
  React.useEffect(() => {
    loadCurrentAddress();
  }, []);

  const loadCurrentAddress = async () => {
    setIsLoadingCurrentAddress(true);
    try {
      // Get current user
      const currentUser = await getCurrentUser();

      // Get current user data
      const { data: userList, errors: listErrors } =
        await client.models.User.list({
          filter: { cognitoId: { eq: currentUser.userId } },
          selectionSet: ['id', 'cognitoId'],
        });

      if (listErrors || !userList || userList.length === 0) {
        return;
      }

      const userData = userList[0];

      // Get user's addresses
      const { data: addresses, errors: addressErrors } =
        await client.models.UserAddress.list({
          filter: { userId: { eq: userData.id } },
        });

      if (!addressErrors && addresses && addresses.length > 0) {
        // Get the default address or the first one
        const defaultAddress =
          addresses.find(addr => addr.isDefault) || addresses[0];
        setCurrentAddress(defaultAddress);
      }
    } catch (error) {
      console.error('Error loading current address:', error);
    } finally {
      setIsLoadingCurrentAddress(false);
    }
  };

  const handleEditAddress = () => {
    if (currentAddress) {
      setAddress({
        addressLine1: currentAddress.addressLine1,
        addressLine2: currentAddress.addressLine2 || '',
        city: currentAddress.city,
        stateProvinceCode: currentAddress.stateProvinceCode,
        postalCode: currentAddress.postalCode,
        countryCode: currentAddress.countryCode,
      });
    }
  };

  const handleSaveAddress = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Get current user
      const currentUser = await getCurrentUser();
      console.log('Current user:', currentUser.userId);

      // Get current user data - try to find user by cognitoId
      const { data: userList, errors: listErrors } =
        await client.models.User.list({
          filter: { cognitoId: { eq: currentUser.userId } },
          selectionSet: ['id', 'cognitoId'],
        });

      if (listErrors) {
        console.error('Failed to fetch user data:', listErrors);
        throw new Error('Failed to fetch user data');
      }

      if (!userList || userList.length === 0) {
        throw new Error('User not found');
      }

      const userData = userList[0];

      let savedAddress;

      if (currentAddress) {
        // Update existing address
        const { data: updatedAddress, errors } =
          await client.models.UserAddress.update({
            id: currentAddress.id,
            addressLine1: address.addressLine1,
            addressLine2: address.addressLine2 || '',
            city: address.city,
            stateProvinceCode: address.stateProvinceCode,
            postalCode: address.postalCode,
            countryCode: address.countryCode,
            updatedAt: new Date().toISOString(),
          });

        if (errors) {
          console.error('Errors updating address:', errors);
          throw new Error('Failed to update address');
        }

        if (!updatedAddress) {
          throw new Error('Address not updated');
        }

        savedAddress = updatedAddress;
      } else {
        // Create new address
        const { data: newAddress, errors } =
          await client.models.UserAddress.create({
            userId: userData.id,
            addressLine1: address.addressLine1,
            addressLine2: address.addressLine2 || '',
            city: address.city,
            stateProvinceCode: address.stateProvinceCode,
            postalCode: address.postalCode,
            countryCode: address.countryCode,
            isDefault: true, // Make this the default address for now
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          });

        if (errors) {
          console.error('Errors creating address:', errors);
          throw new Error('Failed to create address');
        }

        if (!newAddress) {
          throw new Error('Address not created');
        }

        savedAddress = newAddress;
      }

      toast.success('Address saved successfully!');
      onAddressSaved?.();

      // Update current address and reset form
      setCurrentAddress(savedAddress);
      setAddress({
        addressLine1: '',
        addressLine2: '',
        city: '',
        stateProvinceCode: '',
        postalCode: '',
        countryCode: 'US',
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      setError(errorMessage);
      toast.error('Failed to save address: ' + errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const isFormValid = () => {
    return (
      address.addressLine1 &&
      address.city &&
      address.stateProvinceCode &&
      address.postalCode
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          <MapPin className='h-5 w-5' />
          Save Your Address
        </CardTitle>
        <CardDescription>
          Save your address for future shipping labels
        </CardDescription>
      </CardHeader>
      <CardContent className='space-y-6'>
        {error && (
          <Alert variant='destructive'>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Current Address Display */}
        {currentAddress && (
          <div className='space-y-4'>
            <div className='flex items-center justify-between'>
              <h3 className='text-lg font-medium'>Current Saved Address</h3>
              <Button variant='outline' size='sm' onClick={handleEditAddress}>
                Edit
              </Button>
            </div>
            <div className='p-4 bg-muted rounded-lg'>
              <div className='space-y-2'>
                <p className='font-medium'>{currentAddress.addressLine1}</p>
                {currentAddress.addressLine2 && (
                  <p className='text-muted-foreground'>
                    {currentAddress.addressLine2}
                  </p>
                )}
                <p className='text-muted-foreground'>
                  {currentAddress.city}, {currentAddress.stateProvinceCode}{' '}
                  {currentAddress.postalCode}
                </p>
                <p className='text-muted-foreground'>
                  {currentAddress.countryCode}
                </p>
              </div>
            </div>
          </div>
        )}

        {isLoadingCurrentAddress && (
          <div className='flex items-center justify-center py-4'>
            <Loader2 className='h-6 w-6 animate-spin' />
            <span className='ml-2'>Loading current address...</span>
          </div>
        )}

        <AddressAutocomplete
          label='Your Address'
          address={{ ...address, name: '', phone: '' }}
          onChange={updatedAddress => {
            const { name, phone, ...addressOnly } = updatedAddress;
            setAddress(addressOnly);
          }}
          nameAutoComplete='name'
          addressPrefix=''
          required={true}
          hideNameAndPhone={true}
        />

        <Button
          onClick={handleSaveAddress}
          disabled={!isFormValid() || isLoading}
          className='w-full'
          size='lg'
        >
          {isLoading ? (
            <>
              <Loader2 className='mr-2 h-4 w-4 animate-spin' />
              {currentAddress ? 'Updating Address...' : 'Saving Address...'}
            </>
          ) : (
            <>
              <Save className='mr-2 h-4 w-4' />
              {currentAddress ? 'Update Address' : 'Save Address'}
            </>
          )}
        </Button>

        {!isFormValid() && (
          <p className='text-sm text-muted-foreground text-center'>
            Please fill in all required fields to save your address
          </p>
        )}
      </CardContent>
    </Card>
  );
}
