'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { PDFViewer } from '@/components/documents/pdf-viewer';
import { Document } from '@/types/documents';
import { X, FileText, Calendar, User, Download } from 'lucide-react';
import { DocumentStatusBadge } from '@/components/documents/document-status-badge';
import { downloadDocument } from '@/lib/utils/document-download';
import { toast } from 'sonner';

interface DocumentPreviewDialogProps {
  document: Document | null;
  isOpen: boolean;
  onClose: () => void;
}

export function DocumentPreviewDialog({
  document,
  isOpen,
  onClose,
}: DocumentPreviewDialogProps) {
  const [isDownloading, setIsDownloading] = useState(false);

  if (!document) return null;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const handleDownload = async () => {
    try {
      setIsDownloading(true);
      await downloadDocument(document);
      toast.success('Document downloaded successfully!');
    } catch (error) {
      console.error('Download error:', error);
      toast.error('Failed to download document');
    } finally {
      setIsDownloading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-w-[95vw] w-[95vw] max-h-[95vh] h-[95vh] overflow-hidden p-0 flex flex-col'>
        <div className='flex-1 overflow-hidden p-6 min-h-0'>
          <div className='h-full border rounded-lg overflow-auto'>
            <PDFViewer documentHtml={document} />
          </div>
        </div>

        <div className='flex justify-between items-center p-6 pt-4 border-t shrink-0'>
          <div className='text-sm text-muted-foreground'>
            Last modified: {formatDate(document.lastModified)}
          </div>
          <div className='flex items-center gap-2'>
            <Button
              onClick={handleDownload}
              disabled={isDownloading}
              variant='outline'
              className='flex items-center gap-2'
            >
              <Download className='h-4 w-4' />
              {isDownloading ? 'Downloading...' : 'Download PDF'}
            </Button>
            <Button onClick={onClose} variant='outline'>
              Close Preview
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
