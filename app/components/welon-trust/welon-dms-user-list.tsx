'use client';

import React, { useMemo } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table/data-table';
import { DataTableColumnHeader } from '@/components/ui/data-table/data-table-column-header';
import type { DataTableConfig } from '@/components/ui/data-table/data-table';
import type { WelonDMSFailedUser } from '@/lib/api/welon-users';
import { Eye, AlertTriangle, Clock, Mail, Phone } from 'lucide-react';

interface WelonDMSUserListProps {
  users: WelonDMSFailedUser[];
  loading: boolean;
  className?: string;
}

// Define filter configurations
const tableConfig: DataTableConfig = {
  searchColumn: 'email',
  searchPlaceholder: 'Search members...',
  filters: [
    {
      id: 'status',
      title: 'Status',
      options: [
        { value: 'active', label: 'Active' },
        { value: 'pending', label: 'Pending' },
        { value: 'inactive', label: 'Inactive' },
      ],
    },
  ],
  enableColumnVisibility: true,
  enablePagination: true,
  enableRowSelection: false,
  defaultPageSize: 10,
};

const formatDate = (dateString: string | null | undefined) => {
  if (!dateString) return 'Never';
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

const getStatusBadgeVariant = (status: string) => {
  switch (status.toLowerCase()) {
    case 'active':
      return 'default';
    case 'pending':
      return 'secondary';
    case 'inactive':
      return 'destructive';
    default:
      return 'outline';
  }
};

export function WelonDMSUserList({
  users,
  loading,
  className = '',
}: WelonDMSUserListProps) {
  const columns: ColumnDef<WelonDMSFailedUser>[] = useMemo(
    () => [
      {
        accessorKey: 'firstName',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title='Member' />
        ),
        cell: ({ row }) => (
          <div className='flex flex-col'>
            <span className='font-semibold'>
              {row.original.firstName} {row.original.lastName}
            </span>
            <span className='text-sm text-[var(--custom-gray-medium)]'>
              {row.original.email}
            </span>
          </div>
        ),
        enableSorting: true,
      },
      {
        accessorKey: 'role',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title='Role' />
        ),
        cell: ({ row }) => (
          <Badge variant='outline' className='text-xs'>
            {row.original.role}
          </Badge>
        ),
        enableSorting: true,
      },
      {
        accessorKey: 'status',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title='Account Status' />
        ),
        cell: ({ row }) => (
          <Badge
            variant={getStatusBadgeVariant(row.original.status)}
            className='text-xs capitalize'
          >
            {row.original.status}
          </Badge>
        ),
        filterFn: (row, id, value) => {
          return value.includes(row.getValue(id));
        },
        enableSorting: true,
      },
      {
        accessorKey: 'isDmsCheckSuccessful',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title='Wellness Checks Status'
          />
        ),
        cell: ({ row }) => (
          <div className='flex items-center gap-2'>
            <Badge className='bg-red-100 text-red-800 text-xs'>
              <AlertTriangle className='h-3 w-3 mr-1' />
              Wellness Checks Failed
            </Badge>
          </div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'createdAt',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title='Member Since' />
        ),
        cell: ({ row }) => (
          <span className='text-sm'>{formatDate(row.original.createdAt)}</span>
        ),
        enableSorting: true,
      },
      {
        id: 'actions',
        header: 'Actions',
        cell: ({ row }) => (
          <div className='flex items-center gap-2'>
            <Button disabled variant='outline' size='sm'>
              <Mail className='h-4 w-4 mr-1' />
              Contact
            </Button>
          </div>
        ),
        enableSorting: false,
      },
    ],
    []
  );

  if (loading) {
    return (
      <div className='flex justify-center items-center py-8'>
        <div className='text-center'>
          <Clock className='h-8 w-8 text-gray-400 mx-auto mb-2 animate-spin' />
          <p className='text-sm text-[var(--custom-gray-medium)]'>
            Loading emergency data...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      <DataTable columns={columns} data={users} config={tableConfig} />
    </div>
  );
}
