'use client';

import React, { useState, useEffect } from 'react';
import { generateClient } from 'aws-amplify/data';
import { getCurrentUser } from 'aws-amplify/auth';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Package, Download, User } from 'lucide-react';
import LabelPreview from '@/app/components/ups/LabelPreview';
import { ShippingLabelResult } from '@/app/types/ups';
import { toast } from 'sonner';
import type { Schema } from '@/amplify/data/resource';

const client = generateClient<Schema>();

interface IncomingPackage extends ShippingLabelResult {
  id: string;
  userId: string;
  fromAddress: any;
  toAddress: any;
  senderName?: string;
  senderEmail?: string;
}

export default function IncomingPackages() {
  const [incomingPackages, setIncomingPackages] = useState<IncomingPackage[]>(
    []
  );
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadIncomingPackages();
  }, []);

  const loadIncomingPackages = async () => {
    setIsLoading(true);
    try {
      // Get current user
      const currentUser = await getCurrentUser();

      // Get current user data
      const { data: userList } = await client.models.User.list({
        filter: { cognitoId: { eq: currentUser.userId } },
        selectionSet: ['id', 'role'],
      });

      if (!userList || userList.length === 0) {
        return;
      }

      const userData = userList[0];

      // Only load for WelonTrust users
      if (userData.role !== 'WelonTrust') {
        return;
      }

      // Get shipping labels assigned to this WelonTrust user
      const { data: labels } = await client.models.ShippingLabel.list({
        filter: { assignedWelonTrustId: { eq: userData.id } },
        selectionSet: [
          'id',
          'userId',
          'assignedWelonTrustId',
          'trackingNumber',
          'labelUrl',
          'cost',
          'estimatedDelivery',
          'createdAt',
          'fromAddress',
          'toAddress',
          'status',
        ],
      });

      if (labels && labels.length > 0) {
        // Get sender information for each package
        const packagesWithSenders = await Promise.all(
          labels.map(async label => {
            let senderName = 'Unknown Sender';
            let senderEmail = '';

            try {
              // Get sender user data
              const { data: senderList } = await client.models.User.list({
                filter: { id: { eq: label.userId } },
                selectionSet: ['firstName', 'lastName', 'email'],
              });

              if (senderList && senderList.length > 0) {
                const sender = senderList[0];
                senderName = `${sender.firstName} ${sender.lastName}`;
                senderEmail = sender.email;
              }
            } catch (error) {
              console.error('Error fetching sender info:', error);
            }

            return {
              id: label.id,
              userId: label.userId,
              trackingNumber: label.trackingNumber,
              labelUrl: label.labelUrl,
              cost: JSON.parse(label.cost),
              estimatedDelivery: label.estimatedDelivery || undefined,
              createdAt: label.createdAt,
              fromAddress: label.fromAddress
                ? JSON.parse(label.fromAddress)
                : undefined,
              toAddress: label.toAddress
                ? JSON.parse(label.toAddress)
                : undefined,
              senderName,
              senderEmail,
            };
          })
        );

        setIncomingPackages(packagesWithSenders);
      }
    } catch (error) {
      console.error('Error loading incoming packages:', error);
      toast.error('Failed to load incoming packages');
    } finally {
      setIsLoading(false);
    }
  };

  const downloadLabelDirect = (labelUrl: string, trackingNumber: string) => {
    try {
      if (labelUrl.startsWith('data:')) {
        const link = window.document.createElement('a');
        link.href = labelUrl;
        const fileExtension = labelUrl.startsWith('data:application/pdf')
          ? 'pdf'
          : 'gif';
        link.download = `ups-label-${trackingNumber}.${fileExtension}`;
        window.document.body.appendChild(link);
        link.click();
        window.document.body.removeChild(link);
        toast.success('Label downloaded successfully');
      }
    } catch (error) {
      console.error('Error downloading label:', error);
      toast.error('Failed to download label');
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Package className='h-5 w-5' />
            Incoming Packages
          </CardTitle>
          <CardDescription>Loading packages sent to you...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className='flex items-center justify-center py-8'>
            <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary'></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          <Package className='h-5 w-5' />
          Incoming Packages ({incomingPackages.length})
        </CardTitle>
        <CardDescription>
          Packages sent to you by members for document processing
        </CardDescription>
      </CardHeader>
      <CardContent>
        {incomingPackages.length > 0 ? (
          <div className='space-y-4'>
            {incomingPackages.map(pkg => (
              <div key={pkg.id} className='border rounded-lg p-4'>
                <div className='flex items-center justify-between mb-3'>
                  <div className='flex-1'>
                    <div className='flex items-center gap-2 mb-1'>
                      <p className='font-mono text-sm font-medium'>
                        {pkg.trackingNumber}
                      </p>
                      <Badge variant='secondary' className='text-xs'>
                        Incoming Package
                      </Badge>
                    </div>
                    <div className='flex items-center gap-2 text-sm text-muted-foreground mb-2'>
                      <User className='h-4 w-4' />
                      <span className='font-medium'>From:</span>
                      <span>{pkg.senderName}</span>
                      {pkg.senderEmail && (
                        <span className='text-xs'>({pkg.senderEmail})</span>
                      )}
                    </div>
                    <div className='flex items-center gap-3 text-xs text-muted-foreground'>
                      <span>
                        Cost: {pkg.cost.currency} ${pkg.cost.amount}
                      </span>
                      {pkg.createdAt && (
                        <span>
                          Sent: {new Date(pkg.createdAt).toLocaleDateString()}
                        </span>
                      )}
                      {pkg.estimatedDelivery && (
                        <span>
                          Est. Delivery:{' '}
                          {new Date(pkg.estimatedDelivery).toLocaleDateString()}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                <div className='flex items-center gap-2'>
                  <LabelPreview
                    labelUrl={pkg.labelUrl}
                    trackingNumber={pkg.trackingNumber}
                    onDownload={() =>
                      downloadLabelDirect(pkg.labelUrl, pkg.trackingNumber)
                    }
                  />
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() =>
                      downloadLabelDirect(pkg.labelUrl, pkg.trackingNumber)
                    }
                  >
                    <Download className='h-4 w-4 mr-1' />
                    Download
                  </Button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className='text-center py-8'>
            <Package className='h-12 w-12 mx-auto text-muted-foreground mb-4' />
            <p className='text-muted-foreground'>No incoming packages yet</p>
            <p className='text-sm text-muted-foreground mt-1'>
              Packages sent to you by members will appear here
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
