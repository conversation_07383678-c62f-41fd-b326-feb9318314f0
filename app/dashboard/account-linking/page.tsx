'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import {
  ArrowLeft,
  HelpCircle,
  Link,
  Link2Off,
  Shield,
  User,
  UserPlus,
} from 'lucide-react';
import { LinkedAccountsList } from '@/components/account-linking/linked-accounts-list';
import { ContextualContent } from '@/components/education';
import { ContentIntegrationPoint } from '@/types/education';

export default function AccountLinkingPage() {
  const router = useRouter();

  return (
    <div className='container mx-auto p-4 py-8'>
      <div className='flex items-center mb-6'>
        <Button
          variant='ghost'
          size='sm'
          className='mr-4'
          onClick={() => router.push('/dashboard')}
        >
          <ArrowLeft className='h-4 w-4 mr-1' />
          Back to Dashboard
        </Button>
        <h1 className='text-3xl font-geologica font-semibold'>
          Account Linking
        </h1>
      </div>

      <div className='flex justify-between items-center mb-8'>
        <Card className='flex-1 mx-2'>
          <CardHeader className='pb-1'>
            <CardTitle className='flex items-center text-sm'>
              <User className='h-4 w-4 mr-1 text-blue-2157c' />
              Primary Link
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className='text-xs'>Full access for spouses or partners</p>
          </CardContent>
        </Card>

        <Card className='flex-1 mx-2'>
          <CardHeader className='pb-1'>
            <CardTitle className='flex items-center text-sm'>
              <Link className='h-4 w-4 mr-1 text-blue-2157c' />
              Secondary Link
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className='text-xs'>Limited access for family or friends</p>
          </CardContent>
        </Card>

        <Card className='flex-1 mx-2'>
          <CardHeader className='pb-1'>
            <CardTitle className='flex items-center text-sm'>
              <Shield className='h-4 w-4 mr-1 text-blue-2157c' />
              Emergency Link
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className='text-xs'>Access only in critical situations</p>
          </CardContent>
        </Card>
      </div>

      <div className='bg-background rounded-lg shadow-sm p-6 mb-8'>
        <LinkedAccountsList />
      </div>

      <div className='bg-blue-50 border border-blue-100 rounded-lg p-6'>
        <div className='flex items-start'>
          <HelpCircle className='h-6 w-6 text-blue-500 mr-4 mt-1 flex-shrink-0' />
          <div>
            <h3 className='text-lg font-medium text-blue-800 mb-2'>
              About Account Linking
            </h3>
            <p className='text-sm text-blue-700 mb-4'>
              Account linking allows you to share access to your Childfree
              Legacy account with trusted individuals. You can control exactly
              what they can see and do by setting specific permissions.
              <ContextualContent
                integrationPoint={ContentIntegrationPoint.EMERGENCY_FEATURES}
                triggerText='Learn more about account linking'
                displayType='popup'
              >
                <span className='text-blue-600 underline ml-1 cursor-pointer'>
                  Learn more
                </span>
              </ContextualContent>
            </p>
            <h4 className='text-md font-medium text-blue-800 mb-1'>
              How it works:
            </h4>
            <ol className='text-sm text-blue-700 list-decimal pl-5 space-y-1'>
              <li>You invite someone by entering their email address</li>
              <li>They receive an invitation to link accounts</li>
              <li>
                Once they accept, they can access your account based on the
                <ContextualContent
                  integrationPoint={ContentIntegrationPoint.EMERGENCY_FEATURES}
                  triggerText='What are permissions?'
                  displayType='tooltip'
                >
                  <span className='text-blue-600 underline cursor-pointer'>
                    {' '}
                    permissions{' '}
                  </span>
                </ContextualContent>
                you've set
              </li>
              <li>You can change permissions or revoke access at any time</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  );
}
