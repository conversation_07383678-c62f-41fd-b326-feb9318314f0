'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, CheckCircle2 } from 'lucide-react';
import { Headline, Subhead } from '../../../components/ui/brand/typography';
import { useSubscription } from '../../hooks/useSubscription';
import StripeSubscription from '@/components/billing/StripeSubscription';

export default function BillingDashboardPage() {
  const router = useRouter();
  const { subscription, loading: subscriptionLoading } = useSubscription();
  const [alert, setAlert] = useState<{
    type: 'success' | 'error';
    message: string;
  } | null>(null);

  // Auto-hide alerts after 5 seconds
  useEffect(() => {
    if (alert) {
      const timer = setTimeout(() => setAlert(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [alert]);

  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-4xl mx-auto'>
        <div className='mb-8'>
          <Headline className='mb-2'>Billing & Subscriptions</Headline>
          <Subhead className='text-muted-foreground'>
            Manage your subscription and billing information.
          </Subhead>
        </div>

        {alert && (
          <Alert
            className={`mb-6 ${
              alert.type === 'success'
                ? 'bg-green-50 text-green-800 border-green-200'
                : 'bg-destructive/10 text-destructive border-destructive/20'
            }`}
          >
            {alert.type === 'success' ? (
              <CheckCircle2 className='h-4 w-4' />
            ) : (
              <AlertCircle className='h-4 w-4' />
            )}
            <AlertTitle>
              {alert.type === 'success' ? 'Success' : 'Error'}
            </AlertTitle>
            <AlertDescription>{alert.message}</AlertDescription>
          </Alert>
        )}

        <div className='max-w-4xl mx-auto'>
          {subscriptionLoading ? (
            <Card>
              <CardContent className='p-6'>
                <div className='animate-pulse'>
                  <div className='h-4 bg-gray-200 rounded w-1/4 mb-2'></div>
                  <div className='h-6 bg-gray-200 rounded w-1/2'></div>
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className='p-6'>
                <StripeSubscription />
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
