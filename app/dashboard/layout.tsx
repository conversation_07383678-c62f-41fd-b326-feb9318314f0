'use client';

import { Suspense, useEffect } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { Sidebar } from '@/components/dashboard/sidebar';
import { AdminContainer } from '../../components/ui/container';
import { useRole } from '@/lib/roles/role-context';
import { useAuth } from '@/app/context/AuthContext';
import { OnboardingStep } from '@/app/utils/userOnboarding';
import routes from '@/utils/routes';
import { isAdmin, isWelonTrust } from '@/lib/utils/admin-utils';
import { AuthGuard } from '@/lib/auth/auth-guard';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const pathname = usePathname();
  const router = useRouter();
  const { userContext } = useRole();
  const { onboardingStatus, userRoles, loading: isLoadingUser } = useAuth();

  // Map role context to sidebar role format
  const getSidebarRole = ():
    | 'Member'
    | 'Administrator'
    | 'Welon Trust'
    | 'Professional' => {
    if (!userContext) return 'Member';

    switch (userContext.role) {
      case 'administrator':
        return 'Administrator';
      case 'welon_trust':
        return 'Welon Trust';
      case 'linked_account':
        return 'Member'; // Linked accounts use member-like interface but with limited nav
      default:
        return 'Member';
    }
  };

  const userRole = getSidebarRole();

  //TODO logic for fix prevent redirect on onboarding, need refactor
  if (isLoadingUser) return;

  // Check onboarding status and redirect if needed
  useEffect(() => {
    if (!userContext) return;

    const shouldRedirect = pathname === '/dashboard';
    if (!shouldRedirect) return;

    // If user is not admin/welontrust and hasn't completed onboarding, redirect to onboarding
    if (
      !isAdmin(userRoles) &&
      !isWelonTrust(userRoles) &&
      onboardingStatus !== OnboardingStep.COMPLETED
    ) {
      router.push(routes.onboarding);
      return;
    }

    // Redirect users to their appropriate dashboard based on role
    switch (userContext.role) {
      case 'administrator':
        router.push('/admin');
        break;
      case 'welon_trust':
        router.push('/emergency');
        break;
      case 'linked_account':
        router.push('/linked');
        break;
      // Member stays on /dashboard
      default:
        break;
    }
  }, [
    userContext,
    pathname,
    onboardingStatus,
    userRoles,
    router,
    isLoadingUser,
  ]);

  return (
    <Suspense>
    <AuthGuard>
      <div className='min-h-screen flex bg-background'>
        <Sidebar userRole={userRole} />
        <div className='flex-1'>
          <main>
            <AdminContainer>{children}</AdminContainer>
          </main>
        </div>
      </div>
    </AuthGuard>
    </Suspense>
  );
}
