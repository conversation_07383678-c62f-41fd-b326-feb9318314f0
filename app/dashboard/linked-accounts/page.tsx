'use client';

import React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Headline, Subhead } from '../../../components/ui/brand/typography';
import {
  Users,
  FileText,
  Shield,
  AlertTriangle,
  Settings,
  Eye,
  Edit,
  Info,
  ArrowRight,
} from 'lucide-react';
import { AccountSwitcher } from '@/components/account-linking/account-switcher';
import { LinkedAccountManager } from '@/components/account-linking/linked-account-manager';
import { useLinkedAccountContext } from '@/lib/contexts/linked-account-context';
import { useRouter } from 'next/navigation';

export default function LinkedAccountsPage() {
  const router = useRouter();
  const { activeAccount, canView, canEdit, isLoading } =
    useLinkedAccountContext();

  if (isLoading) {
    return (
      <div className='container mx-auto py-8 px-4'>
        <div className='max-w-6xl mx-auto'>
          <div className='flex items-center justify-center py-12'>
            <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-4'></div>
            <span>Loading linked accounts...</span>
          </div>
        </div>
      </div>
    );
  }

  const getAccessibleFeatures = () => {
    const features = [];

    if (canView('documents')) {
      features.push({
        name: 'Documents',
        icon: FileText,
        description: 'View and manage documents',
        canEdit: canEdit('documents'),
        path: '/documents',
      });
    }

    if (canView('emergency_contacts')) {
      features.push({
        name: 'Emergency Contacts',
        icon: Shield,
        description: 'Emergency contact management',
        canEdit: canEdit('emergency_contacts'),
        path: '/emergency',
      });
    }

    if (canView('dead_man_switch')) {
      features.push({
        name: "Dead Man's Switch",
        icon: AlertTriangle,
        description: 'Automated safety monitoring',
        canEdit: canEdit('dead_man_switch'),
        path: '/emergency/dead-mans-switch',
      });
    }

    if (canView('billing')) {
      features.push({
        name: 'Billing',
        icon: Settings,
        description: 'Billing and subscription management',
        canEdit: canEdit('billing'),
        path: '/dashboard/billing',
      });
    }

    return features;
  };

  const accessibleFeatures = getAccessibleFeatures();

  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-6xl mx-auto space-y-8'>
        {/* Header */}
        <div className='mb-8'>
          <Headline className='mb-2'>Linked Account Access</Headline>
          <Subhead className='text-muted-foreground'>
            Manage dual-account access and switch between your own and linked
            accounts
          </Subhead>
        </div>

        {/* Account Switcher */}
        <AccountSwitcher />

        {/* Current Account Info */}
        {activeAccount && (
          <Alert
            className={`border-2 ${
              activeAccount.mode === 'own'
                ? 'border-green-200 bg-green-50'
                : 'border-blue-200 bg-blue-50'
            }`}
          >
            <Info
              className={`h-4 w-4 ${
                activeAccount.mode === 'own'
                  ? 'text-green-600'
                  : 'text-blue-600'
              }`}
            />
            <AlertTitle
              className={
                activeAccount.mode === 'own'
                  ? 'text-green-800'
                  : 'text-blue-800'
              }
            >
              Currently Viewing: {activeAccount.user.name}
            </AlertTitle>
            <AlertDescription
              className={
                activeAccount.mode === 'own'
                  ? 'text-green-700'
                  : 'text-blue-700'
              }
            >
              {activeAccount.mode === 'own'
                ? 'You are viewing your own account with full access to all features.'
                : `You are viewing a linked account with ${activeAccount.permissions.length} permission(s). Some features may be restricted.`}
            </AlertDescription>
          </Alert>
        )}

        {/* Accessible Features */}
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Users className='h-5 w-5' />
              Available Features
            </CardTitle>
            <CardDescription>
              Features you can access with your current account and permissions
            </CardDescription>
          </CardHeader>
          <CardContent>
            {accessibleFeatures.length === 0 ? (
              <div className='text-center py-8 text-muted-foreground'>
                <Shield className='h-12 w-12 mx-auto mb-4 opacity-50' />
                <p className='text-lg font-medium mb-2'>
                  No accessible features
                </p>
                <p>
                  You don't have permission to access any features with the
                  current account.
                </p>
              </div>
            ) : (
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                {accessibleFeatures.map(feature => (
                  <div
                    key={feature.name}
                    className='flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors'
                  >
                    <div className='flex items-center gap-3'>
                      <div className='p-2 bg-blue-100 rounded-full'>
                        <feature.icon className='h-4 w-4 text-blue-600' />
                      </div>
                      <div>
                        <p className='font-medium'>{feature.name}</p>
                        <p className='text-sm text-muted-foreground'>
                          {feature.description}
                        </p>
                      </div>
                    </div>
                    <div className='flex items-center gap-2'>
                      <div className='flex flex-col gap-1'>
                        <Badge variant='outline' className='text-xs'>
                          <Eye className='h-3 w-3 mr-1' />
                          View
                        </Badge>
                        {feature.canEdit && (
                          <Badge variant='outline' className='text-xs'>
                            <Edit className='h-3 w-3 mr-1' />
                            Edit
                          </Badge>
                        )}
                      </div>
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() => router.push(feature.path)}
                      >
                        <ArrowRight className='h-4 w-4' />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Link Management (only for own account) */}
        {activeAccount?.mode === 'own' && (
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <Settings className='h-5 w-5' />
                Link Management
              </CardTitle>
              <CardDescription>
                Manage your account links and permissions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <LinkedAccountManager />
            </CardContent>
          </Card>
        )}

        {/* Restrictions Notice for Linked Accounts */}
        {activeAccount?.mode === 'linked' && (
          <Alert className='border-amber-200 bg-amber-50'>
            <AlertTriangle className='h-4 w-4 text-amber-600' />
            <AlertTitle className='text-amber-800'>
              Limited Access Account
            </AlertTitle>
            <AlertDescription className='text-amber-700'>
              You are currently viewing a linked account. Your access is limited
              to the permissions granted by the account owner. To manage links
              or access additional features, switch back to your own account.
            </AlertDescription>
          </Alert>
        )}

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks for the current account
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
              {canView('documents') && (
                <Button
                  variant='outline'
                  className='h-auto p-4 flex flex-col items-center gap-2'
                  onClick={() => router.push('/documents')}
                >
                  <FileText className='h-6 w-6' />
                  <span>View Documents</span>
                  {canEdit('documents') && (
                    <Badge variant='outline' className='text-xs'>
                      Can Edit
                    </Badge>
                  )}
                </Button>
              )}

              {canView('emergency_contacts') && (
                <Button
                  variant='outline'
                  className='h-auto p-4 flex flex-col items-center gap-2'
                  onClick={() => router.push('/emergency')}
                >
                  <Shield className='h-6 w-6' />
                  <span>Emergency Access</span>
                  {canEdit('emergency_contacts') && (
                    <Badge variant='outline' className='text-xs'>
                      Can Edit
                    </Badge>
                  )}
                </Button>
              )}

              {activeAccount?.mode === 'own' && (
                <Button
                  variant='outline'
                  className='h-auto p-4 flex flex-col items-center gap-2'
                  onClick={() => router.push('/dashboard/account-linking')}
                >
                  <Users className='h-6 w-6' />
                  <span>Manage Links</span>
                  <Badge variant='outline' className='text-xs'>
                    Full Access
                  </Badge>
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
