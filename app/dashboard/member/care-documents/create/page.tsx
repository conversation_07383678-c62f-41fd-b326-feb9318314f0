'use client';

import React from 'react';
import { CareDocumentForm } from '@/components/care-documents/care-document-form';

export default function CreateCareDocumentPage() {
  return (
    <div className='max-w-7xl mx-auto'>
      <div className='mb-8'>
        <h1 className='text-3xl font-bold text-black-6c mb-2'>
          Create Care Document
        </h1>
        <p className='text-[var(--custom-gray-medium)]'>
          Create a new document that requires regular updates, such as emergency
          contacts, pet care instructions, or digital asset management.
        </p>
      </div>

      <CareDocumentForm mode='create' />

      <div className='bg-blue-50 p-6 rounded-lg border border-blue-100 mt-8'>
        <h3 className='text-lg font-medium text-blue-800 mb-2'>
          About Care Documents
        </h3>
        <p className='text-blue-700 mb-4'>
          Care documents are designed to store information that needs regular
          updates to remain relevant and useful.
        </p>
        <ul className='list-disc list-inside text-blue-700 space-y-2'>
          <li>Choose from pre-built templates or create a custom document</li>
          <li>
            Set a review frequency that matches how often the information
            changes
          </li>
          <li>
            Receive reminders when it's time to review and update your document
          </li>
          <li>
            All documents are stored securely and can be accessed at any time
          </li>
        </ul>
      </div>
    </div>
  );
}
