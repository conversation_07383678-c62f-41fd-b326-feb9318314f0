'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Eye, RotateCw, Download } from 'lucide-react';

// Mock document version data
const mockDocumentVersions = [
  {
    id: 'v1.0',
    documentType: 'Last Will and Testament',
    createdAt: 'May 15, 2025',
    status: 'current',
    changes: 'Initial creation',
  },
  {
    id: 'v0.2',
    documentType: 'Last Will and Testament',
    createdAt: 'May 10, 2025',
    status: 'archived',
    changes: 'Updated beneficiary information',
  },
  {
    id: 'v0.1',
    documentType: 'Last Will and Testament',
    createdAt: 'May 5, 2025',
    status: 'archived',
    changes: 'Draft version',
  },
];

export default function DocumentHistoryPage() {
  const router = useRouter();
  const [selectedVersion, setSelectedVersion] = useState<string | null>(null);

  const handleViewDocument = (versionId: string) => {
    setSelectedVersion(versionId);
    // In a real implementation, this would navigate to a specific version preview
    // For now, we'll just navigate to the preview page
    router.push('/dashboard/member/documents/preview');
  };

  const handleRegenerateDocument = (versionId: string) => {
    // In a real implementation, this would call the backend to regenerate the document
    // For now, we'll just navigate to the preview page
    router.push('/dashboard/member/documents/preview');
  };

  const handleDownloadDocument = (versionId: string) => {
    // In a real implementation, this would download the document
    alert(`Downloading document version ${versionId}`);
  };

  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-5xl mx-auto'>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-black-6c mb-2'>
            Document History
          </h1>
          <p className='text-[var(--custom-gray-medium)]'>
            View and manage all versions of your legal documents. You can view,
            regenerate, or download any version.
          </p>
        </div>

        <Card className='mb-8'>
          <CardHeader>
            <CardTitle>Document Versions</CardTitle>
            <CardDescription>
              All versions of your documents are stored securely
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Version</TableHead>
                  <TableHead>Document Type</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Changes</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {mockDocumentVersions.map(version => (
                  <TableRow key={version.id}>
                    <TableCell className='font-medium'>{version.id}</TableCell>
                    <TableCell>{version.documentType}</TableCell>
                    <TableCell>{version.createdAt}</TableCell>
                    <TableCell>
                      {version.status === 'current' ? (
                        <Badge className='bg-green-2010c'>Current</Badge>
                      ) : (
                        <Badge variant='outline'>Archived</Badge>
                      )}
                    </TableCell>
                    <TableCell>{version.changes}</TableCell>
                    <TableCell>
                      <div className='flex space-x-2'>
                        <Button
                          variant='outline'
                          size='sm'
                          onClick={() => handleViewDocument(version.id)}
                        >
                          <Eye className='h-4 w-4 mr-1' />
                          View
                        </Button>

                        <Button
                          variant='outline'
                          size='sm'
                          onClick={() => handleRegenerateDocument(version.id)}
                        >
                          <RotateCw className='h-4 w-4 mr-1' />
                          Regenerate
                        </Button>

                        <Button
                          variant='outline'
                          size='sm'
                          onClick={() => handleDownloadDocument(version.id)}
                        >
                          <Download className='h-4 w-4 mr-1' />
                          Download
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        <div className='bg-blue-50 p-6 rounded-lg border border-blue-100'>
          <h3 className='text-lg font-medium text-blue-800 mb-2'>
            About Document Versioning
          </h3>
          <p className='text-blue-700 mb-4'>
            We maintain a complete history of all your document versions to
            ensure you always have access to previous versions.
          </p>
          <ul className='list-disc list-inside text-blue-700 space-y-2'>
            <li>
              Each time you update your information or regenerate a document, a
              new version is created
            </li>
            <li>The most recent version is always marked as "Current"</li>
            <li>
              You can view, download, or regenerate any version at any time
            </li>
            <li>All versions are stored securely and indefinitely</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
