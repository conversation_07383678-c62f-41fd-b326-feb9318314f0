import React from 'react';
import Link from 'next/link';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { FileX, ArrowLeft, Home } from 'lucide-react';

export default function NotFound() {
  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-2xl mx-auto'>
        <Card className='text-center'>
          <CardHeader>
            <div className='flex justify-center mb-4'>
              <FileX className='h-16 w-16 text-[var(--custom-gray-medium)]' />
            </div>
            <CardTitle className='text-2xl text-[var(--custom-gray-dark)]'>
              Template Not Found
            </CardTitle>
          </CardHeader>
          <CardContent className='space-y-4'>
            <p className='text-[var(--custom-gray-medium)]'>
              The template you're looking for doesn't exist or may have been
              removed.
            </p>
            <p className='text-sm text-[var(--custom-gray-medium)]'>
              Please check the URL or contact support if you believe this is an
              error.
            </p>

            <div className='flex flex-col sm:flex-row gap-3 justify-center pt-4'>
              <Button asChild variant='outline'>
                <Link href='/dashboard'>
                  <ArrowLeft className='h-4 w-4 mr-2' />
                  Back to Dashboard
                </Link>
              </Button>
              <Button asChild>
                <Link href='/member/interview'>
                  <Home className='h-4 w-4 mr-2' />
                  Start New Document
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
