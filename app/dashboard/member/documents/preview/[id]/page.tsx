import React from 'react';
import { DocumentPreviewClient } from './document-preview-client';
import {
  getTemplateServer,
  getUserAnswersServer,
} from '@/app/utils/templates-server';
import { notFound } from 'next/navigation';

export default async function DocumentPreviewPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id: templateId } = await params;

  try {
    // Fetch template data and user answers
    const [{ template, latestVersion }, answersMapping] = await Promise.all([
      getTemplateServer(templateId),
      getUserAnswersServer(),
    ]);

    console.log('===> ANSWERS MAPPING', answersMapping);

    if (!template) {
      console.error(`Template not found with ID: ${templateId}`);
      notFound();
    }

    // Get user's state from userAttributes (which will be fetched client-side)
    // For now, we'll pass the template state and let the client component handle the real user state
    const documentType = template.type || 'Legal Document';
    const documentState = template.templateState || 'Unknown State'; // This will be overridden client-side
    const templateName =
      template.templateName || `${documentState} ${documentType}`;
    const templateContent = latestVersion?.content || '';

    console.log('🔍 Server-side document info:', {
      templateId,
      documentType,
      documentState,
      templateName: templateName.substring(0, 50),
    });

    return (
      <DocumentPreviewClient
        templateId={templateId}
        templateVersion={latestVersion?.versionNumber || null}
        documentType={documentType}
        documentState={documentState}
        templateName={templateName}
        templateContent={templateContent}
        template={template}
        answersMapping={answersMapping}
      />
    );
  } catch (error) {
    console.error('Error fetching template:', error);

    // If template not found, show 404
    if (error instanceof Error && error.message.includes('not found')) {
      notFound();
    }

    // For other errors, you might want to show an error page
    // For now, we'll fall back to mock data with error indication
    return (
      <DocumentPreviewClient
        templateId={templateId}
        templateVersion={null}
        answersMapping={[]}
        documentType='Error Loading Template'
        documentState='Unknown'
        templateName='Template Load Error'
        templateContent=''
        template={null}
        error={
          error instanceof Error ? error.message : 'Unknown error occurred'
        }
      />
    );
  }
}
