'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import {
  FileText,
  Users,
  Save,
  ArrowLeft,
  AlertTriangle,
  CheckCircle,
  Plus,
  X,
} from 'lucide-react';

// Types for document creation
interface LinkedAccount {
  id: string;
  name: string;
  email: string;
}

interface DocumentFormData {
  title: string;
  description: string;
  type: string;
  coOwners: string[];
  // Joint Trust specific fields
  trustName?: string;
  trustees?: string[];
  beneficiaries?: string[];
  assets?: string[];
  // Shared Property specific fields
  propertyAddress?: string;
  propertyType?: string;
  ownershipPercentage?: { [userId: string]: number };
  // Joint Will specific fields
  executor?: string;
  guardians?: string[];
}

function CreateSharedDocumentContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const documentType = searchParams.get('type') || '';

  const [linkedAccounts, setLinkedAccounts] = useState<LinkedAccount[]>([]);
  const [formData, setFormData] = useState<DocumentFormData>({
    title: '',
    description: '',
    type: documentType,
    coOwners: [],
    trustees: [],
    beneficiaries: [],
    assets: [],
    guardians: [],
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [newTrustee, setNewTrustee] = useState('');
  const [newBeneficiary, setNewBeneficiary] = useState('');
  const [newAsset, setNewAsset] = useState('');
  const [newGuardian, setNewGuardian] = useState('');

  // Load linked accounts
  useEffect(() => {
    // Mock linked accounts data
    setLinkedAccounts([
      {
        id: 'user2',
        name: 'Sarah Johnson',
        email: '<EMAIL>',
      },
    ]);

    // Pre-select all linked accounts as co-owners
    setFormData(prev => ({
      ...prev,
      coOwners: ['user2'],
    }));
  }, []);

  // Set default title based on document type
  useEffect(() => {
    if (documentType && !formData.title) {
      const defaultTitles = {
        joint_trust: 'Joint Family Trust',
        joint_will: 'Joint Last Will and Testament',
        shared_property: 'Shared Property Documentation',
        joint_power_of_attorney: 'Joint Power of Attorney',
      };

      setFormData(prev => ({
        ...prev,
        title: defaultTitles[documentType as keyof typeof defaultTitles] || '',
        type: documentType,
      }));
    }
  }, [documentType, formData.title]);

  const getDocumentTypeLabel = (type: string) => {
    switch (type) {
      case 'joint_trust':
        return 'Joint Trust';
      case 'joint_will':
        return 'Joint Will';
      case 'shared_property':
        return 'Shared Property';
      case 'joint_power_of_attorney':
        return 'Joint Power of Attorney';
      default:
        return type;
    }
  };

  const handleInputChange = (field: keyof DocumentFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleAddItem = (
    field: keyof DocumentFormData,
    value: string,
    setter: (value: string) => void
  ) => {
    if (!value.trim()) return;

    const currentArray = (formData[field] as string[]) || [];
    setFormData(prev => ({
      ...prev,
      [field]: [...currentArray, value.trim()],
    }));
    setter('');
  };

  const handleRemoveItem = (field: keyof DocumentFormData, index: number) => {
    const currentArray = (formData[field] as string[]) || [];
    setFormData(prev => ({
      ...prev,
      [field]: currentArray.filter((_, i) => i !== index),
    }));
  };

  const handleSaveDraft = async () => {
    if (!formData.title.trim()) {
      setError('Please enter a document title');
      return;
    }

    if (formData.coOwners.length === 0) {
      setError('Please select at least one co-owner');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Simulate API call to save draft
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Navigate back to shared documents with success message
      router.push('/dashboard/member/shared-documents?created=true');
    } catch (err) {
      setError('Failed to save document. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const renderDocumentSpecificFields = () => {
    switch (formData.type) {
      case 'joint_trust':
        return (
          <div className='space-y-6'>
            <div>
              <Label htmlFor='trustName'>Trust Name</Label>
              <Input
                id='trustName'
                value={formData.trustName || ''}
                onChange={e => handleInputChange('trustName', e.target.value)}
                placeholder='The Johnson Family Revocable Living Trust'
              />
            </div>

            <div>
              <Label>Trustees</Label>
              <div className='space-y-2'>
                <div className='flex gap-2'>
                  <Input
                    value={newTrustee}
                    onChange={e => setNewTrustee(e.target.value)}
                    placeholder='Add trustee name'
                    onKeyPress={e =>
                      e.key === 'Enter' &&
                      handleAddItem('trustees', newTrustee, setNewTrustee)
                    }
                  />
                  <Button
                    type='button'
                    variant='outline'
                    onClick={() =>
                      handleAddItem('trustees', newTrustee, setNewTrustee)
                    }
                  >
                    <Plus className='w-4 h-4' />
                  </Button>
                </div>
                <div className='flex flex-wrap gap-2'>
                  {formData.trustees?.map((trustee, index) => (
                    <Badge
                      key={index}
                      variant='secondary'
                      className='flex items-center gap-1'
                    >
                      {trustee}
                      <X
                        className='w-3 h-3 cursor-pointer'
                        onClick={() => handleRemoveItem('trustees', index)}
                      />
                    </Badge>
                  ))}
                </div>
              </div>
            </div>

            <div>
              <Label>Beneficiaries</Label>
              <div className='space-y-2'>
                <div className='flex gap-2'>
                  <Input
                    value={newBeneficiary}
                    onChange={e => setNewBeneficiary(e.target.value)}
                    placeholder='Add beneficiary name'
                    onKeyPress={e =>
                      e.key === 'Enter' &&
                      handleAddItem(
                        'beneficiaries',
                        newBeneficiary,
                        setNewBeneficiary
                      )
                    }
                  />
                  <Button
                    type='button'
                    variant='outline'
                    onClick={() =>
                      handleAddItem(
                        'beneficiaries',
                        newBeneficiary,
                        setNewBeneficiary
                      )
                    }
                  >
                    <Plus className='w-4 h-4' />
                  </Button>
                </div>
                <div className='flex flex-wrap gap-2'>
                  {formData.beneficiaries?.map((beneficiary, index) => (
                    <Badge
                      key={index}
                      variant='secondary'
                      className='flex items-center gap-1'
                    >
                      {beneficiary}
                      <X
                        className='w-3 h-3 cursor-pointer'
                        onClick={() => handleRemoveItem('beneficiaries', index)}
                      />
                    </Badge>
                  ))}
                </div>
              </div>
            </div>

            <div>
              <Label>Assets to Include</Label>
              <div className='space-y-2'>
                <div className='flex gap-2'>
                  <Input
                    value={newAsset}
                    onChange={e => setNewAsset(e.target.value)}
                    placeholder='Add asset description'
                    onKeyPress={e =>
                      e.key === 'Enter' &&
                      handleAddItem('assets', newAsset, setNewAsset)
                    }
                  />
                  <Button
                    type='button'
                    variant='outline'
                    onClick={() =>
                      handleAddItem('assets', newAsset, setNewAsset)
                    }
                  >
                    <Plus className='w-4 h-4' />
                  </Button>
                </div>
                <div className='flex flex-wrap gap-2'>
                  {formData.assets?.map((asset, index) => (
                    <Badge
                      key={index}
                      variant='secondary'
                      className='flex items-center gap-1'
                    >
                      {asset}
                      <X
                        className='w-3 h-3 cursor-pointer'
                        onClick={() => handleRemoveItem('assets', index)}
                      />
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </div>
        );

      case 'shared_property':
        return (
          <div className='space-y-6'>
            <div>
              <Label htmlFor='propertyAddress'>Property Address</Label>
              <Input
                id='propertyAddress'
                value={formData.propertyAddress || ''}
                onChange={e =>
                  handleInputChange('propertyAddress', e.target.value)
                }
                placeholder='123 Main Street, Springfield, IL 62701'
              />
            </div>

            <div>
              <Label htmlFor='propertyType'>Property Type</Label>
              <Select
                value={formData.propertyType || ''}
                onValueChange={value =>
                  handleInputChange('propertyType', value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder='Select property type' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='primary_residence'>
                    Primary Residence
                  </SelectItem>
                  <SelectItem value='vacation_home'>Vacation Home</SelectItem>
                  <SelectItem value='investment_property'>
                    Investment Property
                  </SelectItem>
                  <SelectItem value='commercial_property'>
                    Commercial Property
                  </SelectItem>
                  <SelectItem value='land'>Land</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        );

      case 'joint_will':
        return (
          <div className='space-y-6'>
            <div>
              <Label htmlFor='executor'>Executor</Label>
              <Input
                id='executor'
                value={formData.executor || ''}
                onChange={e => handleInputChange('executor', e.target.value)}
                placeholder='Name of executor'
              />
            </div>

            <div>
              <Label>Guardians (if applicable)</Label>
              <div className='space-y-2'>
                <div className='flex gap-2'>
                  <Input
                    value={newGuardian}
                    onChange={e => setNewGuardian(e.target.value)}
                    placeholder='Add guardian name'
                    onKeyPress={e =>
                      e.key === 'Enter' &&
                      handleAddItem('guardians', newGuardian, setNewGuardian)
                    }
                  />
                  <Button
                    type='button'
                    variant='outline'
                    onClick={() =>
                      handleAddItem('guardians', newGuardian, setNewGuardian)
                    }
                  >
                    <Plus className='w-4 h-4' />
                  </Button>
                </div>
                <div className='flex flex-wrap gap-2'>
                  {formData.guardians?.map((guardian, index) => (
                    <Badge
                      key={index}
                      variant='secondary'
                      className='flex items-center gap-1'
                    >
                      {guardian}
                      <X
                        className='w-3 h-3 cursor-pointer'
                        onClick={() => handleRemoveItem('guardians', index)}
                      />
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <>
      <div className='space-y-6'>
        <div className='flex items-center gap-4'>
          <Button variant='outline' onClick={() => router.back()}>
            <ArrowLeft className='w-4 h-4 mr-2' />
            Back
          </Button>
          <div>
            <h1 className='text-3xl font-geologica font-semibold mb-2'>
              Create {getDocumentTypeLabel(formData.type)}
            </h1>
            <p className='text-muted-foreground'>
              Collaborate with your linked accounts to create shared estate
              planning documents.
            </p>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <Alert variant='destructive'>
            <AlertTriangle className='h-4 w-4' />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className='grid grid-cols-1 lg:grid-cols-3 gap-6'>
          {/* Main Form */}
          <div className='lg:col-span-2 space-y-6'>
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <FileText className='h-5 w-5' />
                  Document Details
                </CardTitle>
                <CardDescription>
                  Basic information about your shared document
                </CardDescription>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div>
                  <Label htmlFor='title'>Document Title *</Label>
                  <Input
                    id='title'
                    value={formData.title}
                    onChange={e => handleInputChange('title', e.target.value)}
                    placeholder='Enter document title'
                  />
                </div>

                <div>
                  <Label htmlFor='description'>Description</Label>
                  <Textarea
                    id='description'
                    value={formData.description}
                    onChange={e =>
                      handleInputChange('description', e.target.value)
                    }
                    placeholder='Describe the purpose and scope of this document'
                    rows={3}
                  />
                </div>

                <div>
                  <Label>Document Type</Label>
                  <div className='mt-2'>
                    <Badge variant='outline' className='text-sm'>
                      {getDocumentTypeLabel(formData.type)}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Document-specific fields */}
            {formData.type && (
              <Card>
                <CardHeader>
                  <CardTitle>
                    {getDocumentTypeLabel(formData.type)} Details
                  </CardTitle>
                  <CardDescription>
                    Specific information for this type of document
                  </CardDescription>
                </CardHeader>
                <CardContent>{renderDocumentSpecificFields()}</CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className='space-y-6'>
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <Users className='h-5 w-5' />
                  Co-owners
                </CardTitle>
                <CardDescription>
                  People who will have access to this document
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className='space-y-3'>
                  <div className='flex items-center gap-3 p-3 bg-blue-50 rounded-lg'>
                    <div className='w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium'>
                      You
                    </div>
                    <div>
                      <p className='font-medium'>You (Document Creator)</p>
                      <p className='text-sm text-muted-foreground'>
                        Full access
                      </p>
                    </div>
                  </div>

                  {linkedAccounts.map(account => (
                    <div
                      key={account.id}
                      className='flex items-center gap-3 p-3 border rounded-lg'
                    >
                      <div className='w-8 h-8 bg-gray-500 rounded-full flex items-center justify-center text-white text-sm font-medium'>
                        {account.name.charAt(0)}
                      </div>
                      <div className='flex-1'>
                        <p className='font-medium'>{account.name}</p>
                        <p className='text-sm text-muted-foreground'>
                          {account.email}
                        </p>
                      </div>
                      <CheckCircle className='w-5 h-5 text-green-500' />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Next Steps</CardTitle>
              </CardHeader>
              <CardContent>
                <div className='space-y-3 text-sm'>
                  <div className='flex items-start gap-2'>
                    <div className='w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-medium mt-0.5'>
                      1
                    </div>
                    <div>
                      <p className='font-medium'>Save as Draft</p>
                      <p className='text-muted-foreground'>
                        Create the initial document
                      </p>
                    </div>
                  </div>
                  <div className='flex items-start gap-2'>
                    <div className='w-5 h-5 bg-gray-300 rounded-full flex items-center justify-center text-white text-xs font-medium mt-0.5'>
                      2
                    </div>
                    <div>
                      <p className='font-medium'>Collaborative Review</p>
                      <p className='text-muted-foreground'>
                        All co-owners review and provide input
                      </p>
                    </div>
                  </div>
                  <div className='flex items-start gap-2'>
                    <div className='w-5 h-5 bg-gray-300 rounded-full flex items-center justify-center text-white text-xs font-medium mt-0.5'>
                      3
                    </div>
                    <div>
                      <p className='font-medium'>Final Approval</p>
                      <p className='text-muted-foreground'>
                        All parties approve the final version
                      </p>
                    </div>
                  </div>
                  <div className='flex items-start gap-2'>
                    <div className='w-5 h-5 bg-gray-300 rounded-full flex items-center justify-center text-white text-xs font-medium mt-0.5'>
                      4
                    </div>
                    <div>
                      <p className='font-medium'>Execution</p>
                      <p className='text-muted-foreground'>
                        Sign and finalize the document
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Action Buttons */}
        <div className='flex justify-end gap-3 pt-6 border-t'>
          <Button variant='outline' onClick={() => router.back()}>
            Cancel
          </Button>
          <Button onClick={handleSaveDraft} disabled={isLoading}>
            {isLoading ? (
              'Saving...'
            ) : (
              <>
                <Save className='w-4 h-4 mr-2' />
                Save as Draft
              </>
            )}
          </Button>
        </div>
      </div>
    </>
  );
}

export default function CreateSharedDocumentPage() {
  return (
    <Suspense
      fallback={
        <div className='flex items-center justify-center h-64'>Loading...</div>
      }
    >
      <CreateSharedDocumentContent />
    </Suspense>
  );
}
