'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  FileText,
  Users,
  Plus,
  Eye,
  Edit,
  CheckCircle,
  Clock,
  AlertTriangle,
  Download,
  Share,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { UserDocumentsList } from '@/app/components/user-documents/user-documents-list';

// Types for shared documents
interface SharedDocument {
  id: string;
  title: string;
  type:
    | 'joint_trust'
    | 'joint_will'
    | 'shared_property'
    | 'joint_power_of_attorney';
  status: 'draft' | 'review' | 'approved' | 'executed';
  createdAt: string;
  lastUpdated: string;
  coOwners: {
    id: string;
    name: string;
    email: string;
    hasReviewed: boolean;
    hasApproved: boolean;
  }[];
  description: string;
  nextAction?: string;
}

interface LinkedAccount {
  id: string;
  name: string;
  email: string;
}

export default function SharedDocumentsPage() {
  const router = useRouter();
  const [sharedDocuments, setSharedDocuments] = useState<SharedDocument[]>([]);
  const [linkedAccounts, setLinkedAccounts] = useState<LinkedAccount[]>([]);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [selectedDocumentType, setSelectedDocumentType] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Mock data - in real implementation, this would come from API
  useEffect(() => {
    setSharedDocuments([
      {
        id: '1',
        title: 'Johnson Family Joint Trust',
        type: 'joint_trust',
        status: 'review',
        createdAt: '2024-01-15',
        lastUpdated: '2024-01-20',
        description:
          'Revocable living trust for joint assets and estate planning',
        nextAction: "Waiting for Sarah's review",
        coOwners: [
          {
            id: 'user1',
            name: 'John Johnson',
            email: '<EMAIL>',
            hasReviewed: true,
            hasApproved: false,
          },
          {
            id: 'user2',
            name: 'Sarah Johnson',
            email: '<EMAIL>',
            hasReviewed: false,
            hasApproved: false,
          },
        ],
      },
      {
        id: '2',
        title: 'Joint Property Deed - Main Residence',
        type: 'shared_property',
        status: 'executed',
        createdAt: '2024-01-10',
        lastUpdated: '2024-01-18',
        description: 'Joint ownership documentation for primary residence',
        coOwners: [
          {
            id: 'user1',
            name: 'John Johnson',
            email: '<EMAIL>',
            hasReviewed: true,
            hasApproved: true,
          },
          {
            id: 'user2',
            name: 'Sarah Johnson',
            email: '<EMAIL>',
            hasReviewed: true,
            hasApproved: true,
          },
        ],
      },
    ]);

    setLinkedAccounts([
      {
        id: 'user2',
        name: 'Sarah Johnson',
        email: '<EMAIL>',
      },
    ]);
  }, []);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'draft':
        return (
          <Badge variant='secondary'>
            <Edit className='w-3 h-3 mr-1' />
            Draft
          </Badge>
        );
      case 'review':
        return (
          <Badge variant='default' className='bg-yellow-100 text-yellow-800'>
            <Clock className='w-3 h-3 mr-1' />
            In Review
          </Badge>
        );
      case 'approved':
        return (
          <Badge variant='default' className='bg-blue-100 text-blue-800'>
            <CheckCircle className='w-3 h-3 mr-1' />
            Approved
          </Badge>
        );
      case 'executed':
        return (
          <Badge variant='default' className='bg-green-100 text-green-800'>
            <CheckCircle className='w-3 h-3 mr-1' />
            Executed
          </Badge>
        );
      default:
        return <Badge variant='secondary'>{status}</Badge>;
    }
  };

  const getDocumentTypeLabel = (type: string) => {
    switch (type) {
      case 'joint_trust':
        return 'Joint Trust';
      case 'joint_will':
        return 'Joint Will';
      case 'shared_property':
        return 'Shared Property';
      case 'joint_power_of_attorney':
        return 'Joint Power of Attorney';
      default:
        return type;
    }
  };

  const handleCreateDocument = async () => {
    if (!selectedDocumentType) {
      setError('Please select a document type');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Navigate to document creation page
      router.push(
        `/dashboard/member/shared-documents/create?type=${selectedDocumentType}`
      );
    } catch (err) {
      setError('Failed to create document. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewDocument = (documentId: string) => {
    router.push(`/dashboard/member/shared-documents/${documentId}`);
  };

  const handleEditDocument = (documentId: string) => {
    router.push(`/dashboard/member/shared-documents/${documentId}/edit`);
  };

  const getReviewStatus = (document: SharedDocument) => {
    const totalOwners = document.coOwners.length;
    const reviewedCount = document.coOwners.filter(
      owner => owner.hasReviewed
    ).length;
    const approvedCount = document.coOwners.filter(
      owner => owner.hasApproved
    ).length;

    return {
      reviewed: `${reviewedCount}/${totalOwners}`,
      approved: `${approvedCount}/${totalOwners}`,
      allReviewed: reviewedCount === totalOwners,
      allApproved: approvedCount === totalOwners,
    };
  };

  const draftDocuments = sharedDocuments.filter(doc => doc.status === 'draft');
  const activeDocuments = sharedDocuments.filter(doc =>
    ['review', 'approved'].includes(doc.status)
  );
  const executedDocuments = sharedDocuments.filter(
    doc => doc.status === 'executed'
  );

  return (
    <>
      <div className='space-y-6'>
        <div className='flex justify-between items-start'>
          <div>
            <h1 className='text-3xl font-geologica font-semibold mb-2'>
              Shared Documents
            </h1>
            <p className='text-muted-foreground'>
              Collaborate on estate planning documents with your linked
              accounts.
            </p>
          </div>

          <Dialog
            open={isCreateDialogOpen}
            onOpenChange={setIsCreateDialogOpen}
          >
            <DialogTrigger asChild>
              <Button>
                <Plus className='w-4 h-4 mr-2' />
                Create Shared Document
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create Shared Document</DialogTitle>
                <DialogDescription>
                  Select the type of document you want to create with your
                  linked accounts.
                </DialogDescription>
              </DialogHeader>
              <div className='space-y-4'>
                <div>
                  <label className='text-sm font-medium'>Document Type</label>
                  <Select
                    value={selectedDocumentType}
                    onValueChange={setSelectedDocumentType}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select document type' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='joint_trust'>Joint Trust</SelectItem>
                      <SelectItem value='joint_will'>Joint Will</SelectItem>
                      <SelectItem value='shared_property'>
                        Shared Property Documentation
                      </SelectItem>
                      <SelectItem value='joint_power_of_attorney'>
                        Joint Power of Attorney
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <DialogFooter>
                <Button
                  variant='outline'
                  onClick={() => setIsCreateDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button onClick={handleCreateDocument} disabled={isLoading}>
                  {isLoading ? 'Creating...' : 'Create Document'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        {/* Alerts */}
        {error && (
          <Alert variant='destructive'>
            <AlertTriangle className='h-4 w-4' />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert>
            <CheckCircle className='h-4 w-4' />
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        {/* No linked accounts warning */}
        {linkedAccounts.length === 0 && (
          <Alert>
            <AlertTriangle className='h-4 w-4' />
            <AlertDescription>
              You need to link an account before creating shared documents.
              <Button
                variant='link'
                className='p-0 h-auto ml-1'
                onClick={() =>
                  router.push('/dashboard/member/settings/linked-accounts')
                }
              >
                Link an account now
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Documents Tabs */}
        <Tabs defaultValue='welon-trust' className='space-y-4'>
          <TabsList>
            <TabsTrigger value='welon-trust'>From Welon Trust</TabsTrigger>
            <TabsTrigger value='active'>
              Active ({activeDocuments.length})
            </TabsTrigger>
            <TabsTrigger value='drafts'>
              Drafts ({draftDocuments.length})
            </TabsTrigger>
            <TabsTrigger value='executed'>
              Executed ({executedDocuments.length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value='welon-trust' className='space-y-4'>
            <UserDocumentsList />
          </TabsContent>

          <TabsContent value='active' className='space-y-4'>
            {activeDocuments.length === 0 ? (
              <Card>
                <CardContent className='text-center py-8'>
                  <FileText className='h-12 w-12 mx-auto mb-4 opacity-50' />
                  <p className='text-muted-foreground'>
                    No active shared documents
                  </p>
                </CardContent>
              </Card>
            ) : (
              activeDocuments.map(document => {
                const reviewStatus = getReviewStatus(document);
                return (
                  <Card key={document.id}>
                    <CardHeader>
                      <div className='flex justify-between items-start'>
                        <div>
                          <CardTitle className='flex items-center gap-2'>
                            <FileText className='h-5 w-5' />
                            {document.title}
                          </CardTitle>
                          <CardDescription>
                            {document.description}
                          </CardDescription>
                        </div>
                        {getStatusBadge(document.status)}
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className='space-y-4'>
                        <div className='flex justify-between text-sm'>
                          <span>
                            Type: {getDocumentTypeLabel(document.type)}
                          </span>
                          <span>Last updated: {document.lastUpdated}</span>
                        </div>

                        {/* Co-owners and review status */}
                        <div>
                          <p className='text-sm font-medium mb-2'>
                            Co-owners & Review Status:
                          </p>
                          <div className='space-y-2'>
                            {document.coOwners.map(owner => (
                              <div
                                key={owner.id}
                                className='flex items-center justify-between text-sm'
                              >
                                <span>{owner.name}</span>
                                <div className='flex gap-2'>
                                  {owner.hasReviewed ? (
                                    <Badge
                                      variant='outline'
                                      className='text-green-600'
                                    >
                                      Reviewed
                                    </Badge>
                                  ) : (
                                    <Badge
                                      variant='outline'
                                      className='text-yellow-600'
                                    >
                                      Pending Review
                                    </Badge>
                                  )}
                                  {owner.hasApproved && (
                                    <Badge
                                      variant='outline'
                                      className='text-blue-600'
                                    >
                                      Approved
                                    </Badge>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>

                        {document.nextAction && (
                          <Alert>
                            <Clock className='h-4 w-4' />
                            <AlertDescription>
                              {document.nextAction}
                            </AlertDescription>
                          </Alert>
                        )}

                        <div className='flex gap-2'>
                          <Button
                            variant='outline'
                            size='sm'
                            onClick={() => handleViewDocument(document.id)}
                          >
                            <Eye className='w-4 h-4 mr-2' />
                            View
                          </Button>
                          {document.status === 'draft' && (
                            <Button
                              variant='outline'
                              size='sm'
                              onClick={() => handleEditDocument(document.id)}
                            >
                              <Edit className='w-4 h-4 mr-2' />
                              Edit
                            </Button>
                          )}
                          <Button variant='outline' size='sm'>
                            <Share className='w-4 h-4 mr-2' />
                            Share
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })
            )}
          </TabsContent>

          <TabsContent value='drafts' className='space-y-4'>
            {draftDocuments.length === 0 ? (
              <Card>
                <CardContent className='text-center py-8'>
                  <Edit className='h-12 w-12 mx-auto mb-4 opacity-50' />
                  <p className='text-muted-foreground'>No draft documents</p>
                </CardContent>
              </Card>
            ) : (
              draftDocuments.map(document => (
                <Card key={document.id}>
                  <CardHeader>
                    <div className='flex justify-between items-start'>
                      <div>
                        <CardTitle className='flex items-center gap-2'>
                          <FileText className='h-5 w-5' />
                          {document.title}
                        </CardTitle>
                        <CardDescription>
                          {document.description}
                        </CardDescription>
                      </div>
                      {getStatusBadge(document.status)}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className='flex justify-between items-center'>
                      <div className='text-sm text-muted-foreground'>
                        Created: {document.createdAt} • Type:{' '}
                        {getDocumentTypeLabel(document.type)}
                      </div>
                      <div className='flex gap-2'>
                        <Button
                          variant='outline'
                          size='sm'
                          onClick={() => handleEditDocument(document.id)}
                        >
                          <Edit className='w-4 h-4 mr-2' />
                          Continue Editing
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </TabsContent>

          <TabsContent value='executed' className='space-y-4'>
            {executedDocuments.length === 0 ? (
              <Card>
                <CardContent className='text-center py-8'>
                  <CheckCircle className='h-12 w-12 mx-auto mb-4 opacity-50' />
                  <p className='text-muted-foreground'>No executed documents</p>
                </CardContent>
              </Card>
            ) : (
              executedDocuments.map(document => (
                <Card key={document.id}>
                  <CardHeader>
                    <div className='flex justify-between items-start'>
                      <div>
                        <CardTitle className='flex items-center gap-2'>
                          <FileText className='h-5 w-5' />
                          {document.title}
                        </CardTitle>
                        <CardDescription>
                          {document.description}
                        </CardDescription>
                      </div>
                      {getStatusBadge(document.status)}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className='flex justify-between items-center'>
                      <div className='text-sm text-muted-foreground'>
                        Executed: {document.lastUpdated} • Type:{' '}
                        {getDocumentTypeLabel(document.type)}
                      </div>
                      <div className='flex gap-2'>
                        <Button
                          variant='outline'
                          size='sm'
                          onClick={() => handleViewDocument(document.id)}
                        >
                          <Eye className='w-4 h-4 mr-2' />
                          View
                        </Button>
                        <Button variant='outline' size='sm'>
                          <Download className='w-4 h-4 mr-2' />
                          Download
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
}
