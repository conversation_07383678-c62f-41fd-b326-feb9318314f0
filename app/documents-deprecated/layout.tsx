'use client';

import React from 'react';
import { Sidebar } from '@/components/dashboard/sidebar';
import { AdminContainer } from '../../components/ui/container';
import {
  UserProvider,
  useUserContext,
} from '@/components/welon-trust/user-context';
import { UserSelector } from '@/components/welon-trust/user-selector';
import { Card, CardContent } from '@/components/ui/card';
import { useRole } from '@/lib/roles/role-context';

interface DocumentsLayoutProps {
  children: React.ReactNode;
}

function DocumentsLayoutContent({ children }: { children: React.ReactNode }) {
  const { userContext } = useRole();

  // Determine if this is a Welon Trust user who needs user selection
  const isWelonTrust =
    userContext?.role === 'welon_trust' ||
    userContext?.displayName?.includes('Welon');

  // Map role context to sidebar role format
  const getSidebarRole = ():
    | 'Member'
    | 'Administrator'
    | 'Welon Trust'
    | 'Professional' => {
    if (!userContext) return 'Member';

    switch (userContext.role) {
      case 'administrator':
        return 'Administrator';
      case 'welon_trust':
        return 'Welon Trust';
      case 'linked_account':
        return 'Member';
      default:
        return 'Member';
    }
  };

  const userRole = getSidebarRole();

  if (isWelonTrust) {
    // For Welon Trust users, show user selector
    return (
      <WelonTrustDocumentsLayout userRole={userRole}>
        {children}
      </WelonTrustDocumentsLayout>
    );
  } else {
    // For Members, show simple layout without user selector
    return (
      <MemberDocumentsLayout userRole={userRole}>
        {children}
      </MemberDocumentsLayout>
    );
  }
}

function WelonTrustDocumentsLayout({
  children,
  userRole,
}: {
  children: React.ReactNode;
  userRole: 'Member' | 'Administrator' | 'Welon Trust' | 'Professional';
}) {
  const { selectedUser, setSelectedUser, availableUsers } = useUserContext();

  return (
    <div className='min-h-screen flex bg-background'>
      <Sidebar userRole={userRole} />
      <div className='flex-1'>
        <main>
          <AdminContainer>
            {/* User Selection Header for Welon Trust */}
            <Card className='mb-6 border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50'>
              <CardContent className='p-6'>
                <div className='max-w-md'>
                  <UserSelector
                    selectedUser={selectedUser}
                    onUserSelect={setSelectedUser}
                    users={availableUsers}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Main Content */}
            {children}
          </AdminContainer>
        </main>
      </div>
    </div>
  );
}

function MemberDocumentsLayout({
  children,
  userRole,
}: {
  children: React.ReactNode;
  userRole: 'Member' | 'Administrator' | 'Welon Trust' | 'Professional';
}) {
  return (
    <div className='min-h-screen flex bg-background'>
      <Sidebar userRole={userRole} />
      <div className='flex-1'>
        <main>
          <AdminContainer>
            {/* Main Content - no user selector for members */}
            {children}
          </AdminContainer>
        </main>
      </div>
    </div>
  );
}

export default function DocumentsLayout({ children }: DocumentsLayoutProps) {
  const { userContext } = useRole();

  // Determine if this is a Welon Trust user who needs UserProvider
  const isWelonTrust =
    userContext?.role === 'welon_trust' ||
    userContext?.displayName?.includes('Welon');

  if (isWelonTrust) {
    // Welon Trust users need UserProvider for user selection
    return (
      <UserProvider>
        <DocumentsLayoutContent>{children}</DocumentsLayoutContent>
      </UserProvider>
    );
  } else {
    // Members don't need UserProvider
    return <DocumentsLayoutContent>{children}</DocumentsLayoutContent>;
  }
}
