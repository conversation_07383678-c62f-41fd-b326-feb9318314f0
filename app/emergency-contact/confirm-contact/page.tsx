'use client';

import { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, XCircle, Loader2 } from 'lucide-react';

function ConfirmContactContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [loading, setLoading] = useState(true);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const token = searchParams.get('token');
  const email = searchParams.get('email');
  const successParam = searchParams.get('success');
  const errorParam = searchParams.get('error');

  useEffect(() => {
    // Check if we have success/error from query params (from API route redirect)
    if (successParam === 'true') {
      setSuccess(true);
      setLoading(false);
      return;
    }

    if (errorParam) {
      setError(decodeURIComponent(errorParam));
      setLoading(false);
      return;
    }

    // Otherwise, verify the token directly
    const verifyContact = async () => {
      if (!token || !email) {
        setError('Invalid verification link. Missing token or email.');
        setLoading(false);
        return;
      }

      try {
        const client = generateClient<Schema>({
          authMode: 'iam',
        });

        // First, verify the token
        const verifyResult = await client.mutations.verifyEmailToken({
          email: decodeURIComponent(email),
          token,
          verificationType: 'emergencyContactConfirmation',
        });

        const verifyResultData = verifyResult.data as any;


        // Try to parse if it's a string
        let parsedVerifyResultData = verifyResultData;
        if (typeof verifyResultData === 'string') {
          try {
            parsedVerifyResultData = JSON.parse(verifyResultData);
            console.log('Parsed verify result data:', parsedVerifyResultData);
          } catch (e) {
            console.log('Failed to parse verify result data as JSON:', e);
          }
        }

        if (parsedVerifyResultData?.success) {
          // Now update the emergency contact's verification status
          // Find the emergency contact by email
          const contactsResult = await client.models.EmergencyContact.list({
            filter: {
              emailAddress: { eq: decodeURIComponent(email) },
            },
          });

          if (contactsResult.data && contactsResult.data.length > 0) {
            const contact = contactsResult.data[0];
            
            // Update the contact to mark it as verified
            await client.models.EmergencyContact.update({
              id: contact.id,
              isVerified: true,
            });
            
            setSuccess(true);
          } else {
            setError('Emergency contact not found');
          }
        } else {
          setError(parsedVerifyResultData?.error || 'Failed to verify emergency contact');
        }
      } catch (err) {
        console.error('Verification error:', err);
        setError('An error occurred while verifying your emergency contact status');
      } finally {
        setLoading(false);
      }
    };

    verifyContact();
  }, [token, email, successParam, errorParam]);

  const handleContinue = () => {
    // Redirect to a thank you page or home
    router.push('/');
  };

  if (loading) {
    return (
      <div className='min-h-screen flex items-center justify-center bg-gray-50'>
        <Card className='w-full max-w-md'>
          <CardContent className='flex flex-col items-center justify-center p-6'>
            <Loader2 className='h-8 w-8 animate-spin text-primary mb-4' />
            <p className='text-center text-muted-foreground'>
              Verifying your emergency contact status...
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className='min-h-screen flex items-center justify-center bg-gray-50'>
      <Card className='w-full max-w-md'>
        <CardHeader className='text-center'>
          <div className='flex justify-center mb-4'>
            {success ? (
              <CheckCircle className='h-12 w-12 text-green-500' />
            ) : (
              <XCircle className='h-12 w-12 text-red-500' />
            )}
          </div>
          <CardTitle>
            {success ? 'Emergency Contact Verified!' : 'Verification Failed'}
          </CardTitle>
          <CardDescription>
            {success
              ? 'Your emergency contact status has been successfully verified. Thank you for confirming.'
              : error || 'We were unable to verify your emergency contact status.'}
          </CardDescription>
        </CardHeader>
        <CardContent className='space-y-4'>
          {success ? (
            <Button onClick={handleContinue} className='w-full'>
              Continue
            </Button>
          ) : (
            <div className='space-y-2'>
              <Button onClick={handleContinue} className='w-full'>
                Back to Home
              </Button>
              <p className='text-xs text-center text-muted-foreground'>
                If you continue to have issues, please contact support.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

export default function ConfirmContactPage() {
  return (
    <Suspense
      fallback={
        <div className='min-h-screen flex items-center justify-center bg-gray-50'>
          <Card className='w-full max-w-md'>
            <CardContent className='flex flex-col items-center justify-center p-6'>
              <Loader2 className='h-8 w-8 animate-spin text-primary mb-4' />
              <p className='text-center text-muted-foreground'>Loading...</p>
            </CardContent>
          </Card>
        </div>
      }
    >
      <ConfirmContactContent />
    </Suspense>
  );
}