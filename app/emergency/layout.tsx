'use client';

import React from 'react';
import { Sidebar } from '@/components/dashboard/sidebar';
import { AdminContainer } from '../../components/ui/container';
import {
  UserProvider,
  useUserContext,
} from '@/components/welon-trust/user-context';
import { UserSelector } from '@/components/welon-trust/user-selector';
import { UserBreadcrumb } from '@/components/welon-trust/user-breadcrumb';
import { Card, CardContent } from '@/components/ui/card';
import { AdminGuard } from '@/lib/auth';

interface EmergencyLayoutProps {
  children: React.ReactNode;
}

function EmergencyLayoutContent({ children }: { children: React.ReactNode }) {
  const { selectedUser, setSelectedUser, availableUsers, isLoading } =
    useUserContext();

  return (
    <AdminGuard requiredRole={'WELONTRUST'}>
      <div className='min-h-screen flex bg-background'>
        <Sidebar userRole='Welon Trust' />
        <div className='flex-1'>
          <main>
            <AdminContainer>
              {/* User Selection Header */}
              <Card className='mb-6 border-blue-200 bg-[var(--custom-blue-light)]'>
                <CardContent className='p-6'>
                  <div className='max-w-md'>
                    <UserSelector
                      selectedUser={selectedUser}
                      onUserSelect={setSelectedUser}
                      users={availableUsers}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Main Content */}
              {children}
            </AdminContainer>
          </main>
        </div>
      </div>
    </AdminGuard>
  );
}

export default function EmergencyLayout({ children }: EmergencyLayoutProps) {
  return (
    <UserProvider>
      <EmergencyLayoutContent>{children}</EmergencyLayoutContent>
    </UserProvider>
  );
}
