'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { StripeUserSubscription } from '@/components/billing/types';

interface UseSubscriptionReturn {
  subscription: StripeUserSubscription | null;
  hasActiveSubscription: boolean;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export function useSubscription(): UseSubscriptionReturn {
  const { user, userId } = useAuth(); // userId is the database ID, user.userId is cognitoId
  const [subscription, setSubscription] =
    useState<StripeUserSubscription | null>(null);
  const [hasActiveSubscription, setHasActiveSubscription] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSubscription = async () => {
    console.log('useSubscription: fetchSubscription called', {
      userUserId: user?.userId,
      userId: userId,
    });

    if (!user?.userId || !userId) {
      console.log('useSubscription: Missing user data, stopping fetch');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      console.log('useSubscription: Making API call with headers', {
        'x-user-id': userId,
        'x-cognito-id': user.userId,
      });

      const response = await fetch('/api/stripe/subscription', {
        headers: {
          'x-user-id': userId, // Database user ID
          'x-cognito-id': user.userId, // Cognito ID
        },
      });

      console.log('useSubscription: API response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          'useSubscription: API error:',
          response.status,
          errorText
        );
        throw new Error('Failed to fetch subscription');
      }

      const data = await response.json();
      console.log('useSubscription: API response data:', data);
      setSubscription(data.subscription);
      setHasActiveSubscription(data.hasActiveSubscription);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      setSubscription(null);
      setHasActiveSubscription(false);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSubscription();
  }, [user?.userId]);

  return {
    subscription,
    hasActiveSubscription,
    loading,
    error,
    refetch: fetchSubscription,
  };
}

// Helper function to create checkout session
export async function createCheckoutSession(
  plan: 'BASIC' | 'PRO',
  userEmail: string,
  userId: string,
  cognitoId: string
) {
  const response = await fetch('/api/stripe/create-checkout', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'x-user-email': userEmail,
      'x-user-id': userId, // Database user ID
      'x-cognito-id': cognitoId, // Cognito ID
    },
    body: JSON.stringify({ plan }),
  });

  if (!response.ok) {
    throw new Error('Failed to create checkout session');
  }

  return response.json();
}

// Helper function to cancel subscription
export async function cancelSubscription(
  subscriptionId: string,
  userId: string,
  cognitoId: string
) {
  const response = await fetch('/api/stripe/cancel-subscription', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'x-user-id': userId,
      'x-cognito-id': cognitoId,
    },
    body: JSON.stringify({ subscriptionId }),
  });

  if (!response.ok) {
    throw new Error('Failed to cancel subscription');
  }

  return response.json();
}
