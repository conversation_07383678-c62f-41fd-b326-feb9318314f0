'use client';

import { ReactNode, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/app/context/AuthContext';
import { OnboardingStep } from '@/app/utils/userOnboarding';
import { isAdmin, isWelonTrust } from '@/lib/utils/admin-utils';
import routes from '@/utils/routes';
import { DashboardContentLayoutSkeleton } from '@/components/ui/skeletons';

const Layout = ({ children }: { children: ReactNode }) => {
  const router = useRouter();
  const { user, loading, userRoles, onboardingStatus } = useAuth();

  const getOptimizedDashboardPath = (): string => {
    if (userRoles && userRoles.length > 0) {
      if (isAdmin(userRoles)) {
        return routes.admin.dashboard;
      }
      if (isWelonTrust(userRoles)) {
        return routes.welon.dashboard;
      }
    }

    if (onboardingStatus === OnboardingStep.COMPLETED) {
      return routes.member.dashboard;
    } else {
      return routes.member.onboarding;
    }
  };

  useEffect(() => {
    if (!loading && user) {
      const dashboardPath = getOptimizedDashboardPath();
      router.push(dashboardPath);
    }
  }, [user, loading, userRoles, onboardingStatus, router]);

  if (loading) {
    return <DashboardContentLayoutSkeleton />;
  }

  if (!user) {
    return (
      <div className='container mx-auto py-12 px-4'>
        <div className='flex flex-col items-center justify-center'>
          {children}
        </div>
      </div>
    );
  }

  return null;
};

export default Layout;
