/**
 * Mock Educational Content
 *
 * This file provides mock data for the Educational Content Delivery feature.
 */

import {
  Content,
  ContentType,
  ContentStatus,
  VideoContent,
  ArticleContent,
  InfographicContent,
  AvatarContent,
  TooltipContent,
  ContentAnalytics,
  ContentFeedback,
  ContentIntegrationPoint,
} from '../types/education';

// Mock Videos
const mockVideos: VideoContent[] = [
  {
    id: 'video-001',
    type: ContentType.VIDEO,
    title: 'Understanding Wills: The Basics',
    description:
      "A beginner's guide to understanding what a will is and why it's important.",
    tags: ['wills', 'basics', 'estate planning'],
    status: ContentStatus.PUBLISHED,
    createdAt: '2023-01-15T12:00:00Z',
    updatedAt: '2023-01-15T12:00:00Z',
    version: 1,
    url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
    duration: 180, // 3 minutes
    hasCaptions: true,
    hasAudioDescription: true,
    thumbnailUrl: 'https://example.com/thumbnails/understanding-wills.jpg',
  },
  {
    id: 'video-002',
    type: ContentType.VIDEO,
    title: 'How Trusts Work',
    description:
      'Learn about different types of trusts and how they can benefit your estate plan.',
    tags: ['trusts', 'estate planning', 'advanced'],
    status: ContentStatus.PUBLISHED,
    createdAt: '2023-02-10T14:30:00Z',
    updatedAt: '2023-02-10T14:30:00Z',
    version: 1,
    url: 'https://example.com/videos/how-trusts-work.mp4',
    duration: 240, // 4 minutes
    hasCaptions: true,
    hasAudioDescription: false,
    thumbnailUrl: 'https://example.com/thumbnails/how-trusts-work.jpg',
  },
  {
    id: 'video-003',
    type: ContentType.VIDEO,
    title: 'Setting Up Emergency Contacts',
    description:
      'A step-by-step guide to setting up and managing your emergency contacts.',
    tags: ['emergency', 'contacts', 'basics'],
    status: ContentStatus.PUBLISHED,
    createdAt: '2023-03-05T09:15:00Z',
    updatedAt: '2023-03-05T09:15:00Z',
    version: 1,
    url: 'https://example.com/videos/emergency-contacts.mp4',
    duration: 150, // 2.5 minutes
    hasCaptions: true,
    hasAudioDescription: true,
    thumbnailUrl: 'https://example.com/thumbnails/emergency-contacts.jpg',
  },
];

// Mock Articles
const mockArticles: ArticleContent[] = [
  {
    id: 'article-001',
    type: ContentType.ARTICLE,
    title: 'The Importance of Estate Planning',
    description:
      'Learn why estate planning is crucial, especially for those without children.',
    tags: ['estate planning', 'basics', 'introduction'],
    status: ContentStatus.PUBLISHED,
    createdAt: '2023-01-20T10:00:00Z',
    updatedAt: '2023-01-20T10:00:00Z',
    version: 1,
    content:
      '# The Importance of Estate Planning\n\nEstate planning is the process of arranging for the management and disposal of your estate during your lifetime and after death...',
    readingTime: 5,
    tableOfContents: [
      { id: 'section-1', title: 'Introduction', level: 1 },
      { id: 'section-2', title: 'What is Estate Planning?', level: 2 },
      { id: 'section-3', title: 'Why is it Important?', level: 2 },
      { id: 'section-4', title: 'Key Components', level: 2 },
      { id: 'section-5', title: 'Getting Started', level: 2 },
    ],
  },
  {
    id: 'article-002',
    type: ContentType.ARTICLE,
    title: 'Choosing the Right Executor',
    description: 'Tips for selecting the best executor for your estate.',
    tags: ['executor', 'wills', 'advanced'],
    status: ContentStatus.PUBLISHED,
    createdAt: '2023-02-15T11:30:00Z',
    updatedAt: '2023-02-15T11:30:00Z',
    version: 1,
    content:
      '# Choosing the Right Executor\n\nAn executor is the person responsible for managing your estate after your death...',
    readingTime: 7,
    tableOfContents: [
      { id: 'section-1', title: 'What is an Executor?', level: 1 },
      { id: 'section-2', title: 'Responsibilities', level: 2 },
      { id: 'section-3', title: 'Qualities to Look For', level: 2 },
      { id: 'section-4', title: 'Common Mistakes', level: 2 },
      { id: 'section-5', title: 'Making Your Decision', level: 2 },
    ],
  },
];

// Mock Infographics
const mockInfographics: InfographicContent[] = [
  {
    id: 'infographic-001',
    type: ContentType.INFOGRAPHIC,
    title: 'Estate Planning Process Flowchart',
    description: 'A visual guide to the estate planning process.',
    tags: ['estate planning', 'process', 'visual'],
    status: ContentStatus.PUBLISHED,
    createdAt: '2023-01-25T15:45:00Z',
    updatedAt: '2023-01-25T15:45:00Z',
    version: 1,
    imageUrl: 'https://example.com/infographics/estate-planning-flowchart.jpg',
    altText:
      'Flowchart showing the steps of estate planning from initial consultation to document execution.',
  },
  {
    id: 'infographic-002',
    type: ContentType.INFOGRAPHIC,
    title: 'Types of Trusts Comparison',
    description: 'Compare different types of trusts at a glance.',
    tags: ['trusts', 'comparison', 'advanced'],
    status: ContentStatus.PUBLISHED,
    createdAt: '2023-02-20T13:15:00Z',
    updatedAt: '2023-02-20T13:15:00Z',
    version: 1,
    imageUrl: 'https://example.com/infographics/trust-types-comparison.jpg',
    altText:
      'Comparison chart of different trust types including revocable, irrevocable, and special needs trusts.',
  },
];

// Mock Tooltips
const mockTooltips: TooltipContent[] = [
  {
    id: 'tooltip-001',
    type: ContentType.TOOLTIP,
    title: 'What is a Beneficiary?',
    description: 'Quick explanation of what a beneficiary is.',
    tags: ['beneficiary', 'basics', 'definitions'],
    status: ContentStatus.PUBLISHED,
    createdAt: '2023-01-10T09:00:00Z',
    updatedAt: '2023-01-10T09:00:00Z',
    version: 1,
    content:
      'A beneficiary is a person or entity who receives assets or benefits from your will, trust, or other estate planning documents.',
    triggerText: 'beneficiary',
  },
  {
    id: 'tooltip-002',
    type: ContentType.TOOLTIP,
    title: 'What is a Trustee?',
    description: 'Quick explanation of what a trustee is.',
    tags: ['trustee', 'basics', 'definitions'],
    status: ContentStatus.PUBLISHED,
    createdAt: '2023-01-12T10:30:00Z',
    updatedAt: '2023-01-12T10:30:00Z',
    version: 1,
    content:
      'A trustee is a person or entity responsible for managing assets held in a trust according to the trust terms and for the benefit of the beneficiaries.',
    triggerText: 'trustee',
  },
];

// Mock Avatar Content
const mockAvatars: AvatarContent[] = [
  {
    id: 'avatar-001',
    type: ContentType.AVATAR,
    title: 'Estate Planning Assistant',
    description:
      'Virtual assistant to help with common estate planning questions.',
    tags: ['assistant', 'help', 'questions'],
    status: ContentStatus.PUBLISHED,
    createdAt: '2023-03-10T14:00:00Z',
    updatedAt: '2023-03-10T14:00:00Z',
    version: 1,
    avatarImageUrl: 'https://example.com/avatars/estate-planning-assistant.jpg',
    responses: [
      {
        question: 'What is a will?',
        answer:
          'A will is a legal document that outlines how you want your assets distributed after your death. It can also name guardians for minor children and specify funeral arrangements.',
      },
      {
        question: 'Do I need a trust?',
        answer:
          'Whether you need a trust depends on your specific situation. Trusts can help avoid probate, provide privacy, and manage assets for beneficiaries who may not be ready to manage them directly.',
      },
      {
        question: 'What happens if I die without a will?',
        answer:
          'If you die without a will (intestate), state laws will determine how your assets are distributed, which may not align with your wishes. The court will also appoint an administrator for your estate.',
      },
    ],
  },
];

// Combine all content
export const mockEducationalContent: Content[] = [
  ...mockVideos,
  ...mockArticles,
  ...mockInfographics,
  ...mockTooltips,
  ...mockAvatars,
];

// Mock Content Analytics
export const mockContentAnalytics: Record<string, ContentAnalytics> = {
  'video-001': {
    contentId: 'video-001',
    views: 1245,
    completionRate: 78,
    averageRating: 4.2,
    feedback: [
      {
        id: 'feedback-001',
        contentId: 'video-001',
        rating: 5,
        comment: 'Very helpful and easy to understand!',
        createdAt: '2023-02-01T14:30:00Z',
      },
      {
        id: 'feedback-002',
        contentId: 'video-001',
        rating: 4,
        comment: 'Good information but could be more concise.',
        createdAt: '2023-02-05T09:15:00Z',
      },
    ],
  },
  'article-001': {
    contentId: 'article-001',
    views: 876,
    completionRate: 65,
    averageRating: 4.5,
    feedback: [
      {
        id: 'feedback-003',
        contentId: 'article-001',
        rating: 5,
        comment: 'Excellent overview of estate planning!',
        createdAt: '2023-02-10T11:45:00Z',
      },
    ],
  },
};

// Helper functions
export const getContentById = (id: string): Content | undefined => {
  return mockEducationalContent.find(content => content.id === id);
};

export const getContentByType = (type: ContentType): Content[] => {
  return mockEducationalContent.filter(content => content.type === type);
};

export const getContentByTags = (tags: string[]): Content[] => {
  return mockEducationalContent.filter(content =>
    content.tags.some(tag => tags.includes(tag))
  );
};

export const getContentAnalytics = (
  contentId: string
): ContentAnalytics | undefined => {
  return mockContentAnalytics[contentId];
};

export const getContentForIntegrationPoint = (
  point: ContentIntegrationPoint
): Content[] => {
  // Map integration points to relevant tags
  const tagMap: Record<ContentIntegrationPoint, string[]> = {
    [ContentIntegrationPoint.ONBOARDING]: ['basics', 'introduction'],
    [ContentIntegrationPoint.DOCUMENT_CREATION]: [
      'wills',
      'trusts',
      'documents',
    ],
    [ContentIntegrationPoint.REVIEW_AND_SIGNING]: ['review', 'signing'],
    [ContentIntegrationPoint.EMERGENCY_FEATURES]: ['emergency', 'contacts'],
    [ContentIntegrationPoint.NOTIFICATIONS]: ['notifications', 'alerts'],
    [ContentIntegrationPoint.LIVE_DOCUMENTS]: ['documents', 'maintenance'],
  };

  return getContentByTags(tagMap[point] || []);
};
