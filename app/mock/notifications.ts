import {
  Notification,
  NotificationCategory,
  NotificationPriority,
  NotificationStatus,
  NotificationEventType,
  DeliveryChannel,
  NotificationPreferences,
  NotificationTemplate,
} from '@/types/notifications';

/**
 * Mock notification data for testing and development
 */

// Mock notification templates
export const notificationTemplates: NotificationTemplate[] = [
  {
    eventType: NotificationEventType.DOCUMENT_UPDATE,
    category: NotificationCategory.ACTION_REQUIRED,
    priority: NotificationPriority.MEDIUM,
    titleTemplate: 'Document Update Available',
    messageTemplate:
      'Your {{documentType}} has been updated and requires your review.',
    actionUrlTemplate: '/member/documents/review/{{documentId}}',
    actionTextTemplate: 'Review Document',
    defaultChannels: [DeliveryChannel.EMAIL, DeliveryChannel.IN_APP],
  },
  {
    eventType: NotificationEventType.EMERGENCY_ACCESS,
    category: NotificationCategory.URGENT,
    priority: NotificationPriority.HIGH,
    titleTemplate: 'Emergency Access Granted',
    messageTemplate:
      'Emergency access has been granted to {{contactName}} for your estate documents.',
    actionUrlTemplate: '/emergency/documents',
    actionTextTemplate: 'View Details',
    defaultChannels: [
      DeliveryChannel.EMAIL,
      DeliveryChannel.SMS,
      DeliveryChannel.IN_APP,
    ],
  },
  {
    eventType: NotificationEventType.MISSED_PAYMENT,
    category: NotificationCategory.URGENT,
    priority: NotificationPriority.HIGH,
    titleTemplate: 'Payment Failed',
    messageTemplate:
      'Your subscription payment has failed. Please update your payment method to continue service.',
    actionUrlTemplate: '/dashboard/billing',
    actionTextTemplate: 'Update Payment',
    defaultChannels: [DeliveryChannel.EMAIL, DeliveryChannel.IN_APP],
  },
  {
    eventType: NotificationEventType.QUARTERLY_REVIEW_START,
    category: NotificationCategory.ACTION_REQUIRED,
    priority: NotificationPriority.MEDIUM,
    titleTemplate: 'Quarterly Review Started',
    messageTemplate:
      'The quarterly review period has started. Please review the following templates: {{templateList}}',
    actionUrlTemplate: '/admin/quarterly-review',
    actionTextTemplate: 'Start Review',
    defaultChannels: [DeliveryChannel.EMAIL, DeliveryChannel.IN_APP],
  },
  {
    eventType: NotificationEventType.ATTORNEY_REVIEW_REQUEST,
    category: NotificationCategory.ACTION_REQUIRED,
    priority: NotificationPriority.MEDIUM,
    titleTemplate: 'Attorney Review Requested',
    messageTemplate:
      'An attorney review has been requested for your documents. Please select an attorney from the list.',
    actionUrlTemplate: '/member/attorney-list',
    actionTextTemplate: 'Select Attorney',
    defaultChannels: [DeliveryChannel.EMAIL, DeliveryChannel.IN_APP],
  },
];

// Mock notifications for different users
export const mockNotifications: Notification[] = [
  {
    id: 'notif-001',
    userId: 'user-member-001',
    eventType: NotificationEventType.DOCUMENT_UPDATE,
    category: NotificationCategory.ACTION_REQUIRED,
    priority: NotificationPriority.MEDIUM,
    title: 'Will Document Updated',
    message:
      'Your Last Will and Testament has been updated with new state regulations and requires your review and signature.',
    actionUrl: '/member/documents/review/will-001',
    actionText: 'Review Document',
    status: NotificationStatus.DELIVERED,
    channels: [DeliveryChannel.EMAIL, DeliveryChannel.IN_APP],
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-15T10:30:00Z',
    metadata: {
      documentType: 'Last Will and Testament',
      documentId: 'will-001',
    },
  },
  {
    id: 'notif-002',
    userId: 'user-member-001',
    eventType: NotificationEventType.MISSED_PAYMENT,
    category: NotificationCategory.URGENT,
    priority: NotificationPriority.HIGH,
    title: 'Payment Failed',
    message:
      'Your subscription payment of $29.99 has failed. Please update your payment method to continue service.',
    actionUrl: '/dashboard/billing',
    actionText: 'Update Payment',
    status: NotificationStatus.READ,
    channels: [DeliveryChannel.EMAIL, DeliveryChannel.IN_APP],
    createdAt: '2024-01-14T08:15:00Z',
    updatedAt: '2024-01-14T08:15:00Z',
    readAt: '2024-01-14T09:22:00Z',
    metadata: {
      amount: '$29.99',
      paymentMethod: '****1234',
    },
  },
  {
    id: 'notif-003',
    userId: 'user-member-001',
    eventType: NotificationEventType.ROUTINE_CHECK_IN,
    category: NotificationCategory.INFORMATIONAL,
    priority: NotificationPriority.LOW,
    title: 'Monthly Check-in Reminder',
    message:
      "It's time for your monthly wellness check-in. Please confirm you're doing well to keep your Dead Man's Switch active.",
    actionUrl: '/dashboard/emergency/check-in',
    actionText: 'Complete Check-in',
    status: NotificationStatus.DELIVERED,
    channels: [DeliveryChannel.EMAIL, DeliveryChannel.IN_APP],
    createdAt: '2024-01-13T12:00:00Z',
    updatedAt: '2024-01-13T12:00:00Z',
    metadata: {
      checkInType: 'monthly',
      dmsStatus: 'active',
    },
  },
  {
    id: 'notif-004',
    userId: 'user-admin-001',
    eventType: NotificationEventType.QUARTERLY_REVIEW_START,
    category: NotificationCategory.ACTION_REQUIRED,
    priority: NotificationPriority.MEDIUM,
    title: 'Q1 2024 Quarterly Review Started',
    message:
      'The quarterly review period has started. Please review templates for California, Texas, and New York.',
    actionUrl: '/admin/quarterly-review',
    actionText: 'Start Review',
    status: NotificationStatus.DELIVERED,
    channels: [DeliveryChannel.EMAIL, DeliveryChannel.IN_APP],
    createdAt: '2024-01-12T09:00:00Z',
    updatedAt: '2024-01-12T09:00:00Z',
    metadata: {
      quarter: 'Q1 2024',
      states: ['California', 'Texas', 'New York'],
      templateCount: 15,
    },
  },
  {
    id: 'notif-005',
    userId: 'user-welon-001',
    eventType: NotificationEventType.EMERGENCY_ACCESS,
    category: NotificationCategory.URGENT,
    priority: NotificationPriority.HIGH,
    title: 'Emergency Access Granted',
    message:
      "Emergency access has been granted for John Smith's estate documents. Death certificate verified.",
    actionUrl: '/emergency/documents/john-smith-001',
    actionText: 'Access Documents',
    status: NotificationStatus.READ,
    channels: [
      DeliveryChannel.EMAIL,
      DeliveryChannel.SMS,
      DeliveryChannel.IN_APP,
    ],
    createdAt: '2024-01-11T14:30:00Z',
    updatedAt: '2024-01-11T14:30:00Z',
    readAt: '2024-01-11T14:45:00Z',
    metadata: {
      memberName: 'John Smith',
      evidenceType: 'death_certificate',
      requestedBy: 'Jane Smith',
    },
  },
  {
    id: 'notif-006',
    userId: 'user-member-001',
    eventType: NotificationEventType.LINKED_ACCOUNT_INVITATION,
    category: NotificationCategory.ACTION_REQUIRED,
    priority: NotificationPriority.MEDIUM,
    title: 'Account Link Request',
    message:
      'Sarah Johnson has requested to link her account with yours for shared estate planning.',
    actionUrl: '/dashboard/account-linking/pending',
    actionText: 'Review Request',
    status: NotificationStatus.DELIVERED,
    channels: [DeliveryChannel.EMAIL, DeliveryChannel.IN_APP],
    createdAt: '2024-01-10T16:20:00Z',
    updatedAt: '2024-01-10T16:20:00Z',
    metadata: {
      requesterName: 'Sarah Johnson',
      requesterEmail: '<EMAIL>',
      permissions: ['view_documents', 'emergency_access'],
    },
  },
];

// Mock notification preferences
export const mockNotificationPreferences: NotificationPreferences = {
  userId: 'user-member-001',
  preferences: {
    [NotificationCategory.INFORMATIONAL]: {
      email: true,
      sms: false,
      inApp: true,
    },
    [NotificationCategory.ACTION_REQUIRED]: {
      email: true,
      sms: false,
      inApp: true,
    },
    [NotificationCategory.URGENT]: {
      email: true,
      sms: true,
      inApp: true,
    },
  },
  updatedAt: '2024-01-01T00:00:00Z',
};

// Helper function to get notifications for a specific user
export const getNotificationsForUser = (userId: string): Notification[] => {
  return mockNotifications.filter(
    notification => notification.userId === userId
  );
};

// Helper function to get unread count for a user
export const getUnreadCountForUser = (userId: string): number => {
  return mockNotifications.filter(
    notification =>
      notification.userId === userId &&
      notification.status !== NotificationStatus.READ
  ).length;
};

// Helper function to get notifications by category
export const getNotificationsByCategory = (
  userId: string,
  category: NotificationCategory
): Notification[] => {
  return mockNotifications.filter(
    notification =>
      notification.userId === userId && notification.category === category
  );
};

// Helper function to get recent notifications (last 7 days)
export const getRecentNotifications = (userId: string): Notification[] => {
  const sevenDaysAgo = new Date();
  sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

  return mockNotifications.filter(
    notification =>
      notification.userId === userId &&
      new Date(notification.createdAt) >= sevenDaysAgo
  );
};
