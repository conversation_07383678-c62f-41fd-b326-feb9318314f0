'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/app/context/AuthContext';
import { OnboardingStep } from '@/app/utils/userOnboarding';
import { isAdmin, isWelonTrust } from '@/lib/utils/admin-utils';
import routes from '@/utils/routes';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  CheckCircle,
  Star,
  Shield,
  Users,
  ArrowRight,
  Heart,
  FileText,
  UserCheck,
  Calculator,
  BookOpen,
} from 'lucide-react';
import Link from 'next/link';

const Home = () => {
  const router = useRouter();
  const { user, loading, userRoles, onboardingStatus } = useAuth();

  // Function to determine the appropriate dashboard path based on user role and onboarding status
  const getOptimizedDashboardPath = (): string => {
    if (userRoles && userRoles.length > 0) {
      if (isAdmin(userRoles)) {
        return routes.admin.dashboard;
      }
      if (isWelonTrust(userRoles)) {
        return routes.welon.dashboard;
      }
    }

    if (onboardingStatus === OnboardingStep.COMPLETED) {
      return routes.member.dashboard;
    } else {
      return routes.member.onboarding;
    }
  };

  // Redirect authenticated users to their appropriate dashboard
  useEffect(() => {
    if (!loading && user) {
      const dashboardPath = getOptimizedDashboardPath();
      router.replace(dashboardPath); // Use replace instead of push to avoid back button issues
    }
  }, [user, loading, userRoles, onboardingStatus, router]);

  // Redirect unauthenticated users to login
  useEffect(() => {
    if (!loading && !user) {
      router.replace(routes.login); // Use replace instead of push
    }
  }, [user, loading, router]);

  // Show loading state while checking authentication OR if we're about to redirect
  if (loading || (!loading && user) || (!loading && !user)) {
    return (
      <div className='min-h-screen bg-gradient-to-b from-[var(--background)] to-white flex items-center justify-center'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-32 w-32 border-b-2 border-[var(--purple)] mx-auto mb-4'></div>
          <p className='text-lg text-[var(--foreground)]'>
            {loading ? 'Loading...' : 'Redirecting...'}
          </p>
        </div>
      </div>
    );
  }

  // This should never render, but keeping as fallback
  return (
    <div className='min-h-screen bg-gradient-to-b from-[var(--background)] to-white'>
      {/* Hero Section */}
      <section className='relative overflow-hidden bg-gradient-to-r from-[var(--purple)] to-[#7C71FA]'>
        <div className='absolute inset-0 bg-[url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")] opacity-20'></div>
        <div className='relative container mx-auto px-4 py-20 lg:py-32'>
          <div className='text-center max-w-4xl mx-auto'>
            <div className='inline-flex items-center gap-2 bg-[var(--purple)]/60 text-[var(--berry)] px-4 py-2 rounded-full text-sm font-medium mb-8'>
              <Users className='w-4 h-4' />
              Trusted by 10,000+ Childfree individuals
            </div>

            <h1 className='text-5xl lg:text-7xl font-bold text-white mb-6 leading-tight'>
              Let's take your mind off
              <span className='block text-transparent bg-gradient-to-r from-[var(--berry)] to-[var(--lime)] bg-clip-text'>
                estate planning
              </span>
            </h1>

            <p className='text-xl lg:text-2xl text-white mb-8 max-w-3xl mx-auto leading-relaxed'>
              Our experts are ready to help you create your perfect legacy plan,
              guaranteed.
            </p>

            <div className='flex flex-col sm:flex-row gap-4 justify-center items-center'>
              <Button
                className='bg-[var(--lemon)]  text-black hover:bg-[var(--lemon)]'
                variant='success'
                size='xl'
                asChild
              >
                <Link href='/login'>
                  Get started
                  <ArrowRight className='w-5 h-5 ml-2' />
                </Link>
              </Button>

              <Button variant='outline' size='xl' asChild>
                <Link href='/login'>Sign in</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Free Plan Section */}
      <section className='py-16 bg-[var(--background)]'>
        <div className='container mx-auto px-4'>
          <div className='max-w-4xl mx-auto text-center'>
            <h2 className='text-3xl lg:text-4xl font-bold text-[var(--foreground)] mb-4'>
              Simple estate plans 100% free
            </h2>
            <p className='text-lg text-[var(--custom-gray-dark)] mb-8'>
              ~40% of Childfree individuals qualify. Basic estate planning
              documents only (simple wills, basic trusts, and power of
              attorney).
            </p>
            <Button
              className='bg-[var(--lemon)]  text-black hover:bg-[var(--lemon)]'
              variant='success'
              size='xl'
              asChild
            >
              <Link href='/auth/register'>
                Start for free
                <ArrowRight className='w-5 h-5 ml-2' />
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Service Options */}
      <section className='py-20 bg-[var(--background)]'>
        <div className='container mx-auto px-4'>
          <div className='text-center mb-16'>
            <h2 className='text-4xl lg:text-5xl font-bold text-[var(--foreground)] mb-6'>
              Three ways to plan, all backed by our Lifetime Guarantee™
            </h2>
          </div>

          <div className='grid md:grid-cols-3 gap-8 max-w-6xl mx-auto'>
            {/* Full Service */}
            <Card className='relative overflow-hidden border-2 hover:border-[var(--berry)] transition-all duration-300 hover:shadow-xl'>
              <CardHeader className='text-center pb-4'>
                <div className='w-16 h-16 bg-[var(--berry)]/10 rounded-full flex items-center justify-center mx-auto mb-4'>
                  <UserCheck className='w-8 h-8 text-[var(--berry)]' />
                </div>
                <CardTitle className='text-2xl font-bold text-[var(--foreground)]'>
                  We do your estate plan for you
                </CardTitle>
                <CardDescription className='text-[var(--custom-gray-medium)] text-base'>
                  Have an estate planning expert create your complete plan from
                  start to finish, as soon as today.
                </CardDescription>
              </CardHeader>
              <CardContent className='pt-0'>
                <div className='space-y-4'>
                  <Button variant='info' className='w-full' asChild>
                    <Link href='/auth/register'>Get started</Link>
                  </Button>
                  <Button variant='outline' className='w-full' asChild>
                    <Link href='/app'>See how Full Service works</Link>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Assisted */}
            <Card className='relative overflow-hidden border-2 hover:border-[var(--tangerine)] transition-all duration-300 hover:shadow-xl'>
              <CardHeader className='text-center pb-4'>
                <div className='w-16 h-16 bg-[var(--tangerine)]/10 rounded-full flex items-center justify-center mx-auto mb-4'>
                  <Heart className='w-8 h-8 text-[var(--tangerine)]' />
                </div>
                <CardTitle className='text-2xl font-bold text-[var(--foreground)]'>
                  Get expert help as you go
                </CardTitle>
                <CardDescription className='text-[var(--custom-gray-medium)] text-base'>
                  Get advice from estate planning experts as you build your
                  plan, plus a final expert review.
                </CardDescription>
              </CardHeader>
              <CardContent className='pt-0'>
                <div className='space-y-4'>
                  <Button
                    variant='success'
                    className='w-full bg-[var(--tangerine)] hover:bg-[var(--tangerine)]/90'
                    asChild
                  >
                    <Link href='/auth/register'>Get started</Link>
                  </Button>
                  <Button variant='outline' className='w-full' asChild>
                    <Link href='/app'>See how Assisted works</Link>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* DIY */}
            <Card className='relative overflow-hidden border-2 hover:border-[var(--eggplant)] transition-all duration-300 hover:shadow-xl'>
              <CardHeader className='text-center pb-4'>
                <div className='w-16 h-16 bg-[var(--eggplant)]/10 rounded-full flex items-center justify-center mx-auto mb-4'>
                  <FileText className='w-8 h-8 text-[var(--eggplant)]' />
                </div>
                <CardTitle className='text-2xl font-bold text-[var(--foreground)]'>
                  Create your own estate plan
                </CardTitle>
                <CardDescription className='text-[var(--custom-gray-medium)] text-base'>
                  Just answer simple questions, and we'll guide you through
                  creating your estate plan.
                </CardDescription>
              </CardHeader>
              <CardContent className='pt-0'>
                <div className='space-y-4'>
                  <Button variant='purple' className='w-full' asChild>
                    <Link href='/auth/register'>Get started</Link>
                  </Button>
                  <Button variant='outline' className='w-full' asChild>
                    <Link href='/app'>See how DIY works</Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Preparation Tools */}
      <section className='py-20 bg-gradient-to-b from-[var(--purple)] to-[#605B8C]'>
        <div className='container mx-auto px-4'>
          <div className='text-center mb-16'>
            <h2 className='text-4xl lg:text-5xl font-bold text-[var(--off-white)] mb-6'>
              We can help you get ready for estate planning
            </h2>
          </div>

          <div className='grid md:grid-cols-3 gap-8 max-w-6xl mx-auto'>
            <Card className='text-center hover:shadow-lg transition-all duration-300'>
              <CardHeader>
                <div className='w-16 h-16 bg-[var(--berry)]/10 rounded-full flex items-center justify-center mx-auto mb-4'>
                  <Calculator className='w-8 h-8 text-[var(--berry)]' />
                </div>
                <CardTitle className='text-xl font-bold text-[var(--foreground)]'>
                  Get an estimate of your estate value
                </CardTitle>
                <CardDescription className='text-[var(--custom-gray-medium)]'>
                  Use our Estate Calculator to preview your estate value and
                  planning needs.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button variant='outline'>Estimate your estate</Button>
              </CardContent>
            </Card>

            <Card className='text-center hover:shadow-lg transition-all duration-300'>
              <CardHeader>
                <div className='w-16 h-16 bg-[var(--tangerine)]/10 rounded-full flex items-center justify-center mx-auto mb-4'>
                  <CheckCircle className='w-8 h-8 text-[var(--tangerine)]' />
                </div>
                <CardTitle className='text-xl font-bold text-[var(--foreground)]'>
                  Be prepared with our handy checklist
                </CardTitle>
                <CardDescription className='text-[var(--custom-gray-medium)]'>
                  Keep track of which documents you'll need when it's time to
                  create your estate plan.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button variant='outline'>Get the checklist</Button>
              </CardContent>
            </Card>

            <Card className='text-center hover:shadow-lg transition-all duration-300'>
              <CardHeader>
                <div className='w-16 h-16 bg-[var(--eggplant)]/10 rounded-full flex items-center justify-center mx-auto mb-4'>
                  <BookOpen className='w-8 h-8 text-[var(--eggplant)]' />
                </div>
                <CardTitle className='text-xl font-bold text-[var(--foreground)]'>
                  Estate planning tips, guides, and videos
                </CardTitle>
                <CardDescription className='text-[var(--custom-gray-medium)]'>
                  Everything you need to know about estate planning that you
                  didn't learn in school.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button variant='outline'>Start learning</Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className='py-20 bg-background'>
        <div className='container mx-auto px-4'>
          <div className='text-center mb-16'>
            <h2 className='text-4xl lg:text-5xl font-bold text-[var(--foreground)] mb-6'>
              Read why our customers love Childfree Trust
            </h2>
            <div className='flex items-center justify-center gap-2 mb-4'>
              <div className='flex'>
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className='w-6 h-6 fill-yellow-400 text-yellow-400'
                  />
                ))}
              </div>
              <span className='text-lg font-semibold text-[var(--foreground)]'>
                4.8 out of 5 stars
              </span>
            </div>
            <p className='text-[var(--custom-gray-medium)]'>
              by our customers (12,847 reviews of Childfree Trust)
            </p>
          </div>

          <div className='grid md:grid-cols-3 gap-8 max-w-6xl mx-auto'>
            <Card className='hover:shadow-lg transition-all duration-300'>
              <CardHeader>
                <div className='flex items-center gap-1 mb-2'>
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className='w-4 h-4 fill-yellow-400 text-yellow-400'
                    />
                  ))}
                </div>
                <CardTitle className='text-lg font-bold text-[var(--foreground)]'>
                  Childfree Trust is awesome
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className='text-[var(--custom-gray-dark)] mb-4'>
                  "Childfree Trust is very easy to use. If you do need help,
                  estate planning experts are ready to help. I would recommend
                  using this service instead of paying thousands elsewhere."
                </p>
                <div className='text-sm text-[var(--custom-gray-medium)]'>
                  <p className='font-semibold'>Sarah M., California</p>
                  <p>2024 Full Service</p>
                </div>
              </CardContent>
            </Card>

            <Card className='hover:shadow-lg transition-all duration-300'>
              <CardHeader>
                <div className='flex items-center gap-1 mb-2'>
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className='w-4 h-4 fill-yellow-400 text-yellow-400'
                    />
                  ))}
                </div>
                <CardTitle className='text-lg font-bold text-[var(--foreground)]'>
                  Perfect for Childfree individuals
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className='text-[var(--custom-gray-dark)] mb-4'>
                  "Finally, an estate planning service that understands our
                  unique needs. The process was smooth and the experts really
                  knew what they were talking about."
                </p>
                <div className='text-sm text-[var(--custom-gray-medium)]'>
                  <p className='font-semibold'>Michael R., New York</p>
                  <p>2024 Assisted Plan</p>
                </div>
              </CardContent>
            </Card>

            <Card className='hover:shadow-lg transition-all duration-300'>
              <CardHeader>
                <div className='flex items-center gap-1 mb-2'>
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className='w-4 h-4 fill-yellow-400 text-yellow-400'
                    />
                  ))}
                </div>
                <CardTitle className='text-lg font-bold text-[var(--foreground)]'>
                  Highly recommend
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className='text-[var(--custom-gray-dark)] mb-4'>
                  "I have used Childfree Trust for several years and have always
                  had a very positive experience. I feel comfortable with the
                  thoroughness and accuracy."
                </p>
                <div className='text-sm text-[var(--custom-gray-medium)]'>
                  <p className='font-semibold'>Jennifer L., Texas</p>
                  <p>2024 DIY Plan</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Guarantees */}
      <section className='py-20 bg-[var(--background)]'>
        <div className='container mx-auto px-4'>
          <div className='text-center mb-16'>
            <h2 className='text-4xl lg:text-5xl font-bold text-[var(--foreground)] mb-6'>
              Get your estate plan done right and your best outcome—guaranteed
            </h2>
          </div>

          <div className='grid md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto'>
            <Card className='text-center hover:shadow-lg transition-all duration-300 border-0 bg-white'>
              <CardHeader>
                <div className='w-16 h-16 bg-[var(--berry)]/10 rounded-full flex items-center justify-center mx-auto mb-4'>
                  <Shield className='w-8 h-8 text-[var(--berry)]' />
                </div>
                <CardTitle className='text-lg font-bold text-[var(--off-black)] mb-2'>
                  Your estate plan, backed for life™
                </CardTitle>
                <CardDescription className='text-[var(--custom-gray-medium)] text-sm'>
                  100% accurate documents, expert support, and your best
                  outcome. All backed for the full 7-year life of your estate
                  plan.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className='text-center hover:shadow-lg transition-all duration-300 border-0 bg-white'>
              <CardHeader>
                <div className='w-16 h-16 bg-[var(--tangerine)]/10 rounded-full flex items-center justify-center mx-auto mb-4'>
                  <CheckCircle className='w-8 h-8 text-[var(--tangerine)]' />
                </div>
                <CardTitle className='text-lg font-bold text-[var(--off-black)] mb-2'>
                  Your best estate planning outcome
                </CardTitle>
                <CardDescription className='text-[var(--custom-gray-medium)] text-sm'>
                  Whether you create it yourself or get expert help, you'll get
                  your maximum benefit guaranteed, or your money back.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className='text-center hover:shadow-lg transition-all duration-300 border-0 bg-white'>
              <CardHeader>
                <div className='w-16 h-16 bg-[var(--eggplant)]/10 rounded-full flex items-center justify-center mx-auto mb-4'>
                  <FileText className='w-8 h-8 text-[var(--eggplant)]' />
                </div>
                <CardTitle className='text-lg font-bold text-[var(--off-black)] mb-2'>
                  Estate plans done right
                </CardTitle>
                <CardDescription className='text-[var(--custom-gray-medium)] text-sm'>
                  Our documents are 100% accurate so your estate plan will be
                  done right, guaranteed, or we'll pay any legal penalties.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className='text-center hover:shadow-lg transition-all duration-300 border-0 bg-white'>
              <CardHeader>
                <div className='w-16 h-16 bg-[var(--purple)]/10 rounded-full flex items-center justify-center mx-auto mb-4'>
                  <Shield className='w-8 h-8 text-[var(--purple)]' />
                </div>
                <CardTitle className='text-lg font-bold text-[var(--off-black)] mb-2'>
                  Your security. Built into everything we do.
                </CardTitle>
                <CardDescription className='text-[var(--custom-gray-medium)] text-sm'>
                  Bank-level security and encryption protect your personal
                  information and documents at every step.
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* Final CTA */}
      <section className='py-20 bg-[var(--purple)] relative overflow-hidden'>
        <div className='absolute inset-0 bg-[url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")] opacity-20'></div>
        <div className='relative container mx-auto px-4'>
          <div className='text-center max-w-4xl mx-auto'>
            <h2 className='text-4xl lg:text-5xl font-bold text-white mb-6'>
              Ready to create your legacy plan?
            </h2>
            <p className='text-xl text-[var(--off-white)]/80 mb-8 max-w-2xl mx-auto'>
              Join thousands of Childfree individuals who have secured their
              future with our expert estate planning services.
            </p>

            <div className='flex flex-col sm:flex-row gap-4 justify-center items-center'>
              <Button
                className='bg-[var(--lemon)] text-black hover:bg-[var(--lemon)]'
                variant='success'
                size='xl'
                asChild
              >
                <Link href='/auth/register'>
                  Get started today
                  <ArrowRight className='w-5 h-5 ml-2' />
                </Link>
              </Button>

              <Button variant='outline' size='xl' asChild>
                <Link href='/app'>Learn more</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className='bg-[var(--off-black)] text-white py-16'>
        <div className='container mx-auto px-4'>
          <div className='grid md:grid-cols-4 gap-8'>
            <div className='md:col-span-2'>
              <h3 className='text-2xl font-bold mb-4'>ChildFree</h3>
              <p className='text-[var(--off-white)]/80 mb-6 max-w-md'>
                Empowering the Childfree community to plan an amazing life.
              </p>

              <div className='space-y-2'>
                <h4 className='font-semibold text-lg mb-3'>Contact</h4>
                <p className='text-[var(--off-white)]/80'>
                  Email: <EMAIL>
                </p>
                <p className='text-[var(--off-white)]/80'>
                  Phone: (*************
                </p>
              </div>
            </div>

            <div>
              <h4 className='font-semibold text-lg mb-4'>Quick Links</h4>
              <ul className='space-y-2'>
                <li>
                  <Link
                    href='/'
                    className='text-[var(--off-white)]/80 hover:text-white transition-colors'
                  >
                    Home
                  </Link>
                </li>
                <li>
                  <Link
                    href='#'
                    className='text-[var(--off-white)]/80 hover:text-white transition-colors'
                  >
                    About
                  </Link>
                </li>
                <li>
                  <Link
                    href='#'
                    className='text-[var(--off-white)]/80 hover:text-white transition-colors'
                  >
                    Services
                  </Link>
                </li>
                <li>
                  <Link
                    href='#'
                    className='text-[var(--off-white)]/80 hover:text-white transition-colors'
                  >
                    Resources
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h4 className='font-semibold text-lg mb-4'>Legal</h4>
              <ul className='space-y-2'>
                <li>
                  <Link
                    href='#'
                    className='text-[var(--off-white)]/80 hover:text-white transition-colors'
                  >
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link
                    href='#'
                    className='text-[var(--off-white)]/80 hover:text-white transition-colors'
                  >
                    Terms of Service
                  </Link>
                </li>
                <li>
                  <Link
                    href='#'
                    className='text-[var(--off-white)]/80 hover:text-white transition-colors'
                  >
                    Cookie Policy
                  </Link>
                </li>
              </ul>
            </div>
          </div>

          <div className='border-t border-[var(--custom-gray-dark)] mt-12 pt-8 text-center'>
            <p className='text-[var(--off-white)]'>
              © 2025 Childfree Trust. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Home;
