'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, <PERSON><PERSON><PERSON><PERSON>, AlertTriangle } from 'lucide-react';

export default function CreateSampleDataPage() {
  const [isCreating, setIsCreating] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [results, setResults] = useState<any>(null);

  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-2xl mx-auto'>
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              Create Sample Interview Data
            </CardTitle>
          </CardHeader>
          <CardContent className='space-y-6'>
            <div className='text-sm text-gray-600'>
              <p className='mb-4'>
                This will create sample interview data for testing the new
                interview system. It will create two interviews:
              </p>
              <ul className='list-disc list-inside space-y-1 mb-4'>
                <li>
                  <strong>Basic Estate Planning Interview</strong> - 15
                  questions covering basic estate planning needs
                </li>
                <li>
                  <strong>Advanced Estate Planning Interview</strong> - 4
                  questions for complex estate planning
                </li>
                <li>
                  <strong>Comprehensive Estate Planning Interview</strong> - 30
                  questions covering all aspects of estate planning
                </li>
              </ul>
              <p className='text-amber-600'>
                <strong>Note:</strong> This is for testing purposes only. Only
                run this in development environments.
              </p>
            </div>

            {error && (
              <Alert className='border-red-200 bg-red-50'>
                <AlertTriangle className='h-4 w-4 text-red-600' />
                <AlertDescription className='text-red-700'>
                  {error}
                </AlertDescription>
              </Alert>
            )}

            {success && (
              <Alert className='border-green-200 bg-green-50'>
                <CheckCircle className='h-4 w-4 text-green-600' />
                <AlertDescription className='text-green-700'>
                  Sample interviews created successfully!
                </AlertDescription>
              </Alert>
            )}

            {results && (
              <Card className='bg-gray-50'>
                <CardHeader>
                  <CardTitle className='text-sm'>Created Interviews</CardTitle>
                </CardHeader>
                <CardContent className='space-y-2 text-sm'>
                  <div>
                    <strong>Basic Interview ID (15 questions):</strong>{' '}
                    {results.basic.interview.id}
                  </div>
                  <div>
                    <strong>Advanced Interview ID (4 questions):</strong>{' '}
                    {results.advanced.interview.id}
                  </div>
                  <div>
                    <strong>Comprehensive Interview ID (30 questions):</strong>{' '}
                    {results.comprehensive.interview.id}
                  </div>
                </CardContent>
              </Card>
            )}

            <div className='flex justify-center'>
              <Button
                disabled={isCreating || success}
                className='flex items-center gap-2'
              >
                {isCreating ? (
                  <>
                    <Loader2 className='w-4 h-4 animate-spin' />
                    Creating Sample Data...
                  </>
                ) : success ? (
                  <>
                    <CheckCircle className='w-4 h-4' />
                    Sample Data Created
                  </>
                ) : (
                  'Create Sample Data'
                )}
              </Button>
            </div>

            {success && (
              <div className='text-center space-y-2'>
                <p className='text-sm text-gray-600'>
                  You can now test the interview system at:
                </p>
                <div className='space-y-1'>
                  <div>
                    <a
                      href='/dashboard/member/interview'
                      className='text-blue-600 hover:underline text-sm'
                      target='_blank'
                      rel='noopener noreferrer'
                    >
                      /dashboard/member/interview
                    </a>
                  </div>
                  <div>
                    <a
                      href='/dashboard/member/interview/new'
                      className='text-blue-600 hover:underline text-sm'
                      target='_blank'
                      rel='noopener noreferrer'
                    >
                      /dashboard/member/interview/new
                    </a>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
