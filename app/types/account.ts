// Account types for the application

// User status types
export type UserStatus =
  | 'active'
  | 'inactive'
  | 'pending'
  | 'deceased'
  | 'invited';

// Master role types
export type MasterRole =
  | 'Member'
  | 'Administrator'
  | 'WelonTrust'
  | 'Professional';

// User interface
export interface User {
  id: string;
  firstName: string;
  lastName: string;
  name: string;
  email: string;
  phoneNumber?: string;
  birthdate?: string;
  state?: string;
  cognitoId?: string; // Optional - null for invited users until they register
  role: MasterRole;
  subrole: string;
  status: UserStatus;
  journeyStatus?: string;
  createdAt: string;
  linkedAccounts?: LinkedAccount[];
  assignedWelonTrust?: WelonTrustAssignment;
  assignedWelonTrustId?: string;
  howDidYouHearAboutUs?: string; // Optional referral source
}

// Welon Trust assignment interface
export interface WelonTrustAssignment {
  id: string;
  welonTrustUserId: string;
  welonTrustName: string;
  welonTrustEmail: string;
  assignedAt: string;
  assignedBy: string; // Admin user ID who made the assignment
  status: 'active' | 'pending' | 'revoked';
}

// Link types
export type LinkType = 'primary' | 'secondary' | 'delegate' | 'emergency';

// Link status
export type LinkStatus = 'pending' | 'active' | 'revoked';

// Linked account interface
export interface LinkedAccount {
  id: string;
  userId: string; // The ID of the user who owns this link
  linkedUserId: string; // The ID of the user who is linked
  linkType: LinkType; // The type of link
  status: LinkStatus; // The status of the link
  permissions: string[]; // Permissions granted to the linked user
  createdAt: string; // When the link was created
  updatedAt: string; // When the link was last updated
  expiresAt?: string; // Optional expiration date
}

// Link request interface
export interface LinkRequest {
  email: string; // Email of the user to link
  linkType: LinkType; // Type of link to create
  permissions: string[]; // Permissions to grant
  expiresAt?: string; // Optional expiration date
}

// Available permissions for linked accounts
export const LINK_PERMISSIONS = {
  VIEW_DOCUMENTS: 'view_documents',
  EDIT_DOCUMENTS: 'edit_documents',
  VIEW_EMERGENCY_CONTACTS: 'view_emergency_contacts',
  EDIT_EMERGENCY_CONTACTS: 'edit_emergency_contacts',
  VIEW_DEAD_MAN_SWITCH: 'view_dead_man_switch',
  EDIT_DEAD_MAN_SWITCH: 'edit_dead_man_switch',
  EXECUTE_EMERGENCY_PROTOCOL: 'execute_emergency_protocol',
  VIEW_BILLING: 'view_billing',
  EDIT_BILLING: 'edit_billing',
};

// Permission sets based on link type
export const DEFAULT_PERMISSIONS: Record<LinkType, string[]> = {
  primary: [
    LINK_PERMISSIONS.VIEW_DOCUMENTS,
    LINK_PERMISSIONS.EDIT_DOCUMENTS,
    LINK_PERMISSIONS.VIEW_EMERGENCY_CONTACTS,
    LINK_PERMISSIONS.EDIT_EMERGENCY_CONTACTS,
    LINK_PERMISSIONS.VIEW_DEAD_MAN_SWITCH,
    LINK_PERMISSIONS.EDIT_DEAD_MAN_SWITCH,
    LINK_PERMISSIONS.VIEW_BILLING,
    LINK_PERMISSIONS.EDIT_BILLING,
  ],
  secondary: [
    LINK_PERMISSIONS.VIEW_DOCUMENTS,
    LINK_PERMISSIONS.VIEW_EMERGENCY_CONTACTS,
    LINK_PERMISSIONS.VIEW_DEAD_MAN_SWITCH,
  ],
  delegate: [
    LINK_PERMISSIONS.VIEW_DOCUMENTS,
    LINK_PERMISSIONS.EDIT_DOCUMENTS,
    LINK_PERMISSIONS.VIEW_EMERGENCY_CONTACTS,
  ],
  emergency: [
    LINK_PERMISSIONS.VIEW_DOCUMENTS,
    LINK_PERMISSIONS.VIEW_EMERGENCY_CONTACTS,
    LINK_PERMISSIONS.EXECUTE_EMERGENCY_PROTOCOL,
  ],
};
