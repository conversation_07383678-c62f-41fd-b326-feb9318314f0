// Care Document Builder Types

export type CareDocumentQuestionType =
  | 'text'
  | 'select'
  | 'radio'
  | 'file'
  | 'date'
  | 'phone';

export interface CareDocumentQuestionOption {
  id: string;
  label: string;
  value: string;
}

export interface DateConfiguration {
  minDate?: string;
  maxDate?: string;
  format?: 'DD/MM/YYYY' | 'MM/DD/YYYY' | 'YYYY-MM-DD' | 'DD.MM.YYYY';
  includeTime?: boolean;
  defaultToToday?: boolean;
  allowPastDates?: boolean;
  allowFutureDates?: boolean;
}

export interface CareDocumentQuestion {
  id: string;
  text: string;
  type: CareDocumentQuestionType;
  required: boolean;
  placeholder?: string;
  helpText?: string;
  options?: CareDocumentQuestionOption[];
  dateConfig?: DateConfiguration;
  allowMultiple: boolean; // Allow multiple answers (+ button functionality)
  order: number;
}

export interface CareDocumentTemplate {
  id: string;
  createdByEmail: string;
  documentType: string;
  title: string;
  description?: string;
  content?: string;
  questions?: CareDocumentQuestion[];
  version: number;
  status: 'Draft' | 'Active' | 'Archived';
  icon?: string;
  createdAt?: string;
  updatedAt?: string;
}

// Form data types for creating/updating questions
export interface CreateCareDocumentQuestionRequest {
  text: string;
  type: CareDocumentQuestionType;
  required: boolean;
  placeholder?: string;
  helpText?: string;
  options?: Omit<CareDocumentQuestionOption, 'id'>[];
  allowMultiple: boolean;
  order: number;
}

export interface UpdateCareDocumentQuestionRequest
  extends Partial<CreateCareDocumentQuestionRequest> {
  id: string;
}

// Answer types for when users fill out the template
export interface CareDocumentAnswer {
  questionId: string;
  values: string[]; // Array to support multiple answers
}

export interface CareDocumentResponse {
  templateId: string;
  answers: CareDocumentAnswer[];
}

// Validation rules
export interface CareDocumentValidationRule {
  type: 'required' | 'minLength' | 'maxLength' | 'pattern';
  value?: string | number;
  message: string;
}
