export interface Document {
  id: string;
  title: string;
  type:
    | 'Will'
    | 'Trust'
    | 'Healthcare_POA'
    | 'Financial_POA'
    | 'Advance_Directive'
    | 'Other';
  status:
    | 'draft'
    | 'sent'
    | 'approved'
    | 'rejected'
    | 'signed'
    | 'shipped'
    | 'received'
    | 'archived'
    | 'ready_for_review'
    | 'under_review'
    | 'ready_for_signing';
  dateCreated: string;
  lastModified: string;
  version: string;
  content?: string; // Populated template content
  userId: string;
  fileUrl?: string;
  signatureType?: 'electronic' | 'manual' | 'notarized';
  notarizationRequired?: boolean;
  executionDate?: string;
  trackingNumber?: string;
  rejectionReason?: string;
  templateId?: string;
  templateVersion?: number; // Version of template used when document was created
  documentState?: string;
  // Signed document fields
  signedDocumentUrl?: string;
  signedAt?: string;
  documentHash?: string;
}

export interface DocumentReviewData {
  documentId: string;
  reviewerId: string;
  reviewDate: string;
  comments?: string;
  approved: boolean;
}

export interface AttorneyReview {
  id: string;
  documentId: string;
  attorneyId: string;
  attorneyName: string;
  attorneyPhone: string;
  attorneyEmail: string;
  status: 'requested' | 'in_progress' | 'completed';
  requestDate: string;
  completionDate?: string;
  comments?: string;
}

export interface DocumentStatus {
  id: string;
  documentId: string;
  memberId: string;
  status: Document['status'];
  trackingLink?: string;
  receiptDate?: string;
  approvalDate?: string;
  updatedAt: string;
  statusMessage?: string;
}

export interface SigningPackage {
  id: string;
  documentId: string;
  packageUrl: string;
  instructions: string;
  shippingLabel: string;
  generatedAt: string;
  expiresAt: string;
}
