/**
 * Educational Content Types
 *
 * This file defines the types and interfaces for the Educational Content Delivery feature.
 */

/**
 * Content types supported by the system
 */
export enum ContentType {
  VIDEO = 'video',
  ARTICLE = 'article',
  INFOGRAPHIC = 'infographic',
  AVATAR = 'avatar',
  TOOLTIP = 'tooltip',
}

/**
 * Content status
 */
export enum ContentStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ARCHIVED = 'archived',
}

/**
 * Base interface for all educational content
 */
export interface EducationalContent {
  id: string;
  type: ContentType;
  title: string;
  description: string;
  tags: string[];
  status: ContentStatus;
  createdAt: string;
  updatedAt: string;
  version: number;
}

/**
 * Video content
 */
export interface VideoContent extends EducationalContent {
  type: ContentType.VIDEO;
  url: string;
  duration: number; // in seconds
  hasCaptions: boolean;
  hasAudioDescription: boolean;
  thumbnailUrl: string;
}

/**
 * Article content
 */
export interface ArticleContent extends EducationalContent {
  type: ContentType.ARTICLE;
  content: string; // Markdown content
  readingTime: number; // in minutes
  tableOfContents: { id: string; title: string; level: number }[];
}

/**
 * Infographic content
 */
export interface InfographicContent extends EducationalContent {
  type: ContentType.INFOGRAPHIC;
  imageUrl: string;
  altText: string;
}

/**
 * Avatar content
 */
export interface AvatarContent extends EducationalContent {
  type: ContentType.AVATAR;
  avatarImageUrl: string;
  responses: { question: string; answer: string }[];
}

/**
 * Tooltip content
 */
export interface TooltipContent extends EducationalContent {
  type: ContentType.TOOLTIP;
  content: string; // Short HTML content
  triggerText: string;
}

/**
 * Union type for all content types
 */
export type Content =
  | VideoContent
  | ArticleContent
  | InfographicContent
  | AvatarContent
  | TooltipContent;

/**
 * Content filter options
 */
export interface ContentFilters {
  type?: ContentType;
  tags?: string[];
  search?: string;
  status?: ContentStatus;
}

/**
 * Content analytics
 */
export interface ContentAnalytics {
  contentId: string;
  views: number;
  completionRate: number; // percentage
  averageRating: number; // 1-5 scale
  feedback: ContentFeedback[];
}

/**
 * Content feedback
 */
export interface ContentFeedback {
  id: string;
  contentId: string;
  rating: number; // 1-5 scale
  comment?: string;
  createdAt: string;
}

/**
 * Integration points for contextual content
 */
export enum ContentIntegrationPoint {
  ONBOARDING = 'onboarding',
  DOCUMENT_CREATION = 'document_creation',
  REVIEW_AND_SIGNING = 'review_and_signing',
  EMERGENCY_FEATURES = 'emergency_features',
  NOTIFICATIONS = 'notifications',
  LIVE_DOCUMENTS = 'live_documents',
}

/**
 * Contextual content display options
 */
export interface ContextualContentOptions {
  integrationPoint: ContentIntegrationPoint;
  triggerEvent?: string;
  displayType: 'tooltip' | 'popup' | 'inline';
  dismissible: boolean;
  showOnce: boolean;
}
