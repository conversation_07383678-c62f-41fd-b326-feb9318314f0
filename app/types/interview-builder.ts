// Interview Builder Types
export type QuestionType =
  | 'text'
  | 'radio'
  | 'select'
  | 'checkbox'
  | 'date'
  | 'number'
  | 'email'
  | 'phone';

export type QuestionCategory =
  | 'personal'
  | 'financial'
  | 'estate'
  | 'emergency'
  | 'medical';

export type ValidationRule = {
  type:
    | 'required'
    | 'minLength'
    | 'maxLength'
    | 'pattern'
    | 'min'
    | 'max'
    | 'email'
    | 'phone';
  value?: string | number;
  message: string;
};

export type ConditionalLogic = {
  condition:
    | 'equals'
    | 'not_equals'
    | 'contains'
    | 'greater_than'
    | 'less_than'
    | 'is_empty'
    | 'is_not_empty';
  value: string | number | boolean;
  nextQuestionId: string | null; // null means end interview
};

export interface QuestionOption {
  id: string;
  label: string;
  value: string;
}

export interface InterviewSetWithVersion {
  id: string;
  name: string;
  description: string | null;
  isActive: boolean | null;
  createdAt: string | null;
  updatedAt: string | null;
  latestVersionNumber: number | null;
  questionsCount: number;
}

export interface InterviewQuestion {
  id: string;
  text: string;
  type: QuestionType;
  category: QuestionCategory;
  required: boolean;
  helpText?: string;
  placeholder?: string;
  options?: QuestionOption[]; // For radio, select, checkbox types
  validation?: ValidationRule[];
  conditionalLogic?: ConditionalLogic[];
  defaultNextQuestionId?: string | null;
  templateMapping?: string; // Maps to template variable
  order: number;
  createdAt: string;
  updatedAt: string;
}

export interface InterviewQuestionNew {
  id: string;
  questionTitle: string;
  type: string;
  questionDescription?: string | null;
  placeholder?: string | null;
  options: string | number | boolean | object | any[] | null; // For radio, select, checkbox types
  order: number;
  // createdAt: string;
  // updatedAt: string;
  // validation?: ValidationRule[];
  // conditionalLogic?: ConditionalLogic[];
  // defaultNextQuestionId?: string | null;
  // templateMapping?: string; // Maps to template variable
  // category: QuestionCategory;
  // required: boolean;
}

export interface InterviewVersion {
  id: string;
  version: number;
  name: string;
  description: string;
  status: 'draft' | 'published' | 'archived';
  targetTemplate?: string; // Template this interview is designed for
  questions: InterviewQuestion[];
  totalQuestions: number;
  estimatedDuration: number; // in minutes
  yamlContent?: string; // YAML content for this version
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
  parentVersionId?: string; // Reference to the version this was duplicated from
}

export interface Interview {
  id: string; // Always "1" for the single interview
  currentVersion: number;
  versions: InterviewVersion[];
  createdAt?: string;
  updatedAt?: string;
}

export interface InterviewResponse {
  questionId: string;
  value: any;
  timestamp: string;
}

export interface InterviewSession {
  id: string;
  interviewId: string;
  userId: string;
  responses: Record<string, InterviewResponse>;
  currentQuestionId: string | null;
  isComplete: boolean;
  startedAt: string;
  completedAt?: string;
  progress: number; // 0-100
}

// API Request/Response types
export interface CreateVersionRequest {
  name: string;
  description: string;
  targetTemplate?: string;
  parentVersionId?: string; // Version to duplicate from
  yamlContent?: string;
}

export interface UpdateVersionRequest {
  name?: string;
  description?: string;
  targetTemplate?: string;
  status?: 'draft' | 'published' | 'archived';
  yamlContent?: string;
}

export interface CreateQuestionRequest {
  text: string;
  type: QuestionType;
  category: QuestionCategory;
  required: boolean;
  helpText?: string;
  placeholder?: string;
  options?: Omit<QuestionOption, 'id'>[];
  validation?: ValidationRule[];
  conditionalLogic?: ConditionalLogic[];
  defaultNextQuestionId?: string | null;
  templateMapping?: string;
  order: number;
}

export interface UpdateQuestionRequest extends Partial<CreateQuestionRequest> {
  id: string;
}

export interface InterviewResponse {
  interview: Interview;
}

export interface CreateInterviewRequest {
  name: string;
  description: string;
  targetTemplate?: string;
}

export interface UpdateInterviewRequest {
  name?: string;
  description?: string;
  targetTemplate?: string;
}

export interface InterviewFormData {
  name: string;
  description: string;
  targetTemplate: string;
}

export interface VersionListResponse {
  versions: InterviewVersion[];
  total: number;
  page: number;
  limit: number;
}

export interface QuestionListResponse {
  questions: InterviewQuestion[];
  total: number;
}

// Form state types for UI components
export interface QuestionFormData {
  text: string;
  type: QuestionType;
  category: QuestionCategory;
  required: boolean;
  helpText: string;
  placeholder: string;
  options: QuestionOption[];
  validation: ValidationRule[];
  conditionalLogic: ConditionalLogic[];
  defaultNextQuestionId: string;
  templateMapping: string;
}

export interface VersionFormData {
  name: string;
  description: string;
  targetTemplate: string;
  yamlContent?: string;
}

// Template variable mapping
export interface TemplateVariable {
  id: string;
  name: string;
  description: string;
  type: 'text' | 'number' | 'date' | 'boolean';
  category: string;
}

// Question builder UI state
export interface QuestionBuilderState {
  currentQuestion: InterviewQuestion | null;
  isEditing: boolean;
  showPreview: boolean;
  validationErrors: Record<string, string>;
}

// Interview preview state
export interface InterviewPreviewState {
  currentQuestionId: string | null;
  responses: Record<string, any>;
  questionHistory: string[];
  isComplete: boolean;
}

// Version builder state
export interface VersionBuilderState {
  currentVersion: InterviewVersion | null;
  activeTab: 'diagram' | 'editor' | 'questions' | 'preview';
  yamlContent: string;
  isEditing: boolean;
  validationErrors: Record<string, string>;
}
