/**
 * Notification System Types
 *
 * This file defines the types and interfaces for the comprehensive notification system
 * based on the functional specification document.
 */

/**
 * Notification categories as defined in the specification
 */
export enum NotificationCategory {
  INFORMATIONAL = 'informational',
  ACTION_REQUIRED = 'action_required',
  URGENT = 'urgent',
}

/**
 * Priority levels for notifications
 */
export enum NotificationPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
}

/**
 * Delivery channels for notifications
 */
export enum DeliveryChannel {
  EMAIL = 'email',
  SMS = 'sms',
  IN_APP = 'in_app',
}

/**
 * Notification status tracking
 */
export enum NotificationStatus {
  SENT = 'sent',
  DELIVERED = 'delivered',
  OPENED = 'opened',
  READ = 'read',
  FAILED = 'failed',
}

/**
 * Event types that trigger notifications
 */
export enum NotificationEventType {
  DOCUMENT_UPDATE = 'document_update',
  DOCUMENT_STATUS_CHANGE = 'document_status_change',
  EMERGENCY_ACCESS = 'emergency_access',
  MISSED_PAYMENT = 'missed_payment',
  POLICY_CHANGE = 'policy_change',
  ROUTINE_CHECK_IN = 'routine_check_in',
  QUARTERLY_REVIEW_START = 'quarterly_review_start',
  SIGN_OFF_DEADLINE_REMINDER = 'sign_off_deadline_reminder',
  ATTORNEY_REVIEW_REQUEST = 'attorney_review_request',
  LINKED_ACCOUNT_INVITATION = 'linked_account_invitation',
  WELON_TRUST_ASSIGNMENT = 'welon_trust_assignment',
}

/**
 * Base notification interface
 */
export interface Notification {
  id: string;
  userId: string;
  eventType: NotificationEventType;
  category: NotificationCategory;
  priority: NotificationPriority;
  title: string;
  message: string;
  actionUrl?: string;
  actionText?: string;
  status: NotificationStatus;
  channels: DeliveryChannel[];
  createdAt: string;
  updatedAt: string;
  readAt?: string;
  metadata?: Record<string, any>;
}

/**
 * Notification preferences for users
 */
export interface NotificationPreferences {
  userId: string;
  preferences: {
    [key in NotificationCategory]: {
      email: boolean;
      sms: boolean;
      inApp: boolean;
    };
  };
  updatedAt: string;
}

/**
 * Notification delivery tracking
 */
export interface NotificationDelivery {
  id: string;
  notificationId: string;
  channel: DeliveryChannel;
  status: NotificationStatus;
  deliveredAt?: string;
  openedAt?: string;
  clickedAt?: string;
  errorMessage?: string;
  retryCount: number;
}

/**
 * Notification template for different event types
 */
export interface NotificationTemplate {
  eventType: NotificationEventType;
  category: NotificationCategory;
  priority: NotificationPriority;
  titleTemplate: string;
  messageTemplate: string;
  actionUrlTemplate?: string;
  actionTextTemplate?: string;
  defaultChannels: DeliveryChannel[];
}

/**
 * Notification filter options for history views
 */
export interface NotificationFilters {
  category?: NotificationCategory;
  priority?: NotificationPriority;
  status?: NotificationStatus;
  eventType?: NotificationEventType;
  dateFrom?: string;
  dateTo?: string;
  unreadOnly?: boolean;
}

/**
 * Notification statistics for admin dashboard
 */
export interface NotificationStats {
  totalSent: number;
  totalDelivered: number;
  totalOpened: number;
  totalFailed: number;
  deliveryRate: number;
  openRate: number;
  byCategory: {
    [key in NotificationCategory]: {
      sent: number;
      delivered: number;
      opened: number;
    };
  };
  byChannel: {
    [key in DeliveryChannel]: {
      sent: number;
      delivered: number;
      failed: number;
    };
  };
}

/**
 * Notification context for real-time updates
 */
export interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  preferences: NotificationPreferences | null;
  isLoading: boolean;
  error: string | null;

  // Actions
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (notificationId: string) => Promise<void>;
  updatePreferences: (
    preferences: Partial<NotificationPreferences['preferences']>
  ) => Promise<void>;
  fetchNotifications: (filters?: NotificationFilters) => Promise<void>;
  refreshNotifications: () => Promise<void>;
}

/**
 * API request/response types
 */
export interface SendNotificationRequest {
  userId: string;
  eventType: NotificationEventType;
  message: string;
  priority: NotificationPriority;
  metadata?: Record<string, any>;
}

export interface SendNotificationResponse {
  success: boolean;
  notificationId: string;
  error?: string;
}

export interface GetNotificationHistoryRequest {
  userId: string;
  filters?: NotificationFilters;
  limit?: number;
  offset?: number;
}

export interface GetNotificationHistoryResponse {
  notifications: Notification[];
  total: number;
  hasMore: boolean;
}

export interface UpdateNotificationPreferencesRequest {
  userId: string;
  preferences: Partial<NotificationPreferences['preferences']>;
}

export interface UpdateNotificationPreferencesResponse {
  success: boolean;
  error?: string;
}
