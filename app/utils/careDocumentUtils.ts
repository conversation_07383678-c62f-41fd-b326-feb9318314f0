// Utility functions for care documents

export interface CareDocumentTemplate {
  id: string;
  title: string;
  documentType:
    | 'EmergencyContacts'
    | 'PetCare'
    | 'DigitalAssets'
    | 'EndOfLifeWishes'
    | 'MedicalDirectives'
    | 'Other';
  content: string;
  description: string;
}

// Pre-built templates for common document types
export const documentTemplates: CareDocumentTemplate[] = [
  {
    id: 'emergency-contacts-template',
    title: 'Emergency Contacts Template',
    documentType: 'EmergencyContacts',
    description: 'Template for organizing emergency contact information',
    content: `Emergency Contacts

Primary Emergency Contact:
Name: [Full Name]
Relationship: [Relationship to you]
Phone: [Primary Phone Number]
Email: [Email Address]
Address: [Full Address]

Secondary Emergency Contact:
Name: [Full Name]
Relationship: [Relationship to you]
Phone: [Primary Phone Number]
Email: [Email Address]
Address: [Full Address]

Medical Emergency Contact:
Name: [Full Name]
Relationship: [Relationship to you]
Phone: [Primary Phone Number]
Email: [Email Address]
Medical Information Access: [Yes/No]

Additional Notes:
[Any special instructions or additional information]

Last Updated: [Date]
Next Review: [Date]`,
  },
  {
    id: 'pet-care-template',
    title: 'Pet Care Instructions Template',
    documentType: 'PetCare',
    description: 'Template for pet care instructions and emergency information',
    content: `Pet Care Instructions

Pet Information:
Name: [Pet Name]
Species/Breed: [Dog/Cat/Other - Breed]
Age: [Age]
Weight: [Weight]
Microchip ID: [Microchip Number]

Daily Care:
Feeding Schedule: [Times and amounts]
Food Type/Brand: [Specific food requirements]
Medications: [List any medications and schedules]
Exercise Needs: [Daily exercise requirements]

Veterinary Information:
Primary Veterinarian: [Name and Contact]
Emergency Vet: [Name and Contact]
Medical Conditions: [Any ongoing health issues]
Allergies: [Known allergies]

Emergency Contacts:
Primary Caregiver: [Name and Contact]
Backup Caregiver: [Name and Contact]
Pet Boarding Facility: [Name and Contact]

Special Instructions:
[Any special care instructions, behavioral notes, or preferences]

Last Updated: [Date]
Next Review: [Date]`,
  },
  {
    id: 'digital-assets-template',
    title: 'Digital Assets Template',
    documentType: 'DigitalAssets',
    description:
      'Template for organizing digital assets and account information',
    content: `Digital Assets Inventory

Financial Accounts:
Bank Accounts: [List institutions and account types]
Investment Accounts: [Brokerage, retirement accounts]
Cryptocurrency: [Exchanges, wallet information]
Payment Services: [PayPal, Venmo, etc.]

Social Media & Communication:
Email Accounts: [Primary and secondary accounts]
Social Media: [Facebook, Twitter, Instagram, etc.]
Messaging Apps: [WhatsApp, Telegram, etc.]

Digital Storage:
Cloud Storage: [Google Drive, Dropbox, iCloud, etc.]
Photo Storage: [Google Photos, Amazon Photos, etc.]
Document Storage: [Important files locations]

Subscriptions & Services:
Streaming Services: [Netflix, Spotify, etc.]
Software Subscriptions: [Adobe, Microsoft, etc.]
Domain Names: [Registered domains]
Web Hosting: [Hosting services]

Important Notes:
- Account recovery information stored separately
- Two-factor authentication details
- Trusted contacts for account recovery

Last Updated: [Date]
Next Review: [Date]`,
  },
  {
    id: 'end-of-life-wishes-template',
    title: 'End of Life Wishes Template',
    documentType: 'EndOfLifeWishes',
    description: 'Template for documenting end of life preferences and wishes',
    content: `End of Life Wishes

Medical Care Preferences:
Life Support: [Preferences regarding life support]
Pain Management: [Pain management preferences]
Organ Donation: [Yes/No and specific organs]
Body Donation: [Research/medical education preferences]

Funeral/Memorial Preferences:
Service Type: [Funeral, memorial service, celebration of life]
Location: [Preferred location]
Religious/Spiritual Elements: [Specific requests]
Music: [Preferred songs or music]
Readings: [Poems, scriptures, or other readings]

Final Arrangements:
Burial vs. Cremation: [Preference and specific instructions]
Cemetery/Location: [Specific location preferences]
Headstone/Memorial: [Inscription preferences]

Personal Messages:
Messages to Family: [Personal messages to loved ones]
Messages to Friends: [Messages to close friends]
Special Requests: [Any other personal wishes]

Important Documents Location:
Will: [Location]
Advanced Directives: [Location]
Insurance Policies: [Location]

Last Updated: [Date]
Next Review: [Date]`,
  },
  {
    id: 'medical-directives-template',
    title: 'Medical Directives Template',
    documentType: 'MedicalDirectives',
    description: 'Template for medical directives and healthcare preferences',
    content: `Medical Directives Summary

Healthcare Proxy:
Primary: [Name and Contact Information]
Secondary: [Name and Contact Information]

Medical Preferences:
Resuscitation: [DNR preferences]
Mechanical Ventilation: [Preferences]
Artificial Nutrition/Hydration: [Preferences]
Dialysis: [Preferences]
Antibiotics: [Preferences for life-threatening infections]

Current Medical Information:
Primary Care Physician: [Name and Contact]
Specialists: [List of current specialists]
Current Medications: [List with dosages]
Allergies: [Known allergies and reactions]
Medical Conditions: [Current diagnoses]

Emergency Medical Information:
Blood Type: [Blood type]
Emergency Medications: [EpiPen, inhalers, etc.]
Medical Devices: [Pacemaker, insulin pump, etc.]
Insurance Information: [Primary and secondary insurance]

Comfort Care Preferences:
Pain Management: [Preferences for pain relief]
Spiritual Care: [Religious or spiritual preferences]
Visitor Preferences: [Who should be contacted/allowed to visit]

Legal Documents:
Living Will: [Location and date]
Healthcare Proxy Form: [Location and date]
HIPAA Authorization: [Location and date]

Last Updated: [Date]
Next Review: [Date]`,
  },
];

// Document type labels for display
export const documentTypeLabels = {
  EmergencyContacts: 'Emergency Contacts',
  PetCare: 'Pet Care Instructions',
  DigitalAssets: 'Digital Assets',
  EndOfLifeWishes: 'End of Life Wishes',
  MedicalDirectives: 'Medical Directives',
  Other: 'Other',
};

// Reminder frequency labels
export const reminderFrequencyLabels = {
  Monthly: 'Monthly',
  Quarterly: 'Every 3 months',
  SemiAnnually: 'Every 6 months',
  Annually: 'Annually',
};

// Helper function to get template by document type
export const getTemplateByType = (
  documentType: string
): CareDocumentTemplate | undefined => {
  return documentTemplates.find(
    template => template.documentType === documentType
  );
};

// Helper function to calculate next review date
export const calculateNextReviewDate = (
  frequency: string,
  fromDate?: Date
): Date => {
  const baseDate = fromDate || new Date();
  const date = new Date(baseDate);

  switch (frequency) {
    case 'Monthly':
      date.setMonth(date.getMonth() + 1);
      break;
    case 'Quarterly':
      date.setMonth(date.getMonth() + 3);
      break;
    case 'SemiAnnually':
      date.setMonth(date.getMonth() + 6);
      break;
    case 'Annually':
      date.setFullYear(date.getFullYear() + 1);
      break;
    default:
      date.setMonth(date.getMonth() + 6); // Default to semi-annually
  }

  return date;
};

// Helper function to determine document status
export const getDocumentStatus = (
  nextReviewDate?: string
): 'current' | 'due-soon' | 'overdue' => {
  if (!nextReviewDate) return 'current';

  const now = new Date();
  const reviewDate = new Date(nextReviewDate);
  const daysUntilReview = Math.ceil(
    (reviewDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
  );

  if (daysUntilReview < 0) return 'overdue';
  if (daysUntilReview <= 30) return 'due-soon';
  return 'current';
};

// Helper function to format dates consistently
export const formatDate = (dateString?: string): string => {
  if (!dateString) return 'Not set';

  try {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  } catch (error) {
    return 'Invalid date';
  }
};

// Helper function to format relative time
export const formatRelativeTime = (dateString?: string): string => {
  if (!dateString) return 'Not set';

  try {
    const date = new Date(dateString);
    const now = new Date();
    const diffInDays = Math.ceil(
      (date.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
    );

    if (diffInDays < 0) {
      return `${Math.abs(diffInDays)} days overdue`;
    } else if (diffInDays === 0) {
      return 'Due today';
    } else if (diffInDays === 1) {
      return 'Due tomorrow';
    } else if (diffInDays <= 7) {
      return `Due in ${diffInDays} days`;
    } else if (diffInDays <= 30) {
      return `Due in ${Math.ceil(diffInDays / 7)} weeks`;
    } else {
      return `Due in ${Math.ceil(diffInDays / 30)} months`;
    }
  } catch (error) {
    return 'Invalid date';
  }
};

// Helper function to validate document content
export const validateDocumentContent = (
  content: string,
  documentType: string
): string[] => {
  const errors: string[] = [];

  if (!content || content.trim().length === 0) {
    errors.push('Content is required');
    return errors;
  }

  if (content.length < 10) {
    errors.push('Content must be at least 10 characters long');
  }

  if (content.length > 10000) {
    errors.push('Content must be less than 10,000 characters');
  }

  // Type-specific validations could be added here
  switch (documentType) {
    case 'EmergencyContacts':
      if (
        !content.toLowerCase().includes('contact') &&
        !content.toLowerCase().includes('phone')
      ) {
        errors.push('Emergency contacts should include contact information');
      }
      break;
    case 'PetCare':
      if (
        !content.toLowerCase().includes('pet') &&
        !content.toLowerCase().includes('care')
      ) {
        errors.push('Pet care documents should include pet care information');
      }
      break;
    // Add more type-specific validations as needed
  }

  return errors;
};
