import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';

const client = generateClient<Schema>();

/**
 * Check if an email already exists in the database using authenticated Lambda function
 * @param email - The email address to check
 * @returns Promise<{exists: boolean, message: string}>
 */
export async function checkEmailExists(email: string): Promise<{
  exists: boolean;
  message: string;
  error?: string;
}> {
  try {
    // Try to use unauthenticated API access for email checking
    const { data, errors } = await client.queries.checkEmail(
      {
        email: email,
      },
      {
        authMode: 'iam',
      }
    );

    if (errors && errors.length > 0) {
      console.error('GraphQL errors:', errors);
      return {
        exists: false,
        message: 'Error checking email',
        error: errors[0].message || 'GraphQL error',
      };
    }

    if (!data) {
      return {
        exists: false,
        message: 'No response from server',
        error: 'No response from server',
      };
    }

    // Parse the JSON response from Lambda
    const result = typeof data === 'string' ? JSON.parse(data) : data;

    return {
      exists: result.exists || false,
      message: result.message || 'Unknown response',
      error: result.error,
    };
  } catch (error) {
    console.error('Error checking email:', error);
    return {
      exists: false,
      message: 'Error checking email',
      error: 'Network error',
    };
  }
}
