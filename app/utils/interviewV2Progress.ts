'use client';

import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { getCurrentUser } from 'aws-amplify/auth';

export type InterviewProgressV2Data = {
  status?: string | null;
  currentStep?: string | null;
  stepsData?: any;
  createdAt?: string | null;
  updatedAt?: string | null;
};

const client = generateClient<Schema>();

async function getCurrentUserRecord() {
  const cognito = await getCurrentUser();
  const { data: users, errors } = await client.models.User.list({
    filter: { cognitoId: { eq: cognito.userId } },
    selectionSet: [
      'id',
      'interviewProgressV2.status',
      'interviewProgressV2.currentStep',
      'interviewProgressV2.stepsData',
      'interviewProgressV2.createdAt',
      'interviewProgressV2.updatedAt',
    ] as const,
  });
  if (errors) throw new Error(errors.map(e => e.message).join(', '));
  const user = users?.[0];
  if (!user) throw new Error('User not found for current session');
  return user as {
    id: string;
    interviewProgressV2?: InterviewProgressV2Data | null;
  };
}

export async function loadInterviewProgressV2(): Promise<InterviewProgressV2Data> {
  try {
    const user = await getCurrentUserRecord();
    const progress = (user.interviewProgressV2 ||
      {}) as InterviewProgressV2Data;

    // Parse stepsData if it is stored as a JSON string
    let stepsData: any = progress.stepsData;
    if (typeof stepsData === 'string') {
      try {
        stepsData = JSON.parse(stepsData as string);
      } catch {
        stepsData = {};
      }
    }

    return {
      status: progress.status ?? 'not_started',
      currentStep: progress.currentStep ?? 'profile',
      stepsData: stepsData ?? {},
      createdAt: progress.createdAt ?? new Date().toISOString(),
      updatedAt: progress.updatedAt ?? new Date().toISOString(),
    };
  } catch (e) {
    console.error('Failed to load InterviewProgressV2:', e);
    return {
      status: 'not_started',
      currentStep: 'profile',
      stepsData: {},
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
  }
}

export async function saveInterviewProgressV2(
  partial: Partial<InterviewProgressV2Data>,
  options?: { mergeStepsData?: boolean }
): Promise<InterviewProgressV2Data> {
  const { mergeStepsData = true } = options || {};
  const user = await getCurrentUserRecord();
  const existing = (user.interviewProgressV2 || {}) as InterviewProgressV2Data;

  // Ensure existing stepsData is an object
  const existingSteps = (() => {
    if (typeof existing.stepsData === 'string') {
      try {
        return JSON.parse(existing.stepsData);
      } catch {
        return {};
      }
    }
    return existing.stepsData || {};
  })();

  const incomingSteps = partial.stepsData || {};
  const mergedSteps = mergeStepsData
    ? { ...existingSteps, ...incomingSteps }
    : incomingSteps === undefined
      ? existingSteps
      : incomingSteps;

  const next: InterviewProgressV2Data = {
    status: partial.status ?? existing.status ?? 'in_progress',
    currentStep: partial.currentStep ?? existing.currentStep ?? 'profile',
    stepsData: mergedSteps,
    createdAt: existing.createdAt || new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  const { data, errors } = await client.models.User.update({
    id: user.id,
    interviewProgressV2: {
      ...next,
      // Important: store JSON as string for a.json() fields
      stepsData: JSON.stringify(next.stepsData || {}),
    } as any,
  });
  if (errors) throw new Error(errors.map(e => e.message).join(', '));
  if (!data) throw new Error('User update returned no data');
  return next;
}

export async function resetInterviewProgressV2(): Promise<InterviewProgressV2Data> {
  const user = await getCurrentUserRecord();
  const reset: InterviewProgressV2Data = {
    status: 'not_started',
    currentStep: 'profile',
    stepsData: {},
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };
  const { data, errors } = await client.models.User.update({
    id: user.id,
    interviewProgressV2: {
      ...reset,
      stepsData: JSON.stringify({}),
    } as any,
  });
  if (errors) throw new Error(errors.map(e => e.message).join(', '));
  if (!data) throw new Error('User update returned no data');
  return reset;
}
