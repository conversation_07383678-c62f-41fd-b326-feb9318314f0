import { generateClient } from 'aws-amplify/data';
import type { Schema } from '../../amplify/data/resource';

const client = generateClient<Schema>();

// Function to parse user agent into OS and browser information
export const parseDeviceInfo = (
  userAgent: string
): { os: string; browser: string; device: string } => {
  if (!userAgent)
    return {
      os: 'Unknown OS',
      browser: 'Unknown Browser',
      device: 'Unknown Device',
    };

  // Parse Operating System
  let os = 'Unknown OS';
  if (/Windows NT 10\.0/.test(userAgent)) os = 'Windows 10';
  else if (/Windows NT 6\.3/.test(userAgent)) os = 'Windows 8.1';
  else if (/Windows NT 6\.2/.test(userAgent)) os = 'Windows 8';
  else if (/Windows NT 6\.1/.test(userAgent)) os = 'Windows 7';
  else if (/Windows/.test(userAgent)) os = 'Windows';
  else if (/Mac OS X 10[._](\d+)/.test(userAgent)) {
    const match = userAgent.match(/Mac OS X 10[._](\d+)/);
    os = `macOS 10.${match?.[1] || 'x'}`;
  } else if (/Mac/.test(userAgent)) os = 'macOS';
  else if (/Android (\d+)/.test(userAgent)) {
    const match = userAgent.match(/Android (\d+)/);
    os = `Android ${match?.[1] || 'x'}`;
  } else if (/iPhone OS (\d+)/.test(userAgent)) {
    const match = userAgent.match(/iPhone OS (\d+)/);
    os = `iOS ${match?.[1] || 'x'}`;
  } else if (/iPad/.test(userAgent)) os = 'iPadOS';
  else if (/Linux/.test(userAgent)) os = 'Linux';

  // Parse Browser
  let browser = 'Unknown Browser';
  if (/Chrome\/(\d+)/.test(userAgent) && !/Edge/.test(userAgent)) {
    const match = userAgent.match(/Chrome\/(\d+)/);
    browser = `Chrome ${match?.[1] || 'x'}`;
  } else if (/Firefox\/(\d+)/.test(userAgent)) {
    const match = userAgent.match(/Firefox\/(\d+)/);
    browser = `Firefox ${match?.[1] || 'x'}`;
  } else if (/Safari\//.test(userAgent) && !/Chrome/.test(userAgent)) {
    browser = 'Safari';
  } else if (/Edge\/(\d+)/.test(userAgent)) {
    const match = userAgent.match(/Edge\/(\d+)/);
    browser = `Edge ${match?.[1] || 'x'}`;
  }

  // Determine device type
  let device = 'Desktop';
  if (/Mobile/.test(userAgent)) device = 'Mobile';
  else if (/Tablet|iPad/.test(userAgent)) device = 'Tablet';

  return { os, browser, device };
};

// Function to fetch real login history from LoginHistory model
export const fetchLoginHistory = async (userEmail: string) => {
  try {
    console.log('Fetching login history for:', userEmail);

    // Fetch login history records for the user from the LoginHistory table
    const { data: loginHistoryRecords, errors } =
      await client.models.LoginHistory.list({
        filter: {
          email: { eq: userEmail },
          success: { eq: true }, // Only show successful logins
        },
        limit: 20, // Get last 20 successful login attempts
      });

    if (errors) {
      console.error('Error fetching login history:', errors);
      throw new Error('Failed to fetch login history');
    }

    console.log('Raw login history records:', loginHistoryRecords);

    // Transform the data to match our interface
    const transformedHistory =
      loginHistoryRecords?.map(record => {
        const deviceInfo = parseDeviceInfo(record.userAgent || '');
        return {
          id: record.id || `login-${record.email}-${record.attemptTime}`,
          timestamp: record.attemptTime || new Date().toISOString(),
          device: `${deviceInfo.os} • ${deviceInfo.browser}`,
          deviceType: deviceInfo.device,
          os: deviceInfo.os,
          browser: deviceInfo.browser,
          location: deviceInfo.device, // Show device type instead of IP
          success: record.success || false,
        };
      }) || [];

    // Sort by timestamp descending (most recent first)
    const sortedHistory = transformedHistory.sort(
      (a, b) =>
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );

    console.log('Transformed login history:', sortedHistory);

    // If we have history records, return them
    if (sortedHistory.length > 0) {
      return sortedHistory;
    }

    // If no history found, show current session
    const currentDeviceInfo = parseDeviceInfo(navigator.userAgent);
    return [
      {
        id: 'current-session',
        timestamp: new Date().toISOString(),
        device: `${currentDeviceInfo.os} • ${currentDeviceInfo.browser}`,
        deviceType: currentDeviceInfo.device,
        os: currentDeviceInfo.os,
        browser: currentDeviceInfo.browser,
        location: currentDeviceInfo.device,
        success: true,
      },
    ];
  } catch (error) {
    console.error('Error in fetchLoginHistory:', error);

    // Return current session info as fallback
    const fallbackDeviceInfo = parseDeviceInfo(navigator.userAgent);
    return [
      {
        id: 'current-session-fallback',
        timestamp: new Date().toISOString(),
        device: `${fallbackDeviceInfo.os} • ${fallbackDeviceInfo.browser}`,
        deviceType: fallbackDeviceInfo.device,
        os: fallbackDeviceInfo.os,
        browser: fallbackDeviceInfo.browser,
        location: fallbackDeviceInfo.device,
        success: true,
      },
    ];
  }
};

// Type definition for login history item
export interface LoginHistoryItem {
  id: string;
  timestamp: string;
  device: string;
  deviceType: string;
  os: string;
  browser: string;
  location: string;
  success: boolean;
}
