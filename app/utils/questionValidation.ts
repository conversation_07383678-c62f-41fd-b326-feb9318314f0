/**
 * Question validation utilities for interview answers
 */

export type QuestionValidationType =
  | 'number'
  | 'email'
  | 'phone'
  | 'required'
  | 'text'
  | 'date'
  | 'domestic_address'
  | 'international_address'
  | 'full_name'
  | 'currency'
  | 'percentage'
  | 'ssn'
  | 'tax_id';

export interface ValidationResult {
  isValid: boolean;
  errorMessage?: string;
}

export interface ValidationOptions {
  required?: boolean;
  maxLength?: number;
  minLength?: number;
  sanitizeHtml?: boolean;
}

/**
 * Sanitize HTML content to prevent XSS attacks
 */
export function sanitizeHtml(value: string): string {
  if (!value) return value;

  // Remove HTML tags and decode HTML entities
  return value
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&amp;/g, '&')
    .replace(/&quot;/g, '"')
    .replace(/&#x27;/g, "'")
    .replace(/&#x2F;/g, '/');
  // Removed .trim() to preserve spaces during input
}

/**
 * Sanitize HTML content and trim for validation purposes
 */
export function sanitizeHtmlForValidation(value: string): string {
  if (!value) return value;

  // Remove HTML tags and decode HTML entities, then trim
  return value
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&amp;/g, '&')
    .replace(/&quot;/g, '"')
    .replace(/&#x27;/g, "'")
    .replace(/&#x2F;/g, '/')
    .trim();
}

/**
 * Validate required field
 */
export function validateRequired(value: string): ValidationResult {
  const trimmedValue = value?.trim() || '';

  if (!trimmedValue) {
    return {
      isValid: false,
      errorMessage: 'This field is required',
    };
  }

  return { isValid: true };
}

/**
 * Validate text length
 */
export function validateLength(
  value: string,
  options: ValidationOptions = {}
): ValidationResult {
  const trimmedValue = value?.trim() || '';
  const { minLength = 0, maxLength = 5000 } = options;

  if (trimmedValue.length < minLength) {
    return {
      isValid: false,
      errorMessage: `Minimum length is ${minLength} characters`,
    };
  }

  if (trimmedValue.length > maxLength) {
    return {
      isValid: false,
      errorMessage: `Maximum length is ${maxLength} characters`,
    };
  }

  return { isValid: true };
}

/**
 * Validate a number input
 */
export function validateNumber(
  value: string,
  options: ValidationOptions = {}
): ValidationResult {
  const trimmedValue = value?.trim() || '';

  // Check required first
  if (options.required) {
    const requiredResult = validateRequired(trimmedValue);
    if (!requiredResult.isValid) return requiredResult;
  }

  if (!trimmedValue) {
    return { isValid: true }; // Empty values are allowed if not required
  }

  // Check if it's a valid number
  if (isNaN(Number(trimmedValue))) {
    return {
      isValid: false,
      errorMessage: 'Please enter a valid number',
    };
  }

  // Check for valid number format (no leading/trailing spaces, no multiple decimals)
  const numberRegex = /^-?\d*\.?\d+$/;
  if (!numberRegex.test(trimmedValue)) {
    return {
      isValid: false,
      errorMessage: 'Please enter a valid number',
    };
  }

  return { isValid: true };
}

/**
 * Validate an email input
 */
export function validateEmail(
  value: string,
  options: ValidationOptions = {}
): ValidationResult {
  const trimmedValue = value?.trim() || '';

  // Check required first
  if (options.required) {
    const requiredResult = validateRequired(trimmedValue);
    if (!requiredResult.isValid) return requiredResult;
  }

  if (!trimmedValue) {
    return { isValid: true }; // Empty values are allowed if not required
  }

  // Basic email regex pattern
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  if (!emailRegex.test(trimmedValue)) {
    return {
      isValid: false,
      errorMessage: 'Please enter a valid email address',
    };
  }

  // Additional checks for common email issues
  if (trimmedValue.length > 255) {
    return {
      isValid: false,
      errorMessage: 'Email address is too long',
    };
  }

  if (
    trimmedValue.includes('..') ||
    trimmedValue.startsWith('.') ||
    trimmedValue.endsWith('.')
  ) {
    return {
      isValid: false,
      errorMessage: 'Please enter a valid email address',
    };
  }

  return { isValid: true };
}

/**
 * Validate a phone number input
 */
export function validatePhone(
  value: string,
  options: ValidationOptions = {}
): ValidationResult {
  const trimmedValue = value?.trim() || '';

  // Check required first
  if (options.required) {
    const requiredResult = validateRequired(trimmedValue);
    if (!requiredResult.isValid) return requiredResult;
  }

  if (!trimmedValue) {
    return { isValid: true }; // Empty values are allowed if not required
  }

  // US phone number regex - supports various formats
  const phoneRegex =
    /^(\+1\s?)?(\([0-9]{3}\)|[0-9]{3})[\s\-]?[0-9]{3}[\s\-]?[0-9]{4}$/;

  if (!phoneRegex.test(trimmedValue)) {
    return {
      isValid: false,
      errorMessage:
        'Please enter a valid US phone number (e.g., (*************)',
    };
  }

  return { isValid: true };
}

/**
 * Validate a date input in MM/DD/YYYY format
 */
export function validateDate(
  value: string,
  options: ValidationOptions = {}
): ValidationResult {
  const trimmedValue = value?.trim() || '';

  // Check required first
  if (options.required) {
    const requiredResult = validateRequired(trimmedValue);
    if (!requiredResult.isValid) return requiredResult;
  }

  if (!trimmedValue) {
    return { isValid: true }; // Empty values are allowed if not required
  }

  // Date regex for MM/DD/YYYY format
  const dateRegex = /^(0[1-9]|1[0-2])\/(0[1-9]|[12]\d|3[01])\/\d{4}$/;

  if (!dateRegex.test(trimmedValue)) {
    return {
      isValid: false,
      errorMessage: 'Please enter a valid date in MM/DD/YYYY format',
    };
  }

  // Additional validation for actual date validity
  const [month, day, year] = trimmedValue.split('/').map(Number);
  const date = new Date(year, month - 1, day);

  if (
    date.getFullYear() !== year ||
    date.getMonth() !== month - 1 ||
    date.getDate() !== day
  ) {
    return {
      isValid: false,
      errorMessage:
        'Please enter a valid date (check for leap years and month limits)',
    };
  }

  return { isValid: true };
}

/**
 * Validate domestic (US) address
 */
export function validateDomesticAddress(
  value: string,
  options: ValidationOptions = {}
): ValidationResult {
  const trimmedValue = value?.trim() || '';

  // Check required first
  if (options.required) {
    const requiredResult = validateRequired(trimmedValue);
    if (!requiredResult.isValid) return requiredResult;
  }

  if (!trimmedValue) {
    return { isValid: true }; // Empty values are allowed if not required
  }

  // Basic validation for domestic address format
  // Should contain street, city, state, and ZIP
  const addressParts = trimmedValue.split(',').map(part => part.trim());

  if (addressParts.length < 3) {
    return {
      isValid: false,
      errorMessage: 'Please enter a complete address: Street, City, State ZIP',
    };
  }

  // Validate ZIP code in the last part
  const lastPart = addressParts[addressParts.length - 1];
  const zipRegex = /\b\d{5}(-\d{4})?\b/;

  if (!zipRegex.test(lastPart)) {
    return {
      isValid: false,
      errorMessage:
        'Please include a valid ZIP code (e.g., 12345 or 12345-6789)',
    };
  }

  // Validate state (should be 2 letters)
  const stateRegex = /\b[A-Z]{2}\b/i;
  if (!stateRegex.test(lastPart)) {
    return {
      isValid: false,
      errorMessage: 'Please include a valid 2-letter state abbreviation',
    };
  }

  return { isValid: true };
}

/**
 * Validate international address
 */
export function validateInternationalAddress(
  value: string,
  options: ValidationOptions = {}
): ValidationResult {
  const trimmedValue = value?.trim() || '';

  // Check required first
  if (options.required) {
    const requiredResult = validateRequired(trimmedValue);
    if (!requiredResult.isValid) return requiredResult;
  }

  if (!trimmedValue) {
    return { isValid: true }; // Empty values are allowed if not required
  }

  // Basic validation for international address
  // Should have at least street and city/country information
  const addressParts = trimmedValue.split(',').map(part => part.trim());

  if (addressParts.length < 2) {
    return {
      isValid: false,
      errorMessage:
        'Please enter a complete address with at least street and city/country (e.g., 123 Main St, New York)',
    };
  }

  // Check minimum length for meaningful address
  if (trimmedValue.length < 10) {
    return {
      isValid: false,
      errorMessage: 'Please enter a more complete address',
    };
  }

  return { isValid: true };
}

/**
 * Validate text input with HTML sanitization and length checks
 */
export function validateText(
  value: string,
  options: ValidationOptions = {}
): ValidationResult {
  let processedValue = value || '';

  // Sanitize HTML if requested (use validation version that trims)
  if (options.sanitizeHtml !== false) {
    // Default to true
    processedValue = sanitizeHtmlForValidation(processedValue);
  }

  // Check required first
  if (options.required) {
    const requiredResult = validateRequired(processedValue);
    if (!requiredResult.isValid) return requiredResult;
  }

  // Check length constraints
  const lengthResult = validateLength(processedValue, options);
  if (!lengthResult.isValid) return lengthResult;

  return { isValid: true };
}

/**
 * Validate full name
 */
export function validateFullName(
  value: string,
  options: ValidationOptions = {}
): ValidationResult {
  const trimmedValue = value?.trim() || '';

  // Check required first
  if (options.required) {
    const requiredResult = validateRequired(trimmedValue);
    if (!requiredResult.isValid) return requiredResult;
  }

  if (!trimmedValue) {
    return { isValid: true }; // Empty values are allowed if not required
  }

  // Full name regex - letters, spaces, hyphens, and apostrophes only
  const nameRegex = /^[A-Za-z\s'-]{2,100}$/;

  if (!nameRegex.test(trimmedValue)) {
    return {
      isValid: false,
      errorMessage:
        'Please enter a valid name (letters, spaces, hyphens, and apostrophes only)',
    };
  }

  // Check for at least two parts (first and last name)
  const nameParts = trimmedValue.split(/\s+/).filter(part => part.length > 0);
  if (nameParts.length < 2) {
    return {
      isValid: false,
      errorMessage: 'Please enter both first and last name',
    };
  }

  return { isValid: true };
}

/**
 * Validate currency/amount
 */
export function validateCurrency(
  value: string,
  options: ValidationOptions = {}
): ValidationResult {
  const trimmedValue = value?.trim() || '';

  // Check required first
  if (options.required) {
    const requiredResult = validateRequired(trimmedValue);
    if (!requiredResult.isValid) return requiredResult;
  }

  if (!trimmedValue) {
    return { isValid: true }; // Empty values are allowed if not required
  }

  // Currency regex - allows optional $ sign, digits, and up to 2 decimal places
  const currencyRegex = /^\$?\d+(\.\d{1,2})?$/;

  if (!currencyRegex.test(trimmedValue)) {
    return {
      isValid: false,
      errorMessage: 'Please enter a valid amount (e.g., 100.00 or $100.00)',
    };
  }

  return { isValid: true };
}

/**
 * Validate percentage
 */
export function validatePercentage(
  value: string,
  options: ValidationOptions = {}
): ValidationResult {
  const trimmedValue = value?.trim() || '';

  // Check required first
  if (options.required) {
    const requiredResult = validateRequired(trimmedValue);
    if (!requiredResult.isValid) return requiredResult;
  }

  if (!trimmedValue) {
    return { isValid: true }; // Empty values are allowed if not required
  }

  // Percentage regex - 0-100 with optional decimal places
  const percentageRegex = /^(100(\.0{1,2})?|[0-9]{1,2}(\.\d{1,2})?)%?$/;

  if (!percentageRegex.test(trimmedValue)) {
    return {
      isValid: false,
      errorMessage: 'Please enter a valid percentage (0-100)',
    };
  }

  // Extract numeric value for range check
  const numericValue = parseFloat(trimmedValue.replace('%', ''));
  if (numericValue < 0 || numericValue > 100) {
    return {
      isValid: false,
      errorMessage: 'Percentage must be between 0 and 100',
    };
  }

  return { isValid: true };
}

/**
 * Validate SSN (US Social Security Number)
 */
export function validateSSN(
  value: string,
  options: ValidationOptions = {}
): ValidationResult {
  const trimmedValue = value?.trim() || '';

  // Check required first
  if (options.required) {
    const requiredResult = validateRequired(trimmedValue);
    if (!requiredResult.isValid) return requiredResult;
  }

  if (!trimmedValue) {
    return { isValid: true }; // Empty values are allowed if not required
  }

  // SSN regex - XXX-XX-XXXX format
  const ssnRegex = /^\d{3}-\d{2}-\d{4}$/;

  if (!ssnRegex.test(trimmedValue)) {
    return {
      isValid: false,
      errorMessage: 'Please enter a valid SSN in XXX-XX-XXXX format',
    };
  }

  return { isValid: true };
}

/**
 * Validate Tax ID/EIN
 */
export function validateTaxId(
  value: string,
  options: ValidationOptions = {}
): ValidationResult {
  const trimmedValue = value?.trim() || '';

  // Check required first
  if (options.required) {
    const requiredResult = validateRequired(trimmedValue);
    if (!requiredResult.isValid) return requiredResult;
  }

  if (!trimmedValue) {
    return { isValid: true }; // Empty values are allowed if not required
  }

  // Tax ID/EIN regex - XX-XXXXXXX format
  const taxIdRegex = /^\d{2}-\d{7}$/;

  if (!taxIdRegex.test(trimmedValue)) {
    return {
      isValid: false,
      errorMessage: 'Please enter a valid Tax ID/EIN in XX-XXXXXXX format',
    };
  }

  return { isValid: true };
}

/**
 * Main validation function that routes to specific validators
 */
export function validateQuestionAnswer(
  value: string,
  validationType?: QuestionValidationType,
  options: ValidationOptions = {}
): ValidationResult {
  // Default options for better UX
  const defaultOptions: ValidationOptions = {
    required: true, // Make fields required by default
    maxLength: 200, // Reasonable max length
    sanitizeHtml: true, // Sanitize HTML by default
    ...options,
  };

  if (!validationType || validationType === 'text') {
    return validateText(value, defaultOptions);
  }

  switch (validationType) {
    case 'number':
      return validateNumber(value, defaultOptions);
    case 'email':
      return validateEmail(value, defaultOptions);
    case 'phone':
      return validatePhone(value, defaultOptions);
    case 'date':
      return validateDate(value, defaultOptions);
    case 'domestic_address':
      return validateDomesticAddress(value, defaultOptions);
    case 'international_address':
      return validateInternationalAddress(value, defaultOptions);
    case 'full_name':
      return validateFullName(value, defaultOptions);
    case 'currency':
      return validateCurrency(value, defaultOptions);
    case 'percentage':
      return validatePercentage(value, defaultOptions);
    case 'ssn':
      return validateSSN(value, defaultOptions);
    case 'tax_id':
      return validateTaxId(value, defaultOptions);
    case 'required':
      return validateRequired(value);
    default:
      return validateText(value, defaultOptions);
  }
}

/**
 * Get user-friendly validation rule description
 */
export function getValidationDescription(
  validationType?: QuestionValidationType,
  options: ValidationOptions = {}
): string {
  const { required = true, maxLength = 5000 } = options;

  let description = '';

  switch (validationType) {
    case 'number':
      description = 'Only numeric values are allowed';
      break;
    case 'email':
      description = 'Please enter a valid email address';
      break;
    case 'phone':
      description = 'Please enter a valid US phone number';
      break;
    case 'date':
      description = 'Please enter a date in MM/DD/YYYY format';
      break;
    case 'domestic_address':
      description =
        'Please enter a complete US address (Street, City, State ZIP)';
      break;
    case 'international_address':
      description = 'Please enter a complete international address';
      break;
    case 'full_name':
      description = 'Please enter your full name (first and last)';
      break;
    case 'currency':
      description = 'Please enter a monetary amount (e.g., 100.00)';
      break;
    case 'percentage':
      description = 'Please enter a percentage (0-100)';
      break;
    case 'ssn':
      description = 'Please enter SSN in XXX-XX-XXXX format';
      break;
    case 'tax_id':
      description = 'Please enter Tax ID/EIN in XX-XXXXXXX format';
      break;
    case 'text':
    default:
      description = 'Text input';
      break;
  }

  if (required) {
    description += ' (required)';
  }

  if (maxLength && maxLength < 5000) {
    description += ` - Maximum ${maxLength} characters`;
  }

  return description;
}
