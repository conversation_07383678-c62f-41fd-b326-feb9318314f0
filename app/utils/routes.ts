const routes = {
  home: '/',
  profile: '/profile',
  dashboard: '/dashboard',
  interview: '/member/interview',
  emergencyContacts: '/member/emergency-contacts',
  vault: '/member/vault',
  login: '/login',
  resetPassword: '/auth/reset-password',
  emergency: '/emergency',
  member: {
    dashboard: '/member/dashboard',
    documents: '/member/documents',
    documentsCreate: '/member/documents/create',
    careDocuments: '/member/care-documents',
    careDocumentById: (id: string) => `/member/care-documents/${id}`,
    interview: '/member/interview',
    interviewReview: '/member/interview/review',
    interviewV2: '/member/interviewV2',
    settings: '/member/settings',
    emergencyContacts: '/member/emergency-contacts',
    educationalContent: '/member/educational-content',
    wellnessChecks: '/member/wellness-checks', // OLD DEAD MENS SWITCH
    onboarding: '/member/onboarding',
    linked: '/member/linked',
  },
  documentsReview: '/member/documents/review',
  documentsSign: '/member/documents/sign',
  documentsManage: '/member/documents',
  attorneyList: '/member/attorney-list',
  onboarding: '/member/onboarding',
  admin: {
    dashboard: '/admin/dashboard',
    users: '/admin/users',
    roles: '/admin/roles',
    contentAnalytics: '/admin/content-analytics',
    content: '/admin/content',
    notifications: '/admin/notifications',
    interviewBuilder: '/admin/interview-builder',
    interviewBuilderCreate: '/admin/interview-builder/create',
    interviewBuilderBuild: (id: string) =>
      `/admin/interview-builder/build/${id}`,
    interviewBuilderEdit: (id: string) => `/admin/interview-builder/edit/${id}`,
    attorneys: '/admin/attorneys',
    billing: '/admin/billing',
    reports: '/admin/reports',
    settings: '/admin/settings',
    templates: '/admin/templates',
    newTemplate: '/admin/templates/edit/new',
    templatesEdit: (id: string) => `/admin/templates/edit/${id}`,
    templatesHistory: (id: string) => `/admin/templates/history/${id}`,
    templatesPropagate: '/admin/templates/propagate',
    legalUpdates: '/admin/legal-updates',
    quarterlyReview: '/admin/quarterly-review',
    auditDocuments: '/admin/audit/documents',
    emergency: '/admin/emergency',
    todos: '/admin/todos',
    careDocumentsBuilder: '/admin/care-documents-builder',
    careDocumentsBuilderEdit: (id: string) =>
      `/admin/care-documents-builder/edit/${id}`,
    inviteList: '/admin/users/invites',
  },
  welon: {
    dashboard: '/welon/dashboard',
    documents: '/welon/documents',
    emergency: '/welon/emergency',
    submitEvidence: '/welon/submit-evidence',
    uploadDocuments: '/welon/upload-documents',
    notifications: '/welon/notifications',
    emergencyDocuments: '/welon/emergency-documents',
    settings: '/welon/settings',
    shipping: '/welon/shipping',
  },
  adminUsers: '/admin/users',
  adminUsersCreate: '/admin/users/create',
  adminUsersEdit: (id: string) => `/admin/users/edit/${id}`,
};

export default routes;
