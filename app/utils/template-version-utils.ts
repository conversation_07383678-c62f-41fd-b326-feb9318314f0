import type { Schema } from '@/amplify/data/resource';
import { generateClient } from 'aws-amplify/data';

export interface TemplateVersionInfo {
  documentVersion: string; // Version of the document itself
  templateVersion: number | null; // Version of template used when document was created
  latestTemplateVersion: number | null; // Latest available template version
  hasNewerTemplateVersion: boolean; // Whether a newer template version is available
  templateName?: string;
}

/**
 * Порівнює версію темплейту документа з найактуальнішою версією темплейту
 * @param documentVersion - версія документа (для відображення)
 * @param templateVersion - версія темплейту яка була використана при створенні документа
 * @param templateId - ID темплейту
 * @returns Promise<TemplateVersionInfo> - інформація про версії
 */
export async function compareTemplateVersions(
  documentVersion: string,
  templateVersion: number | null | undefined,
  templateId: string | null | undefined
): Promise<TemplateVersionInfo> {
  // Якщо немає templateId, повертаємо базову інформацію
  if (!templateId) {
    return {
      documentVersion,
      templateVersion: templateVersion || null,
      latestTemplateVersion: null,
      hasNewerTemplateVersion: false,
    };
  }

  try {
    const client = generateClient<Schema>();

    // Отримуємо темплейт та його версії
    const [templateResult, versionsResult] = await Promise.all([
      client.models.Template.get({ id: templateId }),
      client.models.TemplateVersion.list({
        filter: { templateId: { eq: templateId } },
      }),
    ]);

    const template = templateResult.data;
    const versions = versionsResult.data || [];

    // Знаходимо найактуальнішу версію темплейту
    const validVersionNumbers = versions
      .map(v => v.versionNumber)
      .filter(
        (versionNumber): versionNumber is number =>
          versionNumber !== null && versionNumber !== undefined
      );

    const latestTemplateVersion =
      validVersionNumbers.length > 0 ? Math.max(...validVersionNumbers) : null;

    // Визначаємо чи є новіша версія темплейту
    const hasNewerTemplateVersion =
      latestTemplateVersion !== null &&
      templateVersion !== null &&
      templateVersion !== undefined &&
      latestTemplateVersion > templateVersion;

    return {
      documentVersion,
      templateVersion: templateVersion || null,
      latestTemplateVersion,
      hasNewerTemplateVersion,
      templateName: template?.templateName || undefined,
    };
  } catch (error) {
    console.error('Error comparing template versions:', error);
    return {
      documentVersion,
      templateVersion: templateVersion || null,
      latestTemplateVersion: null,
      hasNewerTemplateVersion: false,
    };
  }
}

/**
 * Кешована версія порівняння версій для оптимізації
 */
const versionCache = new Map<
  string,
  { data: TemplateVersionInfo; timestamp: number }
>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 хвилин

/**
 * Отримує інформацію про версію з кешем
 * @param documentVersion - версія документа
 * @param templateVersion - версія темплейту документа
 * @param templateId - ID темплейту
 * @returns Promise<TemplateVersionInfo>
 */
export async function getCachedTemplateVersionInfo(
  documentVersion: string,
  templateVersion: number | null | undefined,
  templateId: string | null | undefined
): Promise<TemplateVersionInfo> {
  if (!templateId) {
    return {
      documentVersion,
      templateVersion: templateVersion || null,
      latestTemplateVersion: null,
      hasNewerTemplateVersion: false,
    };
  }

  const cacheKey = `${templateId}-${templateVersion}`;
  const cached = versionCache.get(cacheKey);

  // Перевіряємо чи є валідний кеш
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return { ...cached.data, documentVersion }; // Оновлюємо documentVersion
  }

  // Отримуємо свіжі дані
  const versionInfo = await compareTemplateVersions(
    documentVersion,
    templateVersion,
    templateId
  );

  // Зберігаємо в кеш
  versionCache.set(cacheKey, {
    data: versionInfo,
    timestamp: Date.now(),
  });

  return versionInfo;
}
