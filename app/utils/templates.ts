import type { Schema } from '@/amplify/data/resource';
import { generateClient } from 'aws-amplify/data';
import { getCurrentUser } from 'aws-amplify/auth';

type Template = Schema['Template']['type'];
type TemplateVersion = Schema['TemplateVersion']['type'];

interface TemplateWithVersion extends Template {
  latestVersion?: TemplateVersion;
}

/**
 * Fetches all templates with their latest versions
 * @returns Promise<TemplateWithVersion[]> Array of templates with their latest versions
 * @throws Error if there's an issue fetching templates
 */
export const fetchTemplates = async (): Promise<TemplateWithVersion[]> => {
  try {
    console.log('Fetching templates...');

    // Check if user is authenticated
    try {
      const currentUser = await getCurrentUser();
      console.log('User authenticated:', currentUser.userId);
    } catch (authError) {
      console.error('User not authenticated:', authError);
      throw new Error('Authentication required to fetch templates');
    }

    const client = generateClient<Schema>();
    console.log('Making Template.list query...');

    const { data: templatesData, errors } = await client.models.Template.list({
      filter: {
        isActive: { eq: true },
        isDraft: { eq: false },
      },
    });

    console.log('Templates query result:', {
      templatesData: templatesData
        ? `Array of ${templatesData.length} items`
        : 'null',
      errors: errors ? errors.length + ' errors' : 'no errors',
      firstTemplate: templatesData?.[0] ? 'exists' : 'null',
    });

    if (errors) {
      console.error('Errors fetching templates:', errors);
      // Log detailed error information
      errors.forEach((error, index) => {
        console.error(`Error ${index + 1}:`, {
          message: error.message,
          locations: error.locations,
          path: error.path,
        });
      });
      throw new Error('Failed to fetch templates due to database errors');
    }

    if (!templatesData) {
      console.warn('No templates data returned from database');
      return [];
    }

    // Filter out null templates and fetch all versions for valid templates in parallel
    const validTemplates = templatesData.filter(template => template !== null);
    console.log('Valid templates count:', validTemplates.length);

    const templatesWithVersions = await Promise.allSettled(
      validTemplates.map(async template => {
        try {
          const { data: versions, errors: versionErrors } =
            await client.models.TemplateVersion.list({
              filter: {
                templateId: { eq: template.id },
              },
            });

          if (versionErrors) {
            console.warn(
              `Errors fetching versions for template ${template.id}:`,
              versionErrors
            );
          }

          // Filter out null versions and get the latest version
          const validVersions =
            versions?.filter(version => version !== null) || [];
          const latestVersion =
            validVersions.length > 0
              ? validVersions.reduce((latest, current) =>
                  current.versionNumber > latest.versionNumber
                    ? current
                    : latest
                )
              : undefined;

          return {
            ...template,
            latestVersion,
          };
        } catch (error) {
          console.error(`Error processing template ${template.id}:`, error);
          // Return template without version if there's an error
          return {
            ...template,
            latestVersion: undefined,
          };
        }
      })
    );

    // Filter out failed promises and extract successful results
    const successfulTemplates = templatesWithVersions
      .filter(
        (result): result is PromiseFulfilledResult<any> =>
          result.status === 'fulfilled'
      )
      .map(result => result.value);

    return successfulTemplates;
  } catch (error) {
    console.error('Error fetching templates:', error);
    throw new Error('Failed to fetch templates');
  }
};

/**
 * Fetches a specific template and its latest version
 * @param templateId - The ID of the template to fetch
 * @returns Promise<{template: Template, latestVersion: TemplateVersion | undefined}>
 * @throws Error if template is not found or there's an issue fetching data
 */
export async function getTemplate(templateId: string) {
  try {
    const client = generateClient<Schema>();

    const { data: template } = await client.models.Template.get({
      id: templateId,
    });

    if (!template) {
      throw new Error(`Template not found with ID: ${templateId}`);
    }

    const { data: versions } = await client.models.TemplateVersion.list({
      filter: {
        templateId: { eq: templateId },
      },
    });

    // Get the latest version using reduce instead of sort
    const latestVersion =
      versions.length > 0
        ? versions.reduce((latest, current) =>
            current.versionNumber > latest.versionNumber ? current : latest
          )
        : undefined;

    return {
      template,
      latestVersion,
    };
  } catch (error) {
    console.error(`Error fetching template ${templateId}:`, error);
    throw error;
  }
}

/**
 * Fetches all versions for a specific template, sorted by version number (newest first)
 * @param templateId - The ID of the template to fetch versions for
 * @returns Promise<TemplateVersion[]> Array of all template versions
 * @throws Error if there's an issue fetching template versions
 */
export async function getTemplateVersions(
  templateId: string
): Promise<TemplateVersion[]> {
  try {
    const client = generateClient<Schema>();

    const { data: versions } = await client.models.TemplateVersion.list({
      filter: {
        templateId: { eq: templateId },
      },
    });

    // Sort versions by version number in descending order (newest first)
    return versions.sort((a, b) => b.versionNumber - a.versionNumber);
  } catch (error) {
    console.error(`Error fetching template versions for ${templateId}:`, error);
    throw new Error('Failed to fetch template versions');
  }
}

/**
 * Fetches a template with all its versions for the history page
 * @param templateId - The ID of the template to fetch
 * @returns Promise<{template: Template, versions: TemplateVersion[]}>
 * @throws Error if template is not found or there's an issue fetching data
 */
export async function getTemplateWithHistory(templateId: string) {
  try {
    const [templateResult, versions] = await Promise.all([
      getTemplate(templateId),
      getTemplateVersions(templateId),
    ]);

    return {
      template: templateResult.template,
      versions,
    };
  } catch (error) {
    console.error(`Error fetching template with history ${templateId}:`, error);
    throw error;
  }
}

/**
 * Updates a template's active status (archive/unarchive)
 * @param templateId - The ID of the template to update
 * @param isActive - Boolean indicating whether to archive (false) or unarchive (true)
 * @returns Promise<Template> The updated template
 * @throws Error if template is not found or there's an issue updating
 */
export async function updateTemplateStatus(
  templateId: string,
  isActive: boolean
): Promise<Template> {
  try {
    const client = generateClient<Schema>();
    const { data: template } = await client.models.Template.update({
      id: templateId,
      isActive,
    });

    if (!template) {
      throw new Error(`Template not found with ID: ${templateId}`);
    }

    return template;
  } catch (error) {
    console.error(`Error updating template ${templateId} status:`, error);
    throw error;
  }
}
