'use client';

import { generateClient } from 'aws-amplify/data';
import { type Schema } from '@/amplify/data/resource';
import { getCurrentUser } from 'aws-amplify/auth';
import {
  getInterviewSetWithLastVersion,
  getInterviewsList,
} from '@/lib/api/interview-builder';

// Interface for interview answers
export interface InterviewAnswers {
  [questionId: string]: string | string[] | boolean | number;
}

// Helper function to get the current user's interview progress
export async function getUserInterviewProgress(): Promise<
  Schema['UserInterviewProgress']['type']
> {
  try {
    // Get the current authenticated user
    const user = await getCurrentUser();

    // Generate the data client
    const client = generateClient<Schema>();

    // Query for the user's interview progress record
    const { data: progressRecord } =
      await client.models.UserInterviewProgress.get({
        userId: user.userId,
        interviewSetId: 'default-interview-set', // TODO: Update to use proper interview set
        versionId: 'default-version', // TODO: Update to use proper version
      });

    // If no record exists, create a new one with initial values
    if (!progressRecord) {
      console.log('===> No interview progress found, creating new record');
      const { data: newProgress } =
        await client.models.UserInterviewProgress.create({
          userId: user.userId,
          interviewSetId: 'default-interview-set', // TODO: Update to use proper interview set
          versionId: 'default-version', // TODO: Update to use proper version
          currentQuestionId: null,
          completedQuestionIds: JSON.stringify([]),
          answers: JSON.stringify({}),
          isComplete: false,
          lastUpdated: new Date().toISOString(),
        });

      if (!newProgress) {
        throw new Error('Failed to create interview progress record');
      }

      return {
        ...newProgress, // Include all required properties from the original object
        completedQuestionIds: [],
        answers: {},
      };
    }

    // Parse JSON fields if they exist
    let parsedCompletedQuestionIds: any = (progressRecord as any)
      .completedQuestionIds;
    let parsedAnswers: any = (progressRecord as any).answers;

    if (
      typeof parsedCompletedQuestionIds === 'string' &&
      parsedCompletedQuestionIds
    ) {
      try {
        parsedCompletedQuestionIds = JSON.parse(parsedCompletedQuestionIds);
      } catch (e) {
        console.error('Error parsing completedQuestionIds:', e);
        parsedCompletedQuestionIds = [];
      }
    }

    if (typeof parsedAnswers === 'string' && parsedAnswers) {
      try {
        parsedAnswers = JSON.parse(parsedAnswers);
      } catch (e) {
        console.error('Error parsing answers:', e);
        parsedAnswers = {};
      }
    }

    // Return the existing progress record with parsed fields
    return {
      ...progressRecord,
      completedQuestionIds: parsedCompletedQuestionIds || [],
      answers: parsedAnswers || {},
    };
  } catch (error) {
    console.error('Error getting user interview progress:', error);
    // Return default values if there's an error
    // Create a minimal valid object with required fields
    return {
      userId: 'unknown', // Required field
      currentQuestionId: null,
      completedQuestionIds: [],
      answers: {},
      isComplete: false,
      lastUpdated: new Date().toISOString(),
      createdAt: new Date().toISOString(), // Required field
      updatedAt: new Date().toISOString(), // Required field
    } as Schema['UserInterviewProgress']['type'];
  }
}

// Helper function to save an answer to a question
export async function saveInterviewAnswer(
  questionId: string,
  answer: string | string[] | boolean | number,
  isLastQuestion: boolean = false
): Promise<Schema['UserInterviewProgress']['type']> {
  try {
    // Get the current authenticated user
    const user = await getCurrentUser();

    // Generate the data client
    const client = generateClient<Schema>();

    // Get the current progress
    const currentProgress = await getUserInterviewProgress();

    // Update the answers and completed questions
    const completedQuestionIds = Array.isArray(
      currentProgress.completedQuestionIds
    )
      ? [...currentProgress.completedQuestionIds, questionId]
      : [questionId];

    // Remove duplicates
    const uniqueCompletedQuestionIds = Array.from(
      new Set(completedQuestionIds)
    );

    // Update answers
    const existingAnswers =
      typeof currentProgress.answers === 'object'
        ? (currentProgress.answers as Record<
            string,
            string | string[] | boolean | number
          >)
        : {};

    const answers = {
      ...existingAnswers,
      [questionId]: answer,
    };

    // Query for the user's interview progress record
    const { data: progressRecord } =
      await client.models.UserInterviewProgress.get({
        userId: user.userId,
        interviewSetId: 'default-interview-set', // TODO: Update to use proper interview set
        versionId: 'default-version', // TODO: Update to use proper version
      });

    // If no record exists, create a new one
    if (!progressRecord) {
      const { data: newProgress } =
        await client.models.UserInterviewProgress.create({
          userId: user.userId,
          interviewSetId: 'default-interview-set', // TODO: Update to use proper interview set
          versionId: 'default-version', // TODO: Update to use proper version
          currentQuestionId: questionId,
          completedQuestionIds: JSON.stringify(uniqueCompletedQuestionIds),
          answers: JSON.stringify(answers),
          isComplete: isLastQuestion,
          lastUpdated: new Date().toISOString(),
        });

      if (!newProgress) {
        throw new Error('Failed to create interview progress record');
      }

      return {
        ...newProgress,
        completedQuestionIds: uniqueCompletedQuestionIds,
        answers,
      };
    }

    // Update the existing progress record
    const { data: updatedProgress } =
      await client.models.UserInterviewProgress.update({
        // Primary key is required for update
        userId: user.userId,
        interviewSetId: 'default-interview-set', // TODO: Update to use proper interview set
        versionId: 'default-version', // TODO: Update to use proper version
        // Other fields to update
        currentQuestionId: questionId,
        completedQuestionIds: JSON.stringify(uniqueCompletedQuestionIds),
        answers: JSON.stringify(answers),
        isComplete: isLastQuestion,
        lastUpdated: new Date().toISOString(),
      });

    if (!updatedProgress) {
      throw new Error('Failed to update interview progress record');
    }

    return {
      ...updatedProgress,
      completedQuestionIds: uniqueCompletedQuestionIds,
      answers,
    };
  } catch (error) {
    console.error('Error saving interview answer:', error);
    throw error;
  }
}

// Helper function to check if interview is complete
export async function isInterviewComplete(): Promise<boolean> {
  const progress = await getUserInterviewProgress();
  return progress.isComplete || false;
}

// Helper function to get all interview questions from the set with the biggest number of questions
export async function getInterviewQuestions(): Promise<
  Schema['InterviewQuestion']['type'][]
> {
  try {
    // Get all interview sets with their question counts
    const interviewSets = await getInterviewsList();

    if (!interviewSets || interviewSets.length === 0) {
      console.log('No interview sets found');
      return [];
    }

    // Find the interview set with the biggest number of questions
    const setWithMostQuestions = interviewSets.reduce((maxSet, currentSet) => {
      return currentSet.questionsCount > maxSet.questionsCount
        ? currentSet
        : maxSet;
    });

    // Get the latest version of that interview set with all questions
    const interviewSetWithQuestions = await getInterviewSetWithLastVersion(
      setWithMostQuestions.id
    );

    if (!interviewSetWithQuestions || !interviewSetWithQuestions.questions) {
      console.log('No questions found in the selected interview set');
      return [];
    }

    // Sort questions by order field and cast to the expected type
    const sortedQuestions = [...interviewSetWithQuestions.questions].sort(
      (a, b) => {
        const orderA = a.order || 0;
        const orderB = b.order || 0;
        return orderA - orderB;
      }
    );

    return sortedQuestions as Schema['InterviewQuestion']['type'][];
  } catch (error) {
    console.error('Error fetching interview questions:', error);
    return [];
  }
}

// Helper function to mark the interview as complete
export async function completeInterview(): Promise<boolean> {
  try {
    // Get the current authenticated user
    const user = await getCurrentUser();

    // Generate the data client
    const client = generateClient<Schema>();

    // Query for the user's interview progress record
    const { data: progressRecord } =
      await client.models.UserInterviewProgress.get({
        userId: user.userId,
        interviewSetId: 'default-interview-set', // TODO: Update to use proper interview set
        versionId: 'default-version', // TODO: Update to use proper version
      });

    if (!progressRecord) {
      return false;
    }

    // Update the existing progress record
    const { data: updatedProgress } =
      await client.models.UserInterviewProgress.update({
        // Primary key is required for update
        userId: user.userId,
        interviewSetId: 'default-interview-set', // TODO: Update to use proper interview set
        versionId: 'default-version', // TODO: Update to use proper version
        // Fields to update
        isComplete: true,
        lastUpdated: new Date().toISOString(),
      });

    return !!updatedProgress;
  } catch (error) {
    console.error('Error completing interview:', error);
    return false;
  }
}

// Helper function to reset user interview progress
export async function resetUserInterviewProgress(): Promise<
  Schema['UserInterviewProgress']['type']
> {
  try {
    // Get the current authenticated user
    const user = await getCurrentUser();

    // Generate the data client
    const client = generateClient<Schema>();

    // Query for the user's interview progress record
    const { data: progressRecord } =
      await client.models.UserInterviewProgress.get({
        userId: user.userId,
        interviewSetId: 'default-interview-set', // TODO: Update to use proper interview set
        versionId: 'default-version', // TODO: Update to use proper version
      });

    // If no record exists, create a new one with initial values
    if (!progressRecord) {
      const { data: newProgress } =
        await client.models.UserInterviewProgress.create({
          userId: user.userId,
          interviewSetId: 'default-interview-set', // TODO: Update to use proper interview set
          versionId: 'default-version', // TODO: Update to use proper version
          currentQuestionId: null,
          completedQuestionIds: JSON.stringify([]),
          answers: JSON.stringify({}),
          isComplete: false,
          lastUpdated: new Date().toISOString(),
        });

      if (!newProgress) {
        throw new Error('Failed to create interview progress record');
      }

      return {
        ...newProgress,
        completedQuestionIds: [],
        answers: {},
      };
    }

    // Reset the existing progress record
    const { data: updatedProgress } =
      await client.models.UserInterviewProgress.update({
        // Primary key is required for update
        userId: user.userId,
        interviewSetId: 'default-interview-set', // TODO: Update to use proper interview set
        versionId: 'default-version', // TODO: Update to use proper version
        // Reset all fields to initial values
        currentQuestionId: null,
        completedQuestionIds: JSON.stringify([]),
        answers: JSON.stringify({}),
        isComplete: false,
        lastUpdated: new Date().toISOString(),
      });

    if (!updatedProgress) {
      throw new Error('Failed to reset interview progress record');
    }

    return {
      ...updatedProgress,
      completedQuestionIds: [],
      answers: {},
    };
  } catch (error) {
    console.error('Error resetting user interview progress:', error);
    throw error;
  }
}
