/**
 * Video Utilities
 *
 * Utility functions for handling different video platforms
 */

export interface VideoInfo {
  type: 'youtube' | 'vimeo' | 'html5' | 'iframe' | 'unknown';
  embedUrl: string;
  isEmbeddable: boolean;
  videoId?: string;
  requiresIframe?: boolean;
  fallbackMessage?: string;
}

/**
 * Extract YouTube video ID from URL
 */
export const getYouTubeVideoId = (url: string): string | null => {
  const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
  const match = url.match(regExp);
  return match && match[2].length === 11 ? match[2] : null;
};

/**
 * Extract Vimeo video ID from URL
 */
export const getVimeoVideoId = (url: string): string | null => {
  const regExp = /(?:vimeo)\.com.*(?:videos|video|channels|)\/([\d]+)/i;
  const match = url.match(regExp);
  return match ? match[1] : null;
};

/**
 * Check if URL is a YouTube video
 */
export const isYouTubeVideo = (url: string): boolean => {
  return getYouTubeVideoId(url) !== null;
};

/**
 * Check if URL is a Vimeo video
 */
export const isVimeoVideo = (url: string): boolean => {
  return getVimeoVideoId(url) !== null;
};

/**
 * Get YouTube embed URL
 */
export const getYouTubeEmbedUrl = (url: string, autoPlay = false): string => {
  const videoId = getYouTubeVideoId(url);
  if (videoId) {
    const autoplayParam = autoPlay ? '&autoplay=1' : '';
    return `https://www.youtube.com/embed/${videoId}?rel=0${autoplayParam}&modestbranding=1&showinfo=0`;
  }
  return url;
};

/**
 * Get Vimeo embed URL
 */
export const getVimeoEmbedUrl = (url: string, autoPlay = false): string => {
  const videoId = getVimeoVideoId(url);
  if (videoId) {
    const autoplayParam = autoPlay ? '&autoplay=1' : '';
    return `https://player.vimeo.com/video/${videoId}?title=0&byline=0&portrait=0${autoplayParam}`;
  }
  return url;
};

/**
 * Get YouTube thumbnail URLs in order of preference
 */
export const getYouTubeThumbnailUrls = (videoId: string): string[] => [
  `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,
  `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,
  `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`,
  `https://img.youtube.com/vi/${videoId}/default.jpg`,
];

/**
 * Get Vimeo thumbnail URLs in order of preference
 */
export const getVimeoThumbnailUrls = (videoId: string): string[] => [
  `https://i.vimeocdn.com/video/${videoId}_640x360.jpg`,
  `https://i.vimeocdn.com/video/${videoId}_295x166.jpg`,
  `https://i.vimeocdn.com/video/${videoId}.jpg`,
  `https://vumbnail.com/${videoId}.jpg`, // Fallback to third-party service
];

/**
 * Get Vimeo thumbnail URL (primary)
 */
export const getVimeoThumbnailUrl = (videoId: string): string => {
  return getVimeoThumbnailUrls(videoId)[0];
};

/**
 * Check if URL might be embeddable (contains common video hosting patterns)
 */
export const isLikelyEmbeddableVideo = (url: string): boolean => {
  const embeddablePatterns = [
    /wistia\.com/i,
    /jwplayer\.com/i,
    /brightcove\.com/i,
    /kaltura\.com/i,
    /vidyard\.com/i,
    /loom\.com/i,
    /streamable\.com/i,
    /dailymotion\.com/i,
    /twitch\.tv/i,
    /facebook\.com.*\/videos/i,
    /instagram\.com.*\/p\//i,
    /tiktok\.com/i,
  ];

  return embeddablePatterns.some(pattern => pattern.test(url));
};

/**
 * Get comprehensive video information
 */
export const getVideoInfo = (url: string, autoPlay = false): VideoInfo => {
  if (!url) {
    return {
      type: 'unknown',
      embedUrl: '',
      isEmbeddable: false,
      fallbackMessage: 'No video URL provided',
    };
  }

  const youtubeId = getYouTubeVideoId(url);
  if (youtubeId) {
    return {
      type: 'youtube',
      embedUrl: getYouTubeEmbedUrl(url, autoPlay),
      isEmbeddable: true,
      videoId: youtubeId,
      requiresIframe: true,
    };
  }

  const vimeoId = getVimeoVideoId(url);
  if (vimeoId) {
    return {
      type: 'vimeo',
      embedUrl: getVimeoEmbedUrl(url, autoPlay),
      isEmbeddable: true,
      videoId: vimeoId,
      requiresIframe: true,
    };
  }

  // Check if it's a direct video file
  if (url.match(/\.(mp4|webm|ogg|mov|avi|mkv)$/i)) {
    return {
      type: 'html5',
      embedUrl: url,
      isEmbeddable: false,
      requiresIframe: false,
    };
  }

  // Check if it might be embeddable from other platforms
  if (isLikelyEmbeddableVideo(url)) {
    return {
      type: 'iframe',
      embedUrl: url,
      isEmbeddable: true,
      requiresIframe: true,
      fallbackMessage:
        'This video platform may require additional configuration',
    };
  }

  // Unknown video type - try iframe first, then fallback
  return {
    type: 'unknown',
    embedUrl: url,
    isEmbeddable: true, // Try iframe first
    requiresIframe: true,
    fallbackMessage: 'Video format not recognized. Attempting to display...',
  };
};

/**
 * Get thumbnail URL for any video platform
 */
export const getVideoThumbnailUrl = (url: string): string | null => {
  const youtubeId = getYouTubeVideoId(url);
  if (youtubeId) {
    return getYouTubeThumbnailUrls(youtubeId)[0]; // Return highest quality
  }

  const vimeoId = getVimeoVideoId(url);
  if (vimeoId) {
    return getVimeoThumbnailUrl(vimeoId);
  }

  return null;
};

/**
 * Format video duration in MM:SS format
 */
export const formatVideoDuration = (seconds?: number): string => {
  if (!seconds) return 'N/A';
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

/**
 * Format reading time for articles
 */
export const formatReadingTime = (minutes?: number): string => {
  if (!minutes) return 'N/A';
  return `${minutes} min read`;
};

/**
 * Get player size container classes
 */
export const getPlayerSizeClasses = (
  size: 'small' | 'medium' | 'large'
): string => {
  switch (size) {
    case 'small':
      return 'w-full max-w-md mx-auto';
    case 'medium':
      return 'w-full max-w-2xl mx-auto';
    case 'large':
      return 'w-full max-w-4xl mx-auto';
    default:
      return 'w-full max-w-2xl mx-auto';
  }
};

/**
 * Get player size label
 */
export const getPlayerSizeLabel = (
  size: 'small' | 'medium' | 'large'
): string => {
  switch (size) {
    case 'small':
      return 'Small';
    case 'medium':
      return 'Medium';
    case 'large':
      return 'Large';
    default:
      return 'Medium';
  }
};

/**
 * Cycle through player sizes
 */
export const getNextPlayerSize = (
  currentSize: 'small' | 'medium' | 'large'
): 'small' | 'medium' | 'large' => {
  const sizes = ['small', 'medium', 'large'] as const;
  const currentIndex = sizes.indexOf(currentSize);
  const nextIndex = (currentIndex + 1) % sizes.length;
  return sizes[nextIndex];
};

/**
 * Validate and sanitize video URL
 */
export const sanitizeVideoUrl = (url: string): string => {
  if (!url) return '';

  try {
    // Try to create a URL object to validate
    const urlObj = new URL(url);

    // Only allow http and https protocols
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      console.warn('Invalid protocol for video URL:', urlObj.protocol);
      return '';
    }

    return url.trim();
  } catch (error) {
    console.warn('Invalid video URL:', url, error);
    return '';
  }
};

/**
 * Check if URL is safe to embed
 */
export const isSafeToEmbed = (url: string): boolean => {
  const sanitizedUrl = sanitizeVideoUrl(url);
  if (!sanitizedUrl) return false;

  try {
    const urlObj = new URL(sanitizedUrl);

    // Block potentially dangerous domains
    const blockedDomains = ['localhost', '127.0.0.1', '0.0.0.0', 'file://'];

    const hostname = urlObj.hostname.toLowerCase();
    return !blockedDomains.some(blocked => hostname.includes(blocked));
  } catch {
    return false;
  }
};

/**
 * Get safe video information with validation
 */
export const getSafeVideoInfo = (url: string, autoPlay = false): VideoInfo => {
  const sanitizedUrl = sanitizeVideoUrl(url);

  if (!sanitizedUrl) {
    return {
      type: 'unknown',
      embedUrl: '',
      isEmbeddable: false,
      fallbackMessage: 'Invalid video URL provided',
    };
  }

  if (!isSafeToEmbed(sanitizedUrl)) {
    return {
      type: 'unknown',
      embedUrl: '',
      isEmbeddable: false,
      fallbackMessage: 'Video URL is not safe to embed',
    };
  }

  return getVideoInfo(sanitizedUrl, autoPlay);
};
