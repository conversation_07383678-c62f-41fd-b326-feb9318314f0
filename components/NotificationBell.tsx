import { useState } from 'react';
import { Bell, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNotifications } from '@/hooks/useNotifications';

export function NotificationBell() {
  const [isOpen, setIsOpen] = useState(false);
  const [displayCount, setDisplayCount] = useState(10);
  const {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    refetch,
    loading,
  } = useNotifications();

  const displayedNotifications = notifications.slice(0, displayCount);
  const hasMore = notifications.length > displayCount;

  const handleLoadMore = () => {
    setDisplayCount(prev => prev + 10);
  };

  const handleRefresh = () => {
    setDisplayCount(10);
    refetch();
  };

  return (
    <div className='relative'>
      <Button
        variant='ghost'
        size='icon'
        onClick={() => setIsOpen(!isOpen)}
        className='relative'
        aria-label={`Notifications${unreadCount > 0 ? ` (${unreadCount} unread)` : ''}`}
      >
        <Bell className='h-5 w-5' />
        {unreadCount > 0 && (
          <span className='absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center'>
            {unreadCount}
          </span>
        )}
      </Button>

      {isOpen && (
        <div className='absolute right-0 mt-2 w-100 bg-[var(--background)] border border-[var(--border)] rounded-lg shadow-lg z-50'>
          <div className='p-4 border-b border-[var(--border)] flex justify-between items-center'>
            <h3 className='font-semibold text-[var(--foreground)]'>
              Notifications
            </h3>
            <div className='flex gap-2'>
              {unreadCount > 0 && (
                <button
                  onClick={markAllAsRead}
                  className='px-3 py-1.5 text-sm rounded-md 
                 bg-blue-500 text-white
                 hover:bg-blue-600
                 transition-colors'
                >
                  Mark all as read
                </button>
              )}
              <button
                onClick={handleRefresh}
                disabled={loading}
                className='px-3 py-1.5 text-sm rounded-md
               bg-gray-200 text-gray-800
               hover:bg-gray-300
               dark:bg-gray-700 dark:text-gray-200
               dark:hover:bg-gray-600
               disabled:opacity-50 disabled:cursor-not-allowed
               transition-colors'
              >
                {loading ? 'Loading...' : 'Refresh'}
              </button>
            </div>
          </div>

          <div className='max-h-96 overflow-y-auto'>
            {notifications.length === 0 ? (
              <div className='p-4 text-[var(--muted-foreground)] text-center'>
                {loading ? 'Loading notifications...' : 'No notifications'}
              </div>
            ) : (
              <>
                {displayedNotifications.map(notification => (
                  <div
                    key={notification.id}
                    className={`p-4 border-b border-[var(--border)] cursor-pointer transition-colors ${
                      !notification.isRead
                        ? 'bg-blue-50 dark:bg-blue-950/30 hover:bg-blue-100 dark:hover:bg-blue-950/50'
                        : 'hover:bg-[var(--muted)] dark:hover:bg-[var(--muted)]'
                    }`}
                    onClick={() => markAsRead(notification.id)}
                  >
                    <p className='text-sm text-[var(--foreground)]'>
                      {notification.message}
                    </p>
                    <p className='text-xs text-[var(--muted-foreground)] mt-1'>
                      {new Date(notification.createdAt).toLocaleString()}
                    </p>
                  </div>
                ))}

                {hasMore && (
                  <div className='p-3 border-t border-[var(--border)]'>
                    <button
                      onClick={handleLoadMore}
                      disabled={loading}
                      className='w-full px-3 py-2 text-sm rounded-md
                       bg-gray-100 text-gray-700
                       hover:bg-gray-200
                       dark:bg-gray-800 dark:text-gray-300
                       dark:hover:bg-gray-700
                       disabled:opacity-50 disabled:cursor-not-allowed
                       transition-colors
                       flex items-center justify-center gap-2'
                    >
                      <ChevronDown className='h-4 w-4' />
                      Load more ({notifications.length - displayCount}{' '}
                      remaining)
                    </button>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
