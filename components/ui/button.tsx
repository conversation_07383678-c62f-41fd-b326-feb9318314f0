import * as React from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';
import {
  Plus,
  PlusCircle,
  Edit,
  Trash2,
  Save,
  X,
  Check,
  ArrowLeft,
  ArrowRight,
  Download,
  Upload,
  Search,
  Settings,
  User,
  Users,
  Eye,
  EyeOff,
  Heart,
  Star,
  Home,
  Menu,
  MoreHorizontal,
  MoreVertical,
  ChevronDown,
  ChevronUp,
  ChevronLeft,
  ChevronRight,
  Mail,
  Phone,
  Calendar,
  Clock,
  MapPin,
  Link,
  ExternalLink,
  Copy,
  Share,
  Filter,
  SortAsc,
  SortDesc,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  XCircle,
  Info,
  HelpCircle,
  Lock,
  Unlock,
  Shield,
  Key,
  FileText,
  File,
  Folder,
  Image,
  Video,
  Music,
  Archive,
  Database,
  Server,
  Cloud,
  Wifi,
  WifiOff,
  Battery,
  BatteryLow,
  Volume2,
  VolumeX,
  Play,
  Pause,
  Square,
  SkipBack,
  SkipForward,
  Repeat,
  Shuffle,
  Maximize,
  Minimize,
  RotateCcw,
  RotateCw,
  ZoomIn,
  ZoomOut,
  Move,
  Crop,
  Scissors,
  Palette,
  Brush,
  Eraser,
  Type,
  Bold,
  Italic,
  Underline,
  AlignLeft,
  AlignCenter,
  AlignRight,
  List,
  Grid3X3,
  Layers,
  Package,
  ShoppingCart,
  CreditCard,
  DollarSign,
  TrendingUp,
  TrendingDown,
  BarChart,
  PieChart,
  Activity,
  Zap,
  Target,
  Award,
  Gift,
  Tag,
  Bookmark,
  Flag,
  Bell,
  BellOff,
  MessageCircle,
  MessageSquare,
  Send,
  Inbox,
  Globe,
  Compass,
  Navigation,
  Anchor,
  Umbrella,
  Sun,
  Moon,
  CloudRain,
  Snowflake,
  Thermometer,
  Wind,
  Droplets,
  Flame,
  Lightbulb,
  Coffee,
  Loader2,
  Edit2,
  Edit3,
  Pencil,
} from 'lucide-react';

import { cn } from '@/lib/utils';

// Icon mapping for easy access
const iconMap = {
  plus: Plus,
  'plus-circle': PlusCircle,
  edit: Edit,
  trash: Trash2,
  save: Save,
  close: X,
  check: Check,
  'arrow-left': ArrowLeft,
  'arrow-right': ArrowRight,
  download: Download,
  upload: Upload,
  search: Search,
  settings: Settings,
  user: User,
  users: Users,
  eye: Eye,
  'eye-off': EyeOff,
  heart: Heart,
  star: Star,
  home: Home,
  menu: Menu,
  'more-horizontal': MoreHorizontal,
  'more-vertical': MoreVertical,
  'chevron-down': ChevronDown,
  'chevron-up': ChevronUp,
  'chevron-left': ChevronLeft,
  'chevron-right': ChevronRight,
  mail: Mail,
  phone: Phone,
  calendar: Calendar,
  clock: Clock,
  'map-pin': MapPin,
  link: Link,
  'external-link': ExternalLink,
  copy: Copy,
  share: Share,
  filter: Filter,
  'sort-asc': SortAsc,
  'sort-desc': SortDesc,
  refresh: RefreshCw,
  'alert-circle': AlertCircle,
  'check-circle': CheckCircle,
  'x-circle': XCircle,
  info: Info,
  'help-circle': HelpCircle,
  lock: Lock,
  unlock: Unlock,
  shield: Shield,
  key: Key,
  'file-text': FileText,
  file: File,
  folder: Folder,
  image: Image,
  video: Video,
  music: Music,
  archive: Archive,
  database: Database,
  server: Server,
  cloud: Cloud,
  wifi: Wifi,
  'wifi-off': WifiOff,
  battery: Battery,
  'battery-low': BatteryLow,
  volume: Volume2,
  'volume-off': VolumeX,
  play: Play,
  pause: Pause,
  stop: Square,
  'skip-back': SkipBack,
  'skip-forward': SkipForward,
  repeat: Repeat,
  shuffle: Shuffle,
  maximize: Maximize,
  minimize: Minimize,
  'rotate-ccw': RotateCcw,
  'rotate-cw': RotateCw,
  'zoom-in': ZoomIn,
  'zoom-out': ZoomOut,
  move: Move,
  crop: Crop,
  scissors: Scissors,
  palette: Palette,
  brush: Brush,
  eraser: Eraser,
  type: Type,
  bold: Bold,
  italic: Italic,
  underline: Underline,
  'align-left': AlignLeft,
  'align-center': AlignCenter,
  'align-right': AlignRight,
  list: List,
  grid: Grid3X3,
  layers: Layers,
  package: Package,
  'shopping-cart': ShoppingCart,
  'credit-card': CreditCard,
  'dollar-sign': DollarSign,
  'trending-up': TrendingUp,
  'trending-down': TrendingDown,
  'bar-chart': BarChart,
  'pie-chart': PieChart,
  activity: Activity,
  zap: Zap,
  target: Target,
  award: Award,
  gift: Gift,
  tag: Tag,
  bookmark: Bookmark,
  flag: Flag,
  bell: Bell,
  'bell-off': BellOff,
  'message-circle': MessageCircle,
  'message-square': MessageSquare,
  send: Send,
  inbox: Inbox,
  globe: Globe,
  compass: Compass,
  navigation: Navigation,
  anchor: Anchor,
  umbrella: Umbrella,
  sun: Sun,
  moon: Moon,
  'cloud-rain': CloudRain,
  snowflake: Snowflake,
  thermometer: Thermometer,
  wind: Wind,
  droplets: Droplets,
  flame: Flame,
  lightbulb: Lightbulb,
  coffee: Coffee,
  loader: Loader2,
  loading: Loader2,
  'edit-2': Edit2,
  'edit-3': Edit3,
  pencil: Pencil,
} as const;

export type IconName = keyof typeof iconMap;

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:scale-[1.02] hover:shadow-lg active:scale-[0.98] transform",
  {
    variants: {
      variant: {
        default:
          'bg-primary text-primary-foreground shadow-sm hover:bg-primary/90 hover:shadow-xl hover:brightness-110 hover:-translate-y-0.5',
        destructive:
          'bg-destructive text-white shadow-sm hover:bg-destructive/90 hover:shadow-xl hover:brightness-110 hover:-translate-y-0.5 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',
        outline:
          'border bg-background shadow-sm hover:bg-accent hover:text-accent-foreground hover:shadow-md hover:border-accent-foreground/20 hover:-translate-y-0.5 dark:bg-input/30 dark:border-input dark:hover:bg-input/50',
        secondary:
          'bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 hover:shadow-md hover:brightness-105 hover:-translate-y-0.5',
        ghost:
          'hover:bg-accent hover:text-accent-foreground hover:shadow-sm hover:brightness-105 hover:-translate-y-0.5 dark:hover:bg-accent/50',
        link: 'text-primary underline-offset-4 hover:underline hover:text-primary/80 hover:brightness-110',
        success:
          'bg-green-600 text-white shadow-sm hover:bg-green-700 hover:shadow-xl hover:brightness-110 hover:-translate-y-0.5',
        info: 'bg-blue-600 text-white shadow-sm hover:bg-blue-700 hover:shadow-xl hover:brightness-110 hover:-translate-y-0.5',
        warning:
          'bg-orange-600 text-white shadow-sm hover:bg-orange-700 hover:shadow-xl hover:brightness-110 hover:-translate-y-0.5',
        purple:
          'bg-purple-600 text-white shadow-sm hover:bg-purple-700 hover:shadow-xl hover:brightness-110 hover:-translate-y-0.5',
      },
      size: {
        default: 'h-9 px-4 py-2 has-[>svg]:px-3',
        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',
        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',
        xl: 'h-12 rounded-xl px-8 py-4 text-lg font-semibold has-[>svg]:px-6',
        icon: 'size-9',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

interface ButtonProps
  extends React.ComponentProps<'button'>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  icon?: IconName;
  iconPosition?: 'left' | 'right';
  iconSize?: 'sm' | 'md' | 'lg';
}

function Button({
  className,
  variant,
  size,
  asChild = false,
  icon,
  iconPosition = 'left',
  iconSize = 'md',
  children,
  ...props
}: ButtonProps) {
  // Get the icon component
  const IconComponent = icon ? iconMap[icon] : null;

  // Determine icon size classes
  const iconSizeClasses = {
    sm: 'h-3.5 w-3.5',
    md: 'h-4 w-4',
    lg: 'h-5 w-5',
  };

  const iconClass = iconSizeClasses[iconSize];

  // When using asChild, we need to handle the content differently
  // to avoid React.Children.only errors with Slot
  if (asChild) {
    const child = React.Children.only(children);

    // If we have an icon, we need to inject it into the child's content
    if (IconComponent) {
      return (
        <Slot
          data-slot='button'
          className={cn(buttonVariants({ variant, size, className }))}
          {...props}
        >
          {React.cloneElement(child as React.ReactElement, {
            children: (
              <>
                {iconPosition === 'left' && (
                  <IconComponent className={cn(iconClass, 'mr-2')} />
                )}
                {(child as React.ReactElement).props.children}
                {iconPosition === 'right' && (
                  <IconComponent className={cn(iconClass, 'ml-2')} />
                )}
              </>
            ),
          })}
        </Slot>
      );
    }

    // No icon, just pass through with Slot
    return (
      <Slot
        data-slot='button'
        className={cn(buttonVariants({ variant, size, className }))}
        {...props}
      >
        {child}
      </Slot>
    );
  }

  // Regular button (not asChild)
  return (
    <button
      data-slot='button'
      className={cn(buttonVariants({ variant, size, className }))}
      {...props}
    >
      {IconComponent && iconPosition === 'left' && (
        <IconComponent className={cn(iconClass, children && 'mr-2')} />
      )}
      {children}
      {IconComponent && iconPosition === 'right' && (
        <IconComponent className={cn(iconClass, children && 'ml-2')} />
      )}
    </button>
  );
}

export { Button, buttonVariants };
export type { ButtonProps };
