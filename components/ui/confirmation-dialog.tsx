'use client';

import { ReactNode } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';

interface ConfirmationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description: string;
  icon?: ReactNode;
  confirmLabel?: string;
  cancelLabel?: string;
  confirmVariant?:
    | 'default'
    | 'destructive'
    | 'outline'
    | 'secondary'
    | 'ghost'
    | 'link';
  onConfirm: () => void;
  children?: ReactNode;
  className?: string;
  cancelButtonClassName?: string;
}

export function ConfirmationDialog({
  open,
  onOpenChange,
  title,
  description,
  icon,
  confirmLabel = 'Confirm',
  cancelLabel = 'Cancel',
  confirmVariant = 'default',
  onConfirm,
  children,
  className = 'sm:max-w-md',
  cancelButtonClassName = '',
}: ConfirmationDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className={className}>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>

        {icon && (
          <div className='flex items-center gap-2 py-3'>
            {icon}
            {children}
          </div>
        )}

        {!icon && children && <div className='py-3'>{children}</div>}

        <DialogFooter className='flex justify-end space-x-2'>
          <Button
            type='button'
            variant='outline'
            onClick={() => onOpenChange(false)}
            // className={cancelButtonClassName}
          >
            {cancelLabel}
          </Button>
          <Button type='button' variant={confirmVariant} onClick={onConfirm}>
            {confirmLabel}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
