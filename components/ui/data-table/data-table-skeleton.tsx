import { Skeleton } from '@/components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

interface DataTableSkeletonProps {
  columnCount?: number;
  rowCount?: number;
  showToolbar?: boolean;
  showPagination?: boolean;
}

export function DataTableSkeleton({
  columnCount = 6,
  rowCount = 10,
  showToolbar = true,
  showPagination = true,
}: DataTableSkeletonProps) {
  return (
    <div className='space-y-4'>
      {/* Toolbar Skeleton */}
      {showToolbar && (
        <div className='flex items-center justify-between'>
          <div className='flex flex-1 items-center space-x-2'>
            {/* Search Input Skeleton */}
            <Skeleton className='h-8 w-[150px] lg:w-[250px]' />

            {/* Filter Buttons Skeleton */}
            <Skeleton className='h-8 w-[100px]' />
            <Skeleton className='h-8 w-[80px]' />
            <Skeleton className='h-8 w-[90px]' />

            {/* Reset Button Skeleton */}
            <Skeleton className='h-8 w-[60px]' />
          </div>

          {/* View Options Skeleton */}
          <Skeleton className='h-8 w-[70px]' />
        </div>
      )}

      {/* Table Skeleton */}
      <div className='rounded-md border'>
        <Table>
          <TableHeader>
            <TableRow>
              {Array.from({ length: columnCount }).map((_, index) => (
                <TableHead key={index}>
                  <Skeleton className='h-4 w-[100px]' />
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array.from({ length: rowCount }).map((_, rowIndex) => (
              <TableRow key={rowIndex}>
                {Array.from({ length: columnCount }).map((_, colIndex) => (
                  <TableCell key={colIndex}>
                    {/* Vary skeleton widths for more realistic look */}
                    <Skeleton
                      className={`h-4 ${
                        colIndex === 0
                          ? 'w-[120px]' // Name column
                          : colIndex === 1
                            ? 'w-[180px]' // Email column
                            : colIndex === 2
                              ? 'w-[80px]' // Role column
                              : colIndex === 3
                                ? 'w-[90px]' // Subrole column
                                : colIndex === 4
                                  ? 'w-[60px]' // Status column
                                  : 'w-[100px]' // Other columns
                      }`}
                    />
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination Skeleton */}
      {showPagination && (
        <div className='flex items-center justify-between px-2'>
          <div className='flex-1'>
            <Skeleton className='h-4 w-[200px]' />
          </div>
          <div className='flex items-center space-x-6 lg:space-x-8'>
            {/* Page Size Selector */}
            <div className='flex items-center space-x-2'>
              <Skeleton className='h-4 w-[80px]' />
              <Skeleton className='h-8 w-[70px]' />
            </div>

            {/* Page Info */}
            <Skeleton className='h-4 w-[100px]' />

            {/* Navigation Buttons */}
            <div className='flex items-center space-x-2'>
              <Skeleton className='h-8 w-8' />
              <Skeleton className='h-8 w-8' />
              <Skeleton className='h-8 w-8' />
              <Skeleton className='h-8 w-8' />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// Specific skeleton for user management table
export function UserManagementTableSkeleton() {
  return (
    <div className='space-y-4'>
      {/* Header Skeleton */}
      <div className='flex items-center justify-between'>
        <div>
          <Skeleton className='h-8 w-[200px] mb-2' />
        </div>
        <Skeleton className='h-10 w-[120px]' />
      </div>

      {/* Table Skeleton */}
      <DataTableSkeleton
        columnCount={6}
        rowCount={10}
        showToolbar={true}
        showPagination={true}
      />
    </div>
  );
}

// Specific skeleton for attorney management table
export function AttorneyManagementTableSkeleton() {
  return (
    <div className='space-y-4'>
      {/* Header Skeleton */}
      <div className='flex items-center justify-between'>
        <div>
          <Skeleton className='h-8 w-[220px] mb-2' />
        </div>
        <Skeleton className='h-10 w-[140px]' />
      </div>

      {/* Table Skeleton */}
      <DataTableSkeleton
        columnCount={8}
        rowCount={10}
        showToolbar={true}
        showPagination={true}
      />
    </div>
  );
}
