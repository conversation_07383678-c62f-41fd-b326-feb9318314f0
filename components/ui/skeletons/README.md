# Skeleton Components

This directory contains reusable skeleton loading components for consistent loading states across the application.

## Components

### `HeaderSkeleton`

A skeleton for the main header component.

```tsx
import { HeaderSkeleton } from '@/components/ui/skeletons';

<HeaderSkeleton />;
```

### `HeaderMenuSkeleton`

A skeleton for header menu items with two variants.

```tsx
import { HeaderMenuSkeleton } from '@/components/ui/skeletons';

// Simple variant (default)
<HeaderMenuSkeleton />

// Detailed variant with more elements
<HeaderMenuSkeleton variant="detailed" />
```

### `SidebarSkeleton`

A skeleton for the dashboard sidebar with customizable options.

```tsx
import { SidebarSkeleton } from '@/components/ui/skeletons';

// Default sidebar
<SidebarSkeleton />

// Custom item count and styling
<SidebarSkeleton
  itemCount={8}
  className="w-64 bg-background border-r border-gray-200 min-h-screen shadow-sm"
/>
```

### `DashboardContentSkeleton`

A skeleton for the main dashboard content area.

```tsx
import { DashboardContentSkeleton } from '@/components/ui/skeletons';

// Full content skeleton
<DashboardContentSkeleton />

// Customized content
<DashboardContentSkeleton
  showHeader={false}
  cardCount={4}
  showAdditionalContent={false}
/>
```

### `DashboardLayoutSkeleton`

A complete dashboard layout skeleton that combines header, sidebar, and content.
**Use this for standalone pages that need their own header.**

```tsx
import { DashboardLayoutSkeleton } from '@/components/ui/skeletons';

// Full dashboard layout with header
<DashboardLayoutSkeleton showHeader={true} />

// Customized layout
<DashboardLayoutSkeleton
  showHeader={true}
  sidebarItemCount={8}
  contentCardCount={4}
  showAdditionalContent={false}
/>
```

### `DashboardContentLayoutSkeleton`

A dashboard layout skeleton without header - for use when the header is handled by a parent layout.
**Use this in AuthGuard or other components where the main layout already provides a header.**

```tsx
import { DashboardContentLayoutSkeleton } from '@/components/ui/skeletons';

// Dashboard content without header (most common use case)
<DashboardContentLayoutSkeleton />

// Customized content layout
<DashboardContentLayoutSkeleton
  sidebarItemCount={8}
  contentCardCount={4}
  showAdditionalContent={false}
/>
```

## Usage Examples

### In Auth Guard (Main Layout Already Has Header)

```tsx
if (loading) {
  return <DashboardContentLayoutSkeleton />;
}
```

### For Standalone Pages (Need Own Header)

```tsx
if (loading) {
  return <DashboardLayoutSkeleton showHeader={true} />;
}
```

### In Header Component

```tsx
{
  loading ? <HeaderMenuSkeleton /> : user ? <UserMenu /> : <SignInButton />;
}
```

### Custom Loading States

```tsx
// For a simple page with just content
<div className="flex">
  <SidebarSkeleton />
  <DashboardContentSkeleton
    showHeader={true}
    cardCount={3}
    showAdditionalContent={false}
  />
</div>

// For a page without header (use DashboardContentLayoutSkeleton instead)
<DashboardContentLayoutSkeleton />
```

## Avoiding Duplicate Headers

**Important**: Choose the right skeleton based on your layout context:

- **Use `DashboardContentLayoutSkeleton`** when your component is rendered inside a layout that already has a header (like the main app layout)
- **Use `DashboardLayoutSkeleton` with `showHeader={true}`** only for standalone pages that need their own header
- **Use `HeaderMenuSkeleton`** in the main header component during auth loading

### Common Mistake ❌

```tsx
// This creates duplicate headers if used in AuthGuard
<DashboardLayoutSkeleton showHeader={true} />
```

### Correct Usage ✅

```tsx
// In AuthGuard (main layout already has header)
<DashboardContentLayoutSkeleton />

// In standalone modal or separate page
<DashboardLayoutSkeleton showHeader={true} />
```

## Design Principles

- **Consistent**: All skeletons use the same base `Skeleton` component from shadcn/ui
- **Configurable**: Components accept props to customize appearance and behavior
- **Realistic**: Skeleton layouts match the actual component structures
- **Performant**: Lightweight components that render quickly
- **Accessible**: Proper semantic structure maintained during loading states
