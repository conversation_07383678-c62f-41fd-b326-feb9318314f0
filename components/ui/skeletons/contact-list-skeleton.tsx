import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent } from '@/components/ui/card';

interface ContactListSkeletonProps {
  count?: number;
}

export function ContactListSkeleton({ count = 3 }: ContactListSkeletonProps) {
  return (
    <div className='space-y-4'>
      {Array.from({ length: count }).map((_, index) => (
        <Card key={index} className='overflow-hidden'>
          <CardContent className='p-6'>
            <div className='flex justify-between items-start'>
              <div className='space-y-4 w-full max-w-[70%]'>
                <div className='flex items-center'>
                  <Skeleton className='h-6 w-40' />
                  <Skeleton className='ml-2 h-5 w-24 rounded-full' />
                </div>

                <Skeleton className='h-4 w-32' />

                <div className='space-y-2'>
                  <div className='flex items-center'>
                    <Skeleton className='mr-2 h-4 w-12' />
                    <Skeleton className='h-4 w-48' />
                  </div>
                  <div className='flex items-center'>
                    <Skeleton className='mr-2 h-4 w-12' />
                    <Skeleton className='h-4 w-32' />
                  </div>
                  <div className='flex items-center'>
                    <Skeleton className='mr-2 h-4 w-12' />
                    <Skeleton className='h-4 w-20' />
                  </div>
                </div>
              </div>
              <div className='flex gap-2'>
                <Skeleton className='h-9 w-16' />
                <Skeleton className='h-9 w-16' />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
