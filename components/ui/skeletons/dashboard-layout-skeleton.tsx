import { HeaderSkeleton } from './header-skeleton';
import { SidebarSkeleton } from './sidebar-skeleton';
import { DashboardContentSkeleton } from './dashboard-content-skeleton';

interface DashboardLayoutSkeletonProps {
  showHeader?: boolean;
  sidebarItemCount?: number;
  contentCardCount?: number;
  showAdditionalContent?: boolean;
}

export function DashboardLayoutSkeleton({
  showHeader = false, // Default to false since main layout usually handles header
  sidebarItemCount = 6,
  contentCardCount = 6,
  showAdditionalContent = true,
}: DashboardLayoutSkeletonProps) {
  return (
    <div
      className={
        showHeader
          ? 'min-h-screen bg-background'
          : 'min-h-[calc(100vh-4rem)] bg-background'
      }
    >
      {/* Header Skeleton - only show if explicitly requested */}
      {showHeader && <HeaderSkeleton />}

      {/* Dashboard Layout Skeleton */}
      <div className='flex'>
        {/* Sidebar Skeleton */}
        <SidebarSkeleton
          className={
            showHeader
              ? 'w-64 bg-background border-r border-gray-200 min-h-[calc(100vh-4rem)] shadow-sm'
              : 'w-64 bg-background border-r border-gray-200 min-h-[calc(100vh-4rem)] shadow-sm'
          }
          itemCount={sidebarItemCount}
        />

        {/* Main Content Skeleton */}
        <DashboardContentSkeleton
          showHeader={true}
          cardCount={contentCardCount}
          showAdditionalContent={showAdditionalContent}
        />
      </div>
    </div>
  );
}
