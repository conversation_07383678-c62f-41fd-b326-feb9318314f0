import { Skeleton } from '@/components/ui/skeleton';

interface SidebarSkeletonProps {
  className?: string;
  itemCount?: number;
}

export function SidebarSkeleton({
  className = 'w-64 bg-background border-r border-gray-200 min-h-screen shadow-sm',
  itemCount = 6,
}: SidebarSkeletonProps) {
  return (
    <aside className={className}>
      <div className='p-6'>
        {/* Dashboard Title Skeleton */}
        <Skeleton className='h-6 w-32 mb-6' />

        {/* Navigation Items Skeleton */}
        <nav className='space-y-2'>
          {Array.from({ length: itemCount }).map((_, i) => (
            <div key={i} className='flex items-center px-3 py-2 rounded-lg'>
              <Skeleton className='h-5 w-5 mr-3' />
              <div className='flex-1'>
                <Skeleton className='h-4 w-24 mb-1' />
                <Skeleton className='h-3 w-32' />
              </div>
            </div>
          ))}
        </nav>
      </div>
    </aside>
  );
}
