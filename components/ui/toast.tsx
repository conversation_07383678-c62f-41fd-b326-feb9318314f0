'use client';

import * as React from 'react';
import { X } from 'lucide-react';
import { cn } from '../../lib/utils';

export interface ToastProps {
  id: string;
  title?: string;
  description?: string;
  action?: React.ReactNode;
  variant?: 'default' | 'destructive' | 'success' | 'warning';
  duration?: number;
  onClose?: () => void;
}

const Toast = React.forwardRef<
  HTMLDivElement,
  ToastProps & React.HTMLAttributes<HTMLDivElement>
>(
  (
    {
      className,
      variant = 'default',
      title,
      description,
      action,
      onClose,
      ...props
    },
    ref
  ) => {
    const variantStyles = {
      default:
        'bg-background border border-gray-200 text-[var(--custom-gray-dark)]',
      destructive: 'bg-red-50 border border-red-200 text-red-900',
      success: 'bg-green-50 border border-green-200 text-green-900',
      warning: 'bg-yellow-50 border border-yellow-200 text-yellow-900',
    };

    return (
      <div
        ref={ref}
        className={cn(
          'group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md p-4 pr-8 shadow-lg transition-all',
          variantStyles[variant],
          className
        )}
        {...props}
      >
        <div className='grid gap-1'>
          {title && <div className='text-sm font-semibold'>{title}</div>}
          {description && (
            <div className='text-sm opacity-90'>{description}</div>
          )}
        </div>
        {action}
        {onClose && (
          <button
            onClick={onClose}
            className='absolute right-2 top-2 rounded-md p-1 text-[var(--custom-gray-medium)] opacity-0 transition-opacity hover:text-[var(--custom-gray-dark)] focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100'
          >
            <X className='h-4 w-4' />
          </button>
        )}
      </div>
    );
  }
);
Toast.displayName = 'Toast';

// Toast Provider Context
interface ToastContextType {
  toasts: ToastProps[];
  addToast: (toast: Omit<ToastProps, 'id'>) => void;
  removeToast: (id: string) => void;
}

const ToastContext = React.createContext<ToastContextType | undefined>(
  undefined
);

export function ToastProvider({ children }: { children: React.ReactNode }) {
  const [toasts, setToasts] = React.useState<ToastProps[]>([]);

  const addToast = React.useCallback((toast: Omit<ToastProps, 'id'>) => {
    const id = Math.random().toString(36).substr(2, 9);
    const newToast = { ...toast, id };

    setToasts(prev => [...prev, newToast]);

    // Auto remove after duration
    if (toast.duration !== 0) {
      setTimeout(() => {
        removeToast(id);
      }, toast.duration || 5000);
    }
  }, []);

  const removeToast = React.useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  return (
    <ToastContext.Provider value={{ toasts, addToast, removeToast }}>
      {children}
      <ToastViewport toasts={toasts} removeToast={removeToast} />
    </ToastContext.Provider>
  );
}

function ToastViewport({
  toasts,
  removeToast,
}: {
  toasts: ToastProps[];
  removeToast: (id: string) => void;
}) {
  return (
    <div className='fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]'>
      {toasts.map(toast => (
        <Toast
          key={toast.id}
          {...toast}
          onClose={() => removeToast(toast.id)}
          className='mb-2 last:mb-0'
        />
      ))}
    </div>
  );
}

export function useToast() {
  const context = React.useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
}

export { Toast };
