# User Roles and Hierarchies

# **Document 0.1 \- Functional Specification: User Roles and Hierarchies**

## **Overview**

This functional specification outlines the structure, permissions, and management processes for user roles within the Childfree Legacy Web Application. By leveraging AWS Amplify, the system ensures secure, compliant, and user-friendly access to sensitive estate planning data. Role management is pivotal for enforcing role-based access control (RBAC), safeguarding user privacy, and fostering trust by restricting actions to users’ designated responsibilities. The system adheres to HIPAA, SOC2, and state-specific compliance standards while prioritizing usability, particularly for the 65+ demographic.

---

## **1\. Master Roles**

The application defines four master roles, each with distinct responsibilities and access levels:

- **Member**: The primary end user, typically a childfree individual aged 65+, responsible for creating, updating, and maintaining estate planning documents.
- **Administrator**: A system user with elevated permissions to oversee user accounts, document templates, and system configurations.
- **Welon Trust**: A role for trust administrators tasked with executing estate plans and managing related processes.
- **Professional** (Future Phase \- Not MVP): A role for professionals (e.g., advisors, attorneys) to view and manage member estate plans.

### **Technical Notes with AWS Amplify**

- **Authentication**: Utilize AWS Amplify’s Authentication module with Amazon Cognito User Pools to manage user identities and roles. Custom attributes (e.g., role and subrole) in Cognito store role assignments.
- **Scalability**: Employ AWS Cloud Development Kit (CDK) to integrate additional services (e.g., AWS Lambda for custom authorization) as the system scales to include future roles like Professional.

---

## **2\. Subroles**

Subroles under each master role provide granular permission control, adhering to the principle of least privilege. This ensures users access only the features and data required for their duties.

### **Administrator Subroles**

- **Administrator – Advanced**:
  - **Permissions**: Full system access, including user account management (create, edit, delete, deactivate), document template management, system settings adjustments, role assignments, and audit log oversight.
  - **Typical Tasks**: Onboard new users, publish estate planning templates, configure security policies.
  - **Restrictions**: None; unrestricted access.
- **Administrator – Basic**:
  - **Permissions**: View user details (e.g., name, email, role) and perform basic support tasks (e.g., password resets, account lockout resolutions).
  - **Typical Tasks**: Assist with login issues, update contact information.
  - **Restrictions**: No access to user creation, deletion, templates, or system settings.
- **Administrator – Reporting**:
  - **Permissions**: Generate and export usage and compliance reports (e.g., user activity, audit logs).
  - **Typical Tasks**: Produce monthly activity summaries, export SOC2 compliance data.
  - **Restrictions**: Read-only access; no modifications to user data or settings.
- **Administrator – Finance**:
  - **Permissions**: Manage billing, subscriptions, refunds, and financial reporting.
  - **Typical Tasks**: Process refunds, compile quarterly financial reports.
  - **Restrictions**: No access to user management or non-financial settings.

### **Welon Trust Subroles**

- **Welon – Advanced**:
  - **Permissions**: Full access to trust-related documents, reporting capabilities, and ability to add notes to member files.
  - **Typical Tasks**: Perform Welon-Basic tasks plus generate custom reports.
  - **Restrictions**: No user account or system settings management.
- **Welon – Basic**:
  - **Permissions**: View trust and living documents, update statuses (e.g., “in POA”), flag documents for review, upload signed documents.
  - **Typical Tasks**: Verify document details, update member statuses, upload executed documents.
  - **Restrictions**: Read-only for trust documents; no estate plan execution or data modification.
- **Welon – Emergency Service**:
  - **Permissions**: Manage emergency access, send documents, notify contacts, log actions.
  - **Typical Tasks**: Provide temporary document access during emergencies, alert emergency contacts.
  - **Restrictions**: No member data modifications.
- **Welon – Medical**:
  - **Permissions**: Access medical documents (e.g., healthcare directives), liaise with healthcare providers, send documents in emergencies, log actions.
  - **Typical Tasks**: Share medical wishes with hospitals, advise on medical POA execution.
  - **Restrictions**: Limited to medical documents; no member data edits.

### **Professional Subroles (Future Phase \- Not MVP)**

- **Professional – Financial Advisor**:
  - **Permissions**: Read access to assigned members’ status and changes.
  - **Typical Tasks**: Review quarterly updates to provide financial advice externally.
  - **Restrictions**: Read-only; limited to assigned members.

### **Technical Notes with AWS Amplify**

- **Data Management**: Use AWS Amplify’s Data module with AWS AppSync and Amazon DynamoDB to define role and subrole permissions in TypeScript models, enabling fine-grained access control.
- **Serverless Logic**: Implement AWS Lambda functions to enforce subrole-specific restrictions, such as limiting API endpoint access based on subrole attributes.

---

## **3\. Role-Based Access Control (RBAC)**

RBAC restricts access to features and data based on roles and subroles, ensuring security and compliance through precise permission assignments.

### **Key Requirements**

- Permissions align with subroles (e.g., Administrator – Finance accesses billing APIs, Welon – Medical accesses medical document endpoints).
- Users cannot self-elevate permissions or access unauthorized data.
- Only authorized Administrators (e.g., Administrator – Advanced) can assign or modify roles.

### **User Flow for Role Assignment**

1. Administrator navigates to /admin/users.
2. Selects a user and clicks "Edit."
3. Chooses master role and subrole from dropdowns.
4. System applies default subrole permissions.
5. Adjusts specific permissions if necessary.
6. Clicks "Save," logging the action in the audit trail.

### **Edge Cases**

- **Permission Conflicts**: Prevent contradictory assignments (e.g., “View Only” with “Edit Documents”).
- **Bulk Updates**: Support simultaneous role/permission updates for multiple users.
- **Revoked Permissions**: Notify users via email of significant permission changes.

### **Compliance Considerations**

- **Least Privilege**: Assign minimal necessary permissions.
- **Audit Trails**: Log role assignments and permission changes using AWS CloudWatch or DynamoDB Streams.

### **Technical Notes with AWS Amplify**

- **Authentication**: Map roles and subroles to Cognito groups, enforcing access via AWS AppSync resolvers or Lambda authorizers.
- **Data Access**: Use AppSync resolver logic with Cognito attributes to restrict data retrieval based on subroles.

---

## **4\. User Management**

Administrators with “User Management” permissions oversee account creation, editing, and deactivation, maintaining system integrity.

### **Key Requirements**

#### **Account Actions**

- Create accounts with role-specific settings.
- Edit user details (e.g., name, email, role, subrole).
- Deactivate/reactivate accounts.

#### **Unique Identifier Assignment**

- Members receive a sequential unique identifier (starting at 10000\) upon registration.
- Stored in the users table and embedded in document names (e.g., 10000-Will-CA-********).

#### **Security Measures**

- Restrict actions to authorized Administrators.
- Require re-authentication for sensitive operations.
- Use HTTPS for all communications.

### **User Flows**

#### **Creating a New User**

1. Administrator navigates to /admin/users.
2. Clicks "Create New User."
3. Selects role and subrole, enters user details.
4. Assigns permissions via checkboxes.
5. Clicks "Save," sending a welcome email.

#### **Editing a User**

1. Administrator selects a user and clicks "Edit."
2. Updates details or permissions.
3. Clicks "Save," logging changes and notifying the user if needed.

#### **Deactivating a User**

1. Administrator selects a user and clicks "Deactivate."
2. Confirms via dialog.
3. Account is marked inactive, logging out the user.

### **Edge Cases**

- **Duplicate Email**: Block creation if email exists.
- **Pending Actions**: Warn if deactivating users with pending tasks.
- **Reactivation**: Restore prior roles and permissions.

### **Compliance Considerations**

- **HIPAA/SOC2**: Encrypt data with AWS KMS and TLS.
- **RBAC Enforcement**: Limit Administrators to their permission scope.

### **UI Components**

| Element                  | Description                                 |
| ------------------------ | ------------------------------------------- |
| User List Table          | Columns: Name, Email, Role, Subrole, Status |
| "Create New User" Button | Opens new user form                         |
| "Edit" Button            | Opens edit form                             |
| "Deactivate" Button      | Deactivates user                            |
| Role Dropdown            | Selects master role                         |
| Subrole Dropdown         | Selects subrole                             |
| Permission Checkboxes    | Assigns specific permissions                |
| Confirmation Dialog      | Confirms deactivation                       |

---

## **5\. User Status and Journey Steps**

The system tracks members’ estate planning progress, displayed in admin (/admin/users) and member (/member/dashboard) dashboards.

### **Defined Steps**

- **Registered**: Account created with unique identifier.
- **Eligibility Confirmed**: Eligibility questionnaire completed.
- **Data Entry Completed**: All required data entered.
- **Documents Generated**: Initial documents created.
- **Documents Signed**: Documents executed.
- **Documents Accepted by Welon**: Welon accepts documents.
- **Quarterly Reviews**: Ongoing review cycle begins.

### **Technical Specifications**

- **Database**: Add journey_status (enum) to users table, applicable to Members only.
- **API**: Enhance GET /admin/users to return journey_status.

### **Technical Notes with AWS Amplify**

- **Data Management**: Define journey_status as an enum in AppSync schema.
- **Real-Time Updates**: Use AppSync subscriptions for live dashboard updates.

---

## **6\. Technical Specifications**

### **Frontend**

- **Framework**: React with Redux, hosted via Amplify Hosting.
- **Routes**:
  - /admin/users: User management dashboard.
  - /admin/roles: Role/permission management interface.

### **Backend**

- **APIs (GraphQL via AppSync)**:
  - createUser: Creates user with role/subrole.
  - updateUser: Edits user details/permissions.
  - deactivateUser: Deactivates user.
  - listUsers: Retrieves user list with statuses.
- **Database (DynamoDB)**:
  - **Table: users**
    - Columns: id (sequential), name, email, role, subrole, permissions, status, journey_status
  - **Table: roles**
    - Columns: id, name, description
  - **Table: subroles**
    - Columns: id, role_id, name, description, permissions
- **Encryption**: TLS/SSL for all data transfers.

### **Logging**

- Store audit logs in DynamoDB (audit_logs table) for scalability.

### **Technical Notes with AWS Amplify**

- **Authentication**: Cognito manages user sessions and role enforcement.
- **Serverless**: Lambda handles user management logic, triggered by AppSync or API Gateway.

---

## **7\. Compliance and Security**

- **HIPAA**: Encrypt data with AWS KMS and secure transmission with TLS.
- **SOC2**: Use CloudWatch for logging and incident response.
- **Least Privilege**: Enforce via Cognito groups and AppSync resolvers.
- **Audit Trails**: Store logs in DynamoDB or S3.

---

## **8\. Testing and Validation**

- **Unit Tests**: Validate role/permission logic.
- **Integration Tests**: Confirm RBAC enforcement.
- **Security Testing**: Prevent permission bypass.
- **UAT**: Ensure usability for Administrators and Members.

### **Test Cases**

| Scenario                       | Expected Outcome                      |
| ------------------------------ | ------------------------------------- |
| Create User with Subrole       | User created with correct permissions |
| Edit User Permissions          | Permissions updated successfully      |
| Deactivate User                | Account deactivated, user logged out  |
| Bulk Permission Update         | Multiple users updated correctly      |
| Permission Conflict Prevention | Conflicts blocked                     |
| Unauthorized Role Assignment   | Action denied                         |

---

## **Summary**

This specification delivers a robust framework for user roles and hierarchies in the Childfree Legacy Web Application, leveraging AWS Amplify for secure, scalable implementation. Master roles, subroles, and RBAC ensure compliant access to estate planning data, while journey steps enhance progress tracking. Integrated with Amplify’s authentication, data, and serverless features, the system balances security and usability, supporting a trusted estate planning experience compliant with HIPAA, SOC2, and state standards.
