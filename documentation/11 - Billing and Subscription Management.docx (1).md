# 11 \- Billing and Subscription Management

# **Document 11 \- Functional Specification: Billing and Subscription Management**

## **Overview**

The Billing and Subscription Management system enables users of the Childfree Legacy Web Application to securely manage subscriptions, process payments, and resolve billing issues such as missed payments or cancellations. Built on AWS Amplify, this specification outlines subscription setup, payment processing via integrated gateways, missed payment handling, and cancellation workflows, with real-time feedback on billing status. It prioritizes security (PCI DSS, HIPAA, SOC2 compliance), transparency, and accessibility for users aged 65+, fostering trust and supporting seamless financial interactions within the estate planning platform.

---

## **1\. Subscription Setup**

The subscription setup process allows users to select service tiers, configure payment preferences, and initiate billing cycles.

### **Key Requirements**

#### **Subscription Tiers**

- **Basic**: Access to core estate planning tools.
- **Future Phase \- Not MVP**: Enterprise tier for Professionals or organizations with customized billing plans.

#### **Billing Cycles**

- Members are charged upon document generation and monthly thereafter.
- Subscriptions support discount codes ($ or %, one-time or recurring).

#### **Payment Methods**

- **Stripe**: Credit/debit cards (Visa, MasterCard, etc.) and ACH bank transfers (U.S. users).

#### **Security Measures**

- PCI DSS compliance via Stripe tokenization (no full payment details stored).
- HTTPS enforced through **AWS Amplify’s default configuration**.

### **User Flow**

1. User navigates to /billing/subscribe.
2. Selects a subscription tier and billing cycle.
3. Enters payment details (e.g., card number, expiration, CVV) using **Stripe Elements**.
4. System validates and processes a test transaction via **Stripe**.
5. Upon success, activates subscription and redirects to /member/dashboard.
6. Sends confirmation email via **Amazon SES**.

### **Edge Cases**

- **Payment Failure**: Displays "Payment failed. Please try another method."
- **Duplicate Subscription**: Prevents multiple active subscriptions.
- **Zero $ Billing**: Allows administrators (Advanced and Finance roles) to zero out subscriptions via **AWS AppSync**.

### **Compliance Considerations**

- **PCI DSS**: Uses Stripe for tokenization and compliance.
- **Data Privacy**: Encrypts billing data with **AWS Key Management Service (KMS)**.

### **UI Components**

| Element              | Description                                 |
| -------------------- | ------------------------------------------- |
| Tier Selection Cards | Displays subscription options with pricing  |
| Payment Form         | Fields for card details or bank information |
| "Subscribe" Button   | Submits payment and activates subscription  |
| Error Message        | Displays validation or transaction errors   |

---

## **2\. Payment Processing**

The system integrates with Stripe for secure payment processing and recurring billing.

### **Key Requirements**

#### **Payment Gateways**

- **Stripe**: Handles tokenization and recurring payments.

#### **Recurring Billing**

- Automatically charges users per billing cycle using **Stripe subscriptions**.
- Retries failed payments up to 3 times over 7 days via **AWS Lambda** and **Amazon EventBridge**.

#### **Receipts**

- Emails receipts after successful payments via **Amazon SES**.
- Payment history accessible at /billing/history via **AWS AppSync**.

### **User Flow**

1. On billing date, **AWS Lambda** triggers a payment request to Stripe.
2. If successful, logs transaction in **Amazon DynamoDB** and emails receipt via **SES**.
3. If failed, retries after 1, 3, and 5 days, logging attempts in **CloudWatch**.
4. After 3 failures, marks subscription "Delinquent" and notifies user via **SES**.

### **Edge Cases**

- **Card Expiration**: Notifies users 30 days before expiration via **SES**.
- **Insufficient Funds**: Immediate notification and 5-day grace period.

### **Compliance Considerations**

- **PCI DSS**: No sensitive card data stored; uses Stripe tokens.
- **Audit Trails**: Logs payment attempts and outcomes in **CloudWatch**.

### **UI Components**

| Element                 | Description                                    |
| ----------------------- | ---------------------------------------------- |
| Payment History Table   | Lists past transactions with dates and amounts |
| "Update Payment Method" | Allows changes to payment details              |
| "Download Receipt"      | Generates PDF receipt via **Lambda**           |

---

## **3\. Missed Payments and Delinquency**

The system manages missed payments with notifications, retries, and service suspension if unresolved.

### **Key Requirements**

#### **Missed Payment Handling**

- Immediate notification on failure via **Amazon SNS**.
- Automatic retries after 1, 3, and 5 days via **Lambda**.

#### **Grace Period**

- 2 days after final retry before suspension.

#### **User Notifications**

- Email and in-app alerts for each retry via **SES** and **AppSync**.
- Instructions to update payment method at /billing/update-payment.

### **User Flow**

1. Payment fails; system sends "Payment Failed" email via **SES**.
2. Retries occur at 1, 3, and 5 days with notifications.
3. After 3 retries, marks account "Delinquent" with final notice.
4. If unresolved after 7 days, suspends access and emails "Account Suspended" via **SES**.

### **Edge Cases**

- **Disputed Charges**: Allows disputes via support channels.

### **Compliance Considerations**

- **Fair Billing**: Complies with automatic renewal laws.
- **Transparency**: Clearly communicates payment schedules and consequences.

### **UI Components**

| Element                | Description                                  |
| ---------------------- | -------------------------------------------- |
| Delinquency Banner     | Alerts users of missed payments on dashboard |
| "Resolve Now" Button   | Directs to update payment details            |
| Grace Period Countdown | Shows days remaining before suspension       |

---

## **4\. Cancellation Workflows**

Users can cancel subscriptions at any time, with options for reactivation and temporary suspension under specific conditions.

### **Key Requirements**

#### **Cancellation Process**

- Available at /billing/cancel with a confirmation prompt.
- Displays access end date and offers an optional feedback survey.

#### **Reactivation**

- Allows reactivation within 90 days via /billing/reactivate without data loss.
- After 90 days, accounts are permanently deactivated.

#### **Welon Trust Activation**

- Administrators can change Member status to "In Trust" upon Welon request, disabling access.

### **User Flow for Cancellation**

1. User navigates to /billing/cancel and clicks "Cancel Subscription."
2. Confirms cancellation; system processes and emails confirmation via **SES**.
3. Optional feedback survey is presented.

### **User Flow for Reactivation**

1. Within 90 days, user navigates to /billing/reactivate.
2. Confirms reactivation; subscription and data are restored.

### **User Flow for Welon Trust Activation**

1. Administrator approves Welon request via **AWS AppSync**.
2. Changes status to "In Trust" and logs reason in **DynamoDB**.

### **Edge Cases**

- **Pending Payments**: Prevents cancellation until resolved.
- **Reactivation After 90 Days**: Displays "Account permanently deactivated. Contact support."

### **Compliance Considerations**

- **Refund Policies**: Complies with prorated refund regulations.
- **Data Retention**: Retains data for 90 days post-cancellation.

### **UI Components**

| Element             | Description                              |
| ------------------- | ---------------------------------------- |
| Cancellation Button | Initiates cancellation process           |
| Confirmation Dialog | Confirms cancellation and shows end date |
| Feedback Form       | Optional survey for cancellation reasons |
| "Reactivate" Button | Restores subscription within 90 days     |

---

## **5\. User Feedback for Billing Status**

The system provides real-time billing status updates to keep users informed.

### **Key Requirements**

#### **Billing Dashboard**

- Displays subscription tier, next billing date, and payment method via **AWS AppSync**.
- Shows recent and upcoming transactions.

#### **Status Indicators**

- Visual cues (e.g., "Active," "Delinquent," "Suspended") with color coding.

#### **Proactive Alerts**

- Notifies 7 days before billing via **SES**.
- Alerts for expiring payment methods.

### **User Flow**

1. User navigates to /billing.
2. Views subscription details and status indicators.
3. Highlights issues (e.g., missed payments) with banners.
4. Can update payment method or view history.

### **Edge Cases**

- **Subscription Expiring**: Notifies 3 days before trial ends.
- **Overdue Invoices**: Displays "Pay Now" option for overdue amounts.

### **Compliance Considerations**

- **Transparency**: Itemizes charges and fees.
- **Accessibility**: Uses large text and simple language.

### **UI Components**

| Element                | Description                           |
| ---------------------- | ------------------------------------- |
| Subscription Summary   | Shows tier, status, next billing date |
| Payment Method Display | Masked details (e.g., \*\*\*\*1234)   |
| Transaction History    | Table of past payments and statuses   |
| Status Banner          | Highlights issues like delinquency    |

---

## **6\. Technical Specifications**

### **Frontend**

- **Framework**: React with Redux, hosted on **AWS Amplify Hosting**.
- **Routes**:
  - /billing: Dashboard.
  - /billing/subscribe: Subscription setup.
  - /billing/cancel: Cancellation.
  - /billing/history: Payment history.

### **Backend**

- **APIs (GraphQL via AWS AppSync)**:
  - subscribe:
    - Input: { tier, cycle, paymentMethod }
    - Output: { success, subscriptionId }
  - cancelSubscription:
    - Input: { subscriptionId }
    - Output: { success, cancellationDate }
  - getBillingStatus:
    - Input: { userId }
    - Output: { tier, status, nextBillingDate, paymentMethod }
  - updatePaymentMethod:
    - Input: { userId, newPaymentMethod }
    - Output: { success }
- **Payment Gateway**: Stripe, integrated via **AWS Lambda** for tokenization and processing.
- **Database (Amazon DynamoDB)**:
  - subscriptions: id, user_id, tier, cycle, status, next_billing_date.
  - transactions: id, subscription_id, amount, status, timestamp.

### **Encryption**

- TLS/SSL for all communications; **AWS KMS** for stored data.

### **Logging**

- Logs subscription actions, payments, and status changes in **Amazon CloudWatch**.

---

## **7\. Testing and Validation**

- **Unit Tests**: Validate subscription logic, retries, and cancellations with **AWS Amplify CLI**.
- **Integration Tests**: Confirm Stripe and notification functionality.
- **Security Testing**: Ensure PCI compliance with **AWS WAF**.
- **User Acceptance Testing (UAT)**: Verify usability for 65+ users.

### **Test Cases**

| Scenario                | Expected Outcome                         |
| ----------------------- | ---------------------------------------- |
| Subscribe to Basic Tier | Subscription activated, receipt sent     |
| Missed Payment Retry    | Retried after 1, 3, 5 days               |
| Cancel Subscription     | Prorated refund issued, access ends      |
| Update Payment Method   | New details saved, used for next payment |
| View Billing History    | Accurate transaction display             |

---

## **8\. Compliance and Security**

- **PCI DSS**: Tokenizes payment data via Stripe.
- **HIPAA**: Excludes health data from billing.
- **SOC2**: Controls access with **AWS IAM**; logs audits in **CloudWatch**.
- **State Laws**: Adheres to billing and consumer protection regulations.

---

## **Summary**

The Billing and Subscription Management system, powered by AWS Amplify, provides a secure, transparent, and accessible framework for managing subscriptions, payments, and billing issues. By integrating Stripe for payments, offering real-time feedback, and ensuring compliance, it supports the Childfree Legacy platform’s mission of reliability and trust, tailored to estate planning users, especially those aged 65+.
