# 14 \- Living Documents

# **Document 14 \- Functional Specification: Living Documents**

## **Overview**

Living Documents enable members of the Childfree Legacy Web Application, particularly those aged 65+, to create, store, update, and manage dynamic documents requiring semi-regular updates. Examples include emergency contacts, end-of-life wishes, pet care instructions, and digital asset management. Unlike Estate Documentation, which is updated for significant life changes or legal requirements and remains in effect until formally replaced, Living Documents are fluid, necessitating frequent updates to remain relevant. Leveraging **AWS Amplify**’s serverless architecture, this specification defines processes for creating, storing, updating, and accessing these documents, ensuring security, compliance with regulations like HIPAA and SOC2, and accessibility for the target demographic. Drawing inspiration from LifeHub’s functionality (e.g., Quicken’s LifeHub), this feature delivers a robust, user-friendly solution for dynamic estate planning needs.

---

## **1\. Document Creation**

Members can create Living Documents via an intuitive web interface, using templates or custom inputs, ensuring all critical details are captured seamlessly.

### **Key Requirements**

#### **Templates**

- Pre-built templates for common document types (e.g., Emergency Contacts, Pet Care) streamline creation.

#### **Customization**

- Members can edit templates or build documents from scratch for personalized flexibility.

#### **User Interface**

- Accessible design featuring large fonts, clear instructions, and optional voice guidance via **AWS Polly** to support the 65+ demographic.

#### **Validation**

- Enforces mandatory field completion with real-time feedback powered by **AWS AppSync**.

#### **Educational Support**

- Embeds tooltips or short videos (tied to the Educational Content Delivery feature) to clarify sections or terminology.

### **User Flow**

1. Member navigates to /member/living-documents/create.
2. Selects a document type (e.g., Emergency Contacts, Digital Assets).
3. Chooses a template or opts for a blank document.
4. Inputs data with real-time validation via **AWS AppSync**.
5. Saves the document, securely stored in the vault using **Amazon S3** with **AWS KMS** encryption.

### **Edge Cases**

- **Incomplete Documents**: Prompts users to complete required fields before saving.
- **Multiple Instances**: Supports creating multiple documents of the same type (e.g., distinct pet care plans).

### **Compliance Considerations**

- **Data Privacy**: Encrypts sensitive data (e.g., medical information) during creation with **AWS KMS**.
- **Consent**: Secures explicit member consent for data storage via **AWS Cognito**.

---

## **2\. Vault Storage**

Living Documents reside in a secure digital vault within the application, safeguarding confidentiality and integrity.

### **Key Requirements**

#### **Security**

- Encrypts documents at rest and in transit using **AWS KMS** and TLS.

#### **Access Control**

- Implements role-based access control (RBAC) via **AWS Cognito User Groups**.

#### **Versioning**

- Maintains document version history in **Amazon S3**.

#### **Backup**

- Ensures data durability with regular backups via **AWS Backup**.

#### **Audit Trails**

- Tracks all vault interactions in **Amazon CloudWatch** for compliance and monitoring.

### **Technical Specifications**

- **Database**: Stores encrypted metadata in **Amazon DynamoDB**.
- **Access Logs**: Logs events in a vault_access_logs table in **DynamoDB**.

### **User Flow**

- Post-creation, documents are auto-saved to the vault.
- Members access documents at /member/vault.
- Administrators configure vault settings at /admin/vault.

### **Edge Cases**

- **Unauthorized Access**: Logs and notifies admins of suspicious activity via **Amazon SNS**.
- **Data Corruption**: Detects and recovers corrupted data using checksums and **AWS Lambda**.

### **Compliance Considerations**

- **HIPAA**: Secures medical-related documents per HIPAA standards.
- **SOC2**: Upholds secure access controls and auditability.

---

## **3\. Document Updates**

Members update Living Documents periodically, aided by reminders and an efficient editing process.

### **Key Requirements**

#### **Notifications**

- Delivers configurable reminders (e.g., every 6 months) via **Amazon SNS** (SMS), **Amazon SES** (email), or in-app via **AWS AppSync**.

#### **Update Interface**

- Displays current data in an editable format with highlighted changes.

#### **Change Tracking**

- Records updates with "before" and "after" versions in **DynamoDB**.

#### **Validation**

- Verifies updates in real-time via **AWS AppSync**.

#### **Educational Support**

- Offers contextual guidance on update necessity.

### **User Flow**

1. Member receives a review reminder via **SNS/SES**.
2. Navigates to /member/living-documents/update.
3. Selects and edits a document, with changes highlighted.
4. Saves updates, generating a new version in **S3**.

### **Edge Cases**

- **No Updates Needed**: Allows review confirmation without changes.
- **Frequent Updates**: Caps update frequency (e.g., once every 3 minutes) to prevent misuse.

### **Compliance Considerations**

- **Audit Trails**: Logs updates in document_update_logs table in **DynamoDB**.
- **Data Integrity**: Validates updates with digital signatures via **AWS Signer**.

---

## **4\. Document Access**

Authorized individuals access Living Documents under predefined conditions, such as emergencies or member incapacitation.

### **Key Requirements**

#### **Access Triggers**

- Enables access via conditions like Dead Man’s Switch activation or emergency requests.

#### **Authorization**

- Mandates multi-factor authentication (MFA) via **AWS Cognito**.

#### **Notifications**

- Alerts members (if feasible) and admins upon access via **Amazon SNS**.

#### **Revocation**

- Permits manual access revocation via **AWS AppSync**.

#### **Integration**

- Links with Emergency Features and Dead Man’s Switch for emergency access.

### **User Flow**

1. Authorized individual requests access at /emergency/access.
2. System validates the request (e.g., Dead Man’s Switch triggered).
3. Grants access to specific documents, notifying via **SNS**.
4. Logs access in **CloudWatch**, revocable by the member if conditions shift.

### **Edge Cases**

- **False Triggers**: Allows cancellation of accidental access (e.g., missed Dead Man’s Switch check-in).
- **Multiple Requests**: Manages concurrent requests efficiently.

### **Compliance Considerations**

- **HIPAA**: Limits medical document access to authorized parties.
- **SOC2**: Ensures all access events are auditable.

---

## **5\. Dashboard Review**

Members review Living Documents from their dashboard, facilitating easy oversight and management.

### **Key Requirements**

#### **Document List**

- Shows all Living Documents with details (e.g., type, last updated, next review).

#### **Status Indicators**

- Uses color-coded icons (green, yellow, red) to flag attention-needed documents.

#### **Quick Actions**

- Offers view, edit, or update options directly from the dashboard.

#### **Integration with Notifications**

- Syncs indicators with notifications via **AWS AppSync**.

#### **Security**

- Restricts access to members and authorized admins via **AWS Cognito**.

### **User Flow**

1. Member logs into /member/dashboard.
2. Views "Living Documents" section listing all documents.
3. Each entry displays:
   - Type (e.g., Emergency Contacts).
   - Last updated date.
   - Next review date.
   - Status (green: current, yellow: due soon, red: overdue).
4. Member clicks to view details or edits directly.

### **Edge Cases**

- **No Documents**: Prompts creation with a "Create New Document" button.
- **Multiple Same-Type Documents**: Clearly differentiates similar documents.
- **Overdue Reviews**: Notifies via **SNS** and highlights until addressed.

### **Compliance Considerations**

- **Audit Trails**: Logs interactions in audit_logs table in **DynamoDB**.
- **Data Privacy**: Displays only metadata on the dashboard.

### **UI Components**

| Element                      | Description                                   |
| ---------------------------- | --------------------------------------------- |
| Living Documents Section     | Lists documents with status indicators        |
| Document List Table          | Shows type, last updated, next review, status |
| "View" Button                | Opens document details                        |
| "Edit" Button                | Initiates editing                             |
| Status Indicators            | Green, yellow, red icons                      |
| "Create New Document" Button | Prompts creation if no documents exist        |

---

## **6\. Handling Sensitive Information**

Sensitive data (e.g., medical details, digital credentials) in Living Documents receives heightened protection.

### **Key Requirements**

#### **Field-Level Encryption**

- Encrypts sensitive fields with **AWS KMS**.

#### **Access Restrictions**

- Limits access to explicitly permitted users via **AWS Cognito**.

#### **Data Masking**

- Masks data (e.g., partial passwords) unless fully authorized.

#### **Consent**

- Requires explicit consent for sensitive data storage via **AWS Cognito**.

### **Implementation Notes**

- Utilizes **AWS KMS** for encryption key management.
- Applies granular RBAC permissions for sensitive documents.
- Allows redaction or removal of sensitive data.

### **Compliance Considerations**

- **HIPAA**: Safeguards medical data per HIPAA rules.
- **PCI DSS**: Applies if payment data is present (though rare).

---

## **7\. Technical Specifications**

### **Frontend**

- **Framework**: React with Redux, hosted on **AWS Amplify Hosting**.
- **Routes**:
  - /member/living-documents/create: Document creation.
  - /member/living-documents/update: Document updates.
  - /member/vault: Vault access.
  - /emergency/access: Emergency access requests.
  - /member/dashboard: Dashboard with document review.

### **Backend**

- **APIs (GraphQL via AWS AppSync)**:
  - createLivingDocument:
    - **Input**: { memberId, documentType, content }
    - **Output**: { success, documentId }
  - updateLivingDocument:
    - **Input**: { documentId, updatedContent }
    - **Output**: { success, newVersionId }
  - listVaultDocuments:
    - **Input**: { memberId }
    - **Output**: { documents: \[{ id, type, version, createdAt, updatedAt }\] }
  - requestEmergencyAccess:
    - **Input**: { memberId, requesterId, reason }
    - **Output**: { success, accessToken }
  - getDashboardDocuments:
    - **Input**: { memberId }
    - **Output**: { documents: \[{ id, type, lastUpdated, nextReview, status }\] }

### **Database (Amazon DynamoDB)**

- **living_documents**:
  - **Columns**: id, member_id, type, content (encrypted), version, created_at, updated_at.
- **vault_access**:
  - **Columns**: id, document_id, accessor_id, access_type, granted_at, revoked_at.
- **document_update_logs**:
  - **Columns**: id, document_id, member_id, changes, timestamp.
- **audit_logs**:
  - **Columns**: id, user_id, action, details, timestamp.

### **Storage**

- Documents stored in **Amazon S3**, encrypted with **AWS KMS**.

### **Logging**

- Tracks all actions in **Amazon CloudWatch**.

---

## **8\. Testing and Validation**

- **Unit Tests**: Validates creation, update, and access logic with **AWS Amplify CLI**.
- **Integration Tests**: Tests the full document lifecycle, including dashboard review.
- **Security Testing**: Ensures encryption and access controls via **AWS WAF**.
- **User Acceptance Testing (UAT)**: Confirms usability for 65+ users.

### **Test Cases**

| Scenario         | Expected Outcome                       |
| ---------------- | -------------------------------------- |
| Creation         | Document created and stored securely   |
| Update           | Changes saved with new version tracked |
| Access           | Authorized access granted              |
| Notifications    | Reminders sent and acted upon          |
| Dashboard Review | Accurate document status displayed     |

---

## **9\. Compliance and Security**

- **Encryption**: **AWS KMS** for data at rest, TLS for transit.
- **Access Controls**: RBAC and MFA via **AWS Cognito**.
- **Audit Trails**: Logs in **CloudWatch**.
- **Data Retention**: Managed via **Amazon S3 Lifecycle Policies**.
- **HIPAA**: Protects medical data.
- **SOC2**: Ensures security and availability.
- **State Laws**: Adheres to digital document regulations.

---

## **Summary**

The Living Documents feature, powered by AWS Amplify, offers a secure, accessible solution for managing dynamic documents. With templates, vault storage, reminders, emergency access integration, and a dashboard review section, it keeps critical information current and accessible. Tailored for the 65+ demographic, it balances usability, security, and compliance, fostering trust in the Childfree Legacy platform.
