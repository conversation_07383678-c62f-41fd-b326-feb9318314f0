# Document 16 \- Functional Specification: Joint Account Management & Shared Asset Planning

#### **Overview**

This document details how the Childfree Legacy Web Application enables multiple users—such as married couples, partners, family members, or friends—to collaborate on estate planning elements like joint trusts or jointly owned property. The system allows Members to link their individual accounts to manage shared assets or documents (e.g., a Joint Trust) while keeping their personal estate planning documents (e.g., <PERSON>, Powers of Attorney) private and distinct. To improve the onboarding experience for couples, a new feature enables one partner (Person A) to invite the other (Person B), and upon acceptance, automatically copies shared profile fields (e.g., address, pets, household details) from Person A to Person B’s profile. Person <PERSON> can fully edit these fields, while personal information (e.g., name, date of birth) remains private and is not shared.

Built with AWS Amplify, this solution prioritizes data security (HIPAA, SOC2), adheres to Role-Based Access Control (RBAC) for individual and shared data, and delivers an intuitive, user-friendly experience tailored to the 65+ demographic.

---

#### **1\. Account Linking & Group Formation**

This section explains how Members can link their individual accounts to collaborate on shared estate planning. The Minimum Viable Product (MVP) focuses on linking two Members (e.g., a couple), with plans to support larger groups in future phases.

##### **Key Requirements**

- **Individual Accounts Maintained:** Each Member keeps their own account, login credentials, and private documents.
- **Explicit Linking:** Linking requires one Member to initiate and the other to confirm the connection.
- **Link Purpose:** Links enable co-management of specific shared documents or assets (e.g., Joint Trusts, jointly owned property details).
- **Privacy of Individual Documents:** Linking does not provide access to personal estate plans (e.g., Wills, individual POAs) unless explicitly shared.
- **Unlinking:** Members can unlink accounts, with clear rules for handling shared documents/assets afterward (e.g., one Member assumes responsibility, or the document is archived per legal guidance).
- **Profile Field Classification:** Profile fields are divided into:
  - **Personal Fields:** (e.g., name, date of birth) – not shared or copied.
  - **Shared Fields:** (e.g., address, pets, household details) – copied from the inviter’s profile to the invitee’s during onboarding for new users, with full editing control retained by the invitee.

##### **User Roles (in Context of Linking)**

- **Members:** Can send/accept link requests and co-manage shared documents with linked partners.
- **Administrators:** View linked account statuses and assist with unlinking in disputes (with verification); cannot initiate links.
- **Welon Trust / Professionals:** Access shared documents only with permission from all linked Members involved.

##### **Authentication Method**

Members use their individual email and password for authentication (per Document 1). Multi-Factor Authentication (MFA) remains individual.

##### **Security Measures**

- Link invitations and confirmations use secure, time-limited tokens.
- Linking communications (e.g., invitations) are sent via AWS SES.
- Actions (linking, unlinking, shared document access) are logged in AWS CloudWatch.

##### **User Flow for Account Linking with Profile Sharing (Couple Example \- MVP)**

1. **Initiation:**
   - Member A logs in and navigates to "Link Account" in their dashboard (e.g., /member/settings/linked-accounts).
   - Member A enters Member B’s email address.
   - The system sends an invitation email to Member B via AWS SES, including a secure, time-limited link.
2. **Registration and Profile Setup for New Users:**
   - If Member B has no account:
     - Member B clicks the link and lands on the registration page.
     - They register with an email and password, then verify their email.
     - Post-verification, they’re directed to the profile setup page.
     - Shared fields (e.g., address, pets, household details) are pre-filled with Member A’s data. A message displays: “Some fields have been pre-filled based on your partner’s information. Please review and update as needed.”
     - Member B enters personal fields (e.g., name, date of birth) and can edit the pre-filled shared fields.
     - After completing their profile, Member B accepts the link request from Member A.
3. **Link Acceptance:**
   - For new users: Member B accepts the link after profile setup.
   - For existing users: Member B clicks the link, logs in, and accepts the link request.
   - Upon acceptance, the accounts are linked in the database.
4. **Post-Linking:**
   - Both Members see the linked status in their dashboards/settings.
   - They can now co-manage shared documents (e.g., a Joint Trust).

##### **User Flow for Unlinking**

- Either Member initiates an unlink request from "Linked Accounts" settings.
- The other Member is notified.
- Confirmation is required from the initiator.
- The system explains how shared documents/assets will be handled (e.g., assigned to one Member or archived, per legal rules).
- Once confirmed, the link is removed, and shared documents follow predefined handling rules.

##### **Edge Cases (Account Linking)**

- **Invitation Expired:** Member B sees an error; Member A must resend the invitation.
- **Non-Existent Email:** Email bounces; Member A is notified.
- **Member B Declines:** Member A is notified.
- **Already Linked Account:** System blocks the attempt and informs the user.
- **Account Deactivation:** Link is severed; the remaining Member is notified, and shared documents follow unlinking rules.
- **Existing User Profiles:** If Member B has an account, their profile stays unchanged upon linking; shared fields are not copied to avoid overwriting data.

##### **Compliance Considerations (Account Linking)**

- **Consent:** Both Members must explicitly agree to link.
- **Data Privacy:** Linking grants access only to designated shared items, not entire accounts.

##### **UI Components (Account Linking)**

- **"Link Account" Button/Section:** Starts the linking process.
- **Invitation Email Template:** Simple instructions with a secure link.
- **Link Confirmation Modal:** Member B accepts or declines.
- **Linked Account Display:** Shows linked Member’s name/email in settings.
- **"Unlink Account" Button:** Triggers unlinking.
- **Unlinking Confirmation Modal:** Details consequences and requires confirmation.

---

#### **2\. Shared Document & Asset Management (Post-Linking)**

##### **Key Requirements**

- **Clear Distinction:** UI separates individual and shared documents.
- **Collaborative Creation/Input:** All linked Members can contribute to shared documents (e.g., Joint Trust).
- **Joint Review & Approval:** All linked Members must review and approve shared documents before finalization.
- **Shared Access Permissions:** All linked Members can view/edit shared documents they co-own.
- **Individual Document Privacy:** Personal documents remain private unless explicitly shared separately.

##### **User Roles (in Context of Shared Documents)**

- **Members (Linked):** Co-create, edit, review, and approve shared documents.
- **Administrators / Welon Trust:** View shared documents if all linked Members grant permission.

##### **User Flow for Creating a Shared Document (e.g., Joint Trust \- Couple Example)**

- Member A selects "Create Document" \> "Joint Trust."
- The system pre-populates data from both Members’ profiles (e.g., names).
- Both Members enter shared estate details (e.g., beneficiaries, trustees).
- A draft is generated and both are notified to review.
- Each Member reviews independently; edits are coordinated offline or via versioned updates.
- Both approve the final draft, then sign it jointly.

##### **User Flow for Accessing Existing Shared Documents**

- A Member logs in and sees "My Individual Documents" and "Our Shared Documents" sections.
- They view shared documents; editing depends on document status (draft vs. executed).

##### **Edge Cases (Shared Documents)**

- **One Member Unavailable:** Process stalls until all act; notifications remind them.
- **Disagreements:** Members resolve offline; the platform tracks versions.
- **Unlinking Mid-Creation:** Draft halts; data may be archived.
- **Post-Death/Incapacitation Access:** Surviving Member accesses per document terms and platform rules.

##### **Compliance Considerations (Shared Documents)**

- **Data Integrity:** Logs track changes with Member identities.
- **Audit Trails:** All actions on shared documents are logged.
- **HIPAA/SOC2:** Shared data is encrypted with strict access controls.

##### **UI Components (Shared Documents)**

- **Shared Document Section/Tab:** Separates shared items in the dashboard.
- **Co-Owner Indicators:** Lists all parties on a shared document.
- **Review Status Tracker:** Shows who has reviewed/approved.
- **Notifications:** Alerts for changes or required actions.
- **Joint Signing Package:** Handles multi-party signatures.
- **Property Data Form:** Fields for jointly owned property details.

---

#### **3\. Handling More Than Two Linked Users (Future Phase)**

- **Group Concept:** Supports "Planning Groups" for \>2 Members.
- **Group Admin:** One Member initiates and invites others.
- **Shared Scope:** Documents are shared within the group.
- **Permissions:** Equal rights for group-shared documents.
- **Workflows:** Extend review/approval to multiple parties.

---

#### **4\. Technical Specifications**

##### **Frontend (React with Redux, Amplify Hosting)**

- **New Routes:**
  - /member/settings/linked-accounts
  - /member/shared-documents
  - /member/invite-link
  - /accept-link/:token
- **State Management:** Tracks linked status and shared document data.

##### **Backend (GraphQL via AppSync)**

- **New/Modified APIs:**
  - initiateAccountLink(inviteeEmail: String\!)
  - confirmAccountLink(invitationToken: String\!, accept: Boolean\!)
  - unlinkAccount(linkedMemberId: ID\!)
  - createSharedDocument(type: DocumentType\!, linkedMemberIds: \[ID\!\]\!, documentData: JSON\!)
  - getSharedDocuments(linkedMemberId: ID)
  - approveSharedDocument(documentId: ID\!, memberId: ID\!)
  - getSharedProfileFields(invitationToken: String\!): SharedProfileFields – Returns shared fields (e.g., address, pets) from the inviter for pre-filling new user profiles.
- **Authorization:** Ensures access is limited to shared data only.

##### **Database (DynamoDB)**

- **AccountLinks Table:** linkId, member1Id, member2Id, status, invitationToken, tokenExpiry, createdAt, updatedAt.
- **Documents Table:** Adds ownershipType (individual/shared), linkedMemberIds, approvals (tracks Member approvals).
- **Users Table:** Stores profile fields; logic distinguishes shared vs. personal.

##### **Logging (CloudWatch)**

- Logs linking/unlinking and shared document actions.

---

#### **5\. Testing and Validation**

- **Unit Tests:** Validate linking, permissions, and profile field copying.
- **Integration Tests:** Test end-to-end linking and shared document creation.
- **Security Testing:** Ensure private data isn’t accessible; test linking security.
- **User Acceptance Testing (UAT):** Confirm intuitiveness for 65+ users and clarity of shared vs. individual items.

---

#### **6\. Compliance and Security**

- **Data Segregation:** Strict separation of individual and shared data.
- **Consent Management:** Logs consent for linking and shared data use.
- **Audit Trails:** Tracks all linked account and shared document actions.

---

#### **Summary**

This specification enables Members to link accounts for collaborative estate planning while preserving individual privacy. The new profile-sharing feature streamlines onboarding for couples, copying shared fields to reduce redundancy, with clear UI cues and editing flexibility. Built on AWS Amplify, the solution balances security, scalability, and usability for joint planning needs.
