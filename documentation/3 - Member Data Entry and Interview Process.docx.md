# 3 \- Member Data Entry and Interview Process

# **Document 3 \- Functional Specification: Member Data Entry and Interview Process**

## **Overview**

The Member Data Entry and Interview Process enables Members of the Childfree Legacy Web Application to input life, financial, and estate information through an intuitive, interview-style interface. Leveraging AWS Amplify, this specification outlines a conversational onboarding process that replaces traditional forms with guided questions, ensuring ease of use for the 65+ demographic. It includes mechanisms for data validation, secure storage, and integration with document generation, fostering an accessible experience while maintaining compliance with standards like HIPAA and SOC2. AWS Amplify’s data management, serverless functions, and encryption capabilities transform complex estate planning into a manageable, user-friendly task, empowering Members to provide accurate data for legally sound estate documents.

---

## **1\. Interview Process**

The interview process guides Members through conversational questions to collect essential estate planning data, prioritizing simplicity and accessibility.

### **Key Requirements**

#### **User Role**

- **Members**: End users managing their personal estate plans.

#### **Interview Format**

- Single-question display with branching logic based on responses.
- Plain, jargon-free language (e.g., "Who would you like to receive your home?" instead of "Designate a beneficiary for real property").
- Optional voice narration for questions to enhance accessibility.

#### **Data Categories**

- **Personal details**: Name, address, date of birth, contact information.
- **Financial assets**: Bank accounts, investments, real estate, personal property.
- **Estate preferences**: Beneficiaries, trustees, executors, pet care, power of attorney.
- **Emergency and medical**: Emergency contacts, healthcare directives.

#### **Educational Content**

- Contextual tooltips or brief videos (e.g., "What does a power of attorney do?").
- Definitions of key terms (e.g., "trust", "power of attorney").

#### **Progress Tracking**

- Visual progress bar indicating completion percentage.
- Option to save and resume the interview later.

### **User Flow**

1. Member logs in and navigates to /Member/interview post-onboarding.
2. System displays a welcome message: "Let’s walk through your estate plan together."
3. Questions appear one at a time with response options (e.g., text input, dropdowns, radio buttons).
4. Responses trigger relevant follow-ups (e.g., "Do you own a home?" → "Yes" → "Who should receive it?").
5. Educational content is offered at key decision points (e.g., a tooltip on "trustees").
6. Member can click "Save and Exit" to pause or "Next" to proceed.
7. Upon completion, Member is redirected to /Member/review to verify their inputs.

### **Edge Cases**

- **Incomplete Interview**: Save progress and send a reminder email via **AWS SES** to resume within 48 hours.
- **Contradictory Responses**: Flag issues (e.g., same person as trustee and beneficiary) and prompt clarification.
- **Accessibility**: Support keyboard navigation and screen readers with ARIA labels.

### **Compliance Considerations**

- **Data Accuracy**: Require confirmation for critical inputs (e.g., beneficiary names).
- **Privacy**: Encrypt all sensitive data per HIPAA and SOC2 using **AWS KMS**.
- **Audit Trails**: Log entries and edits in **CloudWatch** for auditing.

### **UI Components**

| Element             | Description                              |
| ------------------- | ---------------------------------------- |
| Question Text       | Large, readable text (min 16pt font)     |
| Response Fields     | Text inputs, dropdowns, or radio buttons |
| "Next" Button       | Advances to next question                |
| "Back" Button       | Revisits previous question               |
| Progress Bar        | Displays completion percentage           |
| Educational Tooltip | Click/hover for explanations             |
| "Save and Exit"     | Saves progress, logs out                 |

---

## **2\. Data Validation and Storage**

This section ensures the accuracy of Member inputs and securely stores data for downstream use.

### **Key Requirements**

#### **Validation Rules**

- **Mandatory fields**: Name, contact info, at least one beneficiary.
- **Format checks**: Email (e.g., <EMAIL>), phone (e.g., (*************).
- **Logical checks**: Asset values ≥ 0, birth date in past.

#### **Real-Time Feedback**

- Inline errors (e.g., "Please enter a valid phone number").
- Green checkmark for valid inputs.

#### **Data Storage**

- Encrypted storage in **Amazon DynamoDB** via **AWS AppSync**.
- Role-based access enforced via **Cognito User Groups**.

### **Validation Flow**

1. Member submits a response; system validates in real time using **Lambda resolvers**.
2. If invalid, an error message appears, and "Next" is disabled until corrected.
3. At interview end, a comprehensive validation ensures all required fields are complete.
4. Valid data is encrypted and stored in DynamoDB, linked to the Member’s profile.

### **Edge Cases**

- **Missing Mandatory Data**: Block completion until resolved.
- **Invalid Inputs**: Show examples (e.g., "Enter date as MM/DD/YYYY").
- **Sensitive Data**: Mask fields like Social Security numbers during input.

### **Compliance Considerations**

- **Data Minimization**: Collect only essential data.
- **Consent**: Log Member consent for data use/storage in DynamoDB.

### **UI Components**

| Element           | Description                                       |
| ----------------- | ------------------------------------------------- |
| Error Message     | Inline, red text for invalid inputs               |
| Success Indicator | Green checkmark for valid inputs                  |
| Input Example     | Gray text showing format (e.g., "(*************") |

---

## **3\. Integration with Document Generation**

Interview data is used to populate legal document templates, enabling Members to create personalized estate plans.

### **Key Requirements**

#### **Data Mapping**

- Responses map to document fields (e.g., "beneficiary_name" → will template).
- State-specific templates selected via Member’s address (no state selection allowed).

#### **Document Preview**

- Real-time preview showing user inputs in context.
- Editable sections highlighted.

#### **Version Control**

- Store document versions with timestamps in **Amazon S3**.
- Option to revert to prior versions.

### **Integration Flow**

1. Post-interview, system maps responses to document fields using **AWS Lambda**.
2. State-specific template is populated with Member data.
3. Member previews document at /Member/review.
4. Member confirms or edits data, triggering targeted re-interview questions.
5. Finalized documents are stored in **S3** and linked to the Member’s account.

### **Edge Cases**

- **Missing Data**: Prompt Member to complete missing sections.
- **State Variations**: Adjust templates for local laws (e.g., notary requirements).
- **Generation Failure**: Display error with support contact via **AWS SES**.

### **Compliance Considerations**

- **Legal Validity**: Ensure documents comply with state regulations.
- **Integrity**: Use hashes to verify document authenticity.

### **UI Components**

| Element          | Description                           |
| ---------------- | ------------------------------------- |
| Preview Window   | Displays populated document           |
| "Edit" Button    | Returns to specific interview section |
| "Confirm" Button | Finalizes document                    |

---

## **4\. Technical Specifications**

### **Frontend**

- **Framework**: React with Redux, hosted via **Amplify Hosting**.
- **Routes**:
  - /Member/interview: Interview interface.
  - /Member/review: Document review page.

### **Backend**

- **APIs (GraphQL via AppSync)**:
  - saveMemberData: Stores interview responses in DynamoDB.
  - getDocumentPreview: Returns document preview URL from S3.
- **Database (DynamoDB)**:
  - Member_data: user_id, question_id, response, timestamp
  - documents: id, user_id, type, version, content_url, status
- **Serverless Functions**: Use **AWS Lambda** for data mapping and document generation.

### **Logging**

- Log all data entries, validations, and document generations in **CloudWatch**.

---

## **5\. Testing and Validation**

- **Unit Tests**: Validate question responses and mappings.
- **Integration Tests**: Confirm interview-to-document flow.
- **Usability Testing**: Verify ease of use for 65+ users.
- **Security Testing**: Ensure no data vulnerabilities.

### **Test Cases**

| Scenario                | Expected Outcome                   |
| ----------------------- | ---------------------------------- |
| Full Interview          | Data saved, document generated     |
| Partial Interview       | Prompts for missing data on resume |
| Invalid Input           | Error shown, correction required   |
| Document Preview        | Matches Member inputs              |
| State-Specific Template | Correct state laws applied         |

---

## **6\. Compliance and Security**

- **HIPAA**: Encrypt data with **AWS KMS** and TLS.
- **SOC2**: Enforce access controls and logging via **Cognito** and **CloudWatch**.
- **Accessibility**: Meet WCAG 2.1 standards for seniors.
- **Retention**: Define data retention/deletion policies in DynamoDB.

---

## **Summary**

The Member Data Entry and Interview Process offers a conversational, user-friendly approach to estate planning for Members aged 65+. By guiding users through simple questions, validating inputs in real time, and integrating with state-specific document generation via AWS Amplify, it ensures accurate, secure, and compliant estate plans. This specification provides developers with a clear roadmap to implement a system that balances usability with robust security and legal standards.
