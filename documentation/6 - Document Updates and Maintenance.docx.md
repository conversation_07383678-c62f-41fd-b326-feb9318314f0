# 6 \- Document Updates and Maintenance

# **Document 6 \- Functional Specification: Document Updates and Maintenance**

## **Overview**

The Document Updates and Maintenance process ensures that estate planning documents—such as wills, trusts, and powers of attorney (POAs)—remain accurate, legally compliant, and reflective of Members’ current circumstances. Leveraging AWS Amplify, this specification outlines mechanisms for prompting Members to review and update their information via periodic notifications, processing those changes, regenerating affected documents, and maintaining version control with comprehensive audit trails. Tailored for the 65+ demographic, the system emphasizes usability, security (e.g., HIPAA and SOC2 compliance), and trust in maintaining document integrity over time. AWS Amplify’s serverless architecture, data storage, and encryption features deliver a scalable, secure, and intuitive experience.

---

## **1\. Periodic Notifications**

The system sends scheduled notifications to Members, encouraging them to review and update their estate planning information to keep it current.

### **Key Requirements**

#### **Notification Triggers**

- **Scheduled Reviews**: Occur every 36 months, configurable per Member preferences or state regulations.
- **Event-Based**: Initiated by significant life events (e.g., marriage, relocation) if reported by the Member.

#### **Notification Methods**

- Email (primary method) via **AWS Simple Email Service (SES)**.
- Optional SMS or in-app alerts via **AWS AppSync** subscriptions, based on Member settings.

#### **Content**

- Clear call-to-action (e.g., "Review Your Estate Plan Now").
- Brief explanation of the importance of updates.
- Secure link to /member/update for direct access, authenticated via **AWS Cognito**.

### **User Flow**

1. The system identifies Members due for review based on their last update date or reported life events using **AWS Lambda** triggers and **Amazon DynamoDB** queries.
2. An email notification is sent via **AWS SES** with a review prompt and a secure link to /member/update.
3. The Member clicks the link, authenticates via **AWS Cognito**, and is directed to the update interface.

### **Edge Cases**

- **Unresponsive Members**: If no response after 3 prompts (e.g., every 30 days), escalate to a phone call or mailed letter via an **AWS Step Functions** workflow.
- **Opt-Out Option**: Members can snooze reminders for a set period (e.g., 3 months), stored in **DynamoDB**.

### **Compliance Considerations**

- **Consent**: Members must opt-in to receive notifications, tracked in **DynamoDB** with a consent timestamp.
- **Audit Trails**: Log all notification sends and Member interactions in **Amazon CloudWatch** for compliance tracking.

### **UI Components**

| Element             | Description                               |
| ------------------- | ----------------------------------------- |
| Notification Banner | In-app alert highlighting pending reviews |
| "Review Now" Button | Direct link to the update process         |
| Snooze Option       | Allows postponing the reminder            |

---

## **2\. Update Process**

The update process enables Members to review and modify their estate planning information, capturing changes accurately and validating them for consistency.

### **Key Requirements**

#### **Data Review**

- Present current estate plan details in an editable format, fetched via **AWS AppSync** GraphQL queries.
- Highlight areas likely needing updates (e.g., beneficiaries, assets) using frontend logic.

#### **Change Capture**

- Record modifications with "before" and "after" snapshots stored in **DynamoDB**.
- Require explicit confirmation for significant changes (e.g., removing a beneficiary) via a confirmation modal.

#### **Validation**

- Enforce data format and logic rules (e.g., beneficiary percentages must total 100%) using **AWS AppSync** resolvers.

#### **Accessibility**

- Use large text, simple navigation, and optional voice guidance (via **AWS Polly**) for senior users.

### **User Flow**

1. Member navigates to /member/update.
2. The system displays a summary of the current estate plan retrieved from **DynamoDB** via **AWS AppSync**.
3. Member selects sections to edit (e.g., "Beneficiaries," "Assets").
4. For each section, Member modifies fields with real-time validation feedback from **AppSync**.
5. Member clicks "Save Updates" to confirm changes.
6. The system saves updates in **DynamoDB** and triggers document regeneration via **AWS Lambda**.

### **Edge Cases**

- **No Changes Needed**: Members can select "No updates required" to log the review in **DynamoDB** without changes.
- **Partial Updates**: Save progress in **DynamoDB** if the Member exits mid-process, with a reminder sent via **AWS SES**.

### **Compliance Considerations**

- **Data Accuracy**: Mandate double confirmation for critical edits, enforced via frontend and backend checks.
- **Privacy**: Encrypt updated data during transmission and at rest using **AWS Key Management Service (KMS)**.

### **UI Components**

| Element               | Description                                 |
| --------------------- | ------------------------------------------- |
| Section Tabs          | Navigation for categories like "Assets"     |
| Editable Fields       | Inputs with real-time validation indicators |
| "Save Updates" Button | Submits changes                             |
| Confirmation Modal    | Pop-up to validate significant edits        |

---

## **3\. Document Regeneration**

After updates are confirmed, the system regenerates affected documents to incorporate the latest Member information while preserving version history.

### **Key Requirements**

#### **Regeneration Triggers**

- Automatic regeneration following confirmed updates via **AWS Lambda**.
- Manual regeneration option available to Members or Administrators via an **AppSync** mutation.

#### **Template Selection**

- Apply the most recent state-specific template version stored in **DynamoDB**.

#### **Data Mapping**

- Populate templates with updated Member data using **AWS Lambda** and a templating library (e.g., Handlebars).

#### **Versioning**

- Increment document version (e.g., v1.0 to v1.1) in **DynamoDB**.
- Archive previous versions securely in **Amazon S3** with versioning enabled.

### **User Flow**

1. The system detects confirmed updates and queues document regeneration via **AWS Lambda**.
2. The appropriate template is fetched from **DynamoDB**, and updated data is mapped into it.
3. A new document version is generated and uploaded to **S3**.
4. The Member receives an email and in-app notification via **AWS SES** and **AppSync** with a link to review the new version at /member/documents.

### **Edge Cases**

- **Template Updates**: If a template changes between versions, notify the Member of potential legal implications via **SES**.
- **Regeneration Failure**: Notify Administrators via **Amazon SNS** and offer a manual regeneration option.

### **Compliance Considerations**

- **Legal Validity**: Ensure regenerated documents comply with current state laws, validated by backend logic.
- **Integrity**: Use cryptographic hashes (e.g., SHA-256) stored in **DynamoDB** to verify document authenticity.

### **UI Components**

| Element                   | Description                      |
| ------------------------- | -------------------------------- |
| Regeneration Status       | Indicates progress or completion |
| "View New Version" Button | Links to the updated document    |
| Alert Banner              | Highlights template changes      |

---

## **4\. Version Control and Audit Trails**

The system maintains a detailed history of document versions and logs all changes for transparency and compliance.

### **Key Requirements**

#### **Version History**

- Store each version with metadata (e.g., version number, timestamp, change summary) in **DynamoDB**.
- Allow Members to view (but not edit) prior versions via **AppSync** queries.

#### **Audit Trails**

- Log all updates, regenerations, and document accesses with user IDs and timestamps in **CloudWatch Logs**.
- Enable Administrators to generate audit reports via **AWS Lambda** and **Amazon S3** exports.

#### **Retention**

- Retain versions and logs for at least 10 years or as required by law, managed by **S3 Lifecycle Policies**.

### **User Flow**

1. Member visits /member/documents/history to access version history.
2. The system lists all versions with summaries (e.g., "Updated beneficiaries on \[date\]") via **AppSync**.
3. Member selects a version to view details or download it from **S3**.
4. Administrators generate audit reports via /admin/audit/documents, exported as CSV to **S3**.

### **Edge Cases**

- **Version Conflicts**: Lock documents during edits using **DynamoDB** conditional writes to prevent simultaneous changes.
- **Data Restoration**: Allow Administrators to revert to a prior version via **Lambda** if errors occur.

### **Compliance Considerations**

- **Regulatory Audits**: Ensure logs meet SOC2 and HIPAA standards, validated by **CloudWatch** retention settings.
- **Member Consent**: Record Member approvals for each version in **DynamoDB**.

### **UI Components**

| Element                | Description                            |
| ---------------------- | -------------------------------------- |
| Version Timeline       | Visual history of document versions    |
| "View Details" Button  | Shows version-specific details         |
| Audit Report Generator | Tool for Administrators to export logs |

---

## **5\. Technical Specifications**

### **Frontend**

- **Framework**: React with Redux, hosted via **AWS Amplify Hosting**.
- **Routes**:
  - /member/update: Interface for updating estate plan data.
  - /member/documents/history: Displays document version history.
  - /admin/audit/documents: Audit report generation for Administrators.

### **Backend**

- **APIs (GraphQL via AWS AppSync)**:
  - updateMemberData: Updates estate plan data in **DynamoDB**.
    - Input: { userId, updates }
    - Output: { success, changesLogged }
  - regenerateDocument: Triggers document regeneration via **Lambda**.
    - Input: { documentId }
    - Output: { success, newVersionId }
  - getDocumentHistory: Retrieves version history from **DynamoDB**.
    - Input: { userId, documentType }
    - Output: { versions: \[{ id, version, timestamp, summary }\] }
- **Database (Amazon DynamoDB)**:
  - document_versions:
    - Columns: id, document_id, version, content_url (S3 link), timestamp, change_summary
  - audit_logs:
    - Columns: id, user_id, action, details, timestamp
- **Storage (Amazon S3)**: Stores document versions with versioning enabled and server-side encryption.

### **Logging**

- Record all updates, regenerations, and document accesses in **CloudWatch Logs** with structured JSON formatting.

---

## **6\. Testing and Validation**

### **Test Types**

- **Unit Tests**: Verify update logic and version increments using Jest and AWS SDK mocks.
- **Integration Tests**: Validate the full notification-to-regeneration flow with Amplify CLI emulators.
- **Compliance Testing**: Confirm audit logs meet legal standards via automated scripts.
- **User Acceptance Testing (UAT)**: Ensure usability for users aged 65+ through moderated sessions.

### **Test Cases**

| Scenario                | Expected Outcome                     |
| ----------------------- | ------------------------------------ |
| Scheduled Notification  | Member receives and acts on prompt   |
| Data Update             | Changes saved and validated          |
| Document Regeneration   | New version reflects updates         |
| Version History Access  | Member views prior versions          |
| Audit Report Generation | Administrator accesses complete logs |

---

## **7\. Compliance and Security**

- **HIPAA**: Encrypt all data at rest and in transit with **AWS KMS**.
- **SOC2**: Implement strict access controls via **AWS Cognito** and detailed logging in **CloudWatch**.
- **State Laws**: Ensure documents comply with current legal requirements, validated by state-specific templates.
- **Data Integrity**: Use digital signatures (e.g., via **AWS Signer**) to verify document authenticity.

---

## **Summary**

The Document Updates and Maintenance process ensures that Members’ estate planning documents remain accurate and legally compliant through automated notifications, a streamlined update process, and robust version control. By leveraging AWS Amplify’s serverless capabilities, the system provides clear prompts, secure change processing, and transparent audit trails, empowering Members to maintain their plans effortlessly while meeting stringent security and compliance standards—reinforcing trust in the Childfree Legacy platform.
