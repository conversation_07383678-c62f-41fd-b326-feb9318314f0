# 9 \- Document Template Management

# **Document 9 \- Functional Specification: Document Template Management**

## **Overview**

The Document Template Management feature enables Administrators to create, maintain, and update document templates used in the estate planning process for the Childfree Legacy Web Application. Leveraging AWS Amplify, this specification outlines how Administrators manage state-specific templates (e.g., wills, trusts, powers of attorney) to ensure they reflect the latest legal standards and estate laws. It includes versioning, secure storage, mechanisms to propagate updates to existing Member documents, and a quarterly review and sign-off process to ensure ongoing compliance and accuracy. Designed with security (e.g., HIPAA and SOC2 compliance), legal precision, and administrative efficiency in mind, this feature safeguards the integrity of estate planning documents through AWS Amplify’s serverless capabilities, real-time data management, and robust encryption.

---

## **1\. Template Management**

Administrators can create, edit, and archive document templates, ensuring they remain up-to-date and compliant with state-specific legal requirements.

### **Key Requirements**

#### **Template Types**

- Wills, trusts, powers of attorney (POAs), advance healthcare directives, and other estate planning documents.

#### **State-Specific Variants**

- Templates accommodate state-specific regulations (e.g., witness requirements, notarization rules).

#### **Template Content**

- Supports placeholders (e.g., \[Member_NAME\], \[BENEFICIARY\]) for dynamic Member data.
- Uses rich text formatting for legal clauses and instructions.
- Allows optional tagging of clauses with legal references (e.g., "Witness Requirements: \[State\] Code Section X").

#### **Document Naming Convention**

- Generated or uploaded documents follow the format: \[Unique Identifier\]-\[Document Type\]-\[State\]-\[MMDDYYYY\] (e.g., 12345-Will-CA-03302025).
- The system auto-generates names using the Member’s unique identifier, document type, state, and current date via **AWS AppSync** queries.

#### **Security Measures**

- Templates are stored encrypted in **Amazon DynamoDB** using **AWS KMS** and accessible only to authorized Administrators via **AWS Cognito**.
- Sensitive operations (e.g., archiving templates) require multi-factor authentication (MFA) through **AWS Cognito**.

#### **Role-Based Access Control (RBAC)**

- Only Administrators with "Template Management" permissions, managed via **AWS Cognito User Groups**, can create, edit, or archive templates.

#### **Draft and Published States**

- Templates can be saved as drafts for review before publishing, managed through **AWS AppSync** mutations.

### **User Flow for Creating or Editing a Template**

1. Administrator navigates to /admin/templates.
2. Selects a state and document type (e.g., Will for California).
3. Clicks "Create New Template" or selects an existing template to edit.
4. Uses a rich text editor (integrated via a third-party library) to modify the template, inserting placeholders and legal tags.
5. Previews the template with sample data fetched via **AWS AppSync**.
6. Enters the effectivity date and saves as a draft or publishes it, triggering a new version in **DynamoDB**.

### **User Flow for Archiving a Template**

1. Administrator selects a template and clicks "Archive."
2. Confirms via a dialog: "Are you sure you want to archive this template?"
3. The template is archived by updating its end date in **DynamoDB**, making it inactive but retained for auditing.

### **Responding to Legal Changes**

- **Notification Mechanism**: Administrators receive real-time alerts on state-specific legal changes via **AWS AppSync subscriptions** in the "Legal Updates" dashboard.
- **Mass Download for Legal Review**: Administrators can download all active templates as a zip file using an **AWS Lambda** function that retrieves files from **Amazon S3**.
- **Template Updates**:
  - Administrators record legal updates in **DynamoDB**, flagging affected templates.
  - Flagged templates are marked "pending review" and updated in draft mode via **AWS AppSync**.
  - Drafts are reviewed by legal experts before publication.

### **Edge Cases**

- **Template in Use**: Prevents archiving active templates with an error: "Cannot archive: Template is currently active," enforced by **DynamoDB** constraints.
- **Concurrent Edits**: Manages edits by creating separate draft versions with unique effectivity dates, avoiding conflicts.
- **Single Active Template**: Ensures only one active template per type and state using **DynamoDB** queries.
- **Invalid Placeholders**: Flags unmapped placeholders before saving via frontend validation and **AppSync** resolvers.

### **Compliance Considerations**

- **Legal Review**: Templates are vetted by state-specific legal experts before publication.
- **Audit Trails**: Logs all actions (create, edit, archive) in **Amazon CloudWatch** with timestamps and admin IDs.
- **Data Privacy**: Encrypts template content at rest with **AWS KMS** and in transit with TLS.

### **UI Components**

| Element                      | Description                                          |
| ---------------------------- | ---------------------------------------------------- |
| Current Template List Table  | Displays active templates by state, type, start date |
| Archived Template List Table | Displays archived templates with start and end dates |
| "Create New Template" Button | Opens editor for new template creation               |
| Template Editor              | Rich text editor with placeholder and tagging tools  |
| Placeholder Dropdown         | Inserts dynamic fields (e.g., \[Member_NAME\])       |
| Legal Reference Tags         | Notes field for legal references                     |
| "Preview" Button             | Displays template with sample data                   |
| "Save Draft" Button          | Saves as draft without publishing                    |
| "Publish" Button             | Publishes as a new version                           |
| "Archive" Button             | Archives the template by setting an end date         |

---

## **2\. Versioning and Storage**

Each template update creates a new version, preserving historical versions for auditing, compliance, and restoration.

### **Key Requirements**

#### **Version Control**

- Each published version is named with an incremented number and start date (e.g., "v1.0 \- Start Date: 2023-01-01").
- Metadata includes version number, start/end dates, timestamp, change summary, and legal update references, stored in **DynamoDB**.

#### **Storage**

- Template content is stored in **Amazon S3** as encrypted objects, with metadata in **DynamoDB**.

#### **Access**

- Administrators can view and restore previous versions via **AWS AppSync** queries.
- Members cannot access template versions, enforced by **AWS Cognito** policies.

### **User Flow for Version Management**

1. Administrator selects a template and clicks "Version History."
2. System displays all versions with summaries, start/end dates, and legal references from **DynamoDB**.
3. Administrator can view differences or restore a version, creating a new draft via **AWS AppSync**.

### **Compliance Considerations**

- **Immutability**: Version history is immutable, ensured by **S3 Versioning** and **DynamoDB** backups.
- **Retention**: Versions are retained for 10 years using **S3 Lifecycle Policies**.

### **UI Components**

| Element                   | Description                                                     |
| ------------------------- | --------------------------------------------------------------- |
| Version History Table     | Lists versions with timestamps, summaries, and legal references |
| "View Differences" Button | Highlights changes between versions                             |
| "Restore Version" Button  | Creates a new draft from a previous version                     |

---

## **3\. Propagation of Updates**

Once templates are updated and published, changes are propagated to existing Member documents to maintain compliance and accuracy.

### **Types of Template Updates**

- **Language Changes**: Modifications to wording without requiring new Member data.
- **Member Information Changes**: Updates requiring additional Member data (e.g., new fields).

### **How Updates Are Managed**

1. **Identify Affected Documents**:
   - **AWS Lambda** functions, triggered by **DynamoDB Streams**, flag documents using outdated templates.
   - For Member Information Changes, identify documents lacking required data.
2. **Notify Members**:
   - Send notifications via **Amazon SNS** or **AWS SES** with update type and reason.
3. **Generate New Documents**:
   - **Language Changes**: Auto-generate new versions via **Lambda** and archive old ones in **S3**.
   - **Member Information Changes**: Prompt Members to provide new data via **AWS AppSync**, then generate new versions.
4. **Follow-Up**:
   - Send reminders via **AWS SES** if Members do not update within 60 days, scheduled via **Amazon EventBridge**.

### **Example**

- **Language Change**: Auto-generates new document versions for all affected Members.
- **Member Information Change**: Prompts Members to provide new witness information for California wills via /member/documents/update.

### **Administrator Oversight**

- Monitor updates via /admin/templates/propagate, tracking affected Members and actions with real-time data from **AppSync**.

### **Interface Elements**

| Component                      | Purpose                                       |
| ------------------------------ | --------------------------------------------- |
| Notification Banner            | Informs Members of updates with type and date |
| "Update Document" Button       | Directs to update page                        |
| Information Form               | Collects new Member data                      |
| "Submit and Regenerate" Button | Submits data and creates new version          |
| "View Updated Document" Button | Displays the new active document              |

---

## **4\. Regulatory Change Management and Quarterly Review**

The system ensures templates remain compliant with legal changes through manual updates and quarterly reviews.

### **Key Requirements**

#### **Detection of Legal Changes**

- Administrators enter updates via the "Legal Updates" dashboard, specifying state, document types, description, and effective date, stored in **DynamoDB**.

#### **Template Review and Update**

- Flagged templates are marked "pending review" in **DynamoDB**.
- Administrators update templates in draft mode, reviewed by legal experts before publication via **AppSync**.

#### **Propagation of Updates**

- Published updates trigger notifications to affected Members via **AWS SES**.

#### **Quarterly Review and Sign-Off**

- All templates are reviewed quarterly, automated by **AWS Lambda** and tracked in **DynamoDB**.
- Designated users sign off digitally via **AWS Cognito**, ensuring compliance.

### **Process Overview**

1. **Legal Change Detection**: Administrator records updates in **DynamoDB**.
2. **Template Flagging**: **Lambda** flags affected templates.
3. **Template Update**: Administrator creates and publishes new versions via **AppSync**.
4. **Member Notification**: Affected Members are notified via **SES**.
5. **Quarterly Review**: Templates are reviewed and signed off every quarter, logged in **CloudWatch**.

### **UI Components**

| Element                    | Description                                |
| -------------------------- | ------------------------------------------ |
| Legal Updates Dashboard    | Lists legal changes with details           |
| "Add Legal Update" Button  | Form to input new legal changes            |
| Affected Templates List    | Displays flagged templates                 |
| Quarterly Review Dashboard | Shows review status and sign-off deadlines |
| "Sign-Off" Button          | Approves templates after review            |

---

## **5\. Technical Specifications**

### **Frontend**

- **Framework**: React with Redux, hosted via **AWS Amplify Hosting**.
- **Routes**:
  - /admin/templates: Template management.
  - /admin/templates/edit/:id: Template editor.
  - /admin/templates/history/:id: Version history.
  - /admin/templates/propagate: Update propagation.
  - /admin/legal-updates: Legal updates dashboard.
  - /admin/quarterly-review: Quarterly review dashboard.
  - /member/documents/update: Member update page.

### **Backend**

- **APIs (GraphQL via AWS AppSync)**:
  - createTemplateDraft: Creates a new draft.
  - updateTemplateDraft: Updates a draft.
  - publishTemplate: Publishes a draft as a new version.
  - getTemplateHistory: Retrieves version history.
  - recordLegalUpdate: Records a legal update.
  - regenerateDocuments: Triggers document regeneration.
  - signOffTemplate: Records quarterly sign-off.
  - getQuarterlyReviewStatus: Retrieves review status.
- **Database (Amazon DynamoDB)**:
  - templates: id, state, type, status, current_version, last_review_date, next_review_date, sign_off_status, created_at, updated_at.
  - template_versions: id, template_id, version, content_url (S3), summary, legal_update_id, timestamp.
  - legal_updates: id, state, document_type, description, effective_date, created_at.
  - member_documents: id, member_id, template_id, version, content_url, status.
  - template_reviews: id, template_id, review_date, reviewer_id, sign_off_status, comments, timestamp.
- **Storage**: **Amazon S3** for template content, encrypted with **AWS KMS**.

### **Security**

- **Encryption**: **AWS KMS** for data at rest; TLS for data in transit.
- **Authentication**: **AWS Cognito** with MFA and RBAC.

### **Logging**

- Tracks all actions in **Amazon CloudWatch** with structured logs.

---

## **6\. Testing and Validation**

- **Unit Tests**: Validate template creation, versioning, and sign-offs using **AWS Amplify CLI** testing tools.
- **Integration Tests**: Ensure legal updates trigger reviews and propagation via **AppSync** and **Lambda**.
- **Compliance Testing**: Verify state-specific compliance post-update.
- **User Acceptance Testing (UAT)**: Confirm usability with **Amplify Hosting** previews.

### **Test Cases**

| Scenario                    | Expected Outcome                           |
| --------------------------- | ------------------------------------------ |
| Record Legal Update         | Affected templates flagged                 |
| Update Template             | New version published with legal reference |
| Notify Members              | Members receive detailed notification      |
| Member Regenerates Document | New compliant version created              |
| Quarterly Review Initiation | Templates marked for review                |
| Sign-Off on Template        | Template approved, sign-off logged         |

---

## **7\. Compliance and Security**

- **HIPAA**: Encrypts data with **AWS KMS**; secures transit with TLS.
- **SOC2**: Enforces RBAC via **AWS Cognito**; logs in **CloudWatch**.
- **Legal Compliance**: Ensures quarterly reviews and expert vetting.
- **Audit Trails**: Records all actions with timestamps and IDs in **CloudWatch**.

---

## **Summary**

The Document Template Management feature, powered by AWS Amplify, provides Administrators with a scalable, secure, and compliant system to manage state-specific estate planning templates. With robust versioning, seamless update propagation, and quarterly reviews, it ensures legal precision and reinforces trust in the Childfree Legacy platform.
