#!/usr/bin/env python3
"""
Git Statistics Analyzer
Analyzes git commit logs to generate developer working pattern statistics
"""

import subprocess
import re
from collections import defaultdict, Counter
from datetime import datetime, timedelta
import json

def get_git_log_data():
    """Get git log data with date, time and author information"""
    try:
        # Get detailed log with timestamps
        result = subprocess.run(
            ['git', 'log', '--pretty=format:%ad %an', '--date=iso'],
            capture_output=True,
            text=True,
            check=True
        )
        return result.stdout.strip().split('\n')
    except subprocess.CalledProcessError as e:
        print(f"Error running git command: {e}")
        return []

def normalize_author_name(author):
    """Normalize author names to handle variations"""
    # Map known variations to canonical names
    name_mapping = {
        'Oleksii': 'Oleksii Stupak',
        '<PERSON>ks<PERSON> Stupak': 'Oleks<PERSON> Stupak',
        'comalex': '<PERSON><PERSON><PERSON>',
        '<PERSON>': '<PERSON>',
        '<PERSON>': '<PERSON>'
    }
    return name_mapping.get(author, author)

def analyze_git_logs():
    """Analyze git logs and generate statistics"""
    log_lines = get_git_log_data()

    if not log_lines or log_lines == ['']:
        print("No git log data found")
        return {}, {}, {}

    # Data structures for analysis
    daily_commits = defaultdict(lambda: defaultdict(int))  # date -> author -> count
    hourly_commits = defaultdict(lambda: defaultdict(lambda: defaultdict(int)))  # date -> author -> hour -> count
    author_stats = defaultdict(lambda: {
        'total_commits': 0,
        'active_days': set(),
        'first_commit': None,
        'last_commit': None,
        'working_hours': defaultdict(int),  # hour -> commit_count
        'daily_sessions': defaultdict(list)  # date -> list of commit times
    })

    # Parse each log line
    for line in log_lines:
        if not line.strip():
            continue

        # Parse ISO format: "2025-06-10 14:30:25 +0200 Author Name"
        match = re.match(r'(\d{4}-\d{2}-\d{2}) (\d{2}):(\d{2}):(\d{2}) ([+-]\d{4}) (.+)', line)
        if not match:
            continue

        date_str, hour_str, minute_str, second_str, timezone, author = match.groups()
        author = normalize_author_name(author.strip())

        try:
            commit_date = datetime.strptime(date_str, '%Y-%m-%d').date()
            commit_hour = int(hour_str)
            commit_time = datetime.strptime(f"{date_str} {hour_str}:{minute_str}:{second_str}", '%Y-%m-%d %H:%M:%S')
        except ValueError:
            continue

        # Update statistics
        daily_commits[commit_date][author] += 1
        hourly_commits[commit_date][author][commit_hour] += 1
        author_stats[author]['total_commits'] += 1
        author_stats[author]['active_days'].add(commit_date)
        author_stats[author]['working_hours'][commit_hour] += 1
        author_stats[author]['daily_sessions'][commit_date].append(commit_time)

        # Track first and last commit dates
        if author_stats[author]['first_commit'] is None or commit_date < author_stats[author]['first_commit']:
            author_stats[author]['first_commit'] = commit_date
        if author_stats[author]['last_commit'] is None or commit_date > author_stats[author]['last_commit']:
            author_stats[author]['last_commit'] = commit_date

    return daily_commits, author_stats, hourly_commits

def estimate_working_time(daily_sessions):
    """Estimate working time based on commit patterns using session-based approach"""
    working_times = {}
    session_details = {}

    for date, commits in daily_sessions.items():
        if not commits:
            continue

        # Sort commits by time
        commits.sort()

        if len(commits) == 1:
            # Single commit - estimate 30 minutes of work
            working_times[date] = 0.5
            session_details[date] = [{'start': commits[0], 'end': commits[0], 'duration': 0.5, 'commits': 1}]
        else:
            # Group commits into work sessions
            # If gap between commits > 2 hours, consider it a new session
            sessions = []
            current_session_start = commits[0]
            current_session_commits = [commits[0]]

            for i in range(1, len(commits)):
                time_gap = (commits[i] - commits[i-1]).total_seconds() / 3600  # hours

                if time_gap <= 2.0:  # Same session if gap <= 2 hours
                    current_session_commits.append(commits[i])
                else:  # New session
                    # Close current session
                    session_end = current_session_commits[-1]
                    session_duration = max(0.5, (session_end - current_session_start).total_seconds() / 3600 + 0.5)  # Add 30min buffer
                    sessions.append({
                        'start': current_session_start,
                        'end': session_end,
                        'duration': min(session_duration, 8.0),  # Cap single session at 8 hours
                        'commits': len(current_session_commits)
                    })

                    # Start new session
                    current_session_start = commits[i]
                    current_session_commits = [commits[i]]

            # Close the last session
            session_end = current_session_commits[-1]
            session_duration = max(0.5, (session_end - current_session_start).total_seconds() / 3600 + 0.5)
            sessions.append({
                'start': current_session_start,
                'end': session_end,
                'duration': min(session_duration, 8.0),
                'commits': len(current_session_commits)
            })

            # Total working time is sum of all sessions
            total_time = sum(session['duration'] for session in sessions)
            working_times[date] = min(total_time, 12.0)  # Cap total daily time at 12 hours
            session_details[date] = sessions

    return working_times, session_details

def generate_statistics_report(daily_commits, author_stats, hourly_commits):
    """Generate comprehensive statistics report"""
    report = []
    report.append("=" * 80)
    report.append("GIT REPOSITORY DEVELOPER ACTIVITY STATISTICS")
    report.append("=" * 80)
    report.append(f"Analysis generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")
    
    # Overall summary
    total_commits = sum(sum(authors.values()) for authors in daily_commits.values())
    total_active_days = len(daily_commits)
    total_authors = len(author_stats)
    
    report.append("OVERALL SUMMARY")
    report.append("-" * 40)
    report.append(f"Total commits: {total_commits}")
    report.append(f"Total active days: {total_active_days}")
    report.append(f"Total contributors: {total_authors}")
    report.append("")
    
    # Author statistics
    report.append("DEVELOPER STATISTICS")
    report.append("-" * 40)

    # Sort authors by total commits (descending)
    sorted_authors = sorted(author_stats.items(), key=lambda x: x[1]['total_commits'], reverse=True)

    for author, stats in sorted_authors:
        active_days_count = len(stats['active_days'])
        avg_commits_per_day = stats['total_commits'] / active_days_count if active_days_count > 0 else 0

        # Calculate estimated working time
        working_times, session_details = estimate_working_time(stats['daily_sessions'])
        total_working_hours = sum(working_times.values())
        avg_hours_per_day = total_working_hours / active_days_count if active_days_count > 0 else 0

        report.append(f"Author: {author}")
        report.append(f"  Total commits: {stats['total_commits']}")
        report.append(f"  Active days: {active_days_count}")
        report.append(f"  Average commits per active day: {avg_commits_per_day:.2f}")
        report.append(f"  Estimated total working hours: {total_working_hours:.1f}")
        report.append(f"  Average working hours per active day: {avg_hours_per_day:.1f}")
        report.append(f"  First commit: {stats['first_commit']}")
        report.append(f"  Last commit: {stats['last_commit']}")

        if stats['first_commit'] and stats['last_commit']:
            period = (stats['last_commit'] - stats['first_commit']).days + 1
            report.append(f"  Active period: {period} days")
            if period > 0:
                activity_rate = (active_days_count / period) * 100
                report.append(f"  Activity rate: {activity_rate:.1f}% of days in period")

        # Working hours distribution
        if stats['working_hours']:
            report.append(f"  Most active hours:")
            sorted_hours = sorted(stats['working_hours'].items(), key=lambda x: x[1], reverse=True)
            for hour, count in sorted_hours[:5]:  # Top 5 hours
                report.append(f"    {hour:02d}:00-{hour+1:02d}:00: {count} commits")

        # Daily working hours breakdown with sessions
        if working_times:
            report.append(f"  Daily working hours breakdown:")
            sorted_working_days = sorted(working_times.items(), reverse=True)  # Most recent first
            for date, hours in sorted_working_days:
                weekday = date.strftime('%A')
                commits_on_day = len(stats['daily_sessions'][date]) if date in stats['daily_sessions'] else 0
                sessions = session_details.get(date, [])

                report.append(f"    {date} ({weekday}): {hours:.1f} hours ({commits_on_day} commits)")

                # Show work sessions for this day
                if len(sessions) > 1:
                    report.append(f"      Work sessions:")
                    for i, session in enumerate(sessions, 1):
                        start_time = session['start'].strftime('%H:%M')
                        end_time = session['end'].strftime('%H:%M')
                        duration = session['duration']
                        commits_in_session = session['commits']
                        report.append(f"        Session {i}: {start_time}-{end_time} ({duration:.1f}h, {commits_in_session} commits)")
                elif len(sessions) == 1:
                    session = sessions[0]
                    start_time = session['start'].strftime('%H:%M')
                    end_time = session['end'].strftime('%H:%M')
                    if session['commits'] > 1:
                        report.append(f"      Single session: {start_time}-{end_time}")

        report.append("")
    
    # Daily activity breakdown with working time estimates
    report.append("DAILY ACTIVITY BREAKDOWN WITH WORKING TIME ESTIMATES")
    report.append("-" * 60)

    # Sort dates in descending order (most recent first)
    sorted_dates = sorted(daily_commits.keys(), reverse=True)

    for date in sorted_dates:
        authors_on_date = daily_commits[date]
        total_commits_on_date = sum(authors_on_date.values())

        report.append(f"{date} ({date.strftime('%A')}) - {total_commits_on_date} commits")

        # Sort authors by commits on this date
        sorted_authors_date = sorted(authors_on_date.items(), key=lambda x: x[1], reverse=True)
        for author, commits in sorted_authors_date:
            # Calculate estimated working time for this author on this date
            if author in author_stats and date in author_stats[author]['daily_sessions']:
                working_times, session_details = estimate_working_time({date: author_stats[author]['daily_sessions'][date]})
                estimated_hours = working_times.get(date, 0)
                sessions = session_details.get(date, [])

                report.append(f"  {author}: {commits} commits (~{estimated_hours:.1f} hours)")

                # Show work sessions for this day
                if len(sessions) > 1:
                    report.append(f"    Work sessions:")
                    for i, session in enumerate(sessions, 1):
                        start_time = session['start'].strftime('%H:%M')
                        end_time = session['end'].strftime('%H:%M')
                        duration = session['duration']
                        commits_in_session = session['commits']
                        report.append(f"      Session {i}: {start_time}-{end_time} ({duration:.1f}h, {commits_in_session} commits)")
                elif len(sessions) == 1:
                    session = sessions[0]
                    start_time = session['start'].strftime('%H:%M')
                    end_time = session['end'].strftime('%H:%M')
                    if session['commits'] > 1:
                        report.append(f"    Session: {start_time}-{end_time}")

                # Show hourly distribution for this day
                if date in hourly_commits and author in hourly_commits[date]:
                    hours_worked = []
                    for hour, count in sorted(hourly_commits[date][author].items()):
                        if count > 0:
                            hours_worked.append(f"{hour:02d}h({count})")
                    if hours_worked:
                        report.append(f"    Hours active: {', '.join(hours_worked)}")
            else:
                report.append(f"  {author}: {commits} commits")
        report.append("")
    
    # Weekly patterns
    report.append("WEEKLY PATTERNS")
    report.append("-" * 40)
    
    weekday_stats = defaultdict(lambda: defaultdict(int))
    for date, authors in daily_commits.items():
        weekday = date.strftime('%A')
        for author, commits in authors.items():
            weekday_stats[weekday][author] += commits
    
    weekdays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
    for weekday in weekdays:
        if weekday in weekday_stats:
            total_commits = sum(weekday_stats[weekday].values())
            report.append(f"{weekday}: {total_commits} commits")
            for author, commits in sorted(weekday_stats[weekday].items(), key=lambda x: x[1], reverse=True):
                report.append(f"  {author}: {commits} commits")
        else:
            report.append(f"{weekday}: 0 commits")
        report.append("")
    
    # Monthly patterns
    report.append("MONTHLY PATTERNS")
    report.append("-" * 40)
    
    monthly_stats = defaultdict(lambda: defaultdict(int))
    for date, authors in daily_commits.items():
        month_key = date.strftime('%Y-%m')
        for author, commits in authors.items():
            monthly_stats[month_key][author] += commits
    
    for month in sorted(monthly_stats.keys(), reverse=True):
        total_commits = sum(monthly_stats[month].values())
        report.append(f"{month}: {total_commits} commits")
        for author, commits in sorted(monthly_stats[month].items(), key=lambda x: x[1], reverse=True):
            report.append(f"  {author}: {commits} commits")
        report.append("")
    
    return "\n".join(report)

def main():
    """Main function to run the analysis"""
    print("Analyzing git repository...")

    daily_commits, author_stats, hourly_commits = analyze_git_logs()

    if not daily_commits:
        print("No commit data found to analyze")
        return

    # Generate report
    report = generate_statistics_report(daily_commits, author_stats, hourly_commits)

    # Save to file
    output_file = "git_developer_statistics_with_time.txt"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(report)

    print(f"Statistics report saved to: {output_file}")
    print("\nPreview of the report:")
    print("-" * 50)
    # Show first 50 lines of the report
    lines = report.split('\n')
    for line in lines[:50]:
        print(line)

    if len(lines) > 50:
        print(f"\n... and {len(lines) - 50} more lines in the full report file.")

if __name__ == "__main__":
    main()
