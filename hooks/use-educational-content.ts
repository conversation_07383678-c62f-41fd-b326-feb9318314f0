/**
 * useEducationalContent Hook
 *
 * Custom React hook for managing educational content with Amplify.
 */

'use client';

import { useState, useCallback, useEffect } from 'react';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { toast } from 'sonner';
import {
  Content,
  ContentType,
  ContentFilters,
  ContentAnalytics,
  ContentIntegrationPoint,
  ContentStatus,
} from '../app/types/education';

const client = generateClient<Schema>();

// Amplify content type
type AmplifyContentStatus = 'draft' | 'published' | 'archived';
type AmplifyContentType =
  | 'video'
  | 'article'
  | 'infographic'
  | 'avatar'
  | 'tooltip';

type EducationalContent = {
  id: string;
  title: string;
  type?: AmplifyContentType | null;
  status?: AmplifyContentStatus | null;
  description?: string | null;
  tags?: string[] | null;
  contentUrl?: string | null;
  thumbnailUrl?: string | null;
  duration?: number | null;
  readingTime?: number | null;
  metadata?: any | null;
  createdAt?: string | null;
  updatedAt?: string | null;
  version?: number | null;
};

interface UseEducationalContentReturn {
  // Content retrieval
  allContent: Content[];
  content: EducationalContent[]; // Amplify content
  contentById: (id: string) => Content | undefined;
  contentByType: (type: ContentType) => Content[];
  contentByTags: (tags: string[]) => Content[];
  contentForIntegrationPoint: (point: ContentIntegrationPoint) => Content[];
  filteredContent: Content[];

  // Content filtering
  filters: ContentFilters;
  setFilters: (filters: ContentFilters) => void;

  // Content analytics
  contentAnalytics: (contentId: string) => ContentAnalytics | undefined;
  trackContentView: (contentId: string) => void;
  submitContentFeedback: (
    contentId: string,
    rating: number,
    comment?: string
  ) => void;

  // CRUD operations
  createContent: (data: Partial<EducationalContent>) => Promise<void>;
  updateContent: (
    id: string,
    data: Partial<EducationalContent>
  ) => Promise<void>;
  deleteContent: (id: string) => Promise<void>;
  refreshContent: () => Promise<void>;
  getContentById: (id: string) => EducationalContent | undefined;
  getContentByType: (type: AmplifyContentType) => EducationalContent[];
  getContentByStatus: (status: AmplifyContentStatus) => EducationalContent[];

  // Loading states
  isLoading: boolean;
  error: string | null;
}

export function useEducationalContent(): UseEducationalContentReturn {
  const [content, setContent] = useState<Content[]>([]);
  const [amplifyContent, setAmplifyContent] = useState<EducationalContent[]>(
    []
  );
  const [filters, setFilters] = useState<ContentFilters>({});
  const [filteredContent, setFilteredContent] = useState<Content[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch content from Amplify
  const fetchAmplifyContent = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      if (client.models && client.models.EducationalContent) {
        const { data } = await client.models.EducationalContent.list();
        setAmplifyContent((data || []) as EducationalContent[]);
      } else {
        setAmplifyContent([]);
      }
    } catch (err) {
      console.error('Error fetching educational content:', err);
      setError('Failed to load educational content');
      toast.error('Failed to load educational content');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Initialize content
  useEffect(() => {
    const initializeContent = async () => {
      await fetchAmplifyContent();
      // Initialize empty legacy content for now
      setContent([]);
    };

    initializeContent();
  }, [fetchAmplifyContent]);

  // Apply filters when content or filters change
  useEffect(() => {
    if (!content.length) return;

    let filtered = [...content];

    // Filter by type
    if (filters.type) {
      filtered = filtered.filter(item => item.type === filters.type);
    }

    // Filter by tags
    if (filters.tags && filters.tags.length > 0) {
      filtered = filtered.filter(item =>
        item.tags.some(tag => filters.tags?.includes(tag))
      );
    }

    // Filter by search term
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(
        item =>
          item.title.toLowerCase().includes(searchLower) ||
          item.description.toLowerCase().includes(searchLower)
      );
    }

    // Filter by status
    if (filters.status) {
      filtered = filtered.filter(item => item.status === filters.status);
    }

    setFilteredContent(filtered);
  }, [content, filters]);

  // Get content by ID (legacy function for backward compatibility)
  const contentById = useCallback(
    (id: string): Content | undefined => {
      // Return from legacy content for now
      return content.find(item => item.id === id);
    },
    [content]
  );

  // Get content by type (legacy function for backward compatibility)
  const contentByType = useCallback(
    (type: ContentType): Content[] => {
      // Return from legacy content for now
      return content.filter(item => item.type === type);
    },
    [content]
  );

  // Get content by tags (legacy function for backward compatibility)
  const contentByTags = useCallback(
    (tags: string[]): Content[] => {
      // Return from legacy content for now
      return content.filter(item => item.tags.some(tag => tags.includes(tag)));
    },
    [content]
  );

  // Get content for integration point (legacy function for backward compatibility)
  const contentForIntegrationPoint = useCallback(
    (_point: ContentIntegrationPoint): Content[] => {
      // Return empty array for now - this would need proper implementation
      return [];
    },
    [content]
  );

  // Get content analytics (legacy function for backward compatibility)
  const contentAnalytics = useCallback(
    (contentId: string): ContentAnalytics | undefined => {
      // Return mock analytics for now
      return {
        contentId,
        views: 0,
        completionRate: 0,
        averageRating: 0,
        feedback: [],
      };
    },
    []
  );

  // Track content view
  const trackContentView = useCallback((_contentId: string): void => {
    // In a real implementation, this would call an API to track the view
  }, []);

  // Submit content feedback
  const submitContentFeedback = useCallback(
    (_contentId: string, _rating: number, _comment?: string): void => {
      // In a real implementation, this would call an API to submit feedback
    },
    []
  );

  // CRUD operations for Amplify content
  const createContent = useCallback(
    async (data: Partial<EducationalContent>) => {
      try {
        setError(null);

        if (client.models && client.models.EducationalContent) {
          const result = await client.models.EducationalContent.create({
            title: data.title!,
            type: data.type!,
            status: data.status || ContentStatus.DRAFT,
            description: data.description,
            contentUrl: data.contentUrl,
            thumbnailUrl: data.thumbnailUrl,
            duration: data.duration,
            readingTime: data.readingTime,
            tags: data.tags || [],
            metadata: data.metadata,
            version: data.version || 1,
          });

          if (result.data) {
            toast.success('Content created successfully');
            await fetchAmplifyContent();
          }
        } else {
          toast.error('EducationalContent model not available');
          throw new Error('EducationalContent model not available');
        }
      } catch (err) {
        console.error('Error creating content:', err);
        setError('Failed to create content');
        toast.error('Failed to create content');
        throw err;
      }
    },
    [fetchAmplifyContent]
  );

  const updateContent = useCallback(
    async (id: string, data: Partial<EducationalContent>) => {
      try {
        setError(null);

        if (client.models && client.models.EducationalContent) {
          const result = await client.models.EducationalContent.update({
            id,
            ...data,
          });

          if (result.data) {
            toast.success('Content updated successfully');
            await fetchAmplifyContent();
          }
        } else {
          toast.error('EducationalContent model not available');
          throw new Error('EducationalContent model not available');
        }
      } catch (err) {
        console.error('Error updating content:', err);
        setError('Failed to update content');
        toast.error('Failed to update content');
        throw err;
      }
    },
    [fetchAmplifyContent]
  );

  const deleteContent = useCallback(
    async (id: string) => {
      try {
        setError(null);

        if (client.models && client.models.EducationalContent) {
          await client.models.EducationalContent.delete({ id });
          toast.success('Content deleted successfully');
          await fetchAmplifyContent();
        } else {
          toast.error('EducationalContent model not available');
          throw new Error('EducationalContent model not available');
        }
      } catch (err) {
        console.error('Error deleting content:', err);
        setError('Failed to delete content');
        toast.error('Failed to delete content');
        throw err;
      }
    },
    [fetchAmplifyContent]
  );

  const refreshContent = useCallback(async () => {
    await fetchAmplifyContent();
  }, [fetchAmplifyContent]);

  // Amplify content getters
  const getContentById = useCallback(
    (id: string) => {
      return amplifyContent.find(item => item.id === id);
    },
    [amplifyContent]
  );

  const getContentByType = useCallback(
    (type: AmplifyContentType) => {
      return amplifyContent.filter(item => item.type === type);
    },
    [amplifyContent]
  );

  const getContentByStatus = useCallback(
    (status: AmplifyContentStatus) => {
      return amplifyContent.filter(item => item.status === status);
    },
    [amplifyContent]
  );

  return {
    allContent: content,
    content: amplifyContent, // Amplify content
    contentById,
    contentByType,
    contentByTags,
    contentForIntegrationPoint,
    filteredContent,
    filters,
    setFilters,
    contentAnalytics,
    trackContentView,
    submitContentFeedback,
    // CRUD operations
    createContent,
    updateContent,
    deleteContent,
    refreshContent,
    getContentById,
    getContentByType,
    getContentByStatus,
    isLoading,
    error,
  };
}
