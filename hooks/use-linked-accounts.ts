import { useState, useEffect, useCallback } from 'react';
import {
  LinkedAccount,
  LinkRequest,
  LinkStatus,
  LinkType,
} from '@/types/account';
import { initMockData, mockLinkedAccounts } from '@/lib/mock/linked-accounts';

// Initialize mock data
const { users, linkedAccounts } = initMockData();

// Mock API functions
const mockApi = {
  // Get linked accounts for the current user
  getLinkedAccounts: async (
    direction: 'outgoing' | 'incoming' | 'both' = 'outgoing'
  ): Promise<LinkedAccount[]> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Mock current user ID (in a real app, this would come from auth context)
    const currentUserId = 'user_1';

    let result: LinkedAccount[] = [];

    if (direction === 'outgoing' || direction === 'both') {
      result = [
        ...result,
        ...linkedAccounts.filter(link => link.userId === currentUserId),
      ];
    }

    if (direction === 'incoming' || direction === 'both') {
      result = [
        ...result,
        ...linkedAccounts.filter(link => link.linkedUserId === currentUserId),
      ];
    }

    return result;
  },

  // Link a new account
  linkAccount: async (request: LinkRequest): Promise<LinkedAccount> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 800));

    // Mock current user ID
    const currentUserId = 'user_1';

    // Find the target user by email
    const targetUser = users.find(
      user => user.email.toLowerCase() === request.email.toLowerCase()
    );

    if (!targetUser) {
      throw new Error(`No user found with email ${request.email}`);
    }

    // Check if the user is trying to link to themselves
    if (targetUser.id === currentUserId) {
      throw new Error('You cannot link your account to itself');
    }

    // Check if the link already exists
    const existingLink = linkedAccounts.find(
      link =>
        link.userId === currentUserId && link.linkedUserId === targetUser.id
    );

    if (existingLink) {
      throw new Error('You already have a link with this user');
    }

    // Create a new link
    const newLink: LinkedAccount = {
      id: `link_${Date.now()}`,
      userId: currentUserId,
      linkedUserId: targetUser.id,
      linkType: request.linkType,
      status: 'pending',
      permissions: request.permissions || [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      expiresAt: request.expiresAt,
    };

    // Add the link to the mock data
    linkedAccounts.push(newLink);

    return newLink;
  },

  // Unlink an account
  unlinkAccount: async (linkId: string): Promise<void> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 600));

    // Mock current user ID
    const currentUserId = 'user_1';

    // Find the link
    const linkIndex = linkedAccounts.findIndex(link => link.id === linkId);

    if (linkIndex === -1) {
      throw new Error(`No link found with ID ${linkId}`);
    }

    const link = linkedAccounts[linkIndex];

    // Check if the user is authorized to remove this link
    if (link.userId !== currentUserId) {
      throw new Error('You are not authorized to remove this link');
    }

    // Remove the link
    linkedAccounts.splice(linkIndex, 1);
  },

  // Update link status
  updateLinkStatus: async (
    linkId: string,
    status: LinkStatus
  ): Promise<LinkedAccount> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Mock current user ID
    const currentUserId = 'user_1';

    // Find the link
    const link = linkedAccounts.find(link => link.id === linkId);

    if (!link) {
      throw new Error(`No link found with ID ${linkId}`);
    }

    // Check if the user is authorized to update this link
    if (link.userId !== currentUserId && link.linkedUserId !== currentUserId) {
      throw new Error('You are not authorized to update this link');
    }

    // Update the link status
    link.status = status;
    link.updatedAt = new Date().toISOString();

    return link;
  },

  // Update link permissions
  updateLinkPermissions: async (
    linkId: string,
    permissions: string[]
  ): Promise<LinkedAccount> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Mock current user ID
    const currentUserId = 'user_1';

    // Find the link
    const link = linkedAccounts.find(link => link.id === linkId);

    if (!link) {
      throw new Error(`No link found with ID ${linkId}`);
    }

    // Check if the user is authorized to update this link
    if (link.userId !== currentUserId) {
      throw new Error('Only the link owner can update permissions');
    }

    // Update the link permissions
    link.permissions = permissions;
    link.updatedAt = new Date().toISOString();

    return link;
  },
};

// Hook for managing linked accounts
export function useLinkedAccounts() {
  const [outgoingLinks, setOutgoingLinks] = useState<LinkedAccount[]>([]);
  const [incomingLinks, setIncomingLinks] = useState<LinkedAccount[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch linked accounts
  const fetchLinkedAccounts = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const outgoing = await mockApi.getLinkedAccounts('outgoing');
      const incoming = await mockApi.getLinkedAccounts('incoming');

      setOutgoingLinks(outgoing);
      setIncomingLinks(incoming);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : 'Failed to fetch linked accounts'
      );
    } finally {
      setLoading(false);
    }
  }, []);

  // Link a new account
  const linkAccount = useCallback(async (request: LinkRequest) => {
    setLoading(true);
    setError(null);

    try {
      const newLink = await mockApi.linkAccount(request);
      setOutgoingLinks(prev => [...prev, newLink]);
      return newLink;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to link account');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Unlink an account
  const unlinkAccount = useCallback(async (linkId: string) => {
    setLoading(true);
    setError(null);

    try {
      await mockApi.unlinkAccount(linkId);
      setOutgoingLinks(prev => prev.filter(link => link.id !== linkId));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to unlink account');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Update link status
  const updateLinkStatus = useCallback(
    async (linkId: string, status: LinkStatus) => {
      setLoading(true);
      setError(null);

      try {
        const updatedLink = await mockApi.updateLinkStatus(linkId, status);

        // Update the appropriate list based on whether it's an outgoing or incoming link
        if (updatedLink.userId === 'user_1') {
          setOutgoingLinks(prev =>
            prev.map(link => (link.id === linkId ? updatedLink : link))
          );
        } else {
          setIncomingLinks(prev =>
            prev.map(link => (link.id === linkId ? updatedLink : link))
          );
        }

        return updatedLink;
      } catch (err) {
        setError(
          err instanceof Error ? err.message : 'Failed to update link status'
        );
        throw err;
      } finally {
        setLoading(false);
      }
    },
    []
  );

  // Update link permissions
  const updateLinkPermissions = useCallback(
    async (linkId: string, permissions: string[]) => {
      setLoading(true);
      setError(null);

      try {
        const updatedLink = await mockApi.updateLinkPermissions(
          linkId,
          permissions
        );
        setOutgoingLinks(prev =>
          prev.map(link => (link.id === linkId ? updatedLink : link))
        );
        return updatedLink;
      } catch (err) {
        setError(
          err instanceof Error
            ? err.message
            : 'Failed to update link permissions'
        );
        throw err;
      } finally {
        setLoading(false);
      }
    },
    []
  );

  // Load linked accounts on mount
  useEffect(() => {
    fetchLinkedAccounts();
  }, [fetchLinkedAccounts]);

  return {
    outgoingLinks,
    incomingLinks,
    loading,
    error,
    linkAccount,
    unlinkAccount,
    updateLinkStatus,
    updateLinkPermissions,
    refreshLinks: fetchLinkedAccounts,
  };
}
