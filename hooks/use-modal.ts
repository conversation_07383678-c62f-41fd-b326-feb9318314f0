'use client';

import { useState, useCallback, useRef, useEffect } from 'react';

export interface ModalState {
  isOpen: boolean;
  data?: any;
  loading?: boolean;
}

export interface ModalActions {
  open: (data?: any) => void;
  close: () => void;
  setLoading: (loading: boolean) => void;
  confirm: (action: () => Promise<void> | void) => Promise<void>;
}

export interface UseModalReturn extends ModalState, ModalActions {
  modalProps: {
    open: boolean;
    onOpenChange: (open: boolean) => void;
  };
}

/**
 * Advanced modal management hook with loading states and confirmation patterns
 *
 * @param initialState - Initial modal state
 * @returns Modal state and actions
 *
 * @example
 * ```tsx
 * const deleteModal = useModal();
 *
 * const handleDelete = async () => {
 *   deleteModal.open({ attorney });
 * };
 *
 * const confirmDelete = async () => {
 *   await deleteModal.confirm(async () => {
 *     await deleteAttorney(deleteModal.data.attorney.id);
 *     toast.success('Attorney deleted');
 *   });
 * };
 *
 * return (
 *   <>
 *     <Button onClick={handleDelete}>Delete</Button>
 *     <ConfirmationDialog
 *       {...deleteModal.modalProps}
 *       onConfirm={confirmDelete}
 *       loading={deleteModal.loading}
 *     />
 *   </>
 * );
 * ```
 */
export function useModal(
  initialState: Partial<ModalState> = {}
): UseModalReturn {
  const [state, setState] = useState<ModalState>({
    isOpen: false,
    data: undefined,
    loading: false,
    ...initialState,
  });

  // Use ref to avoid stale closures in async operations
  const stateRef = useRef(state);
  stateRef.current = state;

  const open = useCallback((data?: any) => {
    setState(prev => ({
      ...prev,
      isOpen: true,
      data,
      loading: false,
    }));
  }, []);

  const close = useCallback(() => {
    setState(prev => ({
      ...prev,
      isOpen: false,
      data: undefined,
      loading: false,
    }));
  }, []);

  const setLoading = useCallback((loading: boolean) => {
    setState(prev => ({
      ...prev,
      loading,
    }));
  }, []);

  const confirm = useCallback(
    async (action: () => Promise<void> | void) => {
      try {
        setLoading(true);
        await action();
        close();
      } catch (error) {
        setLoading(false);
        throw error; // Re-throw to allow caller to handle
      }
    },
    [close, setLoading]
  );

  const onOpenChange = useCallback(
    (open: boolean) => {
      if (!open) {
        close();
      }
    },
    [close]
  );

  return {
    // State
    isOpen: state.isOpen,
    data: state.data,
    loading: state.loading,

    // Actions
    open,
    close,
    setLoading,
    confirm,

    // Props for dialog components
    modalProps: {
      open: state.isOpen,
      onOpenChange,
    },
  };
}

/**
 * Hook for managing multiple modals with a registry pattern
 * Useful when you have multiple modals in the same component
 */
export function useModalManager() {
  const [modals, setModals] = useState<Record<string, ModalState>>({});

  const getModal = useCallback(
    (id: string): UseModalReturn => {
      const modalState = modals[id] || {
        isOpen: false,
        data: undefined,
        loading: false,
      };

      const open = (data?: any) => {
        setModals(prev => ({
          ...prev,
          [id]: { ...prev[id], isOpen: true, data, loading: false },
        }));
      };

      const close = () => {
        setModals(prev => ({
          ...prev,
          [id]: { ...prev[id], isOpen: false, data: undefined, loading: false },
        }));
      };

      const setLoading = (loading: boolean) => {
        setModals(prev => ({
          ...prev,
          [id]: { ...prev[id], loading },
        }));
      };

      const confirm = async (action: () => Promise<void> | void) => {
        try {
          setLoading(true);
          await action();
          close();
        } catch (error) {
          setLoading(false);
          throw error;
        }
      };

      const onOpenChange = (open: boolean) => {
        if (!open) {
          close();
        }
      };

      return {
        isOpen: modalState.isOpen,
        data: modalState.data,
        loading: modalState.loading,
        open,
        close,
        setLoading,
        confirm,
        modalProps: {
          open: modalState.isOpen,
          onOpenChange,
        },
      };
    },
    [modals]
  );

  return { getModal };
}

/**
 * Specialized hook for delete confirmations
 */
export function useDeleteModal<T = any>() {
  const modal = useModal();

  const confirmDelete = useCallback(
    async (item: T, deleteAction: (item: T) => Promise<void>) => {
      modal.open(item);

      return {
        ...modal,
        confirm: async () => {
          await modal.confirm(async () => {
            await deleteAction(item);
          });
        },
      };
    },
    [modal]
  );

  return {
    ...modal,
    confirmDelete,
  };
}
