'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { useState, useEffect, useCallback } from 'react';
import { User } from '@/types/account';
import { fetchWelonTrustUsers } from '@/lib/data/users';
import {
  userFormSchema,
  type UserFormData,
  type UserSubmissionData,
  getDefaultUserFormValues,
  roleSubroles,
  rolePermissions,
} from '@/lib/validations/user';

interface UseUserFormOptions {
  user?: any | null;
  mode: 'create' | 'edit';
  onSave?: (userData: UserSubmissionData) => void;
  isLoading?: boolean;
}

export function useUserForm({
  user,
  mode,
  onSave,
  isLoading = false,
}: UseUserFormOptions) {
  const router = useRouter();
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [welonTrustUsers, setWelonTrustUsers] = useState<User[]>([]);
  const [loadingWelonTrust, setLoadingWelonTrust] = useState(false);
  const [userSubscription, setUserSubscription] = useState<any>(null);
  const [loadingSubscription, setLoadingSubscription] = useState(false);

  // Initialize form with React Hook Form
  const form = useForm<UserFormData>({
    resolver: zodResolver(userFormSchema),
    defaultValues: getDefaultUserFormValues(user, mode),
    mode: 'onChange',
  });

  // Watch form values for conditional logic
  const watchedRole = form.watch('role');
  const watchedAssignedWelonTrust = form.watch('assignedWelonTrust');

  // Load Welon Trust users
  useEffect(() => {
    const loadWelonTrustUsers = async () => {
      setLoadingWelonTrust(true);
      try {
        const users = await fetchWelonTrustUsers();
        setWelonTrustUsers(users);
      } catch (error) {
        console.error('Error loading Welon Trust users:', error);
        setWelonTrustUsers([]);
      } finally {
        setLoadingWelonTrust(false);
      }
    };

    loadWelonTrustUsers();
  }, []);

  // Load user subscription for edit mode
  useEffect(() => {
    const loadUserSubscription = async () => {
      if (mode === 'edit' && user?.id) {
        setLoadingSubscription(true);
        try {
          const response = await fetch(
            `/api/admin/subscription/user/${user.id}`,
            {
              headers: {
                'x-cognito-id': 'admin-user', // TODO: Get actual admin cognito ID
              },
            }
          );

          if (response.ok) {
            const data = await response.json();
            setUserSubscription(data.subscription);
          }
        } catch (error) {
          console.error('Error loading user subscription:', error);
        } finally {
          setLoadingSubscription(false);
        }
      }
    };

    loadUserSubscription();
  }, [mode, user?.id]);

  // Reset form when user or mode changes
  useEffect(() => {
    const defaultValues = getDefaultUserFormValues(user, mode);
    form.reset(defaultValues);
    setSubmitError(null);
  }, [user, mode, form]);

  // Handle role change - reset subrole and permissions
  const handleRoleChange = useCallback(
    (role: UserFormData['role']) => {
      form.setValue('role', role);

      // Set the first available subrole for the selected role
      const availableSubroles = roleSubroles[role] || [];
      const defaultSubrole =
        availableSubroles.length > 0 ? availableSubroles[0] : '';

      form.setValue('subrole', defaultSubrole);

      form.setValue('permissions', []);

      // Clear validation errors
      form.clearErrors(['subrole', 'permissions']);
    },
    [form]
  );

  // Handle subrole change
  const handleSubroleChange = useCallback(
    (subrole: string) => {
      form.setValue('subrole', subrole);
      // In a real app, we would set default permissions based on the subrole
    },
    [form]
  );

  // Handle permission toggle
  const handlePermissionChange = useCallback(
    (permission: string, checked: boolean) => {
      const currentPermissions = form.getValues('permissions');
      const newPermissions = checked
        ? [...currentPermissions, permission]
        : currentPermissions.filter(p => p !== permission);

      form.setValue('permissions', newPermissions);
    },
    [form]
  );

  // Handle Welon Trust assignment change
  const handleWelonTrustChange = useCallback(
    (value: string) => {
      // For Member role, don't allow 'none' value since it's required
      const currentRole = form.getValues('role');
      const assignedValue =
        value === 'none' && currentRole !== 'Member' ? null : value;
      form.setValue('assignedWelonTrust', assignedValue);

      // Clear new email field if not inviting new
      if (value !== 'invite_new') {
        form.setValue('newWelonTrustEmail', '');
        form.clearErrors('newWelonTrustEmail');
      }
    },
    [form]
  );

  // Handle subscription plan change
  const handleSubscriptionPlanChange = useCallback(
    (plan: 'BASIC' | 'PRO') => {
      form.setValue('subscriptionPlan', plan);
    },
    [form]
  );

  // Handle subscription activation
  const handleSubscriptionActivation = useCallback(
    async (
      userId: string,
      cognitoId: string,
      plan: 'BASIC' | 'PRO',
      email: string,
      trialDays: number
    ) => {
      try {
        const response = await fetch('/api/admin/subscription/activate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-cognito-id': 'admin-user', // TODO: Get actual admin cognito ID
          },
          body: JSON.stringify({
            userId,
            cognitoId,
            plan,
            email,
            currency: 'usd',
            trialDays,
          }),
        });

        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.error || 'Failed to activate subscription');
        }

        const result = await response.json();
        return result;
      } catch (error) {
        console.error('Error activating subscription:', error);
        throw error;
      }
    },
    []
  );

  // Form submission handler
  const onSubmit = useCallback(
    async (data: UserFormData) => {
      setSubmitError(null);

      try {
        // Prepare submission data
        const submissionData: UserSubmissionData = {
          ...data,
          ...(user?.id && { id: user.id }),
        };

        // Handle Welon Trust assignment logic
        if (data.role === 'Member') {
          if (
            data.assignedWelonTrust === 'invite_new' &&
            data.newWelonTrustEmail
          ) {
            console.log('Inviting new Welon Trust:', data.newWelonTrustEmail);
            // In a real app, this would send an invitation email
          } else if (
            data.assignedWelonTrust &&
            data.assignedWelonTrust !== 'invite_new'
          ) {
            const selectedWelonTrust = welonTrustUsers.find(
              wt => wt.id === data.assignedWelonTrust
            );
            if (selectedWelonTrust) {
              console.log(
                'Assigning existing Welon Trust:',
                selectedWelonTrust
              );
            }
          }
        }

        // Handle subscription activation if plan is selected
        if (data.subscriptionPlan && user?.cognitoId && data.email) {
          try {
            await handleSubscriptionActivation(
              user.id,
              user.cognitoId,
              data.subscriptionPlan,
              data.email,
              data.subscriptionTrialDays!
            );
            console.log('Subscription activated successfully');
          } catch (error) {
            console.error('Error activating subscription:', error);
            // Don't throw here, just log the error so user save can continue
          }
        }

        // Call the onSave callback if provided
        if (onSave) {
          onSave(submissionData);
        } else {
          // Default behavior: navigate back to users list
          router.push('/admin/users');
        }
      } catch (error) {
        const errorMessage =
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred';

        setSubmitError(errorMessage);
      }
    },
    [
      user?.id,
      user?.cognitoId,
      onSave,
      router,
      welonTrustUsers,
      handleSubscriptionActivation,
    ]
  );

  // Handle form cancellation
  const handleCancel = useCallback(() => {
    router.push('/admin/users');
  }, [router]);

  // Get available subroles for current role
  const availableSubroles = roleSubroles[watchedRole] || [];

  // Get available permissions for current role
  const availablePermissions = rolePermissions[watchedRole] || [];

  // Check if form has unsaved changes
  const hasUnsavedChanges = form.formState.isDirty;

  return {
    // Form instance
    form,

    // Form state
    isSubmitting: form.formState.isSubmitting || isLoading,
    submitError,
    hasUnsavedChanges,
    isValid: form.formState.isValid,

    // Welon Trust state
    welonTrustUsers,
    loadingWelonTrust,

    // Subscription state
    userSubscription,
    loadingSubscription,

    // Watched values
    watchedRole,
    watchedAssignedWelonTrust,

    // Available options
    availableSubroles,
    availablePermissions,

    // Form actions
    onSubmit: form.handleSubmit(onSubmit),
    handleCancel,
    handleRoleChange,
    handleSubroleChange,
    handlePermissionChange,
    handleWelonTrustChange,
    handleSubscriptionPlanChange,
    handleSubscriptionActivation,

    // Form utilities
    clearError: () => setSubmitError(null),
    resetForm: () => {
      const defaultValues = getDefaultUserFormValues(user, mode);
      form.reset(defaultValues);
      setSubmitError(null);
    },
  };
}
