'use client';

import { useState, useEffect, useCallback } from 'react';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { useAdminAccess } from '@/lib/hooks/useAdminAccess';

// Generate the client
const client = generateClient<Schema>();

export interface AdminEmergencyContact {
  id: string;
  userId: string;
  userName?: string; // Will be populated from user data
  userEmail?: string; // Will be populated from user data
  fullName: string;
  relationship: string;
  phoneNumber: string;
  emailAddress: string;
  contactType: 'Medical' | 'Other';
  documentType: 'DeadDocument' | 'CareDocument' | 'All';
  isPrimaryForType?: boolean;
  isVerified?: boolean;
  tokenExpiry?: string | Date;
}

export function useAdminEmergencyContacts() {
  const [contacts, setContacts] = useState<AdminEmergencyContact[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { hasAnyAdminRole } = useAdminAccess();

  const fetchAllContacts = useCallback(async () => {
    try {
      setLoading(true);

      // Check admin access
      if (!hasAnyAdminRole) {
        throw new Error('Unauthorized: Admin access required');
      }

      // Fetch all emergency contacts
      const { data: contactsData, errors: contactErrors } =
        await client.models.EmergencyContact.list();

      if (contactErrors) {
        console.error('GraphQL errors:', contactErrors);
        throw new Error(contactErrors.map(e => e.message).join(', '));
      }

      // Fetch all users to get their names and emails
      const { data: usersData, errors: userErrors } =
        await client.models.User.list();

      if (userErrors) {
        console.error('GraphQL errors:', userErrors);
        throw new Error(userErrors.map(e => e.message).join(', '));
      }

      // Create a map of user IDs to user data for quick lookup
      const userMap = new Map();
      usersData?.forEach(user => {
        userMap.set(user.cognitoId, {
          name:
            user.firstName && user.lastName
              ? `${user.firstName} ${user.lastName}`
              : user.email,
          email: user.email,
        });
      });

      // Map contacts with user information
      const enrichedContacts =
        contactsData?.map(contact => {
          const userData = userMap.get(contact.userId);
          const currentDate = new Date().toISOString();
          return {
            id: contact.id!,
            userId: contact.userId,
            userName: userData?.name || 'Unknown User',
            userEmail: userData?.email || 'No Email',
            fullName: contact.fullName,
            relationship: contact.relationship,
            phoneNumber: contact.phoneNumber,
            emailAddress: contact.emailAddress,
            contactType: contact.contactType as 'Medical' | 'Other',
            isPrimaryForType: Boolean(contact.isPrimaryForType),
            isVerified: Boolean(contact.isVerified),
            documentType: contact.documentType as
              | 'DeadDocument'
              | 'CareDocument'
              | 'All',
            tokenExpiry: contact.tokenExpiry || currentDate,
          };
        }) || [];

      setContacts(enrichedContacts);
      setError(null);
    } catch (error) {
      console.error('Failed to fetch emergency contacts:', error);
      setError(
        error instanceof Error ? error.message : 'Failed to load contacts'
      );
    } finally {
      setLoading(false);
    }
  }, [hasAnyAdminRole]);

  useEffect(() => {
    fetchAllContacts();
  }, [fetchAllContacts]);

  const sendNotificationEmail = async (
    contactId: string,
    email: string,
    userName?: string
  ) => {
    try {
      // Send the email
      const result = await client.mutations.sendEmail({
        to: email,
        subject: 'Emergency Access Notification',
        message: `You have been granted emergency access to documents for ${userName ? userName : 'Childfree member'}.`,
        emailType: 'emergencyNotification',
        baseUrl: window.location.origin,
      });

      // If email was sent successfully, update the contact's tokenExpiry
      if (result) {
        const expireDate = new Date(
          Date.now() + 24 * 60 * 60 * 1000
        ).toISOString();
        await client.models.EmergencyContact.update({
          id: contactId,
          tokenExpiry: expireDate,
        });

        const resultData = result.data;

        // Try to parse if it's a string
        let parsedResultData: any = resultData;
        if (typeof resultData === 'string') {
          try {
            parsedResultData = JSON.parse(resultData);
            console.log('Parsed result data:', parsedResultData);
          } catch (e) {
            console.log('Failed to parse result data as JSON:', e);
          }
        }
        if (parsedResultData?.success) {
          // Update the local state to reflect the change
          setContacts(prevContacts =>
            prevContacts.map(contact =>
              contact.id === contactId
                ? { ...contact, tokenExpiry: expireDate }
                : contact
            )
          );
        } else {
          setError(
            parsedResultData?.error || 'Failed to send emergency notification'
          );
        }
      }

      return result;
    } catch (error) {
      console.error('Failed to send notification email:', error);
      throw error;
    }
  };

  return {
    contacts,
    loading,
    error,
    refetch: fetchAllContacts,
    sendNotificationEmail,
  };
}
