'use client';

import { useState, useEffect, useCallback } from 'react';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { getCurrentUser } from 'aws-amplify/auth';
import {
  CheckInFrequency,
  CommunicationMethod,
  DMSStatus,
  EscalationProtocol,
} from '@/components/emergency/types';

const client = generateClient<Schema>();

export interface DMSConfigData {
  id?: string;
  userId: string;
  isEnable: boolean;
  frequency: CheckInFrequency;
  customFrequencyDays?: number | null;
  communicationMethod: CommunicationMethod;
  escalationProtocol: EscalationProtocol;
  customEscalationSteps?: number | null;
  personalMessage?: string | null;
  status: DMSStatus;
  pauseReason?: string | null;
  pauseUntil?: string | null;
  nextCheckIn?: string | null;
  lastCheckIn?: string | null;
}

const DEFAULT_DMS_CONFIG: Omit<DMSConfigData, 'userId' | 'id'> = {
  isEnable: false,
  frequency: 'WEEKLY',
  communicationMethod: 'EMAIL',
  escalationProtocol: 'STANDARD',
  status: 'DISABLED',
  customFrequencyDays: null,
  customEscalationSteps: null,
  personalMessage: null,
  pauseReason: null,
  pauseUntil: null,
  nextCheckIn: null,
  lastCheckIn: null,
};

async function fetchUserId(): Promise<string> {
  const currentUser = await getCurrentUser();
  if (!currentUser?.username) {
    throw new Error('No current user or username found');
  }
  return currentUser.username;
}

function normalizeConfig(data: any): DMSConfigData {
  return {
    id: data.id,
    userId: data.userId,
    isEnable: Boolean(data.isEnable),
    frequency: data.frequency as CheckInFrequency,
    customFrequencyDays: data.customFrequencyDays ?? null,
    communicationMethod: data.communicationMethod as CommunicationMethod,
    escalationProtocol: data.escalationProtocol as EscalationProtocol,
    customEscalationSteps: data.customEscalationSteps ?? null,
    personalMessage: data.personalMessage ?? null,
    status: data.status as DMSStatus,
    pauseReason: data.pauseReason ?? null,
    pauseUntil: data.pauseUntil ?? null,
    nextCheckIn: data.nextCheckIn ?? null,
    lastCheckIn: data.lastCheckIn ?? null,
  };
}

function getDefaultConfig(userId: string): DMSConfigData {
  return {
    userId,
    ...DEFAULT_DMS_CONFIG,
  };
}

export function useDeadMansSwitch() {
  const [config, setConfig] = useState<DMSConfigData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const fetchConfig = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const userId = await fetchUserId();

      const defaultConfig = getDefaultConfig(userId);

      const response = await client.models.DeadMansSwitch.list({
        filter: { userId: { eq: userId } },
        selectionSet: [
          'id',
          'userId',
          'isEnable',
          'frequency',
          'customFrequencyDays',
          'communicationMethod',
          'escalationProtocol',
          'customEscalationSteps',
          'personalMessage',
          'status',
          'pauseReason',
          'pauseUntil',
          'nextCheckIn',
          'lastCheckIn',
        ] as const,
      });

      if (response.data && response.data.length > 0) {
        setConfig(normalizeConfig(response.data[0]));
      } else {
        // Передаємо userId явно, щоб уникнути типових помилок
        const createResult = await client.models.DeadMansSwitch.create({
          ...defaultConfig,
          userId,
        });
        if (createResult.data) {
          setConfig(normalizeConfig(createResult.data));
        } else {
          setConfig(defaultConfig);
        }
      }
    } catch (err) {
      console.error("Error fetching Dead Man's Switch config:", err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      setConfig(null);
    } finally {
      setLoading(false);
    }
  }, []);

  const saveConfig = useCallback(
    async (configData: Partial<DMSConfigData>): Promise<boolean> => {
      try {
        setLoading(true);
        setError(null);

        if (!config) throw new Error('No existing configuration loaded');

        const userId = await fetchUserId();

        const updatedData: DMSConfigData = {
          ...config,
          ...configData,
          userId,
          status:
            configData.isEnable !== undefined
              ? configData.isEnable
                ? 'ACTIVE'
                : 'DISABLED'
              : config.status,
        };

        const payload = {
          userId: updatedData.userId,
          isEnable: updatedData.isEnable,
          frequency: updatedData.frequency,
          customFrequencyDays: updatedData.customFrequencyDays ?? undefined,
          communicationMethod: updatedData.communicationMethod,
          escalationProtocol: updatedData.escalationProtocol,
          customEscalationSteps: updatedData.customEscalationSteps ?? undefined,
          personalMessage: updatedData.personalMessage ?? undefined,
          status: updatedData.status,
          pauseReason: updatedData.pauseReason ?? undefined,
          pauseUntil: updatedData.pauseUntil ?? undefined,
          nextCheckIn: updatedData.nextCheckIn ?? undefined,
          lastCheckIn: updatedData.lastCheckIn ?? undefined,
        };

        let result;
        if (config.id) {
          result = await client.models.DeadMansSwitch.update({
            id: config.id,
            ...payload,
          });
        } else {
          result = await client.models.DeadMansSwitch.create(payload);
        }

        if (result.data) {
          setConfig(normalizeConfig(result.data));
          if (configData.lastCheckIn) {
            try {
              const userResponse = await client.models.User.list({
                filter: { cognitoId: { eq: userId } },
                selectionSet: [
                  'id',
                  'cognitoId',
                  'firstName',
                  'lastName',
                  'email',
                  'assignedWelonTrustId',
                  'isDmsCheckSuccessful',
                ] as const,
              });

              if (userResponse.data && userResponse.data.length > 0) {
                const user = userResponse.data[0];
                const previousDmsStatus = user.isDmsCheckSuccessful;

                if (!user.isDmsCheckSuccessful) {
                  await client.models.User.update({
                    id: user.id,
                    isDmsCheckSuccessful: true,
                  });
                  console.log(
                    'Successfully updated user isDmsCheckSuccessful to true'
                  );

                  // Create notification for Welon Trust user about recovery
                  if (user.assignedWelonTrustId) {
                    try {
                      const message = `${user.firstName} ${user.lastName} has successfully completed their check-in and their Dead Man's Switch is now active.`;

                      await client.models.Notification.create({
                        message,
                        recipient: user.assignedWelonTrustId,
                        author: 'system-dms-recovery',
                        createdAt: new Date().toISOString(),
                        isRead: false,
                      });

                      console.log(
                        `Created recovery notification for Welon Trust user ${user.assignedWelonTrustId} about user ${user.cognitoId}`
                      );
                    } catch (notificationError) {
                      console.error(
                        'Failed to create Welon Trust recovery notification:',
                        notificationError
                      );
                      // Don't fail the entire operation if notification fails
                    }
                  }
                }
              }
            } catch (userUpdateError) {
              console.warn(
                'Failed to update user isDmsCheckSuccessful:',
                userUpdateError
              );
            }
          }

          return true;
        } else {
          throw new Error('Failed to save configuration');
        }
      } catch (err) {
        console.error("Error saving Dead Man's Switch config:", err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        return false;
      } finally {
        setLoading(false);
      }
    },
    [config]
  );

  useEffect(() => {
    fetchConfig();
  }, [fetchConfig]);

  return {
    config,
    loading,
    error,
    fetchConfig,
    saveConfig,
  };
}
