'use client';

import { useState } from 'react';
import { uploadData, getUrl } from 'aws-amplify/storage';
import { getCurrentUser } from 'aws-amplify/auth';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { toast } from 'sonner';
import { buildFullHtml } from '@/lib/utils/pdf-generation';
import type { Document } from '@/app/types/documents';

const client = generateClient<Schema>();

export interface SignDocumentResult {
  signedDocumentUrl: string;
  documentHash: string;
  message: string;
}

export function useDocumentSigning() {
  const [isGenerating, setIsGenerating] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);

  const signDocumentWithPDF = async (
    documentId: string,
    signatureData: string
  ): Promise<SignDocumentResult> => {
    try {
      setIsGenerating(true);
      console.log('🔐 Starting document signing process:', { documentId });

      // Get current user
      const cognitoUser = await getCurrentUser();
      if (!cognitoUser?.userId) {
        throw new Error('User not authenticated');
      }

      // Get internal user record
      console.log('🔍 Fetching internal user record...');
      const { data: userData, errors: userErrors } =
        await client.models.User.list({
          filter: { cognitoId: { eq: cognitoUser.userId } },
        });

      if (userErrors || !userData || userData.length === 0) {
        console.error('❌ Internal user not found:', userErrors);
        throw new Error('Internal user record not found');
      }

      const internalUser = userData[0];
      console.log('👤 Internal user found:', {
        id: internalUser.id,
        cognitoId: internalUser.cognitoId,
        email: internalUser.email,
      });

      // Get document data
      console.log('📄 Fetching document from database...');
      const { data: documentData, errors: docErrors } =
        await client.models.Document.get({
          id: documentId,
        });

      if (docErrors || !documentData) {
        console.error('❌ Document not found:', docErrors);
        throw new Error('Document not found');
      }

      // Verify ownership
      const hasDirectOwnership = documentData.userId === internalUser.id;
      const hasAssignedAccess =
        documentData.assignedWelonTrustId === cognitoUser.userId;

      console.log('🔒 Verifying ownership:', {
        documentUserId: documentData.userId,
        currentInternalUserId: internalUser.id,
        currentCognitoId: cognitoUser.userId,
        assignedWelonTrustId: documentData.assignedWelonTrustId,
        hasDirectOwnership,
        hasAssignedAccess,
        canAccess: hasDirectOwnership || hasAssignedAccess,
      });

      if (!hasDirectOwnership && !hasAssignedAccess) {
        console.error('❌ Ownership verification failed');
        throw new Error('Unauthorized access to document');
      }

      // Generate HTML for PDF using the same logic as regular downloads
      console.log('📝 Generating HTML with signature...');
      console.log('📄 Document data:', {
        id: documentData.id,
        title: documentData.title,
        type: documentData.type,
        contentLength: documentData.content?.length || 0,
      });

      // Build full HTML for the document
      // Convert GraphQL data to Document type
      const documentForHtml: Document = {
        id: documentData.id!,
        title: documentData.title,
        type: documentData.type as Document['type'],
        status: documentData.status as Document['status'],
        dateCreated: documentData.dateCreated,
        lastModified: documentData.lastModified || documentData.dateCreated,
        version: documentData.version!,
        content: documentData.content || '',
        userId: documentData.userId,
        fileUrl: documentData.fileUrl || undefined,
        signatureType: documentData.signatureType as Document['signatureType'],
        executionDate: documentData.executionDate || undefined,
        trackingNumber: documentData.trackingNumber || undefined,
        templateId: documentData.templateId || undefined,
        templateVersion: documentData.templateVersion || undefined,
        documentState: documentData.documentState || undefined,
        signedDocumentUrl: documentData.signedDocumentUrl || undefined,
        signedAt: documentData.signedAt || undefined,
        documentHash: documentData.documentHash || undefined,
      };

      const fullHtml = buildFullHtml(documentForHtml);
      console.log('📝 Generated HTML content, length:', fullHtml.length);

      // Generate signed PDF using Lambda function
      console.log('🖨️ Starting PDF generation via Lambda...');
      const pdfResult = await client.mutations.generatePdfDoc({
        documentId: documentId,
        html: fullHtml,
        isSignedVersion: true,
        signatureData: signatureData,
        signatureType: 'electronic', // Add signature type for electronic signatures
        userId: cognitoUser.userId, // Pass Cognito user ID for proper S3 {identity} path
        memberId: internalUser.id, // Add member ID for metadata
        documentVersion: documentData.version?.toString(), // Add document version
      });

      // Parse the result from Lambda function
      const lambdaResult =
        typeof pdfResult.data === 'string'
          ? JSON.parse(pdfResult.data)
          : pdfResult.data;

      if (!lambdaResult?.success) {
        throw new Error(lambdaResult?.error || 'Failed to generate signed PDF');
      }

      console.log('✅ PDF generated successfully via Lambda');

      // Generate document hash for integrity (using document ID + timestamp)
      const hashInput = `${documentId}-${Date.now()}-${signatureData}`;
      const hashBuffer = await crypto.subtle.digest(
        'SHA-256',
        new TextEncoder().encode(hashInput)
      );
      const hashArray = Array.from(new Uint8Array(hashBuffer));
      const documentHash = hashArray
        .map(b => b.toString(16).padStart(2, '0'))
        .join('');

      // Update document in database (fileUrl was already updated by Lambda)
      const now = new Date().toISOString();
      const { data: updatedDoc, errors: updateErrors } =
        await client.models.Document.update({
          id: documentId,
          documentHash: documentHash,
          status: 'signed',
          signatureType: 'electronic',
          executionDate: now,
          lastModified: now,
          updatedAt: now,
        });

      if (updateErrors || !updatedDoc) {
        console.error('❌ Failed to update document:', updateErrors);
        throw new Error('Failed to update document record');
      }

      // Log the signing
      try {
        await client.models.DocumentUpdateLog.create({
          documentId: documentId,
          userId: internalUser.id,
          changeType: 'Updated',
          changeDescription: `Document "${documentData.title}" signed with electronic signature`,
          timestamp: now,
        });
      } catch (logError) {
        console.error('Error logging document signing:', logError);
      }

      // Send notification to member about document being signed
      try {
        const notificationMessage = `Your document "${documentData.title}" has been successfully signed and is ready for shipping.`;
        const emailSubject = 'Document Signed Successfully - ChildFree Legacy';

        // Import the notification function
        const { createNotificationWithEmail } = await import(
          '@/lib/api/notifications'
        );
        const { fetchUserByCognitoId } = await import('@/lib/data/users');

        const memberUser = await fetchUserByCognitoId(cognitoUser.userId);
        if (memberUser) {
          await createNotificationWithEmail(
            notificationMessage,
            cognitoUser.userId,
            memberUser.email,
            emailSubject,
            'system-document-signed'
          );
        }
      } catch (notificationError) {
        console.error(
          'Error creating document signed notification:',
          notificationError
        );
        // Continue even if notification fails
      }

      console.log('✅ Document signing completed successfully');
      toast.success('Document signed successfully!');

      return {
        signedDocumentUrl: lambdaResult.fileUrl || '',
        documentHash,
        message: 'Document signed successfully',
      };
    } catch (error) {
      console.error('❌ Document signing failed:', error);
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Failed to sign document: ${errorMessage}`);
      throw error;
    } finally {
      setIsGenerating(false);
    }
  };

  return {
    signDocumentWithPDF,
    isGenerating,
    isDownloading,
  };
}
