import { useState, useEffect } from 'react';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { useAuth } from '@/context/AuthContext';
import { fetchUserByCognitoId } from '@/lib/data/users';

const client = generateClient<Schema>();

export interface Invite {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: string | null;
  subrole?: string | null;
  invitedBy: string;
  invitedByEmail: string;
  token: string;
  status: string | null;
  expiresAt: string;
  acceptedAt?: string | null;
  createdAt: string;
}

export function useInvites(isMember?: boolean) {
  const [invites, setInvites] = useState<Invite[]>([]);
  const [invitesForCurrentMember, setInvitesForCurrentMember] = useState<
    Invite[]
  >([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { user } = useAuth();

  const fetchInvites = async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, errors } = await client.models.UserInvite.list();

      if (errors) {
        throw new Error(errors[0].message);
      }

      setInvites(data || []);
    } catch (err) {
      console.error('Error fetching invites:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch invites');
    } finally {
      setLoading(false);
    }
  };

  const fetchInvitesForMember = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!user) {
        setError('Failed to fetch invites for current accounts');
        setLoading(false);

        return;
      }

      const userData = await fetchUserByCognitoId(user.userId);

      if (!userData) {
        setError('Failed to fetch invites for current accounts');
        setLoading(false);

        return;
      }

      const { data, errors } = await client.models.UserInvite.list({
        filter: {
          userId: {
            eq: userData.id,
          },
        },
      });

      if (errors) {
        throw new Error(errors[0].message);
      }

      setInvitesForCurrentMember(data || []);
    } catch (err) {
      console.error('Error fetching invites:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch invites');
    } finally {
      setLoading(false);
    }
  };

  const cancelInvite = async (inviteId: string) => {
    try {
      const { errors } = await client.models.UserInvite.update({
        id: inviteId,
        status: 'cancelled',
      });

      if (errors) {
        throw new Error(errors[0].message);
      }

      // Refresh the list
      await fetchInvites();
    } catch (err) {
      console.error('Error cancelling invite:', err);
      throw err;
    }
  };

  const resendInvite = async (invite: Invite) => {
    try {
      // Check if invite is still valid
      if (invite.status !== 'pending') {
        throw new Error('Can only resend pending invitations');
      }

      if (new Date(invite.expiresAt) < new Date()) {
        throw new Error('Cannot resend expired invitation');
      }

      // Send email again
      const emailResult = await client.mutations.sendEmail({
        to: invite.email,
        subject: 'Invitation to join Childfree as Welon Trust',
        message: `You have been invited to join Childfree as a Welon Trust member.`,
        emailType: 'invitation',
        verificationLink: `${window.location.origin}/auth/accept-invite?token=${invite.token}`,
        baseUrl: window.location.origin,
      });

      if (emailResult.errors) {
        throw new Error('Failed to resend invitation email');
      }

      return true;
    } catch (err) {
      console.error('Error resending invite:', err);
      throw err;
    }
  };

  useEffect(() => {
    if (isMember) {
      fetchInvitesForMember();
    } else {
      fetchInvites();
    }
  }, []);

  return {
    invites,
    invitesForCurrentMember,
    loading,
    error,
    fetchInvites,
    fetchInvitesForMember,
    cancelInvite,
    resendInvite,
  };
}
