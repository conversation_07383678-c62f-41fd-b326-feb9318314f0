import { useState, useEffect } from 'react';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { fetchUserByCognitoId } from '@/lib/data/users';
import { useAuth } from '@/context/AuthContext';

const client = generateClient<Schema>();

export interface LinkedAccount {
  id: string;
  userId: string; // The ID of the user who owns this link
  linkedUserId: string; // The ID of the user who is linked
  sharedFields: string;
  isAccepted: boolean;
  createdAt: string;
  updatedAt: string;
  linkedUser?: UserDetails | null;
  user?: UserDetails | null;
}

export interface UserDetails {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
}

export function useLinkedAccounts() {
  const [linkedAccounts, setLinkedAccounts] = useState<LinkedAccount[]>([]);
  const [linkedByAccounts, setLinkedByAccounts] = useState<LinkedAccount[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { user } = useAuth();

  const fetchUserDetails = async (
    userId: string
  ): Promise<UserDetails | null> => {
    try {
      const { data, errors } = await client.models.User.get({ id: userId });

      if (errors) {
        console.error('Error fetching user details:', errors);
        return null;
      }

      if (!data) return null;

      return {
        id: data.id,
        email: data.email,
        firstName: data.firstName,
        lastName: data.lastName,
      };
    } catch (err) {
      console.error('Error fetching user details:', err);
      return null;
    }
  };

  const fetchLinkedAccounts = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!user) {
        setError('Failed to fetch linked accounts');
        setLoading(false);

        return;
      }

      const userData = await fetchUserByCognitoId(user.userId);

      if (!userData) {
        setError('Failed to fetch linked accounts');
        setLoading(false);

        return;
      }

      // Fetch accounts that the current user has linked
      const { data: ownedLinks, errors: ownedErrors } =
        await client.models.LinkedAccountWithSharedFields.list({
          filter: {
            userId: { eq: userData.id },
          },
        });

      if (ownedErrors) {
        throw new Error(ownedErrors[0].message);
      }

      // Fetch accounts where the current user is linked by others
      const { data: linkedByOthers, errors: linkedByErrors } =
        await client.models.LinkedAccountWithSharedFields.list({
          filter: {
            linkedUserId: { eq: userData.id },
          },
        });

      if (linkedByErrors) {
        throw new Error(linkedByErrors[0].message);
      }

      // Fetch user details for each linked account
      const enrichedOwnedLinks = await Promise.all(
        (ownedLinks || []).map(async link => {
          const linkedUser = await fetchUserDetails(link.linkedUserId);
          return {
            ...link,
            linkedUser,
            user: null,
          };
        })
      );

      // Fetch user details for each account that linked the current user
      const enrichedLinkedByOthers = await Promise.all(
        (linkedByOthers || []).map(async link => {
          const user = await fetchUserDetails(link.userId);
          return {
            ...link,
            user,
          };
        })
      );

      setLinkedAccounts(enrichedOwnedLinks || []);
      setLinkedByAccounts(enrichedLinkedByOthers || []);
    } catch (err) {
      console.error('Error fetching linked accounts:', err);
      setError(
        err instanceof Error ? err.message : 'Failed to fetch linked accounts'
      );
    } finally {
      setLoading(false);
    }
  };

  // New function to check if a user is already linked by email
  const checkExistingLinkedAccountByEmail = async (
    email: string
  ): Promise<boolean> => {
    try {
      if (!user) {
        throw new Error('User not authenticated');
      }

      const userData = await fetchUserByCognitoId(user.userId);

      if (!userData) {
        throw new Error('Failed to fetch user data');
      }

      // First, find the user with the given email
      const { data: users, errors: userErrors } = await client.models.User.list(
        {
          filter: { email: { eq: email } },
        }
      );

      if (userErrors) {
        throw new Error(userErrors[0].message);
      }

      if (!users || users.length === 0) {
        // No user found with this email, so no linked account exists
        return false;
      }

      const targetUser = users[0];

      // Check if there's already a link between the current user and the target user
      const { data: existingLinks, errors: linkErrors } =
        await client.models.LinkedAccountWithSharedFields.list({
          filter: {
            and: [
              { userId: { eq: userData.id } },
              { linkedUserId: { eq: targetUser.id } },
            ],
          },
        });

      if (linkErrors) {
        throw new Error(linkErrors[0].message);
      }

      // Return true if there's at least one existing link
      return existingLinks && existingLinks.length > 0;
    } catch (err) {
      console.error('Error checking existing linked account:', err);
      throw err;
    }
  };

  const removeLinkedAccount = async (linkId: string) => {
    try {
      const { errors } =
        await client.models.LinkedAccountWithSharedFields.delete({
          id: linkId,
        });

      if (errors) {
        throw new Error(errors[0].message);
      }

      // Refresh the list
      await fetchLinkedAccounts();
      return true;
    } catch (err) {
      console.error('Error removing linked account:', err);
      throw err;
    }
  };

  const acceptLinkedAccount = async (linkId: string) => {
    try {
      const { errors } =
        await client.models.LinkedAccountWithSharedFields.update({
          id: linkId,
          isAccepted: true,
        });

      if (errors) {
        throw new Error(errors[0].message);
      }

      // Refresh the list
      await fetchLinkedAccounts();
      return true;
    } catch (err) {
      console.error('Error accepting linked account:', err);
      throw err;
    }
  };

  useEffect(() => {
    fetchLinkedAccounts();
  }, []);

  return {
    linkedAccounts,
    linkedByAccounts,
    loading,
    error,
    fetchLinkedAccounts,
    removeLinkedAccount,
    acceptLinkedAccount,
    checkExistingLinkedAccountByEmail,
  };
}
