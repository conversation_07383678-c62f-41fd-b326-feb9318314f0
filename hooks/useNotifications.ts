import { useState, useEffect, useCallback } from 'react';
import { getCurrentUser } from 'aws-amplify/auth';
import {
  getNotifications,
  markAsRead,
  markAllAsRead,
  subscribeToNotifications,
  deleteAllNotifications,
  type NotificationData,
} from '@/lib/api/notifications';

export function useNotifications() {
  const [notifications, setNotifications] = useState<NotificationData[]>([]);
  const [loading, setLoading] = useState(true);

  // Calculate unread count from notifications array
  const unreadCount = notifications.filter(n => !n.isRead).length;

  const fetchNotifications = useCallback(async () => {
    try {
      const user = await getCurrentUser();
      const data = await getNotifications(user.userId);
      setNotifications(data);
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  // Add refresh function for manual updates
  const refreshNotifications = useCallback(async () => {
    await fetchNotifications();
  }, [fetchNotifications]);

  const handleMarkAsRead = useCallback(async (id: string) => {
    try {
      await markAsRead(id);
      setNotifications(prev =>
        prev.map(n => (n.id === id ? { ...n, isRead: true } : n))
      );
    } catch (error) {
      console.error('Error marking as read:', error);
    }
  }, []);

  const handleMarkAllAsRead = useCallback(async () => {
    try {
      await markAllAsRead();
      setNotifications(prev => prev.map(n => ({ ...n, isRead: true })));
    } catch (error) {
      console.error('Error marking all as read:', error);
    }
  }, []);

  const handleDeleteAll = useCallback(async () => {
    try {
      await deleteAllNotifications();
      setNotifications([]);
    } catch (error) {
      console.error('Error deleting all notifications:', error);
    }
  }, []);

  useEffect(() => {
    let subscription: any;
    let pollInterval: NodeJS.Timeout;

    const setupSubscription = async () => {
      try {
        const user = await getCurrentUser();
        subscription = subscribeToNotifications(user.userId, notification => {
          setNotifications(prev => {
            // Check if notification already exists
            const exists = prev.some(n => n.id === notification.id);
            if (exists) {
              return prev;
            }
            // Add new notification to the beginning
            return [notification, ...prev];
          });
        });
      } catch (error) {
        console.error('Error setting up subscription:', error);
      }
    };

    // Set up polling as backup for real-time updates
    const setupPolling = () => {
      pollInterval = setInterval(() => {
        fetchNotifications();
      }, 30000); // Poll every 30 seconds
    };

    fetchNotifications();
    setupSubscription();
    setupPolling();

    return () => {
      if (subscription) {
        subscription.unsubscribe();
      }
      if (pollInterval) {
        clearInterval(pollInterval);
      }
    };
  }, [fetchNotifications]);

  return {
    notifications,
    unreadCount,
    loading,
    markAsRead: handleMarkAsRead,
    markAllAsRead: handleMarkAllAsRead,
    deleteAll: handleDeleteAll,
    refetch: fetchNotifications,
    refresh: refreshNotifications,
  };
}
