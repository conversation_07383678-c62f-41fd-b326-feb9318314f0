'use client';

import { useState, useEffect, useCallback } from 'react';

type SetValue<T> = T | ((val: T) => T);

/**
 * Custom hook for managing sessionStorage
 *
 * @param key - The key to store the value under in sessionStorage
 * @param initialValue - The initial value to use if no value is found in sessionStorage
 * @returns A tuple containing the current value and functions to update it
 */
function useSessionStorage<T>(
  key: string,
  initialValue: T
): [T, (value: SetValue<T>) => void, () => void] {
  // Check if we're in a browser environment
  const isBrowser = typeof window !== 'undefined';

  // Helper function to get value from sessionStorage
  const getStoredValue = useCallback((): T => {
    if (!isBrowser) {
      return initialValue;
    }

    try {
      const item = window.sessionStorage.getItem(key);
      return item ? (JSON.parse(item) as T) : initialValue;
    } catch (error) {
      console.error(`Error reading sessionStorage key "${key}":`, error);
      return initialValue;
    }
  }, [isBrowser, key, initialValue]);

  // State to store our value
  const [storedValue, setStoredValue] = useState<T>(getStoredValue);

  // Function to update sessionStorage and state
  const setValue = useCallback(
    (value: SetValue<T>) => {
      if (!isBrowser) {
        console.warn(
          `Cannot set sessionStorage key "${key}" outside of browser environment`
        );
        return;
      }

      try {
        // Allow value to be a function so we have the same API as useState
        const valueToStore =
          value instanceof Function ? value(storedValue) : value;

        // Save state
        setStoredValue(valueToStore);

        // Save to sessionStorage
        window.sessionStorage.setItem(key, JSON.stringify(valueToStore));

        // Dispatch a custom event so other hooks with the same key can stay in sync
        window.dispatchEvent(new Event('session-storage-change'));
      } catch (error) {
        console.error(`Error setting sessionStorage key "${key}":`, error);
      }
    },
    [isBrowser, key, storedValue]
  );

  // Function to remove the item from sessionStorage
  const removeValue = useCallback(() => {
    if (!isBrowser) {
      return;
    }

    try {
      // Remove from sessionStorage
      window.sessionStorage.removeItem(key);

      // Reset state to initial value
      setStoredValue(initialValue);

      // Dispatch a custom event so other hooks with the same key can stay in sync
      window.dispatchEvent(new Event('session-storage-change'));
    } catch (error) {
      console.error(`Error removing sessionStorage key "${key}":`, error);
    }
  }, [isBrowser, key, initialValue]);

  // Listen for changes to this sessionStorage value from other components
  useEffect(() => {
    if (!isBrowser) {
      return;
    }

    // Update state if the item changes in another component
    const handleStorageChange = () => {
      setStoredValue(getStoredValue());
    };

    // Listen for the custom event
    window.addEventListener('session-storage-change', handleStorageChange);

    // Also listen for actual storage events (from other tabs/windows)
    window.addEventListener('storage', event => {
      if (event.storageArea === sessionStorage && event.key === key) {
        handleStorageChange();
      }
    });

    // Clean up
    return () => {
      window.removeEventListener('session-storage-change', handleStorageChange);
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [isBrowser, getStoredValue, key]);

  return [storedValue, setValue, removeValue];
}

export default useSessionStorage;

/**
 * Utility function to check if sessionStorage is available
 * @returns boolean indicating if sessionStorage is available
 */
export function isSessionStorageAvailable(): boolean {
  if (typeof window === 'undefined') {
    return false;
  }

  try {
    const testKey = '__storage_test__';
    window.sessionStorage.setItem(testKey, 'test');
    window.sessionStorage.removeItem(testKey);
    return true;
  } catch (e) {
    return false;
  }
}

/**
 * Utility function to clear all sessionStorage
 */
export function clearSessionStorage(): void {
  if (typeof window !== 'undefined') {
    try {
      window.sessionStorage.clear();
      window.dispatchEvent(new Event('session-storage-change'));
    } catch (error) {
      console.error('Error clearing sessionStorage:', error);
    }
  }
}
