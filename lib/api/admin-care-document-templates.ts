'use client';

import { generateClient } from 'aws-amplify/api';
import type { Schema } from '@/amplify/data/resource';
import { getCurrentUser } from 'aws-amplify/auth';

const client = generateClient<Schema>();

export type CareDocumentTemplate = Schema['CareDocumentTemplate']['type'];

export const adminCareDocumentTemplatesAPI = {
  // List all care document templates
  async listCareDocumentTemplates(filter?: any) {
    try {
      const { data: templates, errors } =
        await client.models.CareDocumentTemplate.list({
          filter: {
            status: { ne: 'Deleted' },
          },
        });

      if (errors) {
        console.error('Error listing care document templates:', errors);
        throw new Error('Failed to fetch care document templates');
      }

      return templates || [];
    } catch (error) {
      console.error('Error listing care document templates:', error);
      throw error;
    }
  },

  // Get a specific care document template by ID
  async getCareDocumentTemplate(templateId: string) {
    try {
      const { data: template, errors } =
        await client.models.CareDocumentTemplate.get({
          id: templateId,
        });

      if (errors) {
        console.error('Error fetching care document template:', errors);
        throw new Error('Failed to fetch care document template');
      }

      if (!template) {
        throw new Error(
          `Care document template not found with ID: ${templateId}`
        );
      }

      return template;
    } catch (error) {
      console.error(
        `Error fetching care document template ${templateId}:`,
        error
      );
      throw error;
    }
  },

  // Create a new care document template
  async createCareDocumentTemplate(templateData: {
    documentType: string;
    title: string;
    description?: string;
    content?: string;
    status?: 'Draft' | 'Active' | 'Archived';
    icon?: string;
    allowMultiple?: boolean;
    reviewFrequency?: 'monthly' | 'quarterly' | 'semiAnnually' | 'annually';
  }) {
    try {
      const user = await getCurrentUser();
      const now = new Date().toISOString();

      const { data: template, errors } =
        await client.models.CareDocumentTemplate.create({
          createdByEmail: user.signInDetails?.loginId || '',
          documentType: templateData.documentType,
          title: templateData.title,
          icon: templateData.icon || '',
          description: templateData.description || '',
          content: templateData.content || '',
          version: 1,
          status: templateData.status || 'Draft',
          createdAt: now,
          updatedAt: now,
          allowMultiple: templateData.allowMultiple || false,
          reviewFrequency: templateData.reviewFrequency || 'monthly',
        });

      if (errors) {
        console.error('Error creating care document template:', errors);
        throw new Error('Failed to create care document template');
      }

      return template;
    } catch (error) {
      console.error('Error creating care document template:', error);
      throw error;
    }
  },

  // Update an existing care document template
  async updateCareDocumentTemplate(
    templateId: string,
    templateData: {
      documentType?: string;
      title?: string;
      description?: string;
      content?: string;
      questions?: any[]; // Array of question objects
      status?: 'Draft' | 'Active' | 'Archived';
      icon?: string;
      allowMultiple?: boolean;
      reviewFrequency?: 'monthly' | 'quarterly' | 'semiAnnually' | 'annually';
    }
  ) {
    try {
      // First, get the existing template to retrieve current version
      const { data: existingTemplate, errors: fetchErrors } =
        await client.models.CareDocumentTemplate.get({
          id: templateId,
        });

      if (fetchErrors) {
        console.error('Error fetching existing template:', fetchErrors);
        throw new Error('Failed to fetch existing template');
      }

      if (!existingTemplate) {
        throw new Error('Template not found');
      }

      const now = new Date().toISOString();
      const newVersion = (existingTemplate.version || 1) + 1;

      const updateData: any = {
        id: templateId,
        version: newVersion,
        updatedAt: now,
      };

      // Only include fields that are provided
      if (templateData.documentType !== undefined)
        updateData.documentType = templateData.documentType;
      if (templateData.title !== undefined)
        updateData.title = templateData.title;
      if (templateData.description !== undefined)
        updateData.description = templateData.description;
      if (templateData.content !== undefined)
        updateData.content = templateData.content;
      if (templateData.questions !== undefined)
        updateData.questions = templateData.questions;
      if (templateData.status !== undefined)
        updateData.status = templateData.status;
      if (templateData.icon !== undefined) updateData.icon = templateData.icon;
      if (templateData.allowMultiple !== undefined)
        updateData.allowMultiple = templateData.allowMultiple;
      if (templateData.reviewFrequency !== undefined)
        updateData.reviewFrequency = templateData.reviewFrequency;

      const { data: template, errors } =
        await client.models.CareDocumentTemplate.update(updateData);

      if (errors) {
        console.error('Error updating care document template:', errors);
        throw new Error('Failed to update care document template');
      }

      return template;
    } catch (error) {
      console.error('Error updating care document template:', error);
      throw error;
    }
  },

  // ARCHIVE A CARE DOCUMENT TEMPLATE
  async archiveCareDocumentTemplate(templateId: string) {
    try {
      const { data: template, errors } =
        await client.models.CareDocumentTemplate.update({
          id: templateId,
          status: 'Archived',
          updatedAt: new Date().toISOString(),
        });

      if (errors) {
        console.error('Error archiving care document template:', errors);
        throw new Error('Failed to archive care document template');
      }

      return template;
    } catch (error) {
      console.error('Error archiving care document template:', error);
      throw error;
    }
  },

  // SOFT DELETE A CARE DOCUMENT TEMPLATE
  async deleteCareDocumentTemplate(templateId: string) {
    try {
      const { data: template, errors } =
        await client.models.CareDocumentTemplate.update({
          id: templateId,
          status: 'Deleted',
          updatedAt: new Date().toISOString(),
        });

      if (errors) {
        console.error('Error archiving care document template:', errors);
        throw new Error('Failed to archive care document template');
      }

      return template;
    } catch (error) {
      console.error('Error archiving care document template:', error);
      throw error;
    }
  },
};
