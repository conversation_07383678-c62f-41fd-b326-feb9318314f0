import { generateClient } from 'aws-amplify/api';
import type { Schema } from '@/amplify/data/resource';

const client = generateClient<Schema>();

// Mock API client - in production this would use AWS Amplify
const mockClient = {
  graphql: async ({ query, variables }: { query: string; variables?: any }) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Mock responses based on query type
    if (query.includes('listTemplates')) {
      return {
        data: {
          listTemplates: {
            items: mockTemplates,
            nextToken: null,
          },
        },
      };
    }

    if (query.includes('getTemplate')) {
      const template = mockTemplates.find(t => t.id === variables?.id);
      return {
        data: {
          getTemplate: template || null,
        },
      };
    }

    // Default mock response
    return {
      data: {
        createTemplate: { id: 'new-template-id', ...variables?.input },
        updateTemplate: { id: variables?.input?.id, ...variables?.input },
      },
    };
  },
};

// Mock templates data
const mockTemplates = [
  {
    id: '1',
    name: 'California Will Template',
    state: 'California',
    jurisdiction: 'State',
    version: '1.0',
    status: 'published',
    effectiveDate: '2024-01-01',
    lastUpdated: '2024-01-15',
    updatedBy: '<EMAIL>',
    description: 'Standard will template for California residents',
    s3Key: 'templates/california-will-v1.zip',
    metadata: {
      jurisdiction: 'California',
      version: '1.0',
      effectiveDate: '2024-01-01',
      documentType: 'Will',
      requiredFields: ['Member_NAME', 'Member_ADDRESS', 'BENEFICIARY'],
    },
    propagationStatus: 'completed',
    affectedUsers: 245,
  },
  {
    id: '2',
    name: 'New York Healthcare POA',
    state: 'New York',
    jurisdiction: 'State',
    version: '1.2',
    status: 'published',
    effectiveDate: '2024-02-01',
    lastUpdated: '2024-02-10',
    updatedBy: '<EMAIL>',
    description: 'Healthcare Power of Attorney for New York',
    s3Key: 'templates/ny-healthcare-poa-v1.2.zip',
    metadata: {
      jurisdiction: 'New York',
      version: '1.2',
      effectiveDate: '2024-02-01',
      documentType: 'Healthcare POA',
      requiredFields: ['Member_NAME', 'AGENT_NAME', 'AGENT_ADDRESS'],
    },
    propagationStatus: 'pending',
    affectedUsers: 156,
  },
];

// Types
export interface DocumentTemplate {
  id: string;
  name: string;
  state: string;
  jurisdiction: string;
  version: string;
  status: 'draft' | 'published' | 'archived';
  effectiveDate: string;
  lastUpdated: string;
  updatedBy: string;
  description?: string;
  s3Key: string;
  metadata: TemplateMetadata;
  propagationStatus?: 'pending' | 'in-progress' | 'completed' | 'failed';
  affectedUsers?: number;
}

export interface TemplateMetadata {
  jurisdiction: string;
  version: string;
  effectiveDate: string;
  documentType: string;
  requiredFields: string[];
  attachments?: string[];
  legalReviewDate?: string;
  reviewedBy?: string;
}

export interface TemplateVersion {
  id: string;
  templateId: string;
  version: string;
  status: 'draft' | 'published' | 'archived';
  createdAt: string;
  createdBy: string;
  changes: string;
  s3Key: string;
  metadata: TemplateMetadata;
  readyForPropagation: boolean;
}

export interface LegalUpdate {
  id: string;
  state: string;
  jurisdiction: string;
  updateType: 'law-change' | 'regulation-update' | 'court-decision';
  description: string;
  effectiveDate: string;
  lastReviewDate?: string;
  reviewedBy?: string;
  status: 'pending' | 'reviewed' | 'implemented';
  affectedTemplates: string[];
  priority: 'low' | 'medium' | 'high' | 'critical';
}

export interface QuarterlyReview {
  id: string;
  quarter: string;
  year: number;
  state: string;
  status: 'pending' | 'signed' | 'overdue';
  dueDate: string;
  signedDate?: string;
  signedBy?: string;
  legalOfficer?: string;
  notes?: string;
}

// GraphQL queries and mutations (simplified - in real app these would be generated)
const LIST_TEMPLATES = `
  query ListTemplates($filter: TemplateFilterInput, $limit: Int, $nextToken: String) {
    listTemplates(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        name
        state
        jurisdiction
        version
        status
        effectiveDate
        lastUpdated
        updatedBy
        description
        s3Key
        metadata
        propagationStatus
        affectedUsers
      }
      nextToken
    }
  }
`;

const GET_TEMPLATE = `
  query GetTemplate($id: ID!) {
    getTemplate(id: $id) {
      id
      name
      state
      jurisdiction
      version
      status
      effectiveDate
      lastUpdated
      updatedBy
      description
      s3Key
      metadata
      propagationStatus
      affectedUsers
    }
  }
`;

const CREATE_TEMPLATE = `
  mutation CreateTemplate($input: CreateTemplateInput!) {
    createTemplate(input: $input) {
      id
      name
      state
      jurisdiction
      version
      status
      effectiveDate
      lastUpdated
      updatedBy
      description
      s3Key
      metadata
    }
  }
`;

const UPDATE_TEMPLATE = `
  mutation UpdateTemplate($input: UpdateTemplateInput!) {
    updateTemplate(input: $input) {
      id
      name
      state
      jurisdiction
      version
      status
      effectiveDate
      lastUpdated
      updatedBy
      description
      s3Key
      metadata
    }
  }
`;

// API functions
export const adminTemplatesAPI = {
  // List templates with filtering
  async listTemplates(filter?: any, limit = 20, nextToken?: string) {
    try {
      const response = await mockClient.graphql({
        query: LIST_TEMPLATES,
        variables: { filter, limit, nextToken },
      });
      return response.data.listTemplates;
    } catch (error) {
      console.error('Error listing templates:', error);
      throw error;
    }
  },

  async listTemplatesV2() {
    try {
      const { data: templatesData, errors } =
        await client.models.Template.list();

      const templatesWithVersions = await Promise.all(
        templatesData.map(async template => {
          const { data: versions, errors } =
            await client.models.TemplateVersion.list({
              filter: {
                templateId: { eq: template.id },
              },
            });

          const latestVersion =
            versions.length > 0
              ? versions.reduce((latest, current) =>
                  current.versionNumber > latest.versionNumber
                    ? current
                    : latest
                )
              : undefined;

          return {
            ...template,
            latestVersion,
          };
        })
      );

      return templatesWithVersions;
    } catch (error) {
      console.error('Error fetching templates:', error);
      throw new Error('Failed to fetch templates');
    }
  },

  async listAdditionalTemplates() {
    try {
      const { data: templatesData } = await client.models.Template.list({
        filter: {
          isAdditional: { eq: true },
        },
      });

      const templatesWithVersions = await Promise.all(
        templatesData.map(async template => {
          const { data: versions } = await client.models.TemplateVersion.list({
            filter: {
              templateId: { eq: template.id },
            },
          });

          const latestVersion =
            versions.length > 0
              ? versions.reduce((latest, current) =>
                  current.versionNumber > latest.versionNumber
                    ? current
                    : latest
                )
              : undefined;

          return {
            ...template,
            latestVersion,
          };
        })
      );

      return templatesWithVersions;
    } catch (error) {
      console.error('Error fetching templates:', error);
      throw new Error('Failed to fetch templates');
    }
  },

  // GET SINGLE TEMPLATE WITH LAST VERSION BY ID
  async getTemplate(templateId: string) {
    try {
      const { data: template } = await client.models.Template.get({
        id: templateId,
      });
      if (!template) {
        throw new Error(`Template not found with ID: ${templateId}`);
      }
      const { data: versions } = await client.models.TemplateVersion.list({
        filter: {
          templateId: { eq: templateId },
        },
      });
      const latestVersion =
        versions.length > 0
          ? versions.reduce((latest, current) =>
              current.versionNumber > latest.versionNumber ? current : latest
            )
          : undefined;

      return {
        template,
        latestVersion,
      };
    } catch (error) {
      console.error(`Error fetching template ${templateId}:`, error);
      throw error;
    }
  },

  // Create new template
  async createTemplate(input: Partial<DocumentTemplate>) {
    try {
      const response = await mockClient.graphql({
        query: CREATE_TEMPLATE,
        variables: { input },
      });
      return response.data.createTemplate;
    } catch (error) {
      console.error('Error creating template:', error);
      throw error;
    }
  },

  // Update template
  async updateTemplate(input: Partial<DocumentTemplate> & { id: string }) {
    try {
      const response = await mockClient.graphql({
        query: UPDATE_TEMPLATE,
        variables: { input },
      });
      return response.data.updateTemplate;
    } catch (error) {
      console.error('Error updating template:', error);
      throw error;
    }
  },

  // Upload template file (mock implementation)
  async uploadTemplateFile(file: File, templateId: string, version: string) {
    try {
      // Simulate file upload delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      const key = `templates/${templateId}/${version}/${file.name}`;
      const mockResult = {
        key,
        uploadId: `upload-${Date.now()}`,
        url: `https://mock-s3-bucket.s3.amazonaws.com/${key}`,
      };

      return { key, result: mockResult };
    } catch (error) {
      console.error('Error uploading template file:', error);
      throw error;
    }
  },

  // Get download URL for template file (mock implementation)
  async getTemplateFileUrl(s3Key: string) {
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      const mockUrl = `https://mock-s3-bucket.s3.amazonaws.com/${s3Key}?expires=3600`;
      return mockUrl;
    } catch (error) {
      console.error('Error getting template file URL:', error);
      throw error;
    }
  },

  // Delete template file (mock implementation)
  async deleteTemplateFile(s3Key: string) {
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      console.log(`Mock: Deleted file ${s3Key}`);
    } catch (error) {
      console.error('Error deleting template file:', error);
      throw error;
    }
  },

  async getGeneralTemplatesData(): Promise<
    { id: string; type: string; isGeneral: boolean }[]
  > {
    try {
      const { data: templates } = await client.models.Template.list({
        filter: {
          isGeneral: { eq: true },
          isActive: { eq: true },
        },
        selectionSet: ['id', 'type', 'isGeneral'],
      });

      return templates.map(template => ({
        id: template.id,
        type: template.type,
        isGeneral: Boolean(template.isGeneral),
      }));
    } catch (error) {
      console.error('Error fetching general templates:', error);
      throw error;
    }
  },

  async getExistingTemplateForState(templateType: string): Promise<
    {
      id: string;
      type: string;
      templateStates: string[] | null;
      isGeneral: boolean;
      templateState: string | null;
    }[]
  > {
    try {
      if (!templateType) return [];

      const { data: templates } = await client.models.Template.list({
        filter: {
          type: { eq: templateType },
        },
        selectionSet: [
          'id',
          'type',
          'templateStates',
          'isGeneral',
          'templateState',
        ],
      });

      // Map and sanitize templateStates
      return templates.map(template => ({
        id: template.id,
        type: template.type,
        isGeneral: Boolean(template.isGeneral),
        templateState: template.templateState,
        templateStates: template.templateStates
          ? template.templateStates.filter(
              (state): state is string => state !== null
            )
          : null,
      }));
    } catch (error) {
      console.error('Error fetching existing templates:', error);
      throw error;
    }
  },

  // Archive templates (set isActive to false)
  async archiveTemplates(templateIds: string[]) {
    try {
      const results = await Promise.all(
        templateIds.map(async id => {
          const { data, errors } = await client.models.Template.update({
            id,
            isActive: false,
          });
          if (errors) {
            console.error(`Error archiving template ${id}:`, errors);
            throw new Error(`Failed to archive template ${id}`);
          }
          return data;
        })
      );
      return results;
    } catch (error) {
      console.error('Error archiving templates:', error);
      throw error;
    }
  },

  // Restore templates from archive (set isActive to true)
  async restoreTemplates(templateIds: string[]) {
    try {
      const results = await Promise.all(
        templateIds.map(async id => {
          const { data, errors } = await client.models.Template.update({
            id,
            isActive: true,
          });
          if (errors) {
            console.error(`Error restoring template ${id}:`, errors);
            throw new Error(`Failed to restore template ${id}`);
          }
          return data;
        })
      );
      return results;
    } catch (error) {
      console.error('Error restoring templates:', error);
      throw error;
    }
  },

  // Delete templates permanently
  async deleteTemplates(templateIds: string[]) {
    try {
      const results = await Promise.all(
        templateIds.map(async id => {
          // First delete all versions
          const { data: versions } = await client.models.TemplateVersion.list({
            filter: { templateId: { eq: id } },
          });

          await Promise.all(
            versions.map(version =>
              client.models.TemplateVersion.delete({
                templateId: version.templateId,
                versionNumber: version.versionNumber,
              })
            )
          );

          // Then delete the template
          const { data, errors } = await client.models.Template.delete({ id });
          if (errors) {
            console.error(`Error deleting template ${id}:`, errors);
            throw new Error(`Failed to delete template ${id}`);
          }
          return data;
        })
      );
      return results;
    } catch (error) {
      console.error('Error deleting templates:', error);
      throw error;
    }
  },

  // Export templates data
  async exportTemplates(templateIds: string[]) {
    try {
      const templates = await Promise.all(
        templateIds.map(async id => {
          // Get template directly from database
          const { data: template } = await client.models.Template.get({
            id: id,
          });
          if (!template) {
            throw new Error(`Template with id ${id} not found`);
          }

          // Get latest version
          const { data: versions } = await client.models.TemplateVersion.list({
            filter: { templateId: { eq: id } },
          });

          const latestVersion =
            versions.length > 0
              ? versions.reduce((latest, current) =>
                  current.versionNumber > latest.versionNumber
                    ? current
                    : latest
                )
              : null;

          return {
            ...template,
            latestVersion,
          };
        })
      );

      // Create export data
      const exportData = {
        exportDate: new Date().toISOString(),
        templateCount: templates.length,
        templates: templates.map(template => ({
          id: template.id,
          templateName: template.templateName,
          type: template.type,
          templateState: template.templateState,
          templateStates: template.templateStates,
          isGeneral: template.isGeneral,
          isActive: template.isActive,
          isAdditional: template.isAdditional,
          createdAt: template.createdAt,
          updatedAt: template.updatedAt,
          createdBy: template.createdBy,
          createdByEmail: template.createdByEmail,
          version: template.latestVersion?.versionNumber,
          content: template.latestVersion?.content,
        })),
      };

      // Convert to JSON and create downloadable blob
      const jsonString = JSON.stringify(exportData, null, 2);
      const blob = new Blob([jsonString], { type: 'application/json' });
      const url = URL.createObjectURL(blob);

      // Create download link
      const link = document.createElement('a');
      link.href = url;
      link.download = `templates-export-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      console.log(`Successfully exported ${templates.length} templates`);
      return exportData;
    } catch (error) {
      console.error('Error exporting templates:', error);
      throw new Error(
        `Failed to export templates: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  },
};
