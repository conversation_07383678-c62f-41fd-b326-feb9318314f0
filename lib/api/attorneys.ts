// Mock API for attorney management
import {
  Attorney,
  CreateAttorneyRequest,
  UpdateAttorneyRequest,
  AttorneyFilters,
} from '@/types/attorney-reviews';

// US States data for dropdown
export const US_STATES = [
  { code: 'AL', name: 'Alabama' },
  { code: 'AK', name: 'Alaska' },
  { code: 'AZ', name: 'Arizona' },
  { code: 'AR', name: 'Arkansas' },
  { code: 'CA', name: 'California' },
  { code: 'CO', name: 'Colorado' },
  { code: 'CT', name: 'Connecticut' },
  { code: 'DE', name: 'Delaware' },
  { code: 'FL', name: 'Florida' },
  { code: 'GA', name: 'Georgia' },
  { code: 'HI', name: 'Hawaii' },
  { code: 'ID', name: 'Idaho' },
  { code: 'IL', name: 'Illinois' },
  { code: 'IN', name: 'Indiana' },
  { code: 'IA', name: 'Iowa' },
  { code: 'KS', name: 'Kansas' },
  { code: 'K<PERSON>', name: 'Kentucky' },
  { code: 'LA', name: 'Louisiana' },
  { code: '<PERSON>', name: 'Maine' },
  { code: 'MD', name: 'Maryland' },
  { code: 'MA', name: 'Massachusetts' },
  { code: 'MI', name: 'Michigan' },
  { code: 'MN', name: 'Minnesota' },
  { code: 'MS', name: 'Mississippi' },
  { code: 'MO', name: 'Missouri' },
  { code: 'MT', name: 'Montana' },
  { code: 'NE', name: 'Nebraska' },
  { code: 'NV', name: 'Nevada' },
  { code: 'NH', name: 'New Hampshire' },
  { code: 'NJ', name: 'New Jersey' },
  { code: 'NM', name: 'New Mexico' },
  { code: 'NY', name: 'New York' },
  { code: 'NC', name: 'North Carolina' },
  { code: 'ND', name: 'North Dakota' },
  { code: 'OH', name: 'Ohio' },
  { code: 'OK', name: 'Oklahoma' },
  { code: 'OR', name: 'Oregon' },
  { code: 'PA', name: 'Pennsylvania' },
  { code: 'RI', name: 'Rhode Island' },
  { code: 'SC', name: 'South Carolina' },
  { code: 'SD', name: 'South Dakota' },
  { code: 'TN', name: 'Tennessee' },
  { code: 'TX', name: 'Texas' },
  { code: 'UT', name: 'Utah' },
  { code: 'VT', name: 'Vermont' },
  { code: 'VA', name: 'Virginia' },
  { code: 'WA', name: 'Washington' },
  { code: 'WV', name: 'West Virginia' },
  { code: 'WI', name: 'Wisconsin' },
  { code: 'WY', name: 'Wyoming' },
];

// Mock attorney data
const mockAttorneys: Attorney[] = [
  {
    id: 'att-1',
    name: 'Sarah Johnson',
    firm: 'Johnson Estate Law',
    phone: '(*************',
    email: '<EMAIL>',
    address: '123 Main Street',
    city: 'Los Angeles',
    state: 'CA',
    zipCode: '90210',
    specialties: ['Estate Planning', 'Wills & Trusts', 'Probate'],
    barNumber: 'CA123456',
    yearsExperience: 15,
    rating: 4.8,
    isPreferred: true,
    isActive: true,
  },
  {
    id: 'att-2',
    name: 'Michael Chen',
    firm: 'Chen & Associates',
    phone: '(*************',
    email: '<EMAIL>',
    address: '456 Oak Avenue',
    city: 'San Francisco',
    state: 'CA',
    zipCode: '94102',
    specialties: ['Estate Planning', 'Tax Law'],
    barNumber: 'CA234567',
    yearsExperience: 12,
    rating: 4.6,
    isPreferred: true,
    isActive: true,
  },
  {
    id: 'att-3',
    name: 'Emily Rodriguez',
    firm: 'Rodriguez Legal Group',
    phone: '(*************',
    email: '<EMAIL>',
    address: '789 Pine Street',
    city: 'Sacramento',
    state: 'CA',
    zipCode: '95814',
    specialties: ['Estate Planning', 'Elder Law'],
    barNumber: 'CA345678',
    yearsExperience: 8,
    rating: 4.7,
    isPreferred: false,
    isActive: true,
  },
  {
    id: 'att-4',
    name: 'David Thompson',
    firm: 'Thompson Law Firm',
    phone: '(*************',
    email: '<EMAIL>',
    address: '321 Broadway',
    city: 'New York',
    state: 'NY',
    zipCode: '10001',
    specialties: ['Estate Planning', 'Wills & Trusts'],
    barNumber: 'NY123456',
    yearsExperience: 20,
    rating: 4.9,
    isPreferred: true,
    isActive: true,
  },
  {
    id: 'att-5',
    name: 'Lisa Williams',
    firm: 'Williams & Partners',
    phone: '(*************',
    email: '<EMAIL>',
    address: '654 Fifth Avenue',
    city: 'New York',
    state: 'NY',
    zipCode: '10022',
    specialties: ['Estate Planning', 'Probate', 'Trust Administration'],
    barNumber: 'NY234567',
    yearsExperience: 14,
    rating: 4.5,
    isPreferred: false,
    isActive: true,
  },
  {
    id: 'att-6',
    name: 'Robert Davis',
    firm: 'Davis Legal Services',
    phone: '(*************',
    email: '<EMAIL>',
    address: '987 Main Street',
    city: 'Houston',
    state: 'TX',
    zipCode: '77002',
    specialties: ['Estate Planning', 'Business Law'],
    barNumber: 'TX123456',
    yearsExperience: 18,
    rating: 4.4,
    isPreferred: true,
    isActive: true,
  },
];

// Simulate API delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Mock API functions
export const attorneyApi = {
  // Get all attorneys with optional filtering
  async getAttorneys(filters?: AttorneyFilters): Promise<Attorney[]> {
    await delay(500); // Simulate API call

    let filteredAttorneys = [...mockAttorneys];

    if (filters?.state && filters.state !== 'all') {
      filteredAttorneys = filteredAttorneys.filter(
        attorney => attorney.state === filters.state
      );
    }

    if (filters?.search) {
      const searchLower = filters.search.toLowerCase();
      filteredAttorneys = filteredAttorneys.filter(
        attorney =>
          attorney.name.toLowerCase().includes(searchLower) ||
          attorney.email.toLowerCase().includes(searchLower) ||
          attorney.firm?.toLowerCase().includes(searchLower) ||
          attorney.phone.includes(filters.search!)
      );
    }

    if (filters?.isActive !== undefined) {
      filteredAttorneys = filteredAttorneys.filter(
        attorney => attorney.isActive === filters.isActive
      );
    }

    return filteredAttorneys;
  },

  // Get attorney by ID
  async getAttorney(id: string): Promise<Attorney | null> {
    await delay(300);
    return mockAttorneys.find(attorney => attorney.id === id) || null;
  },

  // Create new attorney
  async createAttorney(data: CreateAttorneyRequest): Promise<Attorney> {
    await delay(800);

    const newAttorney: Attorney = {
      id: `att-${Date.now()}`,
      ...data,
      rating: 0, // Default rating for new attorneys
      isPreferred: data.isPreferred ?? false, // Default to false if not provided
      isActive: data.isActive ?? true, // Default to true if not provided
    };

    mockAttorneys.push(newAttorney);
    return newAttorney;
  },

  // Update attorney
  async updateAttorney(
    id: string,
    data: UpdateAttorneyRequest
  ): Promise<Attorney> {
    await delay(600);

    const index = mockAttorneys.findIndex(attorney => attorney.id === id);
    if (index === -1) {
      throw new Error('Attorney not found');
    }

    const updatedAttorney = {
      ...mockAttorneys[index],
      ...data,
    };

    mockAttorneys[index] = updatedAttorney;
    return updatedAttorney;
  },

  // Delete attorney (soft delete - set isActive to false)
  async deleteAttorney(id: string): Promise<void> {
    await delay(400);

    const index = mockAttorneys.findIndex(attorney => attorney.id === id);
    if (index === -1) {
      throw new Error('Attorney not found');
    }

    mockAttorneys[index] = {
      ...mockAttorneys[index],
      isActive: false,
    };
  },

  // Permanently delete attorney (for admin use)
  async permanentlyDeleteAttorney(id: string): Promise<void> {
    await delay(400);

    const index = mockAttorneys.findIndex(attorney => attorney.id === id);
    if (index === -1) {
      throw new Error('Attorney not found');
    }

    mockAttorneys.splice(index, 1);
  },
};

// Validation functions
export const validateAttorneyData = (
  data: CreateAttorneyRequest | UpdateAttorneyRequest
): Record<string, string> => {
  const errors: Record<string, string> = {};

  if (!data.name?.trim()) {
    errors.name = 'Attorney name is required';
  }

  if (!data.email?.trim()) {
    errors.email = 'Email is required';
  } else {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.email)) {
      errors.email = 'Please enter a valid email address';
    }
  }

  if (!data.phone?.trim()) {
    errors.phone = 'Phone number is required';
  } else {
    const phoneRegex = /^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$/;
    if (!phoneRegex.test(data.phone)) {
      errors.phone = 'Please enter a valid phone number (e.g., (*************)';
    }
  }

  if (!data.state?.trim()) {
    errors.state = 'State is required';
  }

  if (data.zipCode && data.zipCode.trim()) {
    const zipRegex = /^\d{5}(-\d{4})?$/;
    if (!zipRegex.test(data.zipCode)) {
      errors.zipCode =
        'Please enter a valid ZIP code (e.g., 12345 or 12345-6789)';
    }
  }

  if (data.yearsExperience !== undefined && data.yearsExperience < 0) {
    errors.yearsExperience = 'Years of experience cannot be negative';
  }

  return errors;
};
