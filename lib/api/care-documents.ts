'use client';

import { generateClient } from 'aws-amplify/api';
import type { Schema } from '@/amplify/data/resource';
import { getCurrentUser } from 'aws-amplify/auth';

const client = generateClient<Schema>();

export type CareDocumentTemplate = Schema['CareDocumentTemplate']['type'];

export const careDocumentTemplatesAPI = {
  async getCareDocumentTemplatesForSidebar(): Promise<
    {
      id: string;
      title: string;
      description?: string | null;
      icon?: string | null;
    }[]
  > {
    try {
      const { data: careDocumentsTemplates, errors } =
        await client.models.CareDocumentTemplate.list({
          filter: {
            status: { eq: 'Active' },
          },
          selectionSet: ['id', 'title', 'icon', 'description'],
        });

      if (errors) {
        console.error('Error fetching care document template:', errors);
        throw new Error('Failed to fetch care document template');
      }

      return careDocumentsTemplates;
    } catch (error) {
      console.error(`Error fetching care document templates`, error);
      throw error;
    }
  },

  async getCareDocumentTemplate(templateId?: string) {
    try {
      if (!templateId) {
        throw new Error(
          `Care document template not found with ID: ${templateId}`
        );
      }

      const { data: template, errors } =
        await client.models.CareDocumentTemplate.get(
          {
            id: templateId,
          },
          {
            selectionSet: [
              'id',
              'title',
              'icon',
              'questions.*',
              'description',
              'documentType',
              'allowMultiple',
              'reviewFrequency',
            ],
          }
        );

      if (errors) {
        console.error('Error fetching care document template:', errors);
        throw new Error('Failed to fetch care document template');
      }

      if (!template) {
        throw new Error(
          `Care document template not found with ID: ${templateId}`
        );
      }

      return template;
    } catch (error) {
      console.error(
        `Error fetching care document template ${templateId}:`,
        error
      );
      throw error;
    }
  },
};
