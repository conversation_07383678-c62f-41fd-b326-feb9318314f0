import { generateClient } from 'aws-amplify/data';
import { getCurrentUser } from 'aws-amplify/auth';
import type { Schema } from '@/amplify/data/resource';

// Generate the Amplify data client
const client = generateClient<Schema>();

// Types for the new interview models
export interface InterviewQuestion {
  questionId: string;
  text: string;
  type: 'text' | 'radio' | 'select' | 'checkbox' | 'date';
  options?: string[];
  order: number;
  conditionalLogic?: Array<{
    conditionType: 'equals' | 'notEquals' | 'contains';
    expectedValue: string;
    showQuestionId: string;
  }>;
  questionValidation?: string;
  defaultNextQuestionId?: string;
  isHeadQuestion?: boolean;
  questionMapping?: string;
  educationalVideoId?: string;
  autoFillVariable?: string;
}

export interface InterviewVersionWithQuestions {
  id: string;
  versionNumber: number;
  isActive: boolean;
  questions: InterviewQuestion[];
  interviewId: string;
}

export interface InterviewWithVersion {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
  latestVersion: InterviewVersionWithQuestions;
}

export interface UserAnswer {
  questionId: string;
  answer: string;
  answeredAt: string;
}

export interface UserInterviewProgress {
  interviewVersionId: string;
  isCompleted: boolean;
  startedAt: string;
  completedAt?: string;
  currentQuestionId?: string;
  answers: UserAnswer[];
  newQuestionIds?: string[]; // IDs of questions that are new/modified and need user attention
}

/**
 * Get all active interviews with their latest versions
 */
export const getActiveInterviews = async (): Promise<
  InterviewWithVersion[]
> => {
  try {
    const { data: interviews, errors } = await client.models.Interview.list({
      filter: { isActive: { eq: true } },
      selectionSet: [
        'id',
        'name',
        'description',
        'isActive',
        'createdAt',
        'updatedAt',
        'versions.id',
        'versions.versionNumber',
        'versions.isActive',
        'versions.questions.*',
      ],
    });

    if (errors) {
      console.error('Failed to fetch interviews:', errors);
      return [];
    }

    // Transform and get latest version for each interview
    const interviewsWithLatestVersion: InterviewWithVersion[] = [];

    for (const interview of interviews) {
      if (!interview.versions || interview.versions.length === 0) {
        continue;
      }

      // Find the latest active version
      const activeVersions = interview.versions.filter(v => v.isActive);
      if (activeVersions.length === 0) {
        continue;
      }

      const latestVersion = activeVersions.reduce((latest, current) =>
        current.versionNumber > latest.versionNumber ? current : latest
      );

      // Sort questions by order and transform to match InterviewQuestion interface
      const sortedQuestions: InterviewQuestion[] = (
        latestVersion.questions || []
      )
        .filter(
          (q): q is NonNullable<typeof q> => q !== null && q.type !== null
        )
        .map(q => ({
          questionId: q.questionId,
          text: q.text,
          type: q.type as 'text' | 'radio' | 'select' | 'checkbox',
          options:
            q.options?.filter((opt): opt is string => opt !== null) ||
            undefined,
          order: q.order,
          conditionalLogic:
            q.conditionalLogic
              ?.filter(
                (cl): cl is NonNullable<typeof cl> =>
                  cl !== null && cl.conditionType !== null
              )
              ?.map(cl => ({
                conditionType: cl.conditionType as
                  | 'equals'
                  | 'notEquals'
                  | 'contains',
                expectedValue: cl.expectedValue,
                showQuestionId: cl.showQuestionId,
              })) || undefined,
          defaultNextQuestionId: q.defaultNextQuestionId || undefined,
          isHeadQuestion: q.isHeadQuestion ?? undefined,
          educationalVideoId: q.educationalVideoId || undefined,
        }))
        .sort((a, b) => a.order - b.order);

      interviewsWithLatestVersion.push({
        id: interview.id,
        name: interview.name,
        description: interview.description || undefined,
        isActive: interview.isActive ?? true,
        latestVersion: {
          id: latestVersion.id,
          versionNumber: latestVersion.versionNumber,
          isActive: latestVersion.isActive ?? true,
          questions: sortedQuestions,
          interviewId: interview.id,
        },
      });
    }

    return interviewsWithLatestVersion;
  } catch (error) {
    console.error('Error fetching active interviews:', error);
    return [];
  }
};

/**
 * Get a specific interview with its latest version
 */
export const getInterviewById = async (
  interviewId: string
): Promise<InterviewWithVersion | null> => {
  try {
    const { data: interview, errors } = await client.models.Interview.get(
      { id: interviewId },
      {
        selectionSet: [
          'id',
          'name',
          'description',
          'isActive',
          'createdAt',
          'updatedAt',
          'versions.id',
          'versions.versionNumber',
          'versions.isActive',
          'versions.questions.*',
        ],
      }
    );

    if (errors || !interview) {
      console.error('Failed to fetch interview:', errors);
      return null;
    }

    if (!interview.versions || interview.versions.length === 0) {
      return null;
    }

    // Find the latest active version
    const activeVersions = interview.versions.filter(v => v.isActive);
    if (activeVersions.length === 0) {
      return null;
    }

    const latestVersion = activeVersions.reduce((latest, current) =>
      current.versionNumber > latest.versionNumber ? current : latest
    );

    // Sort questions by order and transform to match InterviewQuestion interface
    const sortedQuestions: InterviewQuestion[] = (latestVersion.questions || [])
      .filter((q): q is NonNullable<typeof q> => q !== null && q.type !== null)
      .map(q => ({
        questionId: q.questionId,
        text: q.text,
        type: q.type as 'text' | 'radio' | 'select' | 'checkbox',
        options:
          q.options?.filter((opt): opt is string => opt !== null) || undefined,
        order: q.order,
        questionValidation: q.questionValidation || undefined,
        conditionalLogic:
          q.conditionalLogic
            ?.filter(
              (cl): cl is NonNullable<typeof cl> =>
                cl !== null && cl.conditionType !== null
            )
            ?.map(cl => ({
              conditionType: cl.conditionType as
                | 'equals'
                | 'notEquals'
                | 'contains',
              expectedValue: cl.expectedValue,
              showQuestionId: cl.showQuestionId,
            })) || undefined,
        defaultNextQuestionId: q.defaultNextQuestionId || undefined,
        isHeadQuestion: q.isHeadQuestion ?? undefined,
        educationalVideoId: q.educationalVideoId || undefined,
        autoFillVariable: q.autoFillVariable || undefined,
      }))
      .sort((a, b) => a.order - b.order);

    return {
      id: interview.id,
      name: interview.name,
      description: interview.description || undefined,
      isActive: interview.isActive ?? true,
      latestVersion: {
        id: latestVersion.id,
        versionNumber: latestVersion.versionNumber,
        isActive: latestVersion.isActive ?? true,
        questions: sortedQuestions,
        interviewId: interview.id,
      },
    };
  } catch (error) {
    console.error('Error fetching interview by ID:', error);
    return null;
  }
};

/**
 * Get interview by version ID
 */
const getInterviewByVersionId = async (
  versionId: string
): Promise<InterviewWithVersion | null> => {
  try {
    // First get the version to find the interview ID
    const { data: version, errors: versionErrors } =
      await client.models.InterviewVersion.get(
        { id: versionId },
        {
          selectionSet: [
            'id',
            'interviewId',
            'versionNumber',
            'isActive',
            'questions.*',
          ],
        }
      );

    if (versionErrors || !version) {
      console.error('Failed to fetch version:', versionErrors);
      return null;
    }

    // Then get the full interview with this version
    const { data: interview, errors: interviewErrors } =
      await client.models.Interview.get(
        { id: version.interviewId },
        {
          selectionSet: [
            'id',
            'name',
            'description',
            'isActive',
            'versions.id',
            'versions.versionNumber',
            'versions.isActive',
            'versions.questions.*',
          ],
        }
      );

    if (interviewErrors || !interview) {
      console.error('Failed to fetch interview:', interviewErrors);
      return null;
    }

    // Find the specific version in the interview
    const targetVersion = interview.versions?.find(v => v.id === versionId);
    if (!targetVersion) {
      return null;
    }

    // Transform to match InterviewWithVersion interface
    const sortedQuestions: InterviewQuestion[] = (targetVersion.questions || [])
      .filter((q): q is NonNullable<typeof q> => q !== null && q.type !== null)
      .map(q => ({
        questionId: q.questionId,
        text: q.text,
        type: q.type as 'text' | 'radio' | 'select' | 'checkbox',
        options:
          q.options?.filter((opt): opt is string => opt !== null) || undefined,
        order: q.order,
        questionValidation: q.questionValidation || undefined,
        conditionalLogic:
          q.conditionalLogic
            ?.filter(
              (cl): cl is NonNullable<typeof cl> =>
                cl !== null && cl.conditionType !== null
            )
            .map(cl => ({
              conditionType: cl.conditionType as
                | 'equals'
                | 'notEquals'
                | 'contains',
              expectedValue: cl.expectedValue,
              showQuestionId: cl.showQuestionId,
            })) || [],
        defaultNextQuestionId: q.defaultNextQuestionId || undefined,
        isHeadQuestion: q.isHeadQuestion ?? true,
        questionMapping: q.questionMapping || undefined,
        educationalVideoId: q.educationalVideoId || undefined,
        autoFillVariable: q.autoFillVariable || undefined,
      }))
      .sort((a, b) => a.order - b.order);

    return {
      id: interview.id,
      name: interview.name,
      description: interview.description || undefined,
      isActive: interview.isActive ?? true,
      latestVersion: {
        id: targetVersion.id,
        versionNumber: targetVersion.versionNumber,
        isActive: targetVersion.isActive ?? true,
        questions: sortedQuestions,
        interviewId: interview.id,
      },
    };
  } catch (error) {
    console.error('Error getting interview by version ID:', error);
    return null;
  }
};

/**
 * Get user's interview progress for a specific interview version
 */
export const getUserInterviewProgress = async (
  interviewVersionId: string
): Promise<UserInterviewProgress | null> => {
  try {
    const user = await getCurrentUser();

    // Get current user data - try to find user by cognitoId
    const { data: userList, errors } = await client.models.User.list({
      filter: { cognitoId: { eq: user.userId } },
      selectionSet: ['id', 'cognitoId', 'interviewProgress.*'],
    });

    if (errors) {
      console.error('Failed to fetch user data:', errors);
      return null;
    }

    const userData = userList?.[0];
    if (!userData) {
      console.error('User not found with cognitoId:', user.userId);
      return null;
    }

    // Get all progress for comparison
    const allProgress = userData.interviewProgress || [];

    // Find progress for the specific interview version
    const progress = allProgress.find(
      p => p !== null && p.interviewVersionId === interviewVersionId
    );

    // If progress exists for current version, check for new questions
    if (progress) {
      // Get current interview version questions to check for new ones
      const currentInterview =
        await getInterviewByVersionId(interviewVersionId);

      const transformedAnswers = (progress.answers || [])
        .filter(
          (a): a is NonNullable<typeof a> => a !== null && a.answer !== null
        )
        .map(a => ({
          questionId: a.questionId,
          answer: a.answer as string,
          answeredAt: a.answeredAt,
        }));

      // Find questions that haven't been answered yet (new questions)
      // Only mark as new if user has completed previous versions
      let newQuestionIds: string[] = [];
      if (currentInterview) {
        // Find the most recent COMPLETED progress to compare with
        const completedProgress = allProgress
          .filter((p): p is NonNullable<typeof p> => p !== null)
          .filter(p => p.isCompleted === true)
          .sort(
            (a, b) =>
              new Date(b.startedAt).getTime() - new Date(a.startedAt).getTime()
          )[0];

        if (completedProgress) {
          // Get the previous completed version for comparison
          const previousInterview = await getInterviewByVersionId(
            completedProgress.interviewVersionId
          );

          if (
            previousInterview &&
            completedProgress.interviewVersionId !== progress.interviewVersionId
          ) {
            // Compare questions between versions
            const previousQuestionIds = new Set(
              previousInterview.latestVersion.questions.map(q => q.questionId)
            );

            // Find questions that are new AND not yet answered in current progress
            const answeredQuestionIds = new Set(
              transformedAnswers.map(a => a.questionId)
            );
            newQuestionIds = currentInterview.latestVersion.questions
              .map(q => q.questionId)
              .filter(qId => !previousQuestionIds.has(qId)) // New question (not in previous version)
              .filter(qId => !answeredQuestionIds.has(qId)); // Not yet answered in current progress
          }
        }
        // If no completed progress found, don't mark any questions as new (first time user)
      }

      const transformedProgress: UserInterviewProgress = {
        interviewVersionId: progress.interviewVersionId,
        isCompleted: progress.isCompleted,
        startedAt: progress.startedAt,
        completedAt: progress.completedAt || undefined,
        currentQuestionId: progress.currentQuestionId || undefined,
        answers: transformedAnswers,
        newQuestionIds: newQuestionIds.length > 0 ? newQuestionIds : undefined,
      };
      return transformedProgress;
    }

    // If no progress for current version, try to migrate from previous versions
    if (allProgress.length === 0) return null;

    // Get current (newest) interview version
    const currentInterview = await getInterviewByVersionId(interviewVersionId);
    if (!currentInterview) return null;

    // Find the most recent COMPLETED progress from any version for migration
    const latestCompletedProgress = allProgress
      .filter((p): p is NonNullable<typeof p> => p !== null)
      .filter(p => p.isCompleted === true) // Only completed interviews
      .sort(
        (a, b) =>
          new Date(b.startedAt).getTime() - new Date(a.startedAt).getTime()
      )[0];

    // If no completed progress found, don't migrate anything (first time user)
    if (!latestCompletedProgress || !latestCompletedProgress.answers)
      return null;

    // Get the previous interview version that user was working with
    const previousInterview = await getInterviewByVersionId(
      latestCompletedProgress.interviewVersionId
    );

    // Get questions from both versions
    const currentQuestions = currentInterview.latestVersion.questions;
    const previousQuestions = previousInterview?.latestVersion.questions || [];

    // Create sets for comparison
    const currentQuestionIds = new Set(currentQuestions.map(q => q.questionId));
    const previousQuestionIds = new Set(
      previousQuestions.map(q => q.questionId)
    );

    // Migrate compatible answers (questions that exist in both versions)
    const migratedAnswers = latestCompletedProgress.answers
      .filter(
        (a): a is NonNullable<typeof a> => a !== null && a.answer !== null
      )
      .filter(a => currentQuestionIds.has(a.questionId))
      .map(a => ({
        questionId: a.questionId,
        answer: a.answer as string,
        answeredAt: a.answeredAt,
      }));

    // Only migrate if we have some compatible answers
    if (migratedAnswers.length === 0) return null;

    // Find NEW questions: questions that exist in current version but NOT in previous version
    // Only mark as new if this is a real migration between different versions AND not yet answered
    const isRealMigration =
      latestCompletedProgress.interviewVersionId !== interviewVersionId;

    const migratedAnswerIds = new Set(migratedAnswers.map(a => a.questionId));
    const newQuestionIds = isRealMigration
      ? currentQuestions
          .map(q => q.questionId)
          .filter(qId => !previousQuestionIds.has(qId)) // New question (not in previous version)
          .filter(qId => !migratedAnswerIds.has(qId)) // Not yet answered (no migrated answer)
      : []; // Empty array for reset of same version

    // Create new progress with migrated answers
    const migratedProgress: UserInterviewProgress = {
      interviewVersionId: interviewVersionId,
      isCompleted: false, // Reset completion status
      startedAt: new Date().toISOString(),
      completedAt: undefined,
      currentQuestionId: undefined, // Let user continue from beginning
      answers: migratedAnswers,
      newQuestionIds: newQuestionIds.length > 0 ? newQuestionIds : undefined, // Only set if there are new questions
    };

    // Save the migrated progress to database
    await saveMigratedProgress(userData.id, migratedProgress);

    return migratedProgress;
  } catch (error) {
    console.error('Error fetching user interview progress:', error);
    return null;
  }
};

/**
 * Save migrated progress to database
 */
const saveMigratedProgress = async (
  userId: string,
  migratedProgress: UserInterviewProgress
): Promise<void> => {
  try {
    // Get current user data to update progress
    const { data: userData, errors } = await client.models.User.get(
      { id: userId },
      { selectionSet: ['id', 'interviewProgress.*'] }
    );

    if (errors || !userData) {
      console.error('Failed to fetch user data for migration:', errors);
      return;
    }

    // Add migrated progress to existing progress array
    const currentProgress = userData.interviewProgress || [];
    const updatedProgress = [
      ...currentProgress.filter((p): p is NonNullable<typeof p> => p !== null),
      {
        interviewVersionId: migratedProgress.interviewVersionId,
        isCompleted: migratedProgress.isCompleted,
        startedAt: migratedProgress.startedAt,
        completedAt: migratedProgress.completedAt || undefined,
        currentQuestionId: migratedProgress.currentQuestionId || undefined,
        answers: migratedProgress.answers.map(a => ({
          questionId: a.questionId,
          answer: a.answer,
          answeredAt: a.answeredAt,
        })),
      },
    ];

    // Update user with migrated progress
    await client.models.User.update({
      id: userId,
      interviewProgress: updatedProgress,
    });

    console.log('Successfully migrated user progress to new interview version');
  } catch (error) {
    console.error('Error saving migrated progress:', error);
  }
};

/**
 * Save or update user's answer to a question
 */
export const saveUserAnswer = async (
  interviewVersionId: string,
  questionId: string,
  answer: string,
  isCompleted: boolean = false
): Promise<boolean> => {
  try {
    const user = await getCurrentUser();

    // Get current user data - try to find user by cognitoId
    const { data: userList, errors: listErrors } =
      await client.models.User.list({
        filter: { cognitoId: { eq: user.userId } },
        selectionSet: ['id', 'cognitoId', 'interviewProgress.*'],
      });

    if (listErrors) {
      console.error('Failed to fetch user data:', listErrors);
      return false;
    }

    const userData = userList?.[0];
    if (!userData) {
      console.error('User not found with cognitoId:', user.userId);
      return false;
    }

    const currentProgress = userData.interviewProgress || [];
    const existingProgressIndex = currentProgress.findIndex(
      p => p !== null && p.interviewVersionId === interviewVersionId
    );

    const now = new Date().toISOString();
    const newAnswer: UserAnswer = {
      questionId,
      answer,
      answeredAt: now,
    };

    let updatedProgress: UserInterviewProgress[];

    if (existingProgressIndex >= 0) {
      // Update existing progress
      const existing = currentProgress[existingProgressIndex];
      if (!existing) {
        console.error('Existing progress is null');
        return false;
      }
      const existingAnswers = existing.answers || [];

      // Remove any existing answer for this question and add the new one
      const filteredAnswers = existingAnswers
        .filter(
          (a): a is NonNullable<typeof a> =>
            a !== null && a.questionId !== questionId && a.answer !== null
        )
        .map(a => ({
          questionId: a.questionId,
          answer: a.answer as string,
          answeredAt: a.answeredAt,
        }));
      const updatedAnswers = [...filteredAnswers, newAnswer];

      // Transform and filter the progress data
      updatedProgress = currentProgress
        .filter((p): p is NonNullable<typeof p> => p !== null)
        .map(p => ({
          interviewVersionId: p.interviewVersionId,
          isCompleted: p.isCompleted,
          startedAt: p.startedAt,
          completedAt: p.completedAt || undefined,
          currentQuestionId: p.currentQuestionId || undefined,
          answers: (p.answers || [])
            .filter(
              (a): a is NonNullable<typeof a> => a !== null && a.answer !== null
            )
            .map(a => ({
              questionId: a.questionId,
              answer: a.answer as string,
              answeredAt: a.answeredAt,
            })),
          newQuestionIds: (p as any).newQuestionIds || undefined,
        }));

      // Remove the answered question from newQuestionIds if it exists
      const existingNewQuestionIds = (existing as any).newQuestionIds || [];
      const updatedNewQuestionIds = existingNewQuestionIds.filter(
        (id: string) => id !== questionId
      );

      updatedProgress[existingProgressIndex] = {
        interviewVersionId: existing.interviewVersionId,
        isCompleted,
        startedAt: existing.startedAt,
        completedAt: isCompleted ? now : existing.completedAt || undefined,
        currentQuestionId: questionId,
        answers: updatedAnswers,
        newQuestionIds:
          updatedNewQuestionIds.length > 0 ? updatedNewQuestionIds : undefined,
      };
    } else {
      // Create new progress entry
      const newProgress: UserInterviewProgress = {
        interviewVersionId,
        isCompleted,
        startedAt: now,
        completedAt: isCompleted ? now : undefined,
        currentQuestionId: questionId,
        answers: [newAnswer],
      };

      // Transform and filter the progress data, then add new progress
      const transformedProgress = currentProgress
        .filter((p): p is NonNullable<typeof p> => p !== null)
        .map(p => ({
          interviewVersionId: p.interviewVersionId,
          isCompleted: p.isCompleted,
          startedAt: p.startedAt,
          completedAt: p.completedAt || undefined,
          currentQuestionId: p.currentQuestionId || undefined,
          answers: (p.answers || [])
            .filter(
              (a): a is NonNullable<typeof a> => a !== null && a.answer !== null
            )
            .map(a => ({
              questionId: a.questionId,
              answer: a.answer as string,
              answeredAt: a.answeredAt,
            })),
          newQuestionIds: (p as any).newQuestionIds || undefined,
        }));

      updatedProgress = [...transformedProgress, newProgress];
    }

    // Update user with new progress - use the user's id as primary key

    const { data: updatedUser, errors: updateErrors } =
      await client.models.User.update({
        id: userData.id, // Use the actual user ID from the fetched data
        interviewProgress: updatedProgress,
      });

    if (updateErrors) {
      console.error('Failed to update user interview progress:', updateErrors);
      return false;
    }

    if (!updatedUser) {
      console.error('No updated user data returned');
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error saving user answer:', error);
    return false;
  }
};

/**
 * Reset user's progress for a specific interview version
 */
export const resetUserInterviewProgress = async (
  interviewVersionId: string
): Promise<boolean> => {
  try {
    const user = await getCurrentUser();

    // Get current user data - try to find user by cognitoId
    const { data: userList, errors: fetchErrors } =
      await client.models.User.list({
        filter: { cognitoId: { eq: user.userId } },
        selectionSet: ['id', 'cognitoId', 'interviewProgress.*'],
      });

    if (fetchErrors) {
      console.error('Failed to fetch user data:', fetchErrors);
      return false;
    }

    const userData = userList?.[0];
    if (!userData) {
      console.error('User not found with cognitoId:', user.userId);
      return false;
    }

    // Remove progress for the specific interview version
    const updatedProgress = (userData.interviewProgress || []).filter(
      p => p !== null && p.interviewVersionId !== interviewVersionId
    );

    // Update user with filtered progress - use the user's id as primary key
    const { data: updatedUser, errors: updateErrors } =
      await client.models.User.update({
        id: userData.id, // Use the actual user ID from the fetched data
        interviewProgress: updatedProgress,
      });

    if (updateErrors || !updatedUser) {
      console.error('Failed to reset user interview progress:', updateErrors);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error resetting user interview progress:', error);
    return false;
  }
};
