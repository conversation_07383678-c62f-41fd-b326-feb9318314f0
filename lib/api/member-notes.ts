'use client';

import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { getCurrentUser } from 'aws-amplify/auth';
import { fetchUserByCognitoId } from '@/lib/data/users';

// Generate the Amplify data client
const client = generateClient<Schema>();

export interface MemberNoteData {
  id: string;
  userId: string;
  authorId: string;
  authorName: string;
  content: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateMemberNoteData {
  userId: string;
  content: string;
}

export interface UpdateMemberNoteData {
  id: string;
  content: string;
}

/**
 * Create a new member note
 */
export async function createMemberNote(
  noteData: CreateMemberNoteData
): Promise<MemberNoteData> {
  try {
    const currentUser = await getCurrentUser();

    // Get current user's profile to get their name
    const userProfile = await fetchUserByCognitoId(currentUser.userId);
    if (!userProfile) {
      throw new Error('User profile not found');
    }

    const now = new Date().toISOString();

    const { data, errors } = await client.models.MemberNote.create({
      userId: noteData.userId,
      authorId: currentUser.userId,
      authorName: `${userProfile.firstName} ${userProfile.lastName}`,
      content: noteData.content,
      createdAt: now,
      updatedAt: now,
    });

    if (errors) {
      console.error('Error creating member note:', errors);
      throw new Error(errors[0].message);
    }

    if (!data) {
      throw new Error('Failed to create member note');
    }

    return data as MemberNoteData;
  } catch (error) {
    console.error('Failed to create member note:', error);
    throw error;
  }
}

/**
 * Get all notes for a specific member
 */
export async function getMemberNotes(
  userId: string
): Promise<MemberNoteData[]> {
  try {
    const { data, errors } = await client.models.MemberNote.list({
      filter: {
        userId: {
          eq: userId,
        },
      },
    });

    if (errors) {
      console.error('Error fetching member notes:', errors);
      throw new Error(errors[0].message);
    }

    if (!data) {
      return [];
    }

    // Sort by createdAt descending (newest first)
    const sortedData = (data as MemberNoteData[]).sort(
      (a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );

    return sortedData;
  } catch (error) {
    console.error('Failed to fetch member notes:', error);
    throw error;
  }
}

/**
 * Update an existing member note
 */
export async function updateMemberNote(
  noteData: UpdateMemberNoteData
): Promise<MemberNoteData> {
  try {
    const now = new Date().toISOString();

    const { data, errors } = await client.models.MemberNote.update({
      id: noteData.id,
      content: noteData.content,
      updatedAt: now,
    });

    if (errors) {
      console.error('Error updating member note:', errors);
      throw new Error(errors[0].message);
    }

    if (!data) {
      throw new Error('Failed to update member note');
    }

    return data as MemberNoteData;
  } catch (error) {
    console.error('Failed to update member note:', error);
    throw error;
  }
}

/**
 * Delete a member note
 */
export async function deleteMemberNote(noteId: string): Promise<void> {
  try {
    const { errors } = await client.models.MemberNote.delete({
      id: noteId,
    });

    if (errors) {
      console.error('Error deleting member note:', errors);
      throw new Error(errors[0].message);
    }
  } catch (error) {
    console.error('Failed to delete member note:', error);
    throw error;
  }
}

/**
 * Get a single member note by ID
 */
export async function getMemberNoteById(
  noteId: string
): Promise<MemberNoteData | null> {
  try {
    const { data, errors } = await client.models.MemberNote.get({
      id: noteId,
    });

    if (errors) {
      console.error('Error fetching member note:', errors);
      throw new Error(errors[0].message);
    }

    return data as MemberNoteData | null;
  } catch (error) {
    console.error('Failed to fetch member note:', error);
    throw error;
  }
}
