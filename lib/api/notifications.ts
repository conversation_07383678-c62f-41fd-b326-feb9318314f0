import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { getCurrentUser } from 'aws-amplify/auth';

const client = generateClient<Schema>();

export interface NotificationData {
  id: string;
  message: string;
  recipient: string;
  createdAt: string;
  isRead: boolean;
}

export async function createNotification(
  message: string,
  recipient: string,
  author?: string
): Promise<NotificationData> {
  const currentUser = await getCurrentUser();

  const { data, errors } = await client.models.Notification.create({
    message,
    recipient,
    author: author || currentUser.userId,
    createdAt: new Date().toISOString(),
    isRead: false,
  });

  if (errors) throw new Error(errors[0].message);
  return data as NotificationData;
}

export async function getNotifications(
  recipient: string
): Promise<NotificationData[]> {
  console.log('API: Getting notifications for recipient:', recipient);

  // FIXED: Actually filter by recipient
  const { data, errors } = await client.models.Notification.list({
    filter: {
      recipient: { eq: recipient },
    },
  });

  console.log('API: Notification query result:', { data, errors });
  if (errors) throw new Error(errors[0].message);

  // Sort by createdAt descending (newest first)
  const sortedData = (data as NotificationData[]).sort(
    (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );

  return sortedData;
}

export async function markAsRead(id: string): Promise<void> {
  const { errors } = await client.models.Notification.update({
    id,
    isRead: true,
  });

  if (errors) throw new Error(errors[0].message);
}

export async function markAllAsRead(): Promise<void> {
  console.log('API: Marking all notifications as read for current user');

  // FIXED: Get current user's cognitoId and filter properly

  const currentUser = await getCurrentUser();
  const notifications = await getNotifications(currentUser.userId);

  // Only update unread notifications
  const unreadNotifications = notifications.filter(n => !n.isRead);

  for (const notification of unreadNotifications) {
    await markAsRead(notification.id);
  }
}

export async function deleteNotification(id: string): Promise<void> {
  console.log('API: Deleting notification:', id);
  const { errors } = await client.models.Notification.delete({
    id,
  });

  if (errors) throw new Error(errors[0].message);
}

export async function deleteAllNotifications(): Promise<void> {
  console.log('API: Deleting all notifications for current user');

  // FIXED: Get current user's cognitoId and filter properly

  const currentUser = await getCurrentUser();
  const notifications = await getNotifications(currentUser.userId);

  for (const notification of notifications) {
    await deleteNotification(notification.id);
  }
}

export function subscribeToNotifications(
  recipient: string,
  onNotification: (notification: NotificationData) => void
) {
  console.log('Setting up subscription for recipient:', recipient);

  // Subscribe to all notifications and filter client-side to avoid GraphQL filter issues
  return client.models.Notification.onCreate().subscribe({
    next: data => {
      if (data && data.recipient === recipient) {
        console.log('Subscription: Received notification for recipient:', data);
        onNotification(data as NotificationData);
      }
    },
    error: error => console.error('Subscription error:', error),
  });
}

/**
 * Send email notification to user
 */
export async function sendEmailNotification(
  recipientEmail: string,
  subject: string,
  message: string
): Promise<void> {
  try {
    console.log('Sending email notification:', { recipientEmail, subject });

    const result = await client.mutations.sendEmail({
      to: recipientEmail,
      subject: subject,
      message: message,
      emailType: 'notification',
    });

    if (!result.data) {
      throw new Error('Failed to send email notification');
    }

    console.log('Email notification sent successfully');
  } catch (error) {
    console.error('Error sending email notification:', error);
    throw error;
  }
}

/**
 * Create notification and optionally send email
 */
export async function createNotificationWithEmail(
  message: string,
  recipient: string,
  recipientEmail?: string,
  emailSubject?: string,
  author?: string
): Promise<NotificationData> {
  try {
    // Create in-app notification
    const notification = await createNotification(message, recipient, author);

    // Send email notification if email is provided
    if (recipientEmail && emailSubject) {
      try {
        await sendEmailNotification(recipientEmail, emailSubject, message);
      } catch (emailError) {
        console.error(
          'Failed to send email notification, but in-app notification was created:',
          emailError
        );
        // Don't throw error here - in-app notification was successful
      }
    }

    return notification;
  } catch (error) {
    console.error('Error creating notification with email:', error);
    throw error;
  }
}
