'use client';

import { generateClient } from 'aws-amplify/api';
import type { Schema } from '@/amplify/data/resource';
import { getCurrentUser } from 'aws-amplify/auth';
import { useCareDocumentFiles } from '@/hooks/useCareDocumentFiles';

const client = generateClient<Schema>();

export type UserCareDocument = Schema['UserCareDocument']['type'];

export const userCareDocumentsAPI = {
  async getUserCareDocumentsByTemplateId(
    templateId: string
  ): Promise<UserCareDocument[]> {
    try {
      const user = await getCurrentUser();

      const { data, errors } = await client.models.UserCareDocument.list({
        filter: {
          userId: { eq: user.userId },
          templateId: { eq: templateId },
        },
      });

      if (errors) {
        console.error('Error fetching user care documents:', errors);
        throw new Error('Failed to fetch user care documents');
      }

      return data || [];
    } catch (error) {
      console.error(`Error fetching user care documents:`, error);
      throw error;
    }
  },

  async getUserCareDocumentById(documentId: string): Promise<UserCareDocument> {
    try {
      const { data, errors } = await client.models.UserCareDocument.get({
        id: documentId,
      });

      if (errors) {
        console.error('Error fetching user care document:', errors);
        throw new Error('Failed to fetch user care document');
      }

      if (!data) {
        throw new Error(`User care document not found with ID: ${documentId}`);
      }

      return data;
    } catch (error) {
      console.error(`Error fetching user care document:`, error);
      throw error;
    }
  },

  async createUserCareDocument(documentData: {
    templateId: string;
    title: string;
    documentType: string;
    answers: any[];
    files?: { questionId: string; file: File }[];
    expirationDate?: string;
  }): Promise<UserCareDocument> {
    try {
      const user = await getCurrentUser();
      const { uploadFile } = useCareDocumentFiles();

      // First create the document record to get an ID
      const { data, errors } = await client.models.UserCareDocument.create({
        userId: user.userId,
        templateId: documentData.templateId,
        title: documentData.title,
        documentType: documentData.documentType,
        version: 1,
        answers: documentData.answers.map(answer => ({
          ...answer,
          fileKeys: [],
          fileNames: [],
          fileTypes: [],
          fileSizes: [],
        })),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        expirationDate: documentData.expirationDate, // Set the expiration date
      });

      if (errors) {
        throw new Error(errors.map(e => e.message).join(', '));
      }

      if (!data) {
        throw new Error('Failed to create care document');
      }

      // Log the creation
      await this.logDocumentChanges({
        documentId: data.id,
        userId: user.userId,
        changeType: 'Created',
        changeDescription: 'Document created',
        newVersion: 1,
        newAnswers: documentData.answers,
      });

      const documentId = data.id;

      // If there are files to upload, process them
      if (documentData.files && documentData.files.length > 0) {
        const updatedAnswers = data.answers
          ? [...data.answers]
          : [...documentData.answers];

        // Upload each file and update the corresponding answer
        for (const fileData of documentData.files) {
          const fileInfo = await uploadFile(
            fileData.file,
            documentId,
            fileData.questionId
          );

          // Find the answer for this question
          const answerIndex = updatedAnswers.findIndex(
            a => a.questionId === fileData.questionId
          );

          if (answerIndex !== -1) {
            // Update the answer with file information
            updatedAnswers[answerIndex] = {
              ...updatedAnswers[answerIndex],
              fileKeys: [
                ...(updatedAnswers[answerIndex].fileKeys || []),
                fileInfo.key,
              ],
              fileNames: [
                ...(updatedAnswers[answerIndex].fileNames || []),
                fileInfo.fileName,
              ],
              fileTypes: [
                ...(updatedAnswers[answerIndex].fileTypes || []),
                fileInfo.fileType,
              ],
              fileSizes: [
                ...(updatedAnswers[answerIndex].fileSizes || []),
                fileInfo.fileSize,
              ],
              // For backward compatibility
              fileUrl: fileInfo.key,
            };
          }
        }

        // Update the document with file information
        const { data: updatedData, errors: updateErrors } =
          await client.models.UserCareDocument.update({
            id: documentId,
            answers: updatedAnswers,
            updatedAt: new Date().toISOString(),
          });

        if (updateErrors) {
          throw new Error(updateErrors.map(e => e.message).join(', '));
        }

        return updatedData!;
      }

      return data;
    } catch (error) {
      console.error(`Error creating user care document:`, error);
      throw error;
    }
  },

  async updateUserCareDocument(
    documentId: string,
    documentData: {
      answers: any[];
      files?: { questionId: string; file: File }[];
      deletedFileKeys?: string[];
      expirationDate?: string;
    }
  ): Promise<UserCareDocument> {
    try {
      const { uploadFile, deleteFile } = useCareDocumentFiles();

      // Get the current document to determine version and track changes
      const document = await this.getUserCareDocumentById(documentId);
      const currentVersion = document.version || 0;
      const previousAnswers = document.answers || [];

      // Delete files that were removed by the user
      if (
        documentData.deletedFileKeys &&
        documentData.deletedFileKeys.length > 0
      ) {
        console.log('Deleting removed files:', documentData.deletedFileKeys);

        for (const fileKey of documentData.deletedFileKeys) {
          try {
            await deleteFile(fileKey);
            console.log(`Successfully deleted file: ${fileKey}`);
          } catch (deleteError) {
            console.error(`Error deleting file ${fileKey}:`, deleteError);
            // Continue with other deletions even if one fails
          }
        }
      }

      // If there are files to upload, process them
      if (documentData.files && documentData.files.length > 0) {
        const updatedAnswers = [...documentData.answers];

        // Upload each file and update the corresponding answer
        for (const fileData of documentData.files) {
          const fileInfo = await uploadFile(
            fileData.file,
            documentId,
            fileData.questionId
          );

          // Find the answer for this question
          const answerIndex = updatedAnswers.findIndex(
            a => a.questionId === fileData.questionId
          );

          if (answerIndex !== -1) {
            // Update the answer with file information
            updatedAnswers[answerIndex] = {
              ...updatedAnswers[answerIndex],
              fileKeys: [
                ...(updatedAnswers[answerIndex].fileKeys || []),
                fileInfo.key,
              ],
              fileNames: [
                ...(updatedAnswers[answerIndex].fileNames || []),
                fileInfo.fileName,
              ],
              fileTypes: [
                ...(updatedAnswers[answerIndex].fileTypes || []),
                fileInfo.fileType,
              ],
              fileSizes: [
                ...(updatedAnswers[answerIndex].fileSizes || []),
                fileInfo.fileSize,
              ],
              // For backward compatibility
              fileUrl: fileInfo.key,
            };
          }
        }

        // Update the document with the new answers and file information
        const { data, errors } = await client.models.UserCareDocument.update({
          id: documentId,
          answers: updatedAnswers,
          version: currentVersion + 1,
          updatedAt: new Date().toISOString(),
          expirationDate: documentData.expirationDate, // Set the updated expiration date
        });

        if (errors) {
          throw new Error(errors.map(e => e.message).join(', '));
        }

        // Log the changes
        await this.logDocumentChanges({
          documentId,
          userId: document.userId,
          changeType: 'Updated',
          changeDescription: 'Document answers updated',
          previousVersion: currentVersion,
          newVersion: currentVersion + 1,
          previousAnswers,
          newAnswers: updatedAnswers,
        });

        return data!;
      } else {
        // Update the document with just the new answers
        const { data, errors } = await client.models.UserCareDocument.update({
          id: documentId,
          answers: documentData.answers,
          version: currentVersion + 1,
          updatedAt: new Date().toISOString(),
          expirationDate: documentData.expirationDate, // Set the updated expiration date
        });

        if (errors) {
          throw new Error(errors.map(e => e.message).join(', '));
        }

        // Log the changes
        await this.logDocumentChanges({
          documentId,
          userId: document.userId,
          changeType: 'Updated',
          changeDescription: 'Document answers updated',
          previousVersion: currentVersion,
          newVersion: currentVersion + 1,
          previousAnswers,
          newAnswers: documentData.answers,
        });

        return data!;
      }
    } catch (error) {
      console.error(`Error updating user care document:`, error);
      throw error;
    }
  },

  // method to log document changes
  async logDocumentChanges({
    documentId,
    userId,
    changeType,
    changeDescription,
    previousVersion,
    newVersion,
    previousAnswers,
    newAnswers,
  }: {
    documentId: string;
    userId: string;
    changeType: 'Created' | 'Updated' | 'Reviewed' | 'Archived';
    changeDescription?: string;
    previousVersion?: number;
    newVersion?: number;
    previousAnswers?: any[];
    newAnswers?: any[];
  }): Promise<void> {
    try {
      // Identify changed fields by comparing previous and new answers
      const changedFields: string[] = [];
      const previousValues: Record<string, any> = {};
      const newValues: Record<string, any> = {};

      if (previousAnswers && newAnswers) {
        // Create maps for easier comparison
        const prevMap = new Map(previousAnswers.map(a => [a.questionId, a]));
        const newMap = new Map(newAnswers.map(a => [a.questionId, a]));

        // Check for changes in existing answers
        prevMap.forEach((prevAnswer, questionId) => {
          const newAnswer = newMap.get(questionId);

          if (!newAnswer) {
            // Answer was removed
            changedFields.push(`question_${questionId}`);
            previousValues[`question_${questionId}`] = prevAnswer;
            newValues[`question_${questionId}`] = null;
          } else if (JSON.stringify(prevAnswer) !== JSON.stringify(newAnswer)) {
            // Answer was changed
            changedFields.push(`question_${questionId}`);
            previousValues[`question_${questionId}`] = prevAnswer;
            newValues[`question_${questionId}`] = newAnswer;
          }
        });

        // Check for new answers
        newMap.forEach((newAnswer, questionId) => {
          if (!prevMap.has(questionId)) {
            changedFields.push(`question_${questionId}`);
            previousValues[`question_${questionId}`] = null;
            newValues[`question_${questionId}`] = newAnswer;
          }
        });
      }

      // Create the change log entry
      await client.models.UserCareDocumentChangeLog.create({
        documentId,
        userId,
        changeType,
        changeDescription:
          changeDescription || `Document ${changeType.toLowerCase()}`,
        previousVersion,
        newVersion,
        changedFields: JSON.stringify(changedFields),
        previousValues: JSON.stringify(previousValues),
        newValues: JSON.stringify(newValues),
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Error logging document changes:', error);
    }
  },

  async getDocumentChangeHistory(documentId: string): Promise<any[]> {
    try {
      const { data, errors } =
        await client.models.UserCareDocumentChangeLog.list({
          filter: {
            documentId: { eq: documentId },
          },
        });

      if (errors) {
        throw new Error(errors.map(e => e.message).join(', '));
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching document change history:', error);
      throw error;
    }
  },

  async deleteUserCareDocument(documentId: string): Promise<void> {
    try {
      // Get the document to find associated files
      const document = await this.getUserCareDocumentById(documentId);
      const { deleteFile } = useCareDocumentFiles();

      // Delete all associated files
      if (document.answers) {
        for (const answer of document.answers) {
          // Handle multiple files (new approach)
          if (answer && answer.fileKeys && answer.fileKeys.length > 0) {
            for (const fileKey of answer.fileKeys) {
              if (fileKey) {
                await deleteFile(fileKey);
              }
            }
          }
          // Handle single file (legacy approach)
          else if (answer && answer.fileUrl) {
            await deleteFile(answer.fileUrl);
          }
        }
      }

      // Delete the document record
      const { errors } = await client.models.UserCareDocument.delete({
        id: documentId,
      });

      if (errors) {
        console.error('Error deleting user care document:', errors);
        throw new Error('Failed to delete user care document');
      }

      console.log('Care document deleted successfully');
    } catch (error) {
      console.error(`Error deleting user care document:`, error);
      throw error;
    }
  },
};
