import { generateClient } from 'aws-amplify/data';
import { getCurrentUser } from 'aws-amplify/auth';
import type { Schema } from '@/amplify/data/resource';
import { fetchUserByCognitoId } from '@/lib/data/users';
import type { UserStatus, MasterRole } from '@/types/account';

const client = generateClient<Schema>();

export interface WelonDMSFailedUser {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  status: UserStatus;
  isDmsCheckSuccessful: boolean;
  createdAt: string;
  role: MasterRole;
  cognitoId?: string | null;
  assignedWelonTrustId?: string | null;
}

/**
 * Fetch DMS failed users assigned to the current Welon Trust user
 * @returns Promise<WelonDMSFailedUser[]> - Array of users with failed DMS checks assigned to current Welon Trust user
 */
async function fetchDMSFailedUsersAssignedToCurrentWelon(): Promise<
  WelonDMSFailedUser[]
> {
  try {
    // Get current authenticated user
    const currentAuthUser = await getCurrentUser();
    if (!currentAuthUser?.username) {
      console.warn('No authenticated user found');
      return [];
    }

    // Get current user's database record using their Cognito ID
    const currentUser = await fetchUserByCognitoId(currentAuthUser.username);
    if (!currentUser) {
      console.error('Current user not found in database');
      return [];
    }

    // Check if current user is a Welon Trust user
    if (currentUser.role !== 'WelonTrust') {
      console.warn('Current user is not a Welon Trust user');
      return [];
    }

    // Fetch users with failed DMS checks assigned to current Welon Trust user
    const { data: users, errors } = await client.models.User.list({
      filter: {
        and: [
          { isDmsCheckSuccessful: { eq: false } },
          { assignedWelonTrustId: { eq: currentUser.cognitoId } },
          { role: { eq: 'Member' } },
        ],
      },
      selectionSet: [
        'id',
        'firstName',
        'lastName',
        'email',
        'status',
        'isDmsCheckSuccessful',
        'createdAt',
        'role',
        'cognitoId',
        'assignedWelonTrustId',
      ] as const,
    });

    if (errors) {
      console.error(
        'Error fetching DMS failed users assigned to Welon Trust:',
        errors
      );
      throw new Error(
        'Failed to fetch DMS failed users assigned to Welon Trust'
      );
    }

    if (!users || users.length === 0) {
      console.log(
        'No DMS failed users found assigned to current Welon Trust user'
      );
      return [];
    }

    console.log(
      `Found ${users.length} DMS failed users assigned to current Welon Trust user`
    );

    // Transform the data to match the expected interface
    return users.map(user => ({
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      status: user.status || 'pending', // Provide default value for null status
      isDmsCheckSuccessful: user.isDmsCheckSuccessful || false, // Provide default value for null
      createdAt: user.createdAt,
      role: user.role || 'Member', // Provide default value for null role
      cognitoId: user.cognitoId,
      assignedWelonTrustId: user.assignedWelonTrustId,
    }));
  } catch (error) {
    console.error(
      'Error fetching DMS failed users assigned to current Welon Trust:',
      error
    );
    throw new Error(
      'Failed to fetch DMS failed users assigned to current Welon Trust'
    );
  }
}

export const welonUsersAPI = {
  fetchDMSFailedUsersAssignedToCurrentWelon,
};
