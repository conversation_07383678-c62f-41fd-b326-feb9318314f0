'use client';

import React from 'react';
import { useAuth } from '@/context/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DashboardContentLayoutSkeleton } from '@/components/ui/skeletons';
import routes from '@/utils/routes';

interface RoleGuardProps {
  children: React.ReactNode;
  allowedRoles: string[];
  requireAllRoles?: boolean;
  fallbackMessage?: string;
  redirectTo?: string;
  showAccessDenied?: boolean;
}

export function RoleGuard({
  children,
  allowedRoles,
  requireAllRoles = false,
  fallbackMessage = 'You need to be logged in with the appropriate role to access this page.',
  redirectTo = routes.login,
  showAccessDenied = true,
}: RoleGuardProps) {
  const { user, userRoles, loading } = useAuth();
  const router = useRouter();

  // Check if user has required roles
  const hasRequiredRoles = requireAllRoles
    ? allowedRoles.every(role => userRoles.includes(role))
    : allowedRoles.some(role => userRoles.includes(role));

  useEffect(() => {
    if (!loading && !user) {
      // Not authenticated, redirect to login
      router.push(redirectTo);
    } else if (!loading && user && !hasRequiredRoles) {
      // Authenticated but doesn't have required roles
      if (!showAccessDenied) {
        router.push('/dashboard');
      }
    }
  }, [user, loading, hasRequiredRoles, router, redirectTo, showAccessDenied]);

  if (loading) {
    return <DashboardContentLayoutSkeleton />;
  }

  if (!user) {
    return (
      <div className='container mx-auto py-12 px-4'>
        <div className='flex flex-col items-center justify-center'>
          <Card className='w-full max-w-3xl'>
            <CardHeader>
              <CardTitle>Authentication Required</CardTitle>
            </CardHeader>
            <CardContent>
              <p className='text-muted-foreground'>{fallbackMessage}</p>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (!hasRequiredRoles) {
    if (!showAccessDenied) {
      return null;
    }

    return (
      <div className='container mx-auto py-12 px-4'>
        <div className='flex flex-col items-center justify-center'>
          <Card className='w-full max-w-3xl'>
            <CardHeader>
              <CardTitle>Access Denied</CardTitle>
            </CardHeader>
            <CardContent>
              <p className='text-muted-foreground'>
                You don't have the required role to access this page.
              </p>
              <p className='text-sm text-muted-foreground mt-2'>
                Required roles:{' '}
                {allowedRoles.join(requireAllRoles ? ' AND ' : ' OR ')}
              </p>
              {userRoles.length > 0 && (
                <p className='text-sm text-muted-foreground mt-1'>
                  Your roles: {userRoles.join(', ')}
                </p>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
