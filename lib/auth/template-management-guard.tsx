'use client';

import React from 'react';
import { useAuth } from '@/context/AuthContext';

interface TemplateManagementGuardProps {
  children: React.ReactNode;
}

export function TemplateManagementGuard({
  children,
}: TemplateManagementGuardProps) {
  const { userSubrole } = useAuth();

  // Allow all admins to view templates, but restrict editing for Basic admins
  // The editing restrictions will be handled at the component level
  return <>{children}</>;
}

// Hook to check if user can edit templates
export function useCanEditTemplates() {
  const { userSubrole } = useAuth();
  return userSubrole !== 'Basic';
}
