'use client';

import React from 'react';
import { AdminGuard } from './admin-guard';
import { RoleGuard } from './role-guard';
import { ADMIN_ROLES, AdminRole } from '@/lib/utils/admin-utils';

/**
 * Higher-order component that wraps a component with admin access protection
 * @param WrappedComponent - The component to protect
 * @param options - Configuration options for admin access
 * @returns Protected component
 */
export function withAdminAccess<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options: {
    requiredRole?: AdminRole;
    allowedRoles?: AdminRole[];
    requireAllRoles?: boolean;
    fallbackMessage?: string;
    redirectTo?: string;
  } = {}
) {
  const {
    requiredRole,
    allowedRoles,
    requireAllRoles = false,
    fallbackMessage,
    redirectTo,
  } = options;

  const ProtectedComponent = (props: P) => {
    // If specific roles are defined, use RoleGuard
    if (allowedRoles && allowedRoles.length > 0) {
      return (
        <RoleGuard
          allowedRoles={allowedRoles}
          requireAllRoles={requireAllRoles}
          fallbackMessage={fallbackMessage}
          redirectTo={redirectTo}
        >
          <WrappedComponent {...props} />
        </RoleGuard>
      );
    }

    // Otherwise use AdminGuard with optional required role
    return (
      <AdminGuard
        requiredRole={requiredRole}
        fallbackMessage={fallbackMessage}
        redirectTo={redirectTo}
      >
        <WrappedComponent {...props} />
      </AdminGuard>
    );
  };

  // Set display name for debugging
  ProtectedComponent.displayName = `withAdminAccess(${WrappedComponent.displayName || WrappedComponent.name})`;

  return ProtectedComponent;
}

/**
 * HOC specifically for components that require full admin privileges
 */
export function withFullAdminAccess<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  fallbackMessage?: string
) {
  return withAdminAccess(WrappedComponent, {
    requiredRole: ADMIN_ROLES.ADMINS,
    fallbackMessage:
      fallbackMessage ||
      'You need full administrator privileges to access this page.',
  });
}

/**
 * HOC for components that can be accessed by any admin role
 */
export function withAnyAdminAccess<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  fallbackMessage?: string
) {
  return withAdminAccess(WrappedComponent, {
    allowedRoles: [ADMIN_ROLES.ADMINS, ADMIN_ROLES.WELONTRUST],
    fallbackMessage:
      fallbackMessage ||
      'You need administrator or Welon Trust privileges to access this page.',
  });
}

/**
 * HOC specifically for Welon Trust components
 */
export function withWelonTrustAccess<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  fallbackMessage?: string
) {
  return withAdminAccess(WrappedComponent, {
    requiredRole: ADMIN_ROLES.WELONTRUST,
    fallbackMessage:
      fallbackMessage || 'You need Welon Trust privileges to access this page.',
  });
}
