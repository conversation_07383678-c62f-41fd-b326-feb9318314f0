'use client';

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from 'react';
import { LinkedAccount, User, LINK_PERMISSIONS } from '@/types/account';
import { useLinkedAccounts } from '../../hooks/use-linked-accounts';
import { getUserById } from '@/lib/mock/linked-accounts';

// Account view mode
export type AccountViewMode = 'own' | 'linked';

// Active account context
export interface ActiveAccount {
  user: User;
  mode: AccountViewMode;
  linkedAccount?: LinkedAccount; // Only present when mode is 'linked'
  permissions: string[];
}

interface LinkedAccountContextType {
  // Current active account
  activeAccount: ActiveAccount | null;

  // Available linked accounts to switch to
  availableLinkedAccounts: LinkedAccount[];

  // Switch between own and linked accounts
  switchToOwnAccount: () => void;
  switchToLinkedAccount: (linkedAccount: LinkedAccount) => void;

  // Permission checking
  hasPermission: (permission: string) => boolean;
  canEdit: (resource: string) => boolean;
  canView: (resource: string) => boolean;

  // UI state
  isLoading: boolean;
  error: string | null;
}

const LinkedAccountContext = createContext<
  LinkedAccountContextType | undefined
>(undefined);

interface LinkedAccountProviderProps {
  children: ReactNode;
}

export function LinkedAccountProvider({
  children,
}: LinkedAccountProviderProps) {
  const [activeAccount, setActiveAccount] = useState<ActiveAccount | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const {
    incomingLinks,
    loading: linksLoading,
    error: linksError,
  } = useLinkedAccounts();

  // Mock current user (in real app, this would come from auth context)
  const currentUser: User = {
    id: 'user_1',
    name: 'John Doe',
    email: '<EMAIL>',
    role: 'Member',
    subrole: 'Basic Member',
    status: 'active',
    createdAt: '2023-01-15T08:30:00Z',
    cognitoId: '',
    firstName: '',
    lastName: '',
  };

  // Initialize with own account
  useEffect(() => {
    if (!linksLoading) {
      setActiveAccount({
        user: currentUser,
        mode: 'own',
        permissions: Object.values(LINK_PERMISSIONS), // Own account has all permissions
      });
      setIsLoading(false);
    }
  }, [linksLoading]);

  // Get available linked accounts (active incoming links)
  const availableLinkedAccounts = incomingLinks.filter(
    link => link.status === 'active'
  );

  const switchToOwnAccount = () => {
    setActiveAccount({
      user: currentUser,
      mode: 'own',
      permissions: Object.values(LINK_PERMISSIONS),
    });
  };

  const switchToLinkedAccount = (linkedAccount: LinkedAccount) => {
    const linkedUser = getUserById(linkedAccount.userId);
    if (!linkedUser) {
      setError('Linked user not found');
      return;
    }

    setActiveAccount({
      user: linkedUser,
      mode: 'linked',
      linkedAccount,
      permissions: linkedAccount.permissions,
    });
  };

  const hasPermission = (permission: string): boolean => {
    if (!activeAccount) return false;
    return activeAccount.permissions.includes(permission);
  };

  const canEdit = (resource: string): boolean => {
    if (!activeAccount) return false;

    // Own account can edit everything
    if (activeAccount.mode === 'own') return true;

    // Check specific edit permissions for linked accounts
    switch (resource) {
      case 'documents':
        return hasPermission(LINK_PERMISSIONS.EDIT_DOCUMENTS);
      case 'emergency_contacts':
        return hasPermission(LINK_PERMISSIONS.EDIT_EMERGENCY_CONTACTS);
      case 'dead_man_switch':
        return hasPermission(LINK_PERMISSIONS.EDIT_DEAD_MAN_SWITCH);
      case 'billing':
        return hasPermission(LINK_PERMISSIONS.EDIT_BILLING);
      default:
        return false;
    }
  };

  const canView = (resource: string): boolean => {
    if (!activeAccount) return false;

    // Own account can view everything
    if (activeAccount.mode === 'own') return true;

    // Check specific view permissions for linked accounts
    switch (resource) {
      case 'documents':
        return hasPermission(LINK_PERMISSIONS.VIEW_DOCUMENTS);
      case 'emergency_contacts':
        return hasPermission(LINK_PERMISSIONS.VIEW_EMERGENCY_CONTACTS);
      case 'dead_man_switch':
        return hasPermission(LINK_PERMISSIONS.VIEW_DEAD_MAN_SWITCH);
      case 'billing':
        return hasPermission(LINK_PERMISSIONS.VIEW_BILLING);
      default:
        return false;
    }
  };

  const value: LinkedAccountContextType = {
    activeAccount,
    availableLinkedAccounts,
    switchToOwnAccount,
    switchToLinkedAccount,
    hasPermission,
    canEdit,
    canView,
    isLoading: isLoading || linksLoading,
    error: error || linksError,
  };

  return (
    <LinkedAccountContext.Provider value={value}>
      {children}
    </LinkedAccountContext.Provider>
  );
}

export function useLinkedAccountContext(): LinkedAccountContextType {
  const context = useContext(LinkedAccountContext);
  if (context === undefined) {
    throw new Error(
      'useLinkedAccountContext must be used within a LinkedAccountProvider'
    );
  }
  return context;
}

// Optional hook for components that might not have the provider
export function useLinkedAccountContextOptional(): LinkedAccountContextType | null {
  try {
    return useLinkedAccountContext();
  } catch {
    return null;
  }
}
