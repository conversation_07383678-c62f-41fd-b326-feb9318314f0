'use client';

import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { Attorney } from '@/types/attorney-reviews';

// Generate the Amplify data client
const client = generateClient<Schema>();

/**
 * Amplify data service for attorney management
 * Replaces the mock data with real Amplify operations
 */

// Transform Amplify Attorney model to frontend Attorney type
function transformAmplifyAttorney(amplifyAttorney: any): Attorney {
  return {
    id: amplifyAttorney.id,
    name: amplifyAttorney.name,
    firm: amplifyAttorney.firm || '',
    phone: amplifyAttorney.phone,
    email: amplifyAttorney.email,
    address: amplifyAttorney.address || '',
    city: amplifyAttorney.city || '',
    state: amplifyAttorney.state,
    zipCode: amplifyAttorney.zipCode || '',
    specialties: amplifyAttorney.specialties || [],
    barNumber: amplifyAttorney.barNumber || '',
    yearsExperience: amplifyAttorney.yearsExperience || 0,
    rating: amplifyAttorney.rating || 0,
    isPreferred: amplifyAttorney.isPreferred || false,
    isActive: amplifyAttorney.isActive !== false, // Default to true if undefined
  };
}

// Transform frontend Attorney type to Amplify input
function transformToAmplifyInput(attorney: Omit<Attorney, 'id'>): any {
  return {
    name: attorney.name,
    firm: attorney.firm || null,
    phone: attorney.phone,
    email: attorney.email,
    address: attorney.address || null,
    city: attorney.city || null,
    state: attorney.state,
    zipCode: attorney.zipCode || null,
    specialties: attorney.specialties || [],
    barNumber: attorney.barNumber || null,
    yearsExperience: attorney.yearsExperience || 0,
    rating: attorney.rating || 0,
    isPreferred: attorney.isPreferred || false,
    isActive: attorney.isActive !== false,
    status: attorney.isActive !== false ? 'active' : 'inactive',
    createdAt: new Date().toISOString(),
  };
}

/**
 * Fetch all attorneys from Amplify
 */
export async function fetchAttorneys(): Promise<Attorney[]> {
  try {
    const response = await client.models.Attorney.list();

    if (response.errors && response.errors.length > 0) {
      console.error('Errors fetching attorneys:', response.errors);
      throw new Error('Failed to fetch attorneys');
    }

    return response.data.map(transformAmplifyAttorney);
  } catch (error) {
    console.error('Error in fetchAttorneys:', error);
    throw error;
  }
}

/**
 * Fetch a single attorney by ID
 */
export async function fetchAttorney(id: string): Promise<Attorney | null> {
  try {
    const response = await client.models.Attorney.get({ id });

    if (response.errors && response.errors.length > 0) {
      console.error('Errors fetching attorney:', response.errors);
      throw new Error('Failed to fetch attorney');
    }

    return response.data ? transformAmplifyAttorney(response.data) : null;
  } catch (error) {
    console.error('Error in fetchAttorney:', error);
    throw error;
  }
}

/**
 * Create a new attorney
 */
export async function createAttorney(
  attorneyData: Omit<Attorney, 'id'>
): Promise<Attorney> {
  try {
    const input = transformToAmplifyInput(attorneyData);
    const response = await client.models.Attorney.create(input);

    if (response.errors && response.errors.length > 0) {
      console.error('Errors creating attorney:', response.errors);
      throw new Error('Failed to create attorney');
    }

    if (!response.data) {
      throw new Error('No data returned from create attorney');
    }

    return transformAmplifyAttorney(response.data);
  } catch (error) {
    console.error('Error in createAttorney:', error);
    throw error;
  }
}

/**
 * Update an existing attorney
 */
export async function updateAttorney(
  id: string,
  updates: Partial<Omit<Attorney, 'id'>>
): Promise<Attorney> {
  try {
    const input = {
      id,
      ...Object.fromEntries(
        Object.entries(updates).map(([key, value]) => [
          key,
          value === '' ? null : value,
        ])
      ),
      updatedAt: new Date().toISOString(),
    };

    const response = await client.models.Attorney.update(input);

    if (response.errors && response.errors.length > 0) {
      console.error('Errors updating attorney:', response.errors);
      throw new Error('Failed to update attorney');
    }

    if (!response.data) {
      throw new Error('No data returned from update attorney');
    }

    return transformAmplifyAttorney(response.data);
  } catch (error) {
    console.error('Error in updateAttorney:', error);
    throw error;
  }
}

/**
 * Delete an attorney (soft delete by setting isActive to false)
 */
export async function deleteAttorney(id: string): Promise<void> {
  try {
    const response = await client.models.Attorney.update({
      id,
      isActive: false,
      status: 'inactive',
      updatedAt: new Date().toISOString(),
    });

    if (response.errors && response.errors.length > 0) {
      console.error('Errors deleting attorney:', response.errors);
      throw new Error('Failed to delete attorney');
    }
  } catch (error) {
    console.error('Error in deleteAttorney:', error);
    throw error;
  }
}

/**
 * Permanently delete an attorney (hard delete)
 */
export async function permanentlyDeleteAttorney(id: string): Promise<void> {
  try {
    const response = await client.models.Attorney.delete({ id });

    if (response.errors && response.errors.length > 0) {
      console.error('Errors permanently deleting attorney:', response.errors);
      throw new Error('Failed to permanently delete attorney');
    }
  } catch (error) {
    console.error('Error in permanentlyDeleteAttorney:', error);
    throw error;
  }
}
