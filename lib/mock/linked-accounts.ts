import {
  LinkedAccount,
  LinkStatus,
  LinkType,
  User,
  DEFAULT_PERMISSIONS,
} from '@/types/account';

// Mock users with linked accounts
export const mockUsers: User[] = [
  {
    id: 'user_1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Member',
    subrole: 'Basic Member',
    status: 'active',
    createdAt: '2023-01-15T08:30:00Z',
    linkedAccounts: [],
    firstName: '',
    lastName: '',
    cognitoId: '',
  },
  {
    id: 'user_2',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Member',
    subrole: 'Basic Member',
    status: 'active',
    createdAt: '2023-02-20T14:45:00Z',
    linkedAccounts: [],
    firstName: '',
    lastName: '',
    cognitoId: '',
  },
  {
    id: 'user_3',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Member',
    subrole: 'Basic Member',
    status: 'active',
    createdAt: '2023-03-10T11:15:00Z',
    linkedAccounts: [],
    firstName: '',
    lastName: '',
    cognitoId: '',
  },
  {
    id: 'user_4',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Member',
    subrole: 'Basic Member',
    status: 'active',
    createdAt: '2023-04-05T09:20:00Z',
    linkedAccounts: [],
    firstName: '',
    lastName: '',
    cognitoId: '',
  },
  {
    id: 'user_5',
    name: 'Michael Wilson',
    email: '<EMAIL>',
    role: 'Member',
    subrole: 'Basic Member',
    status: 'active',
    createdAt: '2023-05-12T16:30:00Z',
    linkedAccounts: [],
    firstName: '',
    lastName: '',
    cognitoId: '',
  },
];

// Mock linked accounts
export const mockLinkedAccounts: LinkedAccount[] = [
  {
    id: 'link_1',
    userId: 'user_1',
    linkedUserId: 'user_2',
    linkType: 'primary',
    status: 'active',
    permissions: DEFAULT_PERMISSIONS.primary,
    createdAt: '2023-06-15T10:30:00Z',
    updatedAt: '2023-06-15T10:30:00Z',
  },
  {
    id: 'link_2',
    userId: 'user_1',
    linkedUserId: 'user_3',
    linkType: 'secondary',
    status: 'active',
    permissions: DEFAULT_PERMISSIONS.secondary,
    createdAt: '2023-06-20T14:45:00Z',
    updatedAt: '2023-06-20T14:45:00Z',
  },
  {
    id: 'link_3',
    userId: 'user_2',
    linkedUserId: 'user_1',
    linkType: 'delegate',
    status: 'active',
    permissions: DEFAULT_PERMISSIONS.delegate,
    createdAt: '2023-07-05T09:15:00Z',
    updatedAt: '2023-07-05T09:15:00Z',
  },
  {
    id: 'link_4',
    userId: 'user_3',
    linkedUserId: 'user_4',
    linkType: 'emergency',
    status: 'pending',
    permissions: DEFAULT_PERMISSIONS.emergency,
    createdAt: '2023-07-10T11:30:00Z',
    updatedAt: '2023-07-10T11:30:00Z',
  },
  {
    id: 'link_5',
    userId: 'user_4',
    linkedUserId: 'user_5',
    linkType: 'primary',
    status: 'active',
    permissions: DEFAULT_PERMISSIONS.primary,
    createdAt: '2023-08-01T15:20:00Z',
    updatedAt: '2023-08-01T15:20:00Z',
  },
  {
    id: 'link_6',
    userId: 'user_5',
    linkedUserId: 'user_1',
    linkType: 'emergency',
    status: 'revoked',
    permissions: DEFAULT_PERMISSIONS.emergency,
    createdAt: '2023-08-15T13:10:00Z',
    updatedAt: '2023-09-01T10:45:00Z',
  },
];

// Initialize mock data with linked accounts
export function initMockData() {
  // Add linked accounts to users
  mockUsers.forEach(user => {
    user.linkedAccounts = mockLinkedAccounts.filter(
      link => link.userId === user.id
    );
  });

  return {
    users: mockUsers,
    linkedAccounts: mockLinkedAccounts,
  };
}

// Get a user by ID
export function getUserById(userId: string): User | undefined {
  return mockUsers.find(user => user.id === userId);
}

// Get a user by email
export function getUserByEmail(email: string): User | undefined {
  return mockUsers.find(
    user => user.email.toLowerCase() === email.toLowerCase()
  );
}

// Get linked accounts for a user
export function getLinkedAccountsForUser(userId: string): LinkedAccount[] {
  return mockLinkedAccounts.filter(link => link.userId === userId);
}

// Get accounts that a user is linked to
export function getAccountsUserIsLinkedTo(userId: string): LinkedAccount[] {
  return mockLinkedAccounts.filter(link => link.linkedUserId === userId);
}
