import {
  User,
  User<PERSON>tatus,
  <PERSON><PERSON><PERSON>,
  <PERSON>edA<PERSON>unt,
  <PERSON><PERSON><PERSON>,
  LinkStatus,
  DEFAULT_PERMISSIONS,
  WelonTrustAssignment,
} from '@/types/account';

// Mock data generator for testing
export function generateMockUsers(count: number = 10): User[] {
  // Focus primarily on Member and Welon Trust roles
  const roles: Master<PERSON><PERSON>[] = [
    'Member',
    'Member',
    'Member',
    'WelonTrust',
    'WelonTrust',
    'Administrator',
  ];
  const statuses: UserStatus[] = [
    'active',
    'active',
    'active',
    'inactive',
    'pending',
  ];

  const subroles = {
    Member: ['Basic Member'],
    Administrator: ['Advanced', 'Basic', 'Reporting', 'Finance'],
    WelonTrust: ['Advanced', 'Basic', 'Emergency Service', 'Medical'],
    Professional: ['Attorney', 'Financial Advisor', 'Healthcare Provider'],
  };

  // Realistic names for members and welon trust users
  const memberNames = [
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
  ];

  const welonTrustNames = [
    '<PERSON><PERSON> <PERSON>',
    '<PERSON>',
    '<PERSON>',
    'Jonathan <PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
  ];

  const admin<PERSON>ames = ['Admin User', 'System Administrator'];
  const professional<PERSON>ames = ['Attorney Smith', 'Financial Advisor Jones'];

  // Generate Welon Trust assignment for members
  const generateWelonTrustAssignment = (
    userId: string,
    userRole: MasterRole
  ): WelonTrustAssignment | undefined => {
    if (userRole !== 'Member') return undefined;

    // Only assign Welon Trust to about 60% of members
    if (Math.random() > 0.6) return undefined;

    const welonTrustName =
      welonTrustNames[Math.floor(Math.random() * welonTrustNames.length)] ||
      'Dr. Patricia Williams';
    const statuses: WelonTrustAssignment['status'][] = [
      'active',
      'active',
      'pending',
    ];
    const status = statuses[
      Math.floor(Math.random() * statuses.length)
    ] as WelonTrustAssignment['status'];

    return {
      id: `wt-assignment-${userId}`,
      welonTrustUserId: `wt-user-${Math.floor(Math.random() * 1000)}`,
      welonTrustName,
      welonTrustEmail:
        welonTrustName
          .toLowerCase()
          .replace(/\s+/g, '.')
          .replace(/[^a-z.]/g, '') + '@welontrust.com',
      assignedAt: new Date(
        Date.now() - Math.floor(Math.random() * **********)
      ).toISOString(),
      assignedBy: 'admin-user-001',
      status,
    };
  };

  // Generate linked accounts for members
  const generateLinkedAccounts = (
    userId: string,
    userRole: MasterRole
  ): LinkedAccount[] => {
    if (userRole !== 'Member') return [];

    const linkedAccounts: LinkedAccount[] = [];
    const linkTypes: LinkType[] = [
      'primary',
      'secondary',
      'delegate',
      'emergency',
    ];
    const linkStatuses: LinkStatus[] = ['active', 'pending'];

    // Generate 1-3 linked accounts per member
    const numLinks = Math.floor(Math.random() * 3) + 1;

    for (let i = 0; i < numLinks; i++) {
      const linkType = linkTypes[
        Math.floor(Math.random() * linkTypes.length)
      ] as LinkType;
      const status = linkStatuses[
        Math.floor(Math.random() * linkStatuses.length)
      ] as LinkStatus;

      linkedAccounts.push({
        id: `link-${userId}-${i}`,
        userId: userId,
        linkedUserId: `linked-user-${userId}-${i}`,
        linkType,
        status,
        permissions: DEFAULT_PERMISSIONS[linkType] || [],
        createdAt: new Date(
          Date.now() - Math.floor(Math.random() * **********0)
        ).toISOString(),
        updatedAt: new Date(
          Date.now() - Math.floor(Math.random() * **********)
        ).toISOString(),
      });
    }

    return linkedAccounts;
  };

  return Array.from({ length: count }).map((_, i) => {
    const role = roles[Math.floor(Math.random() * roles.length)] as MasterRole;
    const subroleOptions = subroles[role];
    const subrole =
      subroleOptions[Math.floor(Math.random() * subroleOptions.length)] ||
      'Basic';
    const status = statuses[
      Math.floor(Math.random() * statuses.length)
    ] as UserStatus;

    // Select appropriate name based on role
    let name: string;
    let email: string;

    switch (role) {
      case 'Member':
        name = memberNames[i % memberNames.length] || `Member ${i + 1}`;
        email =
          name
            .toLowerCase()
            .replace(/\s+/g, '.')
            .replace(/[^a-z.]/g, '') + '@example.com';
        break;
      case 'WelonTrust':
        name =
          welonTrustNames[i % welonTrustNames.length] || `Welon Trust ${i + 1}`;
        email =
          name
            .toLowerCase()
            .replace(/\s+/g, '.')
            .replace(/[^a-z.]/g, '') + '@welontrust.com';
        break;
      case 'Administrator':
        name = adminNames[i % adminNames.length] || `Admin ${i + 1}`;
        email =
          name
            .toLowerCase()
            .replace(/\s+/g, '.')
            .replace(/[^a-z.]/g, '') + '@admin.com';
        break;
      case 'Professional':
        name =
          professionalNames[i % professionalNames.length] ||
          `Professional ${i + 1}`;
        email =
          name
            .toLowerCase()
            .replace(/\s+/g, '.')
            .replace(/[^a-z.]/g, '') + '@professional.com';
        break;
      default:
        name = `User ${i + 1}`;
        email = `user${i + 1}@example.com`;
    }

    const userId = `user-${i + 10000}`;

    return {
      id: userId,
      name,
      email,
      firstName: name,
      lastName: name,
      phoneNumber: '**********',
      birthdate: '1990-01-01',
      state: 'CA',
      cognitoId: '123456',
      role,
      subrole,
      status,
      createdAt: new Date(
        Date.now() - Math.floor(Math.random() * **********0)
      ).toISOString(),
      howDidYouHearAboutUs: [
        'Google',
        'Facebook',
        'Friend',
        'Other',
        undefined,
      ][Math.floor(Math.random() * 5)],
      linkedAccounts: generateLinkedAccounts(userId, role),
      assignedWelonTrust: generateWelonTrustAssignment(userId, role),
    };
  });
}

// Simulate AWS Amplify Gen2 API delay
export async function fetchUsers(): Promise<User[]> {
  // Simulate network delay
  await new Promise(resolve =>
    setTimeout(resolve, 1000 + Math.random() * 1000)
  );

  // Simulate potential error (5% chance)
  if (Math.random() < 0.05) {
    throw new Error('Failed to fetch users from database');
  }

  return generateMockUsers(25);
}

// Simulate user operations
export async function updateUser(
  userId: string,
  updates: Partial<User>
): Promise<User> {
  await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 500));

  if (Math.random() < 0.1) {
    throw new Error('Failed to update user');
  }

  // Return updated user (mock)
  const users = generateMockUsers(25);
  const user = users.find(u => u.id === userId);
  if (!user) throw new Error('User not found');

  return { ...user, ...updates };
}

export async function deleteUser(userId: string): Promise<void> {
  await new Promise(resolve => setTimeout(resolve, 300 + Math.random() * 300));

  if (Math.random() < 0.1) {
    throw new Error('Failed to delete user');
  }
}

export async function createUser(
  userData: Omit<User, 'id' | 'createdAt'>
): Promise<User> {
  await new Promise(resolve => setTimeout(resolve, 800 + Math.random() * 500));

  if (Math.random() < 0.1) {
    throw new Error('Failed to create user');
  }

  return {
    ...userData,
    id: `user-${Date.now()}`,
    createdAt: new Date().toISOString(),
  };
}
