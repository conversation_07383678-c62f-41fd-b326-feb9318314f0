'use client';

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from 'react';
import { RoleManager, UserRole, UserContext } from './role-manager';

interface RoleContextType {
  userContext: UserContext | null;
  switchRole: (role: UserRole, subrole?: string) => void;
  hasPermission: (permission: string) => boolean;
  canAccessRoute: (route: string) => boolean;
  getDashboardPath: (role: UserRole) => string;
  isLoading: boolean;
}

const RoleContext = createContext<RoleContextType | undefined>(undefined);

interface RoleProviderProps {
  children: ReactNode;
}

export function RoleProvider({ children }: RoleProviderProps) {
  const [userContext, setUserContext] = useState<UserContext | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const roleManager = RoleManager.getInstance();

  // Initialize with default role (member) or load from localStorage
  useEffect(() => {
    const savedRole = localStorage.getItem('currentRole');
    const savedSubrole = localStorage.getItem('currentSubrole');

    if (savedRole) {
      try {
        const context = roleManager.getUserContext(
          savedRole as UserRole,
          savedSubrole || undefined
        );
        setUserContext(context);
      } catch (error) {
        console.error('Error loading saved role:', error);
        // Fallback to member role
        setUserContext(roleManager.getUserContext('member'));
      }
    } else {
      // Default to member role
      setUserContext(roleManager.getUserContext('member'));
    }

    setIsLoading(false);
  }, [roleManager]);

  const switchRole = (role: UserRole, subrole?: string) => {
    try {
      const context = roleManager.getUserContext(role, subrole);
      setUserContext(context);

      // Save to localStorage for persistence
      localStorage.setItem('currentRole', role);
      if (subrole) {
        localStorage.setItem('currentSubrole', subrole);
      } else {
        localStorage.removeItem('currentSubrole');
      }
    } catch (error) {
      console.error('Error switching role:', error);
    }
  };

  // Helper function to get the appropriate dashboard path for a role
  const getDashboardPath = (role: UserRole): string => {
    switch (role) {
      case 'administrator':
        return '/admin';
      case 'welon_trust':
        return '/emergency';
      case 'linked_account':
        return '/linked';
      case 'member':
      default:
        return '/dashboard';
    }
  };

  const hasPermission = (permission: string): boolean => {
    if (!userContext) return false;
    return roleManager.hasPermission(userContext, permission);
  };

  const canAccessRoute = (route: string): boolean => {
    if (!userContext) return false;
    return roleManager.canAccessRoute(userContext, route);
  };

  const value: RoleContextType = {
    userContext,
    switchRole,
    hasPermission,
    canAccessRoute,
    getDashboardPath,
    isLoading,
  };

  return <RoleContext.Provider value={value}>{children}</RoleContext.Provider>;
}

export function useRole(): RoleContextType {
  const context = useContext(RoleContext);
  if (context === undefined) {
    throw new Error('useRole must be used within a RoleProvider');
  }
  return context;
}
