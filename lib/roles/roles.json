{"roles": {"member": {"id": "member", "name": "Member", "description": "Regular member with access to personal estate planning", "permissions": ["view_own_documents", "edit_own_documents", "create_documents", "review_documents", "sign_documents", "access_emergency_features", "manage_emergency_contacts", "view_billing", "edit_profile", "access_educational_content", "manage_linked_accounts", "create_shared_documents", "view_shared_documents"], "routes": ["/dashboard", "/dashboard/member/*", "/member/*", "/dashboard/billing", "/dashboard/settings", "/dashboard/educational-content"]}, "administrator": {"id": "administrator", "name": "Administrator", "description": "System administrator with full access", "permissions": ["manage_users", "manage_roles", "manage_templates", "view_all_documents", "manage_system_settings", "view_reports", "manage_billing_system", "manage_content", "view_analytics", "manage_legal_updates", "manage_quarterly_reviews"], "routes": ["/admin", "/admin/*", "/dashboard/admin/*"]}, "welon_trust": {"id": "welon_trust", "name": "Welon Trust", "description": "Trust administrator for document processing", "permissions": ["view_trust_documents", "upload_signed_documents", "update_document_status", "flag_documents_for_review", "access_emergency_documents", "manage_emergency_access", "view_medical_documents", "notify_emergency_contacts", "log_trust_actions"], "routes": ["/staff", "/staff/*", "/emergency", "/emergency/*"]}, "linked_account": {"id": "linked_account", "name": "Linked Account", "description": "Limited access account linked to a member", "permissions": ["view_shared_documents", "view_emergency_contacts", "emergency_access", "view_dead_man_switch"], "routes": ["/linked", "/linked/*", "/emergency/documents"]}}, "subroles": {"administrator": {"advanced": {"id": "admin_advanced", "name": "Administrator - Advanced", "description": "Full system access including user management", "additional_permissions": ["delete_users", "modify_system_security", "access_audit_logs", "manage_integrations"]}, "basic": {"id": "admin_basic", "name": "Administrator - Basic", "description": "Basic admin tasks and user support", "additional_permissions": ["reset_passwords", "view_user_details", "basic_support_tasks"]}, "reporting": {"id": "admin_reporting", "name": "Administrator - Reporting", "description": "Generate and export reports", "additional_permissions": ["generate_reports", "export_data", "view_analytics_detailed"]}, "finance": {"id": "admin_finance", "name": "Administrator - Finance", "description": "Billing and financial management", "additional_permissions": ["manage_subscriptions", "process_refunds", "view_financial_reports", "manage_pricing"]}}, "welon_trust": {"advanced": {"id": "welon_advanced", "name": "Welon - Advanced", "description": "Full trust access with reporting", "additional_permissions": ["generate_trust_reports", "add_member_notes", "manage_trust_settings"]}, "basic": {"id": "welon_basic", "name": "Welon - Basic", "description": "Basic document processing", "additional_permissions": ["process_documents", "update_statuses", "basic_validation"]}, "emergency": {"id": "welon_emergency", "name": "Welon - Emergency Service", "description": "Emergency access management", "additional_permissions": ["activate_emergency_protocols", "contact_emergency_services", "override_access_controls"]}, "medical": {"id": "welon_medical", "name": "Welon - Medical", "description": "Medical document specialist", "additional_permissions": ["access_medical_documents", "coordinate_with_healthcare", "manage_medical_directives"]}}, "linked_account": {"spouse": {"id": "linked_spouse", "name": "Spouse/Partner", "description": "Full access spouse or partner", "additional_permissions": ["edit_shared_documents", "approve_shared_documents", "execute_emergency_protocol"]}, "family": {"id": "linked_family", "name": "Family Member", "description": "Limited family access", "additional_permissions": ["view_emergency_info", "receive_notifications"]}, "professional": {"id": "linked_professional", "name": "Professional", "description": "Professional advisor access", "additional_permissions": ["view_financial_documents", "provide_recommendations"]}}}}