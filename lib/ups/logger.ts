/**
 * UPS API Logger Utility
 * Provides centralized logging for all UPS API operations
 */

export interface UPSLogContext {
  requestId: string;
  operation: 'auth' | 'create-label' | 'track' | 'download-label';
  trackingNumber?: string;
  userId?: string;
  environment?: 'sandbox' | 'production';
}

export class UPSLogger {
  private static formatMessage(
    context: UPSLogContext,
    message: string
  ): string {
    const prefix = this.getOperationEmoji(context.operation);
    return `${prefix} [${context.requestId}] ${message}`;
  }

  private static getOperationEmoji(operation: string): string {
    switch (operation) {
      case 'auth':
        return '🔐';
      case 'create-label':
        return '📦';
      case 'track':
        return '🔍';
      case 'download-label':
        return '📥';
      default:
        return '🚀';
    }
  }

  static info(context: UPSLogContext, message: string, data?: any): void {
    console.log(this.formatMessage(context, message), data || '');
  }

  static success(context: UPSLogContext, message: string, data?: any): void {
    console.log(`✅ [${context.requestId}] ${message}`, data || '');
  }

  static error(context: UPSLogContext, message: string, error?: any): void {
    console.error(`❌ [${context.requestId}] ${message}`, {
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
      name: error instanceof Error ? error.name : undefined,
      context,
    });
  }

  static warn(context: UPSLogContext, message: string, data?: any): void {
    console.warn(`⚠️ [${context.requestId}] ${message}`, data || '');
  }

  static debug(context: UPSLogContext, message: string, data?: any): void {
    if (process.env.NODE_ENV === 'development') {
      console.debug(`🐛 [${context.requestId}] ${message}`, data || '');
    }
  }

  /**
   * Log API request details
   */
  static logRequest(
    context: UPSLogContext,
    endpoint: string,
    payload?: any
  ): void {
    this.info(context, `API Request to ${endpoint}`, {
      endpoint,
      hasPayload: !!payload,
      payloadSize: payload ? JSON.stringify(payload).length : 0,
    });
  }

  /**
   * Log API response details
   */
  static logResponse(context: UPSLogContext, status: number, data?: any): void {
    this.info(context, `API Response received`, {
      status,
      hasData: !!data,
      dataSize: data ? JSON.stringify(data).length : 0,
    });
  }

  /**
   * Log authentication events
   */
  static logAuth(
    context: UPSLogContext,
    event: 'start' | 'success' | 'failed',
    details?: any
  ): void {
    switch (event) {
      case 'start':
        this.info(context, 'Starting authentication', details);
        break;
      case 'success':
        this.success(context, 'Authentication successful', details);
        break;
      case 'failed':
        this.error(context, 'Authentication failed', details);
        break;
    }
  }

  /**
   * Log label creation events
   */
  static logLabelCreation(
    context: UPSLogContext,
    event: 'start' | 'success' | 'failed',
    details?: any
  ): void {
    switch (event) {
      case 'start':
        this.info(context, 'Starting label creation', details);
        break;
      case 'success':
        this.success(context, 'Label created successfully', details);
        break;
      case 'failed':
        this.error(context, 'Label creation failed', details);
        break;
    }
  }

  /**
   * Log tracking events
   */
  static logTracking(
    context: UPSLogContext,
    event: 'start' | 'success' | 'failed',
    details?: any
  ): void {
    switch (event) {
      case 'start':
        this.info(context, 'Starting package tracking', details);
        break;
      case 'success':
        this.success(context, 'Package tracking successful', details);
        break;
      case 'failed':
        this.error(context, 'Package tracking failed', details);
        break;
    }
  }

  /**
   * Log performance metrics
   */
  static logPerformance(
    context: UPSLogContext,
    operation: string,
    duration: number
  ): void {
    const emoji = duration > 5000 ? '🐌' : duration > 2000 ? '⏱️' : '⚡';
    this.info(context, `${emoji} ${operation} completed in ${duration}ms`);
  }

  /**
   * Create a new request context
   */
  static createContext(
    operation: UPSLogContext['operation'],
    trackingNumber?: string
  ): UPSLogContext {
    return {
      requestId: Date.now().toString(),
      operation,
      trackingNumber,
      environment:
        process.env.NODE_ENV === 'production' ? 'production' : 'sandbox',
    };
  }
}

/**
 * Performance measurement decorator
 */
export function measurePerformance<T extends any[], R>(
  context: UPSLogContext,
  operation: string,
  fn: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R> => {
    const startTime = Date.now();
    try {
      const result = await fn(...args);
      const duration = Date.now() - startTime;
      UPSLogger.logPerformance(context, operation, duration);
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      UPSLogger.logPerformance(context, `${operation} (failed)`, duration);
      throw error;
    }
  };
}
