import {
  UPSShipmentRequest,
  UPSTrackingResponse,
  TrackingStatus,
  ShippingLabelResult,
  UPSAddress,
  UPSShipper,
  UPSShipTo,
  UPSPackage,
} from '@/app/types/ups';
import {
  UPS_SERVICE_CODES,
  UPS_PACKAGE_TYPES,
  UPS_LABEL_FORMATS,
  DEFAULT_PACKAGE_DIMENSIONS,
} from './config';

/**
 * Create a standard UPS shipment request
 */
export function createShipmentRequest(
  fromAddress: UPSAddress & { name: string; phone?: string },
  toAddress: UPSAddress & { name: string; phone?: string },
  serviceCode: string = UPS_SERVICE_CODES.GROUND,
  packageOptions?: Partial<UPSPackage>
): UPSShipmentRequest {
  const shipper: UPSShipper = {
    name: fromAddress.name,
    phone: fromAddress.phone,
    address: {
      addressLine1: fromAddress.addressLine1,
      addressLine2: fromAddress.addressLine2,
      addressLine3: fromAddress.addressLine3,
      city: fromAddress.city,
      stateProvinceCode: fromAddress.stateProvinceCode,
      postalCode: fromAddress.postalCode,
      countryCode: fromAddress.countryCode,
    },
  };

  const shipTo: UPSShipTo = {
    name: toAddress.name,
    phone: toAddress.phone,
    address: {
      addressLine1: toAddress.addressLine1,
      addressLine2: toAddress.addressLine2,
      addressLine3: toAddress.addressLine3,
      city: toAddress.city,
      stateProvinceCode: toAddress.stateProvinceCode,
      postalCode: toAddress.postalCode,
      countryCode: toAddress.countryCode,
    },
  };

  const defaultPackage: UPSPackage = {
    description: 'Package',
    packaging: {
      code: UPS_PACKAGE_TYPES.CUSTOMER_SUPPLIED,
      description: 'Customer Supplied Package',
    },
    dimensions: {
      unitOfMeasurement: {
        code: 'IN',
        description: 'Inches',
      },
      length: DEFAULT_PACKAGE_DIMENSIONS.length,
      width: DEFAULT_PACKAGE_DIMENSIONS.width,
      height: DEFAULT_PACKAGE_DIMENSIONS.height,
    },
    packageWeight: {
      unitOfMeasurement: {
        code: 'LBS',
        description: 'Pounds',
      },
      weight: DEFAULT_PACKAGE_DIMENSIONS.weight,
    },
  };

  const packageInfo = packageOptions
    ? {
        ...defaultPackage,
        ...packageOptions,
        // Ensure nested objects are properly merged
        dimensions: packageOptions.dimensions
          ? { ...defaultPackage.dimensions, ...packageOptions.dimensions }
          : defaultPackage.dimensions,
        packageWeight: packageOptions.packageWeight
          ? { ...defaultPackage.packageWeight, ...packageOptions.packageWeight }
          : defaultPackage.packageWeight,
      }
    : defaultPackage;

  return {
    shipper,
    shipTo,
    service: {
      code: serviceCode,
      description: getServiceDescription(serviceCode),
    },
    package: [packageInfo],
    labelImageFormat: {
      code: UPS_LABEL_FORMATS.PDF,
      description: 'PDF',
    },
    labelStockSize: {
      height: '6',
      width: '4',
    },
  };
}

/**
 * Get service description by code
 */
export function getServiceDescription(serviceCode: string): string {
  const serviceMap: Record<string, string> = {
    [UPS_SERVICE_CODES.GROUND]: 'UPS Ground',
    [UPS_SERVICE_CODES.NEXT_DAY_AIR]: 'UPS Next Day Air',
    [UPS_SERVICE_CODES.NEXT_DAY_AIR_SAVER]: 'UPS Next Day Air Saver',
    [UPS_SERVICE_CODES.NEXT_DAY_AIR_EARLY]: 'UPS Next Day Air Early',
    [UPS_SERVICE_CODES.SECOND_DAY_AIR]: 'UPS 2nd Day Air',
    [UPS_SERVICE_CODES.SECOND_DAY_AIR_AM]: 'UPS 2nd Day Air A.M.',
    [UPS_SERVICE_CODES.THREE_DAY_SELECT]: 'UPS 3 Day Select',
    [UPS_SERVICE_CODES.STANDARD]: 'UPS Standard',
  };

  return serviceMap[serviceCode] || 'UPS Service';
}

/**
 * Convert UPS shipment response to simplified shipping label result
 */
export function parseShipmentResponse(response: any): ShippingLabelResult {
  // UPS returns response in ShipmentResponse.ShipmentResults format
  const shipmentResults = response.ShipmentResponse?.ShipmentResults;
  const packageResults = shipmentResults?.PackageResults;

  if (!shipmentResults || !packageResults) {
    console.error('❌ Invalid UPS response structure:', {
      shipmentResults: !!shipmentResults,
      packageResults: !!packageResults,
      fullResponse: JSON.stringify(response, null, 2),
    });
    throw new Error('Invalid UPS response structure');
  }

  // Handle both single package and array of packages
  const firstPackage = Array.isArray(packageResults)
    ? packageResults[0]
    : packageResults;

  const trackingNumber = firstPackage?.TrackingNumber;
  const labelImage = firstPackage?.ShippingLabel?.GraphicImage;
  const totalCharges = shipmentResults?.ShipmentCharges?.TotalCharges;

  if (!trackingNumber) {
    console.error('❌ No tracking number found in response');
    throw new Error('No tracking number in UPS response');
  }

  if (!labelImage) {
    console.error('❌ No label image found in response');
    throw new Error('No label image in UPS response');
  }

  return {
    trackingNumber,
    labelUrl: `data:image/gif;base64,${labelImage}`,
    cost: {
      amount: totalCharges?.MonetaryValue || '0.00',
      currency: totalCharges?.CurrencyCode || 'USD',
    },
    createdAt: new Date().toISOString(),
  };
}

/**
 * Convert UPS tracking response to simplified tracking status
 */
export function parseTrackingResponse(
  response: UPSTrackingResponse
): TrackingStatus {
  const shipment = response.trackResponse?.shipment?.[0];
  if (!shipment) {
    throw new Error('Invalid tracking response: no shipment data');
  }

  const packageInfo = shipment.package?.[0];
  if (!packageInfo) {
    throw new Error('Invalid tracking response: no package data');
  }

  const currentStatus = packageInfo.currentStatus;
  const activities = packageInfo.activity || [];

  // Determine simplified status
  let status: TrackingStatus['status'] = 'unknown';
  const statusCode = currentStatus?.code?.toLowerCase() || '';

  if (statusCode.includes('delivered') || statusCode === 'd') {
    status = 'delivered';
  } else if (statusCode.includes('transit') || statusCode === 'i') {
    status = 'in_transit';
  } else if (statusCode.includes('exception') || statusCode === 'x') {
    status = 'exception';
  }

  // Get estimated delivery date
  const deliveryDate = packageInfo.deliveryDate?.find(d => d.type === 'DEL');

  // Parse activities with safe access
  const parsedActivities = activities.map(activity => {
    const address = activity.location?.address;
    const city = address?.city || '';
    const state = address?.stateProvinceCode || '';
    const location =
      `${city}, ${state}`.trim().replace(/^,\s*/, '').replace(/,\s*$/, '') ||
      'Unknown Location';

    return {
      status: activity.status?.description || 'Unknown Status',
      location,
      date: activity.date,
      time: activity.time,
    };
  });

  return {
    trackingNumber: packageInfo.trackingNumber || 'Unknown',
    status,
    statusDescription: currentStatus?.description || 'No status available',
    lastUpdate:
      `${currentStatus?.statusDate || ''} ${currentStatus?.statusTime || ''}`.trim() ||
      'Unknown',
    estimatedDelivery: deliveryDate?.date,
    currentLocation: parsedActivities[0]?.location || 'Unknown Location',
    activities: parsedActivities,
  };
}

/**
 * Validate UPS address
 */
export function validateAddress(address: UPSAddress): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!address.addressLine1?.trim()) {
    errors.push('Address line 1 is required');
  }

  if (!address.city?.trim()) {
    errors.push('City is required');
  }

  if (!address.stateProvinceCode?.trim()) {
    errors.push('State/Province code is required');
  }

  if (!address.postalCode?.trim()) {
    errors.push('Postal code is required');
  }

  if (!address.countryCode?.trim()) {
    errors.push('Country code is required');
  }

  // Basic US postal code validation
  if (address.countryCode === 'US' && address.postalCode) {
    const zipRegex = /^\d{5}(-\d{4})?$/;
    if (!zipRegex.test(address.postalCode)) {
      errors.push('Invalid US postal code format');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Format address for display
 */
export function formatAddress(address: UPSAddress): string {
  const lines = [
    address.addressLine1,
    address.addressLine2,
    address.addressLine3,
    `${address.city}, ${address.stateProvinceCode} ${address.postalCode}`,
    address.countryCode,
  ].filter(Boolean);

  return lines.join('\n');
}

/**
 * Calculate estimated delivery date based on service code
 */
export function getEstimatedDeliveryDays(serviceCode: string): number {
  const deliveryDays: Record<string, number> = {
    [UPS_SERVICE_CODES.NEXT_DAY_AIR]: 1,
    [UPS_SERVICE_CODES.NEXT_DAY_AIR_SAVER]: 1,
    [UPS_SERVICE_CODES.NEXT_DAY_AIR_EARLY]: 1,
    [UPS_SERVICE_CODES.SECOND_DAY_AIR]: 2,
    [UPS_SERVICE_CODES.SECOND_DAY_AIR_AM]: 2,
    [UPS_SERVICE_CODES.THREE_DAY_SELECT]: 3,
    [UPS_SERVICE_CODES.GROUND]: 5,
    [UPS_SERVICE_CODES.STANDARD]: 7,
  };

  return deliveryDays[serviceCode] || 5;
}
