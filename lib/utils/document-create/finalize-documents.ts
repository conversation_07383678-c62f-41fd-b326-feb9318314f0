import { fetchUserAttributes } from 'aws-amplify/auth';
import {
  createDocument,
  CreateDocumentData,
  checkDocumentDuplicate,
} from '@/lib/api/documents';
import {
  getMemberAvailableTemplates,
  getMemberInterviewAnswers,
} from '@/lib/api/member-documents';
import { getTemplate } from '@/app/utils/templates';
import { fetchUserByCognitoId } from '@/lib/data/users';
import { processTemplate } from '@/lib/utils/handlebars-engine';
import { cleanTipTapHTML } from '@/lib/utils/html-cleaner';
import { generateClient } from 'aws-amplify/data';
import { Schema } from '@/amplify/data/resource';

// Legacy functions replaced by Handlebars engine
// These functions are now handled by lib/utils/handlebars-engine.ts

/**
 * Fetches template content by template ID
 * @param templateId - The ID of the template to fetch
 * @returns Promise that resolves to the template content or empty string if not found
 */
const fetchTemplateContent = async (templateId: string): Promise<string> => {
  try {
    const { latestVersion } = await getTemplate(templateId);
    return latestVersion?.content || '';
  } catch (error) {
    console.error(
      `Error fetching template content for ID ${templateId}:`,
      error
    );
    return '';
  }
};

/**
 * Creates a document from a template with populated placeholders
 * @param template - The template to create document from
 * @param userAttributes - User attributes for template processing
 * @param userAnswers - User interview answers for template processing
 * @param userId - The user ID to associate with the document
 * @param userState - The user's state (used for documentState)
 * @returns Promise that resolves when document is created
 */
const createDocumentFromTemplate = async (
  template: any,
  userAttributes: Record<string, any>,
  userAnswers: any[],
  userId: string,
  userState: string,
  cognitoId?: string
): Promise<void> => {
  const documentType = template.type || 'Legal Document';
  const documentState = userState; // Use user's state instead of template state
  const templateName =
    template.templateName || `${documentState} ${documentType}`;

  // Map template type to valid document type enum values
  const mapTemplateTypeToDocumentType = (templateType: string): string => {
    const typeMapping: Record<string, string> = {
      Will: 'Will',
      Trust: 'Trust',
      'Healthcare POA': 'Healthcare_POA',
      'Financial POA': 'Financial_POA',
      'Advance Directive': 'Advance_Directive',
      POA: 'Healthcare_POA', // Default POA to Healthcare_POA
      Medical: 'Healthcare_POA',
      Financial: 'Financial_POA',
      Healthcare: 'Healthcare_POA',
    };

    const mappedType = typeMapping[templateType] || 'Other';
    console.log(
      `Mapping template type "${templateType}" to document type "${mappedType}"`
    );
    return mappedType;
  };
  const templateContent = template.latestVersion?.content || '';

  // Fetch start page and end page content if they exist
  const startPageContent = template.startPageTemplateId
    ? await fetchTemplateContent(template.startPageTemplateId)
    : '';
  const endPageContent = template.endPageTemplateId
    ? await fetchTemplateContent(template.endPageTemplateId)
    : '';

  console.log('===> 1  TEMPLATE CONTENT', templateContent);
  // Process templates using Handlebars
  const populatedMainTemplate = await processTemplate(
    templateContent,
    userAttributes,
    userAnswers,
    [], // questions array - will be empty for now
    documentType,
    documentState
  );

  console.log('===> 1  populatedMainTemplate', populatedMainTemplate);
  const populatedStartPage = startPageContent
    ? await processTemplate(
        startPageContent,
        userAttributes,
        userAnswers,
        [],
        documentType,
        documentState
      )
    : '';
  const populatedEndPage = endPageContent
    ? await processTemplate(
        endPageContent,
        userAttributes,
        userAnswers,
        [],
        documentType,
        documentState
      )
    : '';

  // Combine all content parts with proper spacing and section markers
  const contentParts: string[] = [];

  if (populatedStartPage) {
    contentParts.push(
      `<!-- START_PAGE_SECTION -->\n${populatedStartPage}\n<!-- END_START_PAGE_SECTION -->`
    );
  }

  if (populatedMainTemplate) {
    contentParts.push(
      `<!-- MAIN_CONTENT_SECTION -->\n${populatedMainTemplate}\n<!-- END_MAIN_CONTENT_SECTION -->`
    );
  }

  if (populatedEndPage) {
    contentParts.push(
      `<!-- END_PAGE_SECTION -->\n${populatedEndPage}\n<!-- END_END_PAGE_SECTION -->`
    );
  }

  // Join content parts with double line breaks for proper separation
  const finalContent = contentParts.join('\n\n');

  // Clean the HTML content before saving
  const cleanedContent = cleanTipTapHTML(finalContent || templateContent || '');

  // Calculate version if cognitoId is provided
  let version = '1.0';
  if (cognitoId) {
    try {
      const mappedType = mapTemplateTypeToDocumentType(
        template.type || 'Other'
      );
      const duplicateCheck = await checkDocumentDuplicate(
        mappedType,
        cognitoId
      );
      version = duplicateCheck.suggestedVersion;
    } catch (error) {
      console.error('Error calculating version:', error);
      // Fallback to 1.0 if version calculation fails
    }
  }

  const documentData: CreateDocumentData = {
    title: templateName || `${documentType} for ${documentState}`,
    type: mapTemplateTypeToDocumentType(template.type || 'Other') as any,
    status: 'draft',
    version: version,
    content: cleanedContent,
    templateId: template.id,
    templateVersion: template.latestVersion?.versionNumber || null,
    templateContent: cleanedContent,
    documentState: documentState,
  };

  await createDocument(documentData, userId);
};

/**
 * Creates a document from a specific template with user data
 * @param templateId - The ID of the template to create document from
 * @param cognitoId - The user's Cognito ID to create document for
 * @returns Promise that resolves when the document is created
 */
export const createDocumentFromSpecificTemplate = async (
  templateId: string,
  cognitoId: string
): Promise<void> => {
  try {
    // First get user data to determine their state
    const [userAttributes, userData] = await Promise.all([
      fetchUserAttributes(),
      fetchUserByCognitoId(cognitoId),
    ]);

    if (!userData) {
      throw new Error('User data not found');
    }

    // Fetch the specific template and user answers
    const [templateData, userAnswers] = await Promise.all([
      getTemplate(templateId),
      getMemberInterviewAnswers(),
    ]);

    if (!templateData.template) {
      throw new Error(`Template not found with ID: ${templateId}`);
    }

    // Create the template object in the expected format
    const template = {
      ...templateData.template,
      latestVersion: templateData.latestVersion,
    };

    // Create document from the specific template using Handlebars
    await createDocumentFromTemplate(
      template,
      userAttributes,
      userAnswers,
      userData.id,
      userData.state || 'Unknown State',
      cognitoId // Pass cognitoId for version calculation
    );

    console.log(
      `Successfully created document from template ${templateId} for user ${cognitoId}`
    );
  } catch (error) {
    console.error('Error creating document from specific template:', error);
    throw new Error(
      `Failed to finalize documents: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`
    );
  }
};

/**
 * Finalizes documents by creating them from specific templates with user data
 * @param cognitoId - The user's Cognito ID to create documents for
 * @param templatesToCreate - Array of specific templates to create documents from
 * @returns Promise that resolves when all documents are created
 */
export const finalizeDocumentsForTemplates = async (
  cognitoId: string,
  templatesToCreate: any[]
): Promise<void> => {
  try {
    // First get user data to determine their state
    const [userAttributes, userData] = await Promise.all([
      fetchUserAttributes(),
      fetchUserByCognitoId(cognitoId),
    ]);

    if (!userData) {
      throw new Error('User data not found');
    }

    // Fetch user address
    let formattedAddress = '';
    try {
      const client = generateClient<Schema>();

      // Get user's addresses
      const { data: userAddresses, errors: addressErrors } =
        await client.models.UserAddress.list({
          filter: { userId: { eq: userData.id } },
        });

      if (!addressErrors && userAddresses && userAddresses.length > 0) {
        // Get the default address or the first one
        const userAddress =
          userAddresses.find(addr => addr.isDefault) || userAddresses[0];

        // Format the address into a readable string
        const addressLines = [
          userAddress.addressLine1,
          userAddress.addressLine2,
          `${userAddress.city}, ${userAddress.stateProvinceCode} ${userAddress.postalCode}`,
          userAddress.countryCode,
        ].filter(Boolean);

        formattedAddress = addressLines.join('\n');
      }
    } catch (error) {
      console.error('Error fetching user address:', error);
      // Continue without address if there's an error
    }

    // Add the formatted address to userAttributes
    if (formattedAddress) {
      userAttributes.address = formattedAddress;
    }

    // Get user answers
    const userAnswers = await getMemberInterviewAnswers();

    // Process only the specified templates in parallel for better performance using Handlebars
    const documentCreationPromises = templatesToCreate.map(template =>
      createDocumentFromTemplate(
        template,
        userAttributes,
        userAnswers,
        userData.id,
        userData.state || 'Unknown State',
        cognitoId // Pass cognitoId for version calculation
      )
    );

    await Promise.all(documentCreationPromises);

    console.log(
      `Successfully created ${templatesToCreate.length} documents for user ${cognitoId}`
    );
  } catch (error) {
    console.error('Error finalizing documents for templates:', error);
    throw error;
  }
};

/**
 * Finalizes documents by creating them from available templates with user data
 * @param cognitoId - The user's Cognito ID to create documents for
 * @returns Promise that resolves when all documents are created
 */
export const finalizeDocuments = async (cognitoId: string): Promise<void> => {
  try {
    // First get user data to determine their state
    const [userAttributes, userData] = await Promise.all([
      fetchUserAttributes(),
      fetchUserByCognitoId(cognitoId),
    ]);

    if (!userData) {
      throw new Error('User data not found');
    }

    // Then fetch templates and answers using user's state
    const [templates, userAnswers] = await Promise.all([
      getMemberAvailableTemplates(userData.state),
      getMemberInterviewAnswers(),
    ]);

    // Process all templates in parallel for better performance using Handlebars
    const documentCreationPromises = templates.map(template =>
      createDocumentFromTemplate(
        template,
        userAttributes,
        userAnswers,
        userData.id,
        userData.state || 'Unknown State',
        cognitoId // Pass cognitoId for version calculation
      )
    );

    await Promise.all(documentCreationPromises);

    console.log(
      `Successfully created ${templates.length} documents for user ${cognitoId}`
    );
  } catch (error) {
    console.error('Error finalizing documents:', error);
    throw error;
  }
};
