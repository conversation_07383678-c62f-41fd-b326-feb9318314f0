'use client';

import { getUrl } from 'aws-amplify/storage';
import { getCurrentUser } from 'aws-amplify/auth';
import { toast } from 'sonner';
import type { Document } from '@/types/documents';

/**
 * Downloads a document from S3 if available, otherwise shows an error
 * This replaces the old PDF generation approach
 */
export async function downloadDocument(document: Document): Promise<void> {
  try {
    // Check if document has a fileUrl (generated PDF)
    if (!document.fileUrl) {
      toast.error('Document PDF is not available. Please contact support.');
      return;
    }

    // Get current user for proper identity-based access
    const cognitoUser = await getCurrentUser();
    if (!cognitoUser?.userId) {
      toast.error('User not authenticated');
      return;
    }

    console.log(`Downloading document from S3: ${document.fileUrl}`);

    // Use the stored path as-is (<PERSON><PERSON> saves with actual Cognito ID)
    // Just ensure the current user matches the file owner
    let s3Key = document.fileUrl;

    console.log(`Using S3 path: ${s3Key}`);

    // Get signed URL from S3 using key
    const result = await getUrl({
      key: s3Key,
      options: {
        validateObjectExistence: true,
      },
    });

    console.log(`Download URL: ${result.url.toString()}`);

    // Create download link
    const link = window.document.createElement('a');
    link.href = result.url.toString();
    link.download = `${document.title.replace(/[^a-zA-Z0-9]/g, '_')}.pdf`;
    link.target = '_blank';

    // Trigger download
    window.document.body.appendChild(link);
    link.click();
    window.document.body.removeChild(link);

    toast.success(`Downloaded ${document.title}`);
  } catch (error) {
    console.error('Error downloading document:', error);
    toast.error('Failed to download document. Please try again.');
  }
}

/**
 * Downloads a signed document (same as regular download since signed version replaces original)
 */
export async function downloadSignedDocument(
  document: Document
): Promise<void> {
  return downloadDocument(document);
}

/**
 * Checks if a document has a PDF available for download
 */
export function hasDocumentPDF(document: Document): boolean {
  return !!document.fileUrl;
}

/**
 * Gets the download status message for a document
 */
export function getDocumentDownloadStatus(document: Document): string {
  if (!document.fileUrl) {
    return 'PDF not available';
  }

  if (document.status === 'signed') {
    return 'Download signed version';
  }

  return 'Download PDF';
}
