'use client';

// Dynamic import for Handlebars to avoid webpack issues
import Handlebars from 'handlebars';

/**
 * Interface for Handlebars template context
 */
export interface HandlebarsContext {
  user: {
    firstName: string;
    lastName: string;
    fullName: string;
    email: string;
    phone: string;
    address: string;
    state: string;
    dateOfBirth: string;
    gender: string;
  };
  document: {
    type: string;
    state: string;
    todayDate: string;
    currentDate: string;
  };
  answers: Record<
    string,
    {
      value: any;
      type: string;
      questionText?: string;
    }
  >;
  // Flat structure for easier access in templates
  [key: string]: any;
}

/**
 * Register Handlebars helpers - only register once
 */
let helpersRegistered = false;

const registerHelpers = () => {
  if (helpersRegistered) return;

  // Date formatting helper
  Handlebars.registerHelper('formatDate', function (date: string) {
    if (!date) return '';

    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) return date;

    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  });

  // Conditional helpers
  Handlebars.registerHelper('eq', function (a: any, b: any) {
    return a === b;
  });

  Handlebars.registerHelper('ne', function (a: any, b: any) {
    return a !== b;
  });

  Handlebars.registerHelper('exists', function (value: any) {
    return value != null && value !== '';
  });

  // Have helper - checks if value exists and is not empty
  Handlebars.registerHelper('have', function (value: any) {
    return value != null && value !== '' && value !== undefined;
  });

  // Has helper - checks if value exists and is not empty
  Handlebars.registerHelper('has', function (value: any) {
    return value != null && value !== '' && value !== undefined;
  });

  // Lookup helper - access variables with spaces in names
  Handlebars.registerHelper('lookup', function (obj: any, key: string) {
    return obj && obj[key];
  });

  // Uppercase helper
  Handlebars.registerHelper('upper', function (str: string) {
    return str ? str.toUpperCase() : '';
  });

  // Lowercase helper
  Handlebars.registerHelper('lower', function (str: string) {
    return str ? str.toLowerCase() : '';
  });

  helpersRegistered = true;
};

/**
 * Creates Handlebars context from user attributes
 */
export const createUserContext = (
  userAttributes: Record<string, any>
): HandlebarsContext['user'] => {
  const fullName =
    `${userAttributes.given_name || ''} ${
      userAttributes.family_name || ''
    }`.trim() || '';

  return {
    firstName: userAttributes.given_name || '',
    lastName: userAttributes.family_name || '',
    fullName,
    email: userAttributes.email || '',
    phone: userAttributes.phone_number || '',
    address: userAttributes.address || '',
    state: userAttributes.address || '', // address field contains state
    dateOfBirth: userAttributes.birthdate || '',
    gender: userAttributes.gender || '',
  };
};

/**
 * Creates answers context from user interview answers
 */
export const createAnswersContext = (
  userAnswers: any[],
  questions: any[] = []
): HandlebarsContext['answers'] => {
  const answers: HandlebarsContext['answers'] = {};

  if (!Array.isArray(userAnswers)) {
    return answers;
  }

  // Create a map of questionId to question for quick lookup
  const questionMap = new Map<string, any>();
  questions.forEach(question => {
    if (question?.questionId) {
      questionMap.set(question.questionId, question);
    }
  });

  userAnswers.forEach(answerData => {
    if (answerData?.questionMapping && answerData?.answer !== undefined) {
      // Clean the mapping name (remove brackets in proper order)
      let cleanMapping = answerData.questionMapping;

      // First, handle complex cases like [{{variable}}] -> variable
      cleanMapping = cleanMapping.replace(/^\[\{\{(.+)\}\}\]$/, '$1');

      // Then handle simple cases
      cleanMapping = cleanMapping.replace(/^\{\{(.+)\}\}$/, '$1'); // {{variable}} -> variable
      cleanMapping = cleanMapping.replace(/^\[(.+)\]$/, '$1'); // [variable] -> variable

      const question = questionMap.get(answerData.questionId);

      console.log(
        `===> CLEAN MAPPING: "${answerData.questionMapping}" -> "${cleanMapping}"`
      );

      // Try to parse the answer if it's JSON (for arrays, objects)
      let parsedAnswer = answerData.answer;
      try {
        parsedAnswer = JSON.parse(answerData.answer);
      } catch {
        // If parsing fails, keep as string
      }

      // Convert boolean values to Yes/No for template compatibility
      let displayValue = parsedAnswer;
      if (typeof parsedAnswer === 'boolean') {
        displayValue = parsedAnswer ? 'Yes' : 'No';
      }

      answers[cleanMapping] = {
        value: displayValue,
        type: question?.type || 'text',
        questionText: question?.text,
      };

      console.log(
        `===> ADDED TO ANSWERS: "${cleanMapping}" = "${parsedAnswer}"`
      );
    } else {
      console.log(`===> SKIPPED ANSWER (no mapping or answer):`, {
        questionId: answerData?.questionId,
        hasMapping: !!answerData?.questionMapping,
        hasAnswer: !!answerData?.answer,
      });
    }
  });

  console.log('===> HANDLEBARS ANSWERS CONTEXT:', answers);
  return answers;
};

/**
 * Creates document context
 */
export const createDocumentContext = (
  documentType?: string,
  documentState?: string
): HandlebarsContext['document'] => {
  const currentDate = new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  return {
    type: documentType || '',
    state: documentState || '',
    todayDate: currentDate,
    currentDate,
  };
};

/**
 * Main function to render Handlebars templates
 * Works both synchronously and asynchronously
 */
export const renderTemplate = (
  templateContent: string,
  context: HandlebarsContext
): string => {
  try {
    // Register helpers
    registerHelpers();

    console.log('===> RENDERING TEMPLATE WITH CONTEXT:');
    console.log('Template snippet:', templateContent.substring(0, 200) + '...');
    console.log(
      'Context answers:',
      Object.keys(context.answers).map(key => ({
        key,
        value: context.answers[key].value,
        type: typeof context.answers[key].value,
      }))
    );
    console.log(
      'Context flat keys:',
      Object.keys(context).filter(
        key => !['user', 'document', 'answers'].includes(key)
      )
    );

    // Compile and render template
    const template = Handlebars.compile(templateContent);
    const result = template(context);

    console.log('===> HANDLEBARS RENDERED SUCCESSFULLY');
    console.log('Result snippet:', result.substring(0, 300) + '...');
    return result;
  } catch (error) {
    console.error('Error rendering Handlebars template:', error);
    console.error(
      'Template content:',
      templateContent.substring(0, 200) + '...'
    );
    console.error('Context:', context);
    return templateContent; // Return original content if compilation fails
  }
};

/**
 * Synchronous version of renderHandlebarsTemplate for client-side use
 * @deprecated Use renderTemplate instead
 */
export const renderHandlebarsTemplateSync = (
  templateContent: string,
  context: HandlebarsContext
): string => {
  return renderTemplate(templateContent, context);
};

/**
 * Compiles and renders a Handlebars template
 * @deprecated Use renderTemplate instead
 */
export const renderHandlebarsTemplate = async (
  templateContent: string,
  context: HandlebarsContext
): Promise<string> => {
  return renderTemplate(templateContent, context);
};

/**
 * Creates complete Handlebars context from all data sources
 */
export const createHandlebarsContext = (
  userAttributes: Record<string, any>,
  userAnswers: any[] = [],
  questions: any[] = [],
  documentType?: string,
  documentState?: string
): HandlebarsContext => {
  const userContext = createUserContext(userAttributes);
  const documentContext = createDocumentContext(documentType, documentState);
  const answersContext = createAnswersContext(userAnswers, questions);

  // Create flat structure for easier template access
  const flatContext: HandlebarsContext = {
    user: userContext,
    document: documentContext,
    answers: answersContext,
    // Add execution date variables
    execution_date_day: new Date().getDate().toString(),
    execution_date_month: new Date().toLocaleDateString('en-US', {
      month: 'long',
    }),
    execution_date_year: new Date().getFullYear().toString(),
  };

  // Add all answer values directly to the root context for easier access
  Object.keys(answersContext).forEach(key => {
    flatContext[key] = answersContext[key].value;
  });

  // Add user properties to root context
  Object.keys(userContext).forEach(key => {
    flatContext[`user.${key}`] = (userContext as any)[key];
  });

  // Add document properties to root context
  Object.keys(documentContext).forEach(key => {
    flatContext[`document.${key}`] = (documentContext as any)[key];
  });

  return flatContext;
};

/**
 * Synchronous fallback for client-side template processing
 * @deprecated Use processTemplate instead
 */
export const processTemplateSync = (
  templateContent: string,
  userAttributes: Record<string, any>,
  userAnswers: any[] = [],
  questions: any[] = [],
  documentType?: string,
  documentState?: string
): string => {
  const context = createHandlebarsContext(
    userAttributes,
    userAnswers,
    questions,
    documentType,
    documentState
  );

  console.log('===> PROCESS TEMPLATE SYNC INPUT:', {
    templateContent: templateContent.substring(0, 100) + '...',
    userAnswersCount: userAnswers.length,
    answersContextKeys: Object.keys(context.answers),
  });

  return renderTemplate(templateContent, context);
};

/**
 * Main function to replace placeholders using Handlebars
 * This replaces the old regex-based replacePlaceholders function
 */
export const processTemplate = async (
  templateContent: string,
  userAttributes: Record<string, any>,
  userAnswers: any[] = [],
  questions: any[] = [],
  documentType?: string,
  documentState?: string
): Promise<string> => {
  const context = createHandlebarsContext(
    userAttributes,
    userAnswers,
    questions,
    documentType,
    documentState
  );

  console.log('===> PROCESS TEMPLATE INPUT:', {
    templateContent: templateContent.substring(0, 100) + '...',
    userAnswersCount: userAnswers.length,
    answersContextKeys: Object.keys(context.answers),
  });

  return renderTemplate(templateContent, context);
};
