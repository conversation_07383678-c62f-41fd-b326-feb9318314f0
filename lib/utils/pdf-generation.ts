import type { Document } from '@/types/documents';

export const buildFullHtml = (document: Document): string => {
  // Clean the document content to remove any problematic CSS
  const cleanContent = document.content;

  // Show watermark if document is not signed
  const shouldShowWatermark =
    document.status !== 'approved' &&
    document.status !== 'signed' &&
    document.status !== 'shipped';

  // For the new PDF generation, we'll return the raw content
  // The parsing and sectioning will be handled in generateAndDownloadPDF
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <title>${document.title}</title>
      <style>
        /* Legal Document Formatting Standards */
        @page {
          size: 8.5in 11in;
          margin: 1in; /* 1 inch margins on all sides */
        }

        body {
          font-family: 'Times New Roman', serif;
          font-size: 12pt; /* 12 pt font size */
          line-height: 2.0; /* Double line spacing for main text */
          color: #000000; /* Plain black text */
          max-width: 6.5in; /* 8.5in - 2in margins */
          margin: 0 auto;
          padding: 1in;
          background-color: #ffffff;
          position: relative;
          text-align: justify; /* Justified alignment for main text */
        }

        /* Paragraph formatting */
        p {
          text-indent: 0.5in; /* First line indent of 0.5 inch */
          margin-bottom: 0; /* Remove default margins */
          margin-top: 0;
          line-height: 2.0; /* Double spacing */
          text-align: justify; /* Justified alignment */
        }

        /* Headings and titles */
        h1, h2, h3, h4, h5, h6 {
          text-align: center; /* Center alignment for headings */
          font-weight: bold;
          margin-top: 1em;
          margin-bottom: 0.5em;
          line-height: 2.0;
        }

        h1 {
          font-size: 12pt; /* Same as body text unless specified */
          text-transform: uppercase; /* For main titles */
        }

        h2, h3, h4, h5, h6 {
          font-size: 12pt;
        }

        /* Document info section */
        .document-info {
          text-align: center;
          color: #000000;
          font-size: 12pt;
          margin-bottom: 2rem;
          line-height: 2.0;
        }

        /* Main content */
        .content {
          text-align: justify;
          line-height: 2.0;
        }

        /* Bold formatting for article names and main headings */
        strong, b {
          font-weight: bold;
        }

        /* Italic formatting for defined terms and legal references */
        em, i {
          font-style: italic;
        }

        /* Underline - avoid unless required by jurisdiction */
        u {
          text-decoration: underline;
        }

        /* Block quotes - single line spacing */
        blockquote {
          line-height: 1.0; /* Single spacing for block quotes */
          margin: 1em 0;
          padding-left: 1in;
          text-indent: 0; /* No first line indent for quotes */
        }

        /* Signature blocks - centered with single spacing */
        .signature-block {
          text-align: center;
          line-height: 1.0; /* Single spacing for signature areas */
          margin: 2em 0;
          page-break-inside: avoid; /* Keep signature blocks together */
        }

        /* Witness/Notary blocks - single spacing */
        .witness-block, .notary-block {
          line-height: 1.0; /* Single spacing */
          margin: 1em 0;
          page-break-inside: avoid;
        }

        /* Numbered articles and sections */
        .article {
          margin: 1em 0;
          text-indent: 0.5in;
        }

        .article-title {
          font-weight: bold;
          text-align: center;
          margin-bottom: 0.5em;
        }

        /* Page breaks */
        .page-break {
          page-break-before: always;
        }

        /* Section breaks */
        .section-break {
          page-break-before: auto;
          margin-top: 2em;
        }

        /* Lists */
        ol, ul {
          line-height: 2.0;
          margin: 1em 0;
        }

        li {
          margin-bottom: 0.5em;
        }

        /* Tables */
        table {
          width: 100%;
          border-collapse: collapse;
          margin: 1em 0;
          line-height: 1.5;
        }

        th, td {
          border: 1px solid #000000;
          padding: 0.25in;
          text-align: left;
        }

        th {
          font-weight: bold;
          text-align: center;
        }

        /* Special formatting for legal documents */
        .party-names {
          text-transform: uppercase; /* UPPERCASE for party names */
          font-weight: bold;
        }

        .defined-term {
          font-style: italic;
        }

        .legal-reference {
          font-style: italic;
        }

        /* Footer for page numbers */
        .page-footer {
          position: fixed;
          bottom: 0.5in;
          left: 0;
          right: 0;
          text-align: center;
          font-size: 12pt;
          line-height: 1.0;
        }

        /* Hide page number on first page */
        .first-page .page-footer {
          display: none;
        }
        /* Better spacing for legal documents */
        .content p {
          margin-bottom: 0.5rem;
        }
        .content h1, .content h2, .content h3 {
          text-align: center;
          margin: 1rem 0;
        }
        /* Ensure all colors are basic hex values */
        * {
          color: inherit;
          background-color: transparent;
        }
      </style>
    </head>
    <body>
      <h1>${document.title} ${shouldShowWatermark ? '- DRAFT' : ''}</h1>
      <div class="document-info">
        Document Type: ${document.type} | Version: ${document.version} |
        Created: ${new Date(document.dateCreated).toLocaleDateString()}
      </div>
      <div class="content">
        ${cleanContent}
      </div>
    </body>
    </html>
  `;
};

// Helper function to clean document content for PDF generation
function cleanDocumentContentForPDF(content: string): string {
  if (!content) return '';

  let cleaned = content;

  // Clean inline styles more selectively - preserve important formatting
  cleaned = cleaned.replace(/style="([^"]*)"/gi, (_, styleContent) => {
    let cleanedStyle = styleContent;

    // Remove only problematic CSS functions, preserve basic formatting
    cleanedStyle = cleanedStyle.replace(/oklch\s*\([^)]+\)/gi, '#333333');
    cleanedStyle = cleanedStyle.replace(/hsl\s*\([^)]+\)/gi, '#333333');
    cleanedStyle = cleanedStyle.replace(/var\s*\([^)]+\)/gi, '#333333');
    cleanedStyle = cleanedStyle.replace(/calc\s*\([^)]+\)/gi, '1rem');
    cleanedStyle = cleanedStyle.replace(/clamp\s*\([^)]+\)/gi, '1rem');

    // Remove CSS variables
    cleanedStyle = cleanedStyle.replace(/--[a-zA-Z0-9-_]+\s*:\s*[^;]+;/gi, '');

    // Clean up malformed CSS
    cleanedStyle = cleanedStyle.replace(/:\s*;/g, '');
    cleanedStyle = cleanedStyle.replace(/;\s*;/g, ';');

    // If style is empty after cleaning, remove the attribute
    cleanedStyle = cleanedStyle.trim();
    if (!cleanedStyle) {
      return '';
    }

    return `style="${cleanedStyle}"`;
  });

  // Replace any remaining CSS color functions in the content (not in attributes)
  cleaned = cleaned.replace(/oklch\s*\([^)]+\)/gi, '#333333');
  cleaned = cleaned.replace(/var\s*\([^)]+\)/gi, '#333333');
  cleaned = cleaned.replace(/calc\s*\([^)]+\)/gi, '1rem');

  return cleaned;
}

export const generatePdfFilename = (document: Document): string => {
  const sanitizedTitle = document.title.replace(/[^a-zA-Z0-9]/g, '_');
  const currentDate = new Date().toISOString().split('T')[0];
  return `${sanitizedTitle}_${currentDate}.pdf`;
};
