import * as z from 'zod';

// US States for validation
export const usStates = [
  'AL',
  'AK',
  'AZ',
  'AR',
  'CA',
  'CO',
  'CT',
  'DE',
  'FL',
  'GA',
  'HI',
  'ID',
  'IL',
  'IN',
  'IA',
  'KS',
  'KY',
  'LA',
  'ME',
  'MD',
  'MA',
  'MI',
  'MN',
  'MS',
  'MO',
  'MT',
  'NE',
  'NV',
  'NH',
  'NJ',
  'NM',
  'NY',
  'NC',
  'ND',
  'OH',
  'OK',
  'OR',
  'PA',
  'RI',
  'SC',
  'SD',
  'TN',
  'TX',
  'UT',
  'VT',
  'VA',
  'WA',
  'WV',
  'WI',
  'WY',
] as const;

// Common legal specialties
export const legalSpecialties = [
  'Estate Planning',
  'Wills & Trusts',
  'Probate',
  'Elder Law',
  'Tax Law',
  'Business Law',
  'Trust Administration',
  'Asset Protection',
  'Guardianship',
  'Special Needs Planning',
  'Real Estate Law',
  'Family Law',
  'Corporate Law',
  'Immigration Law',
  'Criminal Law',
  'Personal Injury',
  'Employment Law',
  'Intellectual Property',
  'Environmental Law',
  'Healthcare Law',
] as const;

// Phone number regex for US phone numbers
const phoneRegex =
  /^(\+1\s?)?(\([0-9]{3}\)|[0-9]{3})[\s\-]?[0-9]{3}[\s\-]?[0-9]{4}$/;

// Base attorney form schema
export const attorneyFormSchema = z.object({
  name: z
    .string()
    .min(1, 'Name is required')
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name must be less than 100 characters')
    .trim(),

  firm: z
    .string()
    .max(100, 'Firm name must be less than 100 characters')
    .trim()
    .default(''),

  phone: z
    .string()
    .min(1, 'Phone number is required')
    .regex(phoneRegex, 'Please enter a valid US phone number')
    .trim(),

  email: z
    .string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address')
    .max(255, 'Email must be less than 255 characters')
    .toLowerCase()
    .trim(),

  address: z
    .string()
    .max(200, 'Address must be less than 200 characters')
    .trim()
    .default(''),

  city: z
    .string()
    .max(100, 'City must be less than 100 characters')
    .trim()
    .default(''),

  state: z.enum(usStates, {
    required_error: 'Please select a state',
    invalid_type_error: 'Invalid state selected',
  }),

  zipCode: z
    .string()
    .regex(
      /^$|\d{5}(-\d{4})?$/,
      'Please enter a valid ZIP code (12345 or 12345-6789)'
    )
    .default(''),

  specialties: z.array(z.string()).default([]),

  barNumber: z
    .string()
    .max(50, 'Bar number must be less than 50 characters')
    .trim()
    .default(''),

  yearsExperience: z
    .number()
    .min(0, 'Years of experience cannot be negative')
    .max(70, 'Years of experience seems too high')
    .default(0),

  isPreferred: z.boolean().default(false),

  isActive: z.boolean().default(true),
});

export type AttorneyFormData = z.infer<typeof attorneyFormSchema>;

// Form props interface
export interface AttorneyFormProps {
  attorney?: any | null; // Using any to match existing Attorney type
  mode: 'create' | 'edit';
  onSave?: (attorneyData: AttorneyFormData) => Promise<void> | void;
  onDelete?: () => Promise<void> | void;
  isLoading?: boolean;
}

// Submission data interface
export interface AttorneySubmissionData extends AttorneyFormData {
  id?: string;
}

// Helper function to get default values
export function getDefaultAttorneyFormValues(
  attorney?: any,
  mode?: 'create' | 'edit'
): AttorneyFormData {
  if (mode === 'edit' && attorney) {
    return {
      name: attorney.name || '',
      firm: attorney.firm || '',
      phone: attorney.phone || '',
      email: attorney.email || '',
      address: attorney.address || '',
      city: attorney.city || '',
      state: attorney.state || 'CA',
      zipCode: attorney.zipCode || '',
      specialties: attorney.specialties || [],
      barNumber: attorney.barNumber || '',
      yearsExperience: attorney.yearsExperience || 0,
      isPreferred: attorney.isPreferred || false,
      isActive: attorney.isActive !== false,
    };
  }

  return {
    name: '',
    firm: '',
    phone: '',
    email: '',
    address: '',
    city: '',
    state: 'CA',
    zipCode: '',
    specialties: [],
    barNumber: '',
    yearsExperience: 0,
    isPreferred: false,
    isActive: true,
  };
}
