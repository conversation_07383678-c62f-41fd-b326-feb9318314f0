# Drag and Drop Implementation Test Guide

## What was implemented:

1. **Added reorderQuestions function** to `lib/api/interview-builder-new.ts`

   - Creates a new version with reordered questions
   - Maintains head question logic and default next question IDs
   - Follows the same pattern as other API functions

2. **Updated main page** (`app/(protected)/admin/interview-builder/build/[id]/page.tsx`)

   - Added DndProvider with HTML5Backend
   - Wrapped the entire interview builder with drag and drop context

3. **Enhanced QuestionBuilder component** (`app/components/dashboard/admin/interview-builder-new/question-builder.tsx`)

   - Added drag state management with `draggedQuestions` state
   - Added reorder mutation with React Query
   - Added drag and drop handlers: `handleMoveQuestion` and `handleDragEnd`
   - Updated table structure to use DraggableQuestionRow for main questions
   - Added drag handle column to table header

4. **Updated DraggableQuestionRow component** (`app/components/dashboard/admin/interview-builder-new/draggable-question-row.tsx`)
   - Fixed column structure to match the new table layout
   - Separated drag handle and order badge into different columns

## How to test:

1. Navigate to an interview builder page: `/admin/interview-builder/build/[id]`
2. Go to the "Question Builder" tab
3. If there are questions, you should see:
   - A grip handle (⋮⋮) in the first column of each main question row
   - Questions should be draggable by clicking and dragging the grip handle
   - Visual feedback during drag (opacity change, hover highlighting)
   - Questions should reorder in real-time during drag
   - On drop, the API should be called to persist the new order

## Key features:

- **Only main questions are draggable** - Conditional questions remain fixed under their parent
- **Real-time visual feedback** during drag operations
- **Optimistic updates** - UI updates immediately, then syncs with backend
- **Error handling** - Reverts changes if API call fails
- **Version management** - Creates new interview version with reordered questions

## Technical details:

- Uses react-dnd with HTML5Backend for drag and drop
- Maintains question relationships and conditional logic
- Preserves all question metadata during reordering
- Uses React Query for state management and API calls
